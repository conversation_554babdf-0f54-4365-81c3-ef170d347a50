#!/usr/bin/env node

/**
 * 测试报告生成脚本
 * 收集各种测试结果并生成综合测试报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TestReportGenerator {
  constructor() {
    this.reportData = {};
    this.templatePath = path.join(__dirname, '../test-report-template.md');
    this.outputPath = path.join(__dirname, '../test-reports');
  }

  async generateReport() {
    console.log('🚀 开始生成测试报告...');

    try {
      // 确保输出目录存在
      if (!fs.existsSync(this.outputPath)) {
        fs.mkdirSync(this.outputPath, { recursive: true });
      }

      // 收集测试数据
      await this.collectTestResults();
      await this.collectPerformanceResults();
      await this.collectSecurityResults();
      await this.collectCoverageResults();
      await this.collectBugData();

      // 生成报告
      await this.generateHTMLReport();
      await this.generateMarkdownReport();
      await this.generateJSONReport();

      console.log('✅ 测试报告生成完成！');
      console.log(`📁 报告位置: ${this.outputPath}`);

    } catch (error) {
      console.error('❌ 报告生成失败:', error.message);
      process.exit(1);
    }
  }

  async collectTestResults() {
    console.log('📊 收集功能测试结果...');

    try {
      // 收集Jest测试结果
      const jestResults = this.parseJestResults();
      
      // 收集Vitest测试结果
      const vitestResults = this.parseVitestResults();
      
      // 收集Playwright测试结果
      const playwrightResults = this.parsePlaywrightResults();

      this.reportData.functional = {
        jest: jestResults,
        vitest: vitestResults,
        playwright: playwrightResults,
        summary: this.calculateFunctionalSummary(jestResults, vitestResults, playwrightResults)
      };

    } catch (error) {
      console.warn('⚠️ 功能测试结果收集失败:', error.message);
      this.reportData.functional = this.getDefaultFunctionalData();
    }
  }

  parseJestResults() {
    const resultsPath = path.join(__dirname, '../backend/coverage/test-results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      return {
        totalTests: results.numTotalTests || 0,
        passedTests: results.numPassedTests || 0,
        failedTests: results.numFailedTests || 0,
        skippedTests: results.numPendingTests || 0,
        passRate: results.numTotalTests > 0 ? 
          ((results.numPassedTests / results.numTotalTests) * 100).toFixed(2) : 0
      };
    }
    
    return this.getDefaultTestData();
  }

  parseVitestResults() {
    const resultsPath = path.join(__dirname, '../frontend/test-results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      return {
        totalTests: results.numTotalTests || 0,
        passedTests: results.numPassedTests || 0,
        failedTests: results.numFailedTests || 0,
        skippedTests: results.numSkippedTests || 0,
        passRate: results.numTotalTests > 0 ? 
          ((results.numPassedTests / results.numTotalTests) * 100).toFixed(2) : 0
      };
    }
    
    return this.getDefaultTestData();
  }

  parsePlaywrightResults() {
    const resultsPath = path.join(__dirname, '../e2e/test-results/results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      return {
        totalTests: results.stats?.total || 0,
        passedTests: results.stats?.passed || 0,
        failedTests: results.stats?.failed || 0,
        skippedTests: results.stats?.skipped || 0,
        passRate: results.stats?.total > 0 ? 
          ((results.stats.passed / results.stats.total) * 100).toFixed(2) : 0
      };
    }
    
    return this.getDefaultTestData();
  }

  async collectPerformanceResults() {
    console.log('⚡ 收集性能测试结果...');

    try {
      const performanceResults = this.parseK6Results();
      this.reportData.performance = performanceResults;
    } catch (error) {
      console.warn('⚠️ 性能测试结果收集失败:', error.message);
      this.reportData.performance = this.getDefaultPerformanceData();
    }
  }

  parseK6Results() {
    const resultsPath = path.join(__dirname, '../performance-tests/results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      return {
        httpReqDuration: {
          avg: results.metrics?.http_req_duration?.values?.avg || 0,
          p95: results.metrics?.http_req_duration?.values?.['p(95)'] || 0,
          p99: results.metrics?.http_req_duration?.values?.['p(99)'] || 0
        },
        httpReqFailed: {
          rate: (results.metrics?.http_req_failed?.values?.rate || 0) * 100
        },
        httpReqs: {
          rate: results.metrics?.http_reqs?.values?.rate || 0
        },
        virtualUsers: results.metrics?.vus?.values?.value || 0
      };
    }
    
    return this.getDefaultPerformanceData();
  }

  async collectSecurityResults() {
    console.log('🔒 收集安全测试结果...');

    try {
      // 收集OWASP ZAP结果
      const zapResults = this.parseZapResults();
      
      // 收集Snyk结果
      const snykResults = this.parseSnykResults();

      this.reportData.security = {
        zap: zapResults,
        snyk: snykResults,
        summary: this.calculateSecuritySummary(zapResults, snykResults)
      };

    } catch (error) {
      console.warn('⚠️ 安全测试结果收集失败:', error.message);
      this.reportData.security = this.getDefaultSecurityData();
    }
  }

  parseZapResults() {
    const resultsPath = path.join(__dirname, '../security-tests/zap-results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const alerts = results.site?.[0]?.alerts || [];
      return {
        high: alerts.filter(alert => alert.riskdesc === 'High').length,
        medium: alerts.filter(alert => alert.riskdesc === 'Medium').length,
        low: alerts.filter(alert => alert.riskdesc === 'Low').length,
        info: alerts.filter(alert => alert.riskdesc === 'Informational').length
      };
    }
    
    return { high: 0, medium: 0, low: 0, info: 0 };
  }

  parseSnykResults() {
    const resultsPath = path.join(__dirname, '../security-tests/snyk-results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      return {
        critical: results.vulnerabilities?.critical || 0,
        high: results.vulnerabilities?.high || 0,
        medium: results.vulnerabilities?.medium || 0,
        low: results.vulnerabilities?.low || 0
      };
    }
    
    return { critical: 0, high: 0, medium: 0, low: 0 };
  }

  async collectCoverageResults() {
    console.log('📈 收集代码覆盖率结果...');

    try {
      const backendCoverage = this.parseBackendCoverage();
      const frontendCoverage = this.parseFrontendCoverage();

      this.reportData.coverage = {
        backend: backendCoverage,
        frontend: frontendCoverage,
        overall: this.calculateOverallCoverage(backendCoverage, frontendCoverage)
      };

    } catch (error) {
      console.warn('⚠️ 代码覆盖率收集失败:', error.message);
      this.reportData.coverage = this.getDefaultCoverageData();
    }
  }

  parseBackendCoverage() {
    const coveragePath = path.join(__dirname, '../backend/coverage/coverage-summary.json');
    
    if (fs.existsSync(coveragePath)) {
      const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
      return {
        lines: coverage.total?.lines?.pct || 0,
        functions: coverage.total?.functions?.pct || 0,
        branches: coverage.total?.branches?.pct || 0,
        statements: coverage.total?.statements?.pct || 0
      };
    }
    
    return { lines: 0, functions: 0, branches: 0, statements: 0 };
  }

  parseFrontendCoverage() {
    const coveragePath = path.join(__dirname, '../frontend/coverage/coverage-summary.json');
    
    if (fs.existsSync(coveragePath)) {
      const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
      return {
        lines: coverage.total?.lines?.pct || 0,
        functions: coverage.total?.functions?.pct || 0,
        branches: coverage.total?.branches?.pct || 0,
        statements: coverage.total?.statements?.pct || 0
      };
    }
    
    return { lines: 0, functions: 0, branches: 0, statements: 0 };
  }

  async collectBugData() {
    console.log('🐛 收集缺陷数据...');

    try {
      // 这里可以集成到缺陷管理系统
      // 目前使用模拟数据
      this.reportData.bugs = this.getDefaultBugData();
    } catch (error) {
      console.warn('⚠️ 缺陷数据收集失败:', error.message);
      this.reportData.bugs = this.getDefaultBugData();
    }
  }

  async generateMarkdownReport() {
    console.log('📝 生成Markdown报告...');

    const template = fs.readFileSync(this.templatePath, 'utf8');
    const report = this.replaceTemplateVariables(template);
    
    const outputFile = path.join(this.outputPath, `test-report-${this.getTimestamp()}.md`);
    fs.writeFileSync(outputFile, report);
    
    console.log(`✅ Markdown报告已生成: ${outputFile}`);
  }

  async generateHTMLReport() {
    console.log('🌐 生成HTML报告...');

    const htmlTemplate = this.generateHTMLTemplate();
    const outputFile = path.join(this.outputPath, `test-report-${this.getTimestamp()}.html`);
    
    fs.writeFileSync(outputFile, htmlTemplate);
    console.log(`✅ HTML报告已生成: ${outputFile}`);
  }

  async generateJSONReport() {
    console.log('📋 生成JSON报告...');

    const outputFile = path.join(this.outputPath, `test-data-${this.getTimestamp()}.json`);
    
    fs.writeFileSync(outputFile, JSON.stringify(this.reportData, null, 2));
    console.log(`✅ JSON数据已生成: ${outputFile}`);
  }

  replaceTemplateVariables(template) {
    const now = new Date();
    const variables = {
      REPORT_DATE: now.toISOString().split('T')[0],
      GENERATED_TIME: now.toLocaleString('zh-CN'),
      TEST_MANAGER: '测试团队',
      TEST_TEAM: '张三、李四、王五',
      
      // 功能测试数据
      FUNCTIONAL_PASS_RATE: this.reportData.functional?.summary?.passRate || '0',
      TOTAL_TEST_CASES: this.reportData.functional?.summary?.totalTests || '0',
      PASSED_TEST_CASES: this.reportData.functional?.summary?.passedTests || '0',
      FAILED_TEST_CASES: this.reportData.functional?.summary?.failedTests || '0',
      
      // 性能测试数据
      PERFORMANCE_PASS_RATE: this.calculatePerformancePassRate(),
      LOGIN_AVG: this.reportData.performance?.httpReqDuration?.avg || '0',
      LOGIN_P95: this.reportData.performance?.httpReqDuration?.p95 || '0',
      
      // 安全测试数据
      SECURITY_PASS_RATE: this.calculateSecurityPassRate(),
      HIGH_RISK_VULNS: this.reportData.security?.zap?.high || '0',
      MEDIUM_RISK_VULNS: this.reportData.security?.zap?.medium || '0',
      
      // 代码覆盖率
      CODE_COVERAGE: this.reportData.coverage?.overall?.lines || '0',
      
      // 缺陷数据
      CRITICAL_BUGS: this.reportData.bugs?.p0 || '0',
      P0_COUNT: this.reportData.bugs?.p0 || '0',
      P1_COUNT: this.reportData.bugs?.p1 || '0',
      P2_COUNT: this.reportData.bugs?.p2 || '0',
      P3_COUNT: this.reportData.bugs?.p3 || '0',
      
      // 状态
      OVERALL_STATUS: this.calculateOverallStatus(),
      RELEASE_RECOMMENDATION: this.generateReleaseRecommendation(),
    };

    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }

  generateHTMLTemplate() {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牛马动物园测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .metric-label { color: #666; font-size: 0.9em; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warn { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f8f9fa; }
        .chart-container { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐮 牛马动物园测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value status-${this.getStatusClass(this.reportData.functional?.summary?.passRate)}">${this.reportData.functional?.summary?.passRate || 0}%</div>
                <div class="metric-label">功能测试通过率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-${this.getStatusClass(this.calculatePerformancePassRate())}">${this.calculatePerformancePassRate()}%</div>
                <div class="metric-label">性能测试通过率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-${this.getStatusClass(this.calculateSecurityPassRate())}">${this.calculateSecurityPassRate()}%</div>
                <div class="metric-label">安全测试通过率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-${this.getStatusClass(this.reportData.coverage?.overall?.lines)}">${this.reportData.coverage?.overall?.lines || 0}%</div>
                <div class="metric-label">代码覆盖率</div>
            </div>
        </div>
        
        <div class="chart-container">
            <h3>测试结果分布</h3>
            <canvas id="testResultsChart" width="400" height="200"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>缺陷分布</h3>
            <table>
                <tr><th>严重程度</th><th>数量</th><th>占比</th></tr>
                <tr><td>P0 (严重)</td><td>${this.reportData.bugs?.p0 || 0}</td><td>${this.calculateBugPercentage('p0')}%</td></tr>
                <tr><td>P1 (高)</td><td>${this.reportData.bugs?.p1 || 0}</td><td>${this.calculateBugPercentage('p1')}%</td></tr>
                <tr><td>P2 (中)</td><td>${this.reportData.bugs?.p2 || 0}</td><td>${this.calculateBugPercentage('p2')}%</td></tr>
                <tr><td>P3 (低)</td><td>${this.reportData.bugs?.p3 || 0}</td><td>${this.calculateBugPercentage('p3')}%</td></tr>
            </table>
        </div>
        
        <div class="chart-container">
            <h3>测试结论</h3>
            <p><strong>总体状态:</strong> <span class="status-${this.getOverallStatusClass()}">${this.calculateOverallStatus()}</span></p>
            <p><strong>发布建议:</strong> ${this.generateReleaseRecommendation()}</p>
        </div>
    </div>
</body>
</html>`;
  }

  // 辅助方法
  calculateFunctionalSummary(jest, vitest, playwright) {
    const totalTests = (jest.totalTests || 0) + (vitest.totalTests || 0) + (playwright.totalTests || 0);
    const passedTests = (jest.passedTests || 0) + (vitest.passedTests || 0) + (playwright.passedTests || 0);
    const failedTests = (jest.failedTests || 0) + (vitest.failedTests || 0) + (playwright.failedTests || 0);
    
    return {
      totalTests,
      passedTests,
      failedTests,
      passRate: totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0
    };
  }

  calculatePerformancePassRate() {
    const avgTime = this.reportData.performance?.httpReqDuration?.avg || 0;
    const errorRate = this.reportData.performance?.httpReqFailed?.rate || 0;
    
    // 简单的性能评分逻辑
    let score = 100;
    if (avgTime > 500) score -= 30;
    if (avgTime > 1000) score -= 30;
    if (errorRate > 5) score -= 40;
    
    return Math.max(0, score);
  }

  calculateSecurityPassRate() {
    const high = this.reportData.security?.zap?.high || 0;
    const critical = this.reportData.security?.snyk?.critical || 0;
    
    return (high === 0 && critical === 0) ? 100 : 0;
  }

  calculateOverallStatus() {
    const functionalRate = parseFloat(this.reportData.functional?.summary?.passRate || 0);
    const performanceRate = this.calculatePerformancePassRate();
    const securityRate = this.calculateSecurityPassRate();
    
    const overallRate = (functionalRate + performanceRate + securityRate) / 3;
    
    if (overallRate >= 95) return '优秀 ✅';
    if (overallRate >= 85) return '良好 ✅';
    if (overallRate >= 70) return '一般 ⚠️';
    return '需要改进 ❌';
  }

  generateReleaseRecommendation() {
    const criticalBugs = this.reportData.bugs?.p0 || 0;
    const securityRate = this.calculateSecurityPassRate();
    const functionalRate = parseFloat(this.reportData.functional?.summary?.passRate || 0);
    
    if (criticalBugs > 0) {
      return '❌ 不建议发布：存在严重缺陷，需要修复后再发布';
    }
    
    if (securityRate < 100) {
      return '⚠️ 谨慎发布：存在安全风险，建议修复后发布';
    }
    
    if (functionalRate < 90) {
      return '⚠️ 有条件发布：功能测试通过率较低，建议修复主要问题后发布';
    }
    
    return '✅ 建议发布：所有测试通过，质量符合发布标准';
  }

  getStatusClass(value) {
    const numValue = parseFloat(value || 0);
    if (numValue >= 95) return 'pass';
    if (numValue >= 80) return 'warn';
    return 'fail';
  }

  getOverallStatusClass() {
    const status = this.calculateOverallStatus();
    if (status.includes('✅')) return 'pass';
    if (status.includes('⚠️')) return 'warn';
    return 'fail';
  }

  calculateBugPercentage(level) {
    const total = (this.reportData.bugs?.p0 || 0) + (this.reportData.bugs?.p1 || 0) + 
                  (this.reportData.bugs?.p2 || 0) + (this.reportData.bugs?.p3 || 0);
    const count = this.reportData.bugs?.[level] || 0;
    return total > 0 ? ((count / total) * 100).toFixed(1) : 0;
  }

  calculateOverallCoverage(backend, frontend) {
    return {
      lines: ((backend.lines + frontend.lines) / 2).toFixed(2),
      functions: ((backend.functions + frontend.functions) / 2).toFixed(2),
      branches: ((backend.branches + frontend.branches) / 2).toFixed(2),
      statements: ((backend.statements + frontend.statements) / 2).toFixed(2)
    };
  }

  calculateSecuritySummary(zap, snyk) {
    return {
      totalVulns: (zap.high || 0) + (zap.medium || 0) + (snyk.critical || 0) + (snyk.high || 0),
      highRisk: (zap.high || 0) + (snyk.critical || 0) + (snyk.high || 0),
      passRate: ((zap.high || 0) === 0 && (snyk.critical || 0) === 0) ? 100 : 0
    };
  }

  getTimestamp() {
    return new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  }

  // 默认数据方法
  getDefaultTestData() {
    return { totalTests: 0, passedTests: 0, failedTests: 0, skippedTests: 0, passRate: 0 };
  }

  getDefaultFunctionalData() {
    return {
      jest: this.getDefaultTestData(),
      vitest: this.getDefaultTestData(),
      playwright: this.getDefaultTestData(),
      summary: this.getDefaultTestData()
    };
  }

  getDefaultPerformanceData() {
    return {
      httpReqDuration: { avg: 0, p95: 0, p99: 0 },
      httpReqFailed: { rate: 0 },
      httpReqs: { rate: 0 },
      virtualUsers: 0
    };
  }

  getDefaultSecurityData() {
    return {
      zap: { high: 0, medium: 0, low: 0, info: 0 },
      snyk: { critical: 0, high: 0, medium: 0, low: 0 },
      summary: { totalVulns: 0, highRisk: 0, passRate: 100 }
    };
  }

  getDefaultCoverageData() {
    return {
      backend: { lines: 0, functions: 0, branches: 0, statements: 0 },
      frontend: { lines: 0, functions: 0, branches: 0, statements: 0 },
      overall: { lines: 0, functions: 0, branches: 0, statements: 0 }
    };
  }

  getDefaultBugData() {
    return { p0: 0, p1: 2, p2: 5, p3: 8 };
  }
}

// 运行脚本
if (require.main === module) {
  const generator = new TestReportGenerator();
  generator.generateReport().catch(console.error);
}

module.exports = TestReportGenerator;