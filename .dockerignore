# Git
.git
.gitignore
README.md
*.md

# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Development files
.env.local
.env.development
.env.test

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs

# Test coverage
coverage

# Build artifacts
dist
build

# Cache
.cache
.parcel-cache

# Temporary files
.tmp
.temp

# Database
*.sqlite
*.db