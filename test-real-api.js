// Test script for real Gemini API
const axios = require('axios');

async function testRealGeminiAPI() {
    try {
        // Create a simple test image (1x1 pixel white PNG in base64)
        const testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";
        
        console.log('Testing real Gemini API integration...');
        
        const response = await axios.post('http://localhost:3005/api/v1/avatar/real-generate', {
            imageData: `data:image/png;base64,${testImageBase64}`,
            animalType: 'PET'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 seconds timeout for AI processing
        });
        
        console.log('✅ API Response Status:', response.status);
        console.log('✅ Success:', response.data.success);
        console.log('📝 Message:', response.data.message);
        
        if (response.data.avatar) {
            console.log('🎨 Generated Avatar ID:', response.data.avatar.avatarId);
            console.log('🦴 Animal Type:', response.data.avatar.animalType);
            console.log('🔮 Generation Method:', response.data.avatar.features?.quality);
            console.log('📊 Has Gemini Analysis:', !!response.data.avatar.geminiAnalysis);
            console.log('🖼️  Has Generated Image:', !!response.data.avatar.imageUrl);
        }
        
    } catch (error) {
        console.error('❌ Test Failed:');
        console.error('Error message:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Response:', error.response.data);
        }
    }
}

testRealGeminiAPI();