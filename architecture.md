# 牛马动物园 - 系统架构设计方案

**文档版本:** 1.0  
**创建日期:** 2025-08-21  
**架构师:** Claude Code  

## 目录
1. [整体系统架构](#1-整体系统架构)
2. [技术栈选型](#2-技术栈选型)
3. [数据库设计](#3-数据库设计)
4. [部署架构](#4-部署架构)
5. [性能优化策略](#5-性能优化策略)
6. [安全考虑](#6-安全考虑)
7. [可扩展性方案](#7-可扩展性方案)
8. [开发环境配置](#8-开发环境配置)

## 1. 整体系统架构

### 1.1 架构概述
采用**微服务架构**设计，前后端分离，支持高并发和水平扩展。系统分为以下几个层次：

```
┌─────────────────────────────────────────────────────────────────┐
│                        CDN + 负载均衡层                          │
├─────────────────────────────────────────────────────────────────┤
│                         API 网关层                              │
├─────────────────────────────────────────────────────────────────┤
│  Web端(React)  │  移动端(React Native)  │  管理后台(Vue.js)      │
├─────────────────────────────────────────────────────────────────┤
│                         微服务层                                │
│ ┌──────────────┬──────────────┬──────────────┬─────────────────┐ │
│ │  用户服务    │  测试服务    │  动物园服务  │  社交互动服务   │ │
│ │ (用户管理)   │ (AI分类)     │ (3D渲染)     │ (点赞投喂评论)  │ │
│ ├──────────────┼──────────────┼──────────────┼─────────────────┤ │
│ │  内容服务    │  排行榜服务  │  通知服务    │  文件存储服务   │ │
│ │ (吐槽墙)     │ (统计分析)   │ (消息推送)   │ (图片视频)      │ │
│ └──────────────┴──────────────┴──────────────┴─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     数据层 + 缓存层                             │
│ ┌──────────────┬──────────────┬──────────────┬─────────────────┐ │
│ │   用户库     │   内容库     │   互动库     │     Redis       │ │
│ │ (PostgreSQL) │ (MongoDB)    │ (PostgreSQL) │    (缓存)       │ │
│ └──────────────┴──────────────┴──────────────┴─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 服务分解说明

**核心业务服务：**
- **用户服务 (User Service)**: 注册、登录、认证、个人信息管理
- **测试服务 (Test Service)**: 打工人分类测试、AI分析算法
- **动物园服务 (Zoo Service)**: 3D场景渲染、动物状态管理
- **社交互动服务 (Social Service)**: 点赞、投喂、评论、关注

**支撑服务：**
- **内容服务 (Content Service)**: 吐槽墙内容管理、审核
- **排行榜服务 (Ranking Service)**: 各类排行榜计算、统计
- **通知服务 (Notification Service)**: 消息推送、通知管理
- **文件存储服务 (File Service)**: 图片、视频、3D资源管理

### 1.3 数据流架构

```
用户请求 → CDN → 负载均衡 → API网关 → 具体微服务 → 数据库/缓存
                                    ↓
              消息队列 ← 异步处理服务 ← 业务事件
                ↓
            通知服务 → 推送服务 → 用户设备
```

## 2. 技术栈选型

### 2.1 前端技术栈

**Web端 (桌面浏览器访问)**
- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit + RTK Query
- **3D引擎**: Three.js + React Three Fiber
- **UI组件**: Ant Design + styled-components
- **构建工具**: Vite
- **选型理由**: React生态成熟，Three.js是WebGL领域标准，性能优异

**移动端 (主要平台)**
- **框架**: React Native 0.72 + TypeScript
- **3D渲染**: React Native Skia + Three.js (WebGL)
- **状态管理**: Redux Toolkit
- **导航**: React Navigation 6
- **选型理由**: 一套代码双端部署，开发效率高，性能接近原生

**管理后台**
- **框架**: Vue.js 3 + TypeScript
- **UI组件**: Element Plus
- **图表**: ECharts
- **选型理由**: Vue适合管理后台开发，Element Plus组件丰富

### 2.2 后端技术栈

**API服务**
- **主框架**: Node.js + NestJS (微服务框架)
- **编程语言**: TypeScript
- **API网关**: Kong 或 Nginx
- **选型理由**: JavaScript全栈，开发效率高，NestJS提供企业级架构

**关键服务实现**
- **AI分类服务**: Python + FastAPI + TensorFlow/PyTorch
- **3D资源处理**: Node.js + Sharp + FFmpeg
- **实时通信**: Socket.io + Redis
- **选型理由**: Python适合AI算法，Node.js适合IO密集型业务

### 2.3 数据存储

**主数据库**
- **用户数据**: PostgreSQL 14 (ACID特性，用户一致性要求高)
- **内容数据**: MongoDB 5.0 (灵活的文档结构，适合非结构化内容)
- **时序数据**: InfluxDB (用户行为分析、统计数据)

**缓存系统**
- **Redis 7.0**: 会话存储、热点数据缓存、排行榜
- **CDN**: 阿里云CDN (静态资源分发)

**文件存储**
- **对象存储**: 阿里云OSS (图片、视频、3D模型)
- **本地缓存**: Nginx (静态文件代理)

### 2.4 基础设施

**容器化**
- **Docker**: 应用容器化
- **Kubernetes**: 容器编排和管理
- **Helm**: 应用部署管理

**监控与日志**
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**: Jaeger

**消息队列**
- **Redis Stream**: 轻量级消息队列
- **RabbitMQ**: 复杂业务流程

## 3. 数据库设计

### 3.1 用户数据库 (PostgreSQL)

```sql
-- 用户基本信息表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    nickname VARCHAR(50),
    bio TEXT,
    status SMALLINT DEFAULT 1, -- 1:正常 2:禁用 3:删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户测试结果表
CREATE TABLE user_test_results (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    animal_type VARCHAR(20) NOT NULL, -- 神兽/宠物/牛马
    animal_species VARCHAR(50) NOT NULL, -- 具体品种
    test_answers JSONB NOT NULL, -- 测试答案
    personality_score JSONB NOT NULL, -- 性格评分
    test_version VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户关系表 (关注/拉黑)
CREATE TABLE user_relationships (
    id BIGSERIAL PRIMARY KEY,
    follower_id BIGINT REFERENCES users(id),
    following_id BIGINT REFERENCES users(id),
    relationship_type SMALLINT NOT NULL, -- 1:关注 2:拉黑
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(follower_id, following_id)
);

-- 用户积分表
CREATE TABLE user_points (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    points INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 积分变动记录表
CREATE TABLE point_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    change_amount INTEGER NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- 签到/发布/互动等
    description VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 社交互动数据库 (PostgreSQL)

```sql
-- 互动行为表 (点赞、投喂)
CREATE TABLE interactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    target_user_id BIGINT REFERENCES users(id),
    interaction_type SMALLINT NOT NULL, -- 1:点赞 2:投喂
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_user_id, interaction_type, created_at::date)
);

-- 评论表
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    content_id BIGINT NOT NULL, -- 内容ID (可能是用户、帖子等)
    content_type VARCHAR(20) NOT NULL, -- user/post/comment
    parent_id BIGINT REFERENCES comments(id), -- 回复评论
    content TEXT NOT NULL,
    like_count INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1, -- 1:正常 2:隐藏 3:删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 排行榜数据表
CREATE TABLE rankings (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    ranking_type VARCHAR(20) NOT NULL, -- 加班王/摸鱼大师等
    score DECIMAL(10,2) NOT NULL,
    rank_position INTEGER,
    period VARCHAR(10) NOT NULL, -- daily/weekly/monthly
    period_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, ranking_type, period_date)
);
```

### 3.3 内容数据库 (MongoDB)

```javascript
// 吐槽内容集合
db.posts.createIndex({ "userId": 1, "createdAt": -1 })
db.posts.createIndex({ "tags": 1, "status": 1 })
db.posts.createIndex({ "createdAt": -1, "likeCount": -1 })

// 文档结构示例
{
  "_id": ObjectId("..."),
  "userId": "user123",
  "content": "今天又被产品经理折磨了...",
  "images": ["image1.jpg", "image2.jpg"],
  "tags": ["产品", "加班"],
  "isAnonymous": false,
  "likeCount": 23,
  "commentCount": 5,
  "shareCount": 12,
  "status": 1, // 1:正常 2:审核中 3:违规
  "aiModeration": {
    "score": 0.85,
    "labels": ["正常"]
  },
  "createdAt": ISODate("2025-01-15T10:30:00Z"),
  "updatedAt": ISODate("2025-01-15T10:30:00Z")
}

// 系统配置集合
db.configs.insertOne({
  "key": "test_questions",
  "value": {
    "version": "v1.0",
    "questions": [...] // 测试题目配置
  }
})
```

### 3.4 索引优化策略

```sql
-- PostgreSQL 索引
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_test_results_user_id ON user_test_results(user_id);
CREATE INDEX idx_interactions_target_user ON interactions(target_user_id, interaction_type);
CREATE INDEX idx_interactions_created_at ON interactions(created_at);
CREATE INDEX idx_rankings_type_period ON rankings(ranking_type, period_date);

-- 复合索引
CREATE INDEX idx_comments_content_status ON comments(content_id, content_type, status);
CREATE INDEX idx_relationships_following ON user_relationships(following_id, relationship_type);
```

## 4. 部署架构

### 4.1 基础设施架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        Internet                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 CDN + WAF                                       │
│            (阿里云CDN + Web应用防火墙)                          │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                负载均衡器                                       │
│              (阿里云SLB)                                        │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                Kubernetes 集群                                  │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 API Gateway                             │   │
│  │              (Kong + Nginx)                             │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 微服务集群                              │   │
│  │ ┌─────────┬─────────┬─────────┬─────────┬─────────────┐ │   │
│  │ │用户服务 │测试服务 │动物园   │社交服务 │其他微服务   │ │   │
│  │ │3个Pod   │2个Pod   │4个Pod   │3个Pod   │...          │ │   │
│  │ └─────────┴─────────┴─────────┴─────────┴─────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                    数据层                                       │
│ ┌─────────────┬─────────────┬─────────────┬─────────────────┐   │
│ │ PostgreSQL  │  MongoDB    │   Redis     │    OSS对象存储  │   │
│ │   集群      │   副本集    │   集群      │                 │   │
│ │ (主从复制)  │ (3节点)     │ (哨兵模式)  │    (静态资源)   │   │
│ └─────────────┴─────────────┴─────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 4.2 环境划分

**生产环境 (Production)**
- **3个可用区部署**: 华东1、华东2、华北2
- **自动扩缩容**: HPA + VPA
- **资源配置**: 
  - API服务: 2C4G × 6个Pod
  - 数据库: 4C8G 主库 + 2C4G × 2 从库
  - Redis: 2C4G × 3节点

**预生产环境 (Staging)**
- **单可用区**: 华东1
- **资源配置**: 生产环境的1/3规模
- **数据**: 脱敏的生产数据子集

**开发环境 (Development)**
- **本地Docker Compose**: 开发阶段
- **云端开发环境**: Kubernetes测试集群

### 4.3 容器化配置

**Dockerfile示例 (Node.js服务)**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源码
COPY . .

# 构建应用
RUN npm run build

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

**Kubernetes部署配置**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: registry.cn-hangzhou.aliyuncs.com/niuma/user-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 5. 性能优化策略

### 5.1 前端性能优化

**3D渲染优化**
```javascript
// Three.js LOD (Level of Detail) 实现
const createLODAnimal = (animalType) => {
  const lod = new THREE.LOD();
  
  // 高精度模型 (近距离)
  const highDetail = loadAnimalModel(`${animalType}_high.gltf`);
  lod.addLevel(highDetail, 0);
  
  // 中精度模型 (中距离)
  const mediumDetail = loadAnimalModel(`${animalType}_medium.gltf`);
  lod.addLevel(mediumDetail, 50);
  
  // 低精度模型 (远距离)
  const lowDetail = loadAnimalModel(`${animalType}_low.gltf`);
  lod.addLevel(lowDetail, 200);
  
  return lod;
};

// 实例化渲染 (大量相同动物)
const instancedMesh = new THREE.InstancedMesh(
  animalGeometry,
  animalMaterial,
  maxAnimalCount
);

// 视锥剔除优化
scene.traverse((object) => {
  if (object.isMesh) {
    object.frustumCulled = true;
  }
});
```

**资源加载优化**
```javascript
// 预加载关键资源
const preloadAssets = async () => {
  const loader = new THREE.GLTFLoader();
  const promises = [
    'cow.gltf', 'horse.gltf', 'dog.gltf'
  ].map(file => 
    loader.loadAsync(`/models/${file}`)
  );
  
  const models = await Promise.all(promises);
  return models.reduce((cache, model, index) => {
    cache[animalTypes[index]] = model;
    return cache;
  }, {});
};

// 懒加载非关键资源
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// Service Worker缓存策略
self.addEventListener('fetch', event => {
  if (event.request.url.includes('/models/')) {
    event.respondWith(
      caches.open('models-cache').then(cache =>
        cache.match(event.request).then(response =>
          response || fetch(event.request).then(fetchResponse => {
            cache.put(event.request, fetchResponse.clone());
            return fetchResponse;
          })
        )
      )
    );
  }
});
```

### 5.2 后端性能优化

**数据库查询优化**
```sql
-- 分页查询优化 (游标分页)
SELECT id, username, created_at 
FROM users 
WHERE id > $1 
ORDER BY id 
LIMIT 20;

-- 排行榜查询优化 (物化视图)
CREATE MATERIALIZED VIEW daily_rankings AS
SELECT 
  user_id,
  rank() OVER (ORDER BY score DESC) as rank_position,
  score
FROM rankings 
WHERE period_date = CURRENT_DATE
ORDER BY score DESC;

-- 定时刷新物化视图
REFRESH MATERIALIZED VIEW CONCURRENTLY daily_rankings;
```

**Redis缓存策略**
```javascript
// 多层缓存策略
class CacheManager {
  // L1: 应用内存缓存 (1分钟)
  private memoryCache = new Map();
  
  // L2: Redis缓存 (1小时)
  async get(key) {
    // 先查内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // 再查Redis
    const redisValue = await redis.get(key);
    if (redisValue) {
      this.memoryCache.set(key, redisValue);
      setTimeout(() => this.memoryCache.delete(key), 60000);
      return redisValue;
    }
    
    return null;
  }
  
  // 缓存预热
  async warmUpCache() {
    const hotUsers = await this.getTopUsers(100);
    for (const user of hotUsers) {
      await this.cacheUserData(user.id);
    }
  }
}

// 排行榜缓存 (使用Redis Sorted Set)
await redis.zadd('ranking:daily:like', score, userId);
const topUsers = await redis.zrevrange('ranking:daily:like', 0, 99, 'WITHSCORES');
```

**API性能优化**
```javascript
// GraphQL数据加载优化 (DataLoader)
const userLoader = new DataLoader(async (userIds) => {
  const users = await User.findByIds(userIds);
  return userIds.map(id => users.find(user => user.id === id));
});

// 接口聚合 (减少网络请求)
app.get('/api/zoo/dashboard', async (req, res) => {
  const [userInfo, animalStats, interactions, rankings] = await Promise.all([
    getUserInfo(req.userId),
    getAnimalStats(req.userId),
    getRecentInteractions(req.userId),
    getUserRankings(req.userId)
  ]);
  
  res.json({
    user: userInfo,
    stats: animalStats,
    interactions,
    rankings
  });
});

// 接口限流
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100个请求
  message: '请求过于频繁，请稍后重试'
});
```

### 5.3 系统架构优化

**负载均衡策略**
```yaml
# Kong 配置
services:
  - name: user-service
    url: http://user-service:3000
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
      - name: prometheus
        config:
          per_consumer: true

# Nginx upstream配置
upstream user_service {
    least_conn;  # 最少连接数负载均衡
    server user-service-1:3000 weight=3;
    server user-service-2:3000 weight=2;
    server user-service-3:3000 weight=1;
    
    # 健康检查
    health_check interval=30s fails=3 passes=2;
}
```

**数据库连接池优化**
```javascript
// PostgreSQL 连接池配置
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: 5432,
  max: 20,          // 最大连接数
  idleTimeoutMillis: 30000,  // 空闲连接超时
  connectionTimeoutMillis: 2000,  // 连接超时
  maxUses: 7500,    // 连接最大使用次数
});

// MongoDB 连接优化
mongoose.connect(mongoUri, {
  maxPoolSize: 10,  // 最大连接池大小
  serverSelectionTimeoutMS: 5000,  // 服务器选择超时
  socketTimeoutMS: 45000,  // Socket超时
  bufferMaxEntries: 0  // 禁用缓冲
});
```

## 6. 安全考虑

### 6.1 身份认证与授权

**JWT令牌机制**
```javascript
// 双令牌机制 (Access Token + Refresh Token)
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { 
      userId: user.id, 
      username: user.username,
      permissions: user.permissions 
    },
    process.env.JWT_SECRET,
    { expiresIn: '15m' }  // 访问令牌15分钟过期
  );
  
  const refreshToken = jwt.sign(
    { userId: user.id },
    process.env.REFRESH_SECRET,
    { expiresIn: '7d' }   // 刷新令牌7天过期
  );
  
  // 将刷新令牌存储到Redis
  redis.setex(`refresh:${user.id}`, 7 * 24 * 3600, refreshToken);
  
  return { accessToken, refreshToken };
};

// 权限中间件
const requireAuth = (permissions = []) => {
  return async (req, res, next) => {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      if (!token) {
        return res.status(401).json({ error: '未授权访问' });
      }
      
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      
      // 检查权限
      if (permissions.length > 0) {
        const hasPermission = permissions.some(p => 
          decoded.permissions.includes(p)
        );
        if (!hasPermission) {
          return res.status(403).json({ error: '权限不足' });
        }
      }
      
      next();
    } catch (error) {
      return res.status(401).json({ error: '令牌无效' });
    }
  };
};
```

**OAuth集成**
```javascript
// 微信登录集成
const wechatAuth = passport.use(new WechatStrategy({
  appID: process.env.WECHAT_APP_ID,
  appSecret: process.env.WECHAT_APP_SECRET,
  callbackURL: "/auth/wechat/callback"
}, async (accessToken, refreshToken, profile, done) => {
  try {
    let user = await User.findOne({ wechatId: profile.id });
    
    if (!user) {
      user = await User.create({
        wechatId: profile.id,
        username: profile.displayName,
        avatar: profile.photos[0]?.value,
        authProvider: 'wechat'
      });
    }
    
    return done(null, user);
  } catch (error) {
    return done(error, null);
  }
}));
```

### 6.2 数据安全

**敏感数据加密**
```javascript
const crypto = require('crypto');

// AES加密工具
class CryptoManager {
  private algorithm = 'aes-256-gcm';
  private secretKey = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
  
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey);
    cipher.setAAD(Buffer.from('additional data', 'utf8'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      encrypted
    };
  }
  
  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    decipher.setAAD(Buffer.from('additional data', 'utf8'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// 数据库敏感字段加密
const userSchema = new mongoose.Schema({
  username: String,
  email: {
    type: String,
    set: function(email) {
      return cryptoManager.encrypt(email);
    },
    get: function(encryptedEmail) {
      return cryptoManager.decrypt(encryptedEmail);
    }
  }
});
```

**SQL注入防护**
```javascript
// 使用参数化查询
const getUserById = async (userId) => {
  // 危险写法 (SQL注入风险)
  // const query = `SELECT * FROM users WHERE id = ${userId}`;
  
  // 安全写法
  const query = 'SELECT * FROM users WHERE id = $1';
  const result = await pool.query(query, [userId]);
  return result.rows[0];
};

// 输入验证中间件
const validateInput = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: '输入数据不合法',
        details: error.details
      });
    }
    next();
  };
};

// 使用Joi进行数据验证
const userRegistrationSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  password: Joi.string().pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$')),
  email: Joi.string().email().required()
});
```

### 6.3 API安全

**请求限流与防刷**
```javascript
// 分层限流策略
const createRateLimiter = (options) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    keyGenerator: (req) => {
      // 根据用户ID + IP进行限流
      return `${req.ip}:${req.user?.id || 'anonymous'}`;
    },
    handler: (req, res) => {
      res.status(429).json({
        error: '请求过于频繁，请稍后重试',
        retryAfter: Math.ceil(options.windowMs / 1000)
      });
    }
  });
};

// 不同接口不同限流策略
app.use('/api/auth', createRateLimiter({ windowMs: 15 * 60 * 1000, max: 5 }));  // 登录接口
app.use('/api/posts', createRateLimiter({ windowMs: 60 * 1000, max: 10 }));     // 发帖接口
app.use('/api/likes', createRateLimiter({ windowMs: 60 * 1000, max: 60 }));     // 点赞接口

// 防止暴力破解
const bruteForceProtection = new ExpressBrute(store, {
  freeRetries: 5,
  minWait: 30 * 1000,  // 30秒
  maxWait: 15 * 60 * 1000,  // 15分钟
  lifetime: 24 * 60 * 60,   // 24小时后重置
});

app.post('/api/auth/login', bruteForceProtection.prevent, async (req, res) => {
  // 登录逻辑
});
```

**内容安全审核**
```javascript
// 集成阿里云内容安全
const aliContentSecurity = require('@alicloud/green');

const contentModerator = {
  async checkText(content) {
    try {
      const result = await aliContentSecurity.scanText({
        content,
        scenes: ['antispam', 'keyword', 'porn', 'terrorism', 'politics']
      });
      
      return {
        safe: result.data.suggestion === 'pass',
        risk: result.data.suggestion === 'block',
        review: result.data.suggestion === 'review',
        labels: result.data.results
      };
    } catch (error) {
      console.error('内容审核失败:', error);
      // 审核服务异常时，返回需要人工审核
      return { safe: false, review: true };
    }
  },
  
  async checkImage(imageUrl) {
    const result = await aliContentSecurity.scanImage({
      url: imageUrl,
      scenes: ['porn', 'terrorism', 'politics', 'ad', 'live']
    });
    
    return {
      safe: result.data.suggestion === 'pass',
      risk: result.data.suggestion === 'block',
      labels: result.data.results
    };
  }
};

// 内容发布中间件
app.post('/api/posts', requireAuth(), async (req, res) => {
  const { content, images } = req.body;
  
  // 文本审核
  const textCheck = await contentModerator.checkText(content);
  if (textCheck.risk) {
    return res.status(400).json({ error: '内容包含违规信息' });
  }
  
  // 图片审核
  if (images && images.length > 0) {
    for (const imageUrl of images) {
      const imageCheck = await contentModerator.checkImage(imageUrl);
      if (imageCheck.risk) {
        return res.status(400).json({ error: '图片包含违规内容' });
      }
    }
  }
  
  // 创建帖子
  const post = await Post.create({
    userId: req.user.id,
    content,
    images,
    status: textCheck.review ? 'reviewing' : 'published'
  });
  
  res.json(post);
});
```

### 6.4 隐私保护

**数据脱敏**
```javascript
// 用户数据脱敏
const maskPersonalInfo = (user) => {
  return {
    id: user.id,
    username: user.username,
    // 手机号脱敏: 138****1234
    phone: user.phone ? user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : null,
    // 邮箱脱敏: abc***@domain.com
    email: user.email ? user.email.replace(/(.{3}).*(@.*)/, '$1***$2') : null,
    avatar: user.avatar,
    createdAt: user.createdAt
  };
};

// GDPR合规 - 数据导出
app.get('/api/user/export-data', requireAuth(), async (req, res) => {
  const userId = req.user.id;
  
  const userData = await Promise.all([
    User.findById(userId),
    Post.find({ userId }),
    Comment.find({ userId }),
    Interaction.find({ userId })
  ]);
  
  const exportData = {
    user: userData[0],
    posts: userData[1],
    comments: userData[2],
    interactions: userData[3],
    exportDate: new Date().toISOString()
  };
  
  res.json(exportData);
});

// 数据删除 (软删除)
app.delete('/api/user/account', requireAuth(), async (req, res) => {
  const userId = req.user.id;
  
  await Promise.all([
    User.findByIdAndUpdate(userId, { 
      status: 'deleted',
      deletedAt: new Date(),
      // 敏感信息清空
      email: null,
      phone: null
    }),
    Post.updateMany({ userId }, { status: 'deleted' }),
    Comment.updateMany({ userId }, { status: 'deleted' })
  ]);
  
  res.json({ message: '账号已删除' });
});
```

## 7. 可扩展性方案

### 7.1 数据库扩展

**读写分离**
```javascript
// 主从数据库配置
const masterDB = new Pool({
  host: process.env.MASTER_DB_HOST,
  // ... 其他配置
});

const slaveDBs = [
  new Pool({ host: process.env.SLAVE_DB_HOST_1 }),
  new Pool({ host: process.env.SLAVE_DB_HOST_2 }),
  new Pool({ host: process.env.SLAVE_DB_HOST_3 })
];

// 数据库路由
class DatabaseRouter {
  // 写操作路由到主库
  async write(query, params) {
    return masterDB.query(query, params);
  }
  
  // 读操作路由到从库
  async read(query, params) {
    // 负载均衡选择从库
    const slaveIndex = Math.floor(Math.random() * slaveDBs.length);
    return slaveDBs[slaveIndex].query(query, params);
  }
}

// ORM层封装
class UserRepository {
  async findById(id) {
    return db.read('SELECT * FROM users WHERE id = $1', [id]);
  }
  
  async create(userData) {
    return db.write('INSERT INTO users (...) VALUES (...)', [...]);
  }
  
  async update(id, userData) {
    return db.write('UPDATE users SET ... WHERE id = $1', [..., id]);
  }
}
```

**分库分表策略**
```javascript
// 分库分表路由器
class ShardingRouter {
  // 用户表按用户ID hash分片
  getUserShard(userId) {
    const shardCount = 4;  // 4个分片
    const shardIndex = userId % shardCount;
    return `user_db_${shardIndex}`;
  }
  
  // 内容表按时间分片 (月表)
  getContentShard(createdAt) {
    const date = new Date(createdAt);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `content_${year}_${month}`;
  }
  
  // 分片查询聚合
  async queryAllShards(queryFn, params) {
    const promises = this.getAllShards().map(shard => 
      queryFn(shard, params)
    );
    
    const results = await Promise.allSettled(promises);
    return results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value)
      .flat();
  }
}

// 分布式事务处理 (基于Saga模式)
class SagaTransaction {
  constructor() {
    this.steps = [];
    this.compensations = [];
  }
  
  addStep(step, compensation) {
    this.steps.push(step);
    this.compensations.unshift(compensation);  // 补偿操作逆序
  }
  
  async execute() {
    const completedSteps = [];
    
    try {
      for (const step of this.steps) {
        const result = await step();
        completedSteps.push(result);
      }
      return completedSteps;
    } catch (error) {
      // 执行补偿操作
      for (let i = 0; i < completedSteps.length; i++) {
        try {
          await this.compensations[i](completedSteps[i]);
        } catch (compensationError) {
          console.error('补偿操作失败:', compensationError);
        }
      }
      throw error;
    }
  }
}
```

### 7.2 微服务扩展

**服务发现与注册**
```javascript
// Consul服务注册
const consul = require('consul')();

class ServiceRegistry {
  async register(serviceName, port, health) {
    await consul.agent.service.register({
      name: serviceName,
      id: `${serviceName}-${process.env.NODE_ID}`,
      address: process.env.NODE_IP,
      port: port,
      check: {
        http: `http://${process.env.NODE_IP}:${port}${health}`,
        interval: '10s',
        timeout: '5s'
      }
    });
  }
  
  async discover(serviceName) {
    const services = await consul.health.service(serviceName);
    return services[0]
      .filter(service => service.Checks.every(check => check.Status === 'passing'))
      .map(service => ({
        host: service.Service.Address,
        port: service.Service.Port
      }));
  }
}

// 服务间通信 (带熔断器)
const CircuitBreaker = require('opossum');

const options = {
  timeout: 3000,          // 超时时间
  errorThresholdPercentage: 50,  // 错误率阈值
  resetTimeout: 30000     // 熔断器重置时间
};

const userServiceClient = new CircuitBreaker(async (userId) => {
  const services = await serviceRegistry.discover('user-service');
  const service = services[Math.floor(Math.random() * services.length)];
  
  const response = await fetch(`http://${service.host}:${service.port}/users/${userId}`);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`);
  }
  
  return response.json();
}, options);

// 熔断器事件处理
userServiceClient.on('open', () => console.log('熔断器开启'));
userServiceClient.on('halfOpen', () => console.log('熔断器半开'));
userServiceClient.on('close', () => console.log('熔断器关闭'));
```

**消息队列集成**
```javascript
// Redis Stream消息队列
class MessageQueue {
  constructor(redisClient) {
    this.redis = redisClient;
  }
  
  // 发送消息
  async publish(stream, message) {
    return this.redis.xadd(stream, '*', ...Object.entries(message).flat());
  }
  
  // 消费消息 (消费者组)
  async consume(stream, group, consumer, callback) {
    try {
      // 创建消费者组
      await this.redis.xgroup('CREATE', stream, group, '$', 'MKSTREAM');
    } catch (error) {
      // 组已存在，忽略错误
    }
    
    while (true) {
      try {
        const results = await this.redis.xreadgroup(
          'GROUP', group, consumer,
          'COUNT', 1,
          'BLOCK', 5000,
          'STREAMS', stream, '>'
        );
        
        if (results && results.length > 0) {
          const [streamName, messages] = results[0];
          
          for (const [id, fields] of messages) {
            try {
              const message = this.parseMessage(fields);
              await callback(message);
              
              // 确认消息处理完成
              await this.redis.xack(stream, group, id);
            } catch (error) {
              console.error('消息处理失败:', error);
              // 可以实现重试机制或死信队列
            }
          }
        }
      } catch (error) {
        console.error('消费消息失败:', error);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }
  
  parseMessage(fields) {
    const message = {};
    for (let i = 0; i < fields.length; i += 2) {
      message[fields[i]] = fields[i + 1];
    }
    return message;
  }
}

// 事件发布订阅
class EventBus {
  constructor(messageQueue) {
    this.mq = messageQueue;
  }
  
  // 发布事件
  async publish(eventType, data) {
    await this.mq.publish('events', {
      type: eventType,
      data: JSON.stringify(data),
      timestamp: Date.now()
    });
  }
  
  // 订阅事件
  async subscribe(eventTypes, handler) {
    await this.mq.consume('events', 'event-processors', 'processor-1', 
      async (message) => {
        if (eventTypes.includes(message.type)) {
          await handler(message.type, JSON.parse(message.data));
        }
      }
    );
  }
}

// 事件处理示例
const eventBus = new EventBus(messageQueue);

// 用户注册事件
eventBus.subscribe(['user.registered'], async (eventType, data) => {
  const { userId, email } = data;
  
  // 发送欢迎邮件
  await emailService.sendWelcomeEmail(email);
  
  // 创建默认动物形象
  await animalService.createDefaultAnimal(userId);
  
  // 记录用户行为
  await analyticsService.track(userId, 'user_registered');
});
```

### 7.3 缓存架构扩展

**多层缓存体系**
```javascript
// 缓存抽象层
class CacheManager {
  constructor() {
    this.l1Cache = new NodeCache({ stdTTL: 60 });      // L1: 内存缓存
    this.l2Cache = redis;                              // L2: Redis缓存
    this.l3Cache = new CDNCache();                     // L3: CDN缓存
  }
  
  async get(key, options = {}) {
    const { level = 'all', ttl } = options;
    
    // L1缓存检查
    if (level === 'all' || level === 'l1') {
      const l1Value = this.l1Cache.get(key);
      if (l1Value !== undefined) {
        return l1Value;
      }
    }
    
    // L2缓存检查
    if (level === 'all' || level === 'l2') {
      const l2Value = await this.l2Cache.get(key);
      if (l2Value) {
        const parsed = JSON.parse(l2Value);
        // 回填L1缓存
        this.l1Cache.set(key, parsed, ttl || 60);
        return parsed;
      }
    }
    
    return null;
  }
  
  async set(key, value, ttl = 300) {
    // 同时写入L1和L2缓存
    this.l1Cache.set(key, value, Math.min(ttl, 60));
    await this.l2Cache.setex(key, ttl, JSON.stringify(value));
  }
  
  async invalidate(pattern) {
    // 失效L1缓存
    this.l1Cache.flushAll();
    
    // 失效L2缓存
    const keys = await this.l2Cache.keys(pattern);
    if (keys.length > 0) {
      await this.l2Cache.del(...keys);
    }
  }
}

// 智能缓存策略
class SmartCache {
  constructor(cacheManager) {
    this.cache = cacheManager;
    this.hitRates = new Map();  // 缓存命中率统计
  }
  
  async getWithFallback(key, fetcher, options = {}) {
    const startTime = Date.now();
    
    // 尝试从缓存获取
    let value = await this.cache.get(key);
    let fromCache = true;
    
    if (value === null) {
      // 缓存未命中，执行获取函数
      value = await fetcher();
      fromCache = false;
      
      // 动态TTL计算
      const ttl = this.calculateTTL(key, value, options);
      await this.cache.set(key, value, ttl);
    }
    
    // 统计命中率
    this.updateHitRate(key, fromCache);
    
    return value;
  }
  
  calculateTTL(key, value, options) {
    const baseTime = options.baseTTL || 300;
    const hitRate = this.hitRates.get(key) || { hits: 0, total: 0 };
    
    // 根据命中率调整TTL
    const rate = hitRate.total > 0 ? hitRate.hits / hitRate.total : 0;
    
    if (rate > 0.8) {
      return baseTime * 2;  // 高命中率，延长缓存时间
    } else if (rate < 0.3) {
      return baseTime / 2;  // 低命中率，缩短缓存时间
    }
    
    return baseTime;
  }
  
  updateHitRate(key, hit) {
    const current = this.hitRates.get(key) || { hits: 0, total: 0 };
    current.total += 1;
    if (hit) current.hits += 1;
    
    // 保持最近1000次统计
    if (current.total > 1000) {
      current.hits = Math.floor(current.hits * 0.9);
      current.total = 900;
    }
    
    this.hitRates.set(key, current);
  }
}
```

### 7.4 监控与运维扩展

**分布式链路追踪**
```javascript
const opentracing = require('opentracing');
const jaeger = require('jaeger-client');

// Jaeger配置
const config = {
  serviceName: 'user-service',
  sampler: {
    type: 'const',
    param: 1,
  },
  reporter: {
    logSpans: true,
    agentHost: 'jaeger-agent',
    agentPort: 6832,
  },
};

const tracer = jaeger.initTracer(config);
opentracing.setGlobalTracer(tracer);

// 链路追踪中间件
const tracingMiddleware = (req, res, next) => {
  const span = tracer.startSpan(`${req.method} ${req.path}`);
  
  // 添加标签
  span.setTag('http.method', req.method);
  span.setTag('http.url', req.url);
  span.setTag('user.id', req.user?.id);
  
  // 在请求中传递span
  req.span = span;
  
  // 请求结束时完成span
  res.on('finish', () => {
    span.setTag('http.status_code', res.statusCode);
    if (res.statusCode >= 400) {
      span.setTag('error', true);
    }
    span.finish();
  });
  
  next();
};

// 数据库查询追踪
const tracedQuery = async (query, params, parentSpan) => {
  const span = tracer.startSpan('db.query', { childOf: parentSpan });
  span.setTag('db.statement', query);
  span.setTag('db.type', 'postgresql');
  
  try {
    const result = await pool.query(query, params);
    span.setTag('db.rows_affected', result.rowCount);
    return result;
  } catch (error) {
    span.setTag('error', true);
    span.log({ error: error.message });
    throw error;
  } finally {
    span.finish();
  }
};
```

**自动化运维**
```yaml
# Kubernetes HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
```

```javascript
// 健康检查端点
app.get('/health', async (req, res) => {
  const checks = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'unknown',
    redis: 'unknown',
    version: process.env.APP_VERSION
  };
  
  try {
    // 数据库连接检查
    await pool.query('SELECT 1');
    checks.database = 'healthy';
  } catch (error) {
    checks.database = 'unhealthy';
  }
  
  try {
    // Redis连接检查
    await redis.ping();
    checks.redis = 'healthy';
  } catch (error) {
    checks.redis = 'unhealthy';
  }
  
  const isHealthy = checks.database === 'healthy' && checks.redis === 'healthy';
  
  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    checks
  });
});

// 就绪检查端点
app.get('/ready', async (req, res) => {
  // 检查应用是否就绪接收流量
  const isReady = await checkDependencies();
  
  res.status(isReady ? 200 : 503).json({
    status: isReady ? 'ready' : 'not ready'
  });
});
```

## 8. 开发环境配置

### 8.1 本地开发环境

**Docker Compose配置**
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 用户服务
  user-service:
    build: ./services/user-service
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/user-service:/app
      - /app/node_modules

  # 测试服务
  test-service:
    build: ./services/test-service
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  # PostgreSQL数据库
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=niuma
      - POSTGRES_USER=niuma
      - POSTGRES_PASSWORD=niuma123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  # MongoDB
  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=niuma
      - MONGO_INITDB_ROOT_PASSWORD=niuma123
    volumes:
      - mongodb_data:/data/db

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # API网关
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - user-service
      - test-service

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
```

**开发脚本**
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:services\" \"npm run dev:web\" \"npm run dev:mobile\"",
    "dev:services": "docker-compose up -d && npm run wait-services",
    "dev:web": "cd frontend/web && npm run dev",
    "dev:mobile": "cd frontend/mobile && npm run start",
    "wait-services": "wait-on http://localhost:5432 http://localhost:27017 http://localhost:6379",
    "migrate": "cd services/user-service && npm run migrate",
    "seed": "cd services/user-service && npm run seed",
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "jest --config=jest.unit.config.js",
    "test:integration": "jest --config=jest.integration.config.js",
    "lint": "eslint . --ext .js,.ts,.tsx",
    "format": "prettier --write .",
    "build": "npm run build:services && npm run build:web",
    "build:services": "docker-compose -f docker-compose.prod.yml build",
    "build:web": "cd frontend/web && npm run build"
  }
}
```

### 8.2 开发工具配置

**VSCode配置 (.vscode/settings.json)**
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "eslint.autoFixOnSave": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "files.associations": {
    "*.env.*": "properties"
  },
  "rest-client.environmentVariables": {
    "local": {
      "baseUrl": "http://localhost:8080/api",
      "token": "your-dev-token"
    }
  }
}
```

**ESLint配置 (.eslintrc.js)**
```javascript
module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'react-hooks'],
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'prettier'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'react/prop-types': 'off',
    'react/react-in-jsx-scope': 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};
```

**Git Hooks配置**
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged && npm run test:unit",
      "pre-push": "npm run test:integration",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{json,md,yml,yaml}": [
      "prettier --write",
      "git add"
    ]
  },
  "commitlint": {
    "extends": ["@commitlint/config-conventional"]
  }
}
```

### 8.3 测试配置

**Jest测试配置**
```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.test.(js|ts)'],
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  globalSetup: '<rootDir>/tests/globalSetup.ts',
  globalTeardown: '<rootDir>/tests/globalTeardown.ts'
};
```

**测试工具配置**
```javascript
// tests/setup.ts
import { testDB } from './utils/testDB';
import { testRedis } from './utils/testRedis';

// 全局测试设置
beforeAll(async () => {
  await testDB.connect();
  await testRedis.connect();
});

afterAll(async () => {
  await testDB.disconnect();
  await testRedis.disconnect();
});

beforeEach(async () => {
  await testDB.clear();
  await testRedis.clear();
});

// Mock外部服务
jest.mock('../src/services/emailService', () => ({
  sendEmail: jest.fn().mockResolvedValue(true)
}));

jest.mock('../src/services/aliContentSecurity', () => ({
  scanText: jest.fn().mockResolvedValue({
    data: { suggestion: 'pass' }
  })
}));
```

### 8.4 部署流水线

**GitHub Actions配置 (.github/workflows/deploy.yml)**
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Registry
        uses: docker/login-action@v2
        with:
          registry: registry.cn-hangzhou.aliyuncs.com
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: registry.cn-hangzhou.aliyuncs.com/niuma/app:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Kubernetes
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            k8s/deployment.yml
            k8s/service.yml
          images: |
            registry.cn-hangzhou.aliyuncs.com/niuma/app:${{ github.sha }}
          kubeconfig: ${{ secrets.KUBE_CONFIG }}
```

---

## 总结

这份架构设计方案为"牛马动物园"项目提供了：

1. **可扩展的微服务架构**: 支持从1万到100万用户的增长
2. **高性能3D渲染**: 优化的WebGL技术栈，支持移动端流畅体验
3. **完整的技术选型**: 基于JavaScript/TypeScript的全栈方案
4. **健壮的数据设计**: PostgreSQL+MongoDB混合存储，支持复杂业务场景
5. **企业级安全**: 多层安全防护，符合隐私保护要求
6. **DevOps最佳实践**: 容器化部署，自动化CI/CD流水线

该架构能够满足PRD中提出的所有功能需求，同时具备良好的可维护性和扩展性，为项目的长期发展奠定了坚实的技术基础。
