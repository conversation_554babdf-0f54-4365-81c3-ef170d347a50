{"permissions": {"allow": ["WebSearch", "WebFetch(domain:aldenhallak.com)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-project.sh:*)", "Bash(./start-dev.sh:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(./stop-project.sh:*)", "Bash(kill:*)", "Bash(npm install)", "Bash(node:*)", "<PERSON><PERSON>(python3:*)", "Bash(npm install:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(export:*)", "WebFetch(domain:github.com)", "Bash(open /Users/<USER>/WorkSpace/niuma/enhanced-zoo.html)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run dev:*)", "Bash(npm test)", "Bash(npm test:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx tsc:*)", "Bash(grep:*)", "Bash(echo $AI_GENERATION_ENABLED)", "Bash(AI_GENERATION_ENABLED=true node mock-server.js)", "Bash(AI_GENERATION_ENABLED=true USE_REAL_AI=true node mock-server.js)", "Bash(AI_GENERATION_ENABLED=false node mock-server.js)", "Bash(AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini node mock-server.js)", "<PERSON><PERSON>(echo:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(npm run start:dev:*)", "Bash(AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini GEMINI_API_KEY=$GEMINI_API_KEY node mock-server.js)", "Bash(HTTP_PROXY=http://127.0.0.1:7890 HTTPS_PROXY=http://127.0.0.1:7890 AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini GEMINI_API_KEY=$GEMINI_API_KEY node mock-server.js)", "WebFetch(domain:developers.googleblog.com)", "Bash(HTTP_PROXY=http://127.0.0.1:7890 HTTPS_PROXY=http://127.0.0.1:7890 GEMINI_API_KEY=$GEMINI_API_KEY node test-gemini-debug.js)", "Bash(GEMINI_API_KEY=$GEMINI_API_KEY node test-gemini-debug.js)", "Bash(GEMINI_API_KEY=$GEMINI_API_KEY node test-gemini-simple.js)", "Bash(GEMINI_API_KEY=$GEMINI_API_KEY node test-gemini-proxy.js)", "Bash(npm run build:*)", "Bash(HTTP_PROXY=http://127.0.0.1:7890 HTTPS_PROXY=http://127.0.0.1:7890 AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini GEMINI_API_KEY=$GEMINI_API_KEY node simple-server.js)", "Bash(GEMINI_API_KEY=$GEMINI_API_KEY node simple-server.js)", "Bash(AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini GEMINI_API_KEY=$GEMINI_API_KEY node simple-server.js)", "Bash(git reset:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(./docker-deploy.sh)", "Bash(AI_GENERATION_ENABLED=true USE_REAL_AI=true PREFERRED_AI_MODEL=gemini GEMINI_API_KEY=AIzaSyD5oMgLaCth6rIWCQ9O17QR8Rlo7uQzO5Y node simple-server.js)"], "deny": [], "ask": []}}