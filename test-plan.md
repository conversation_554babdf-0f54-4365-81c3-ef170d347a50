# 牛马动物园测试计划

## 文档信息
- **项目名称**: 牛马动物园 (Working Animal Zoo)
- **文档版本**: 1.0
- **创建日期**: 2025-08-21
- **测试负责人**: 测试团队
- **审核状态**: 待审核

## 1. 测试概述

### 1.1 测试目标
本测试计划旨在确保牛马动物园项目的质量，包括功能正确性、性能指标、安全性和用户体验。

### 1.2 测试范围
- **功能测试**: 覆盖所有用户故事和业务需求
- **性能测试**: 验证系统在不同负载下的性能表现
- **安全测试**: 检查潜在的安全漏洞和威胁
- **兼容性测试**: 确保在不同浏览器和设备上的兼容性
- **易用性测试**: 验证用户体验和界面设计

### 1.3 测试策略
采用测试金字塔模型：
- **单元测试 (75%)**: 快速、可靠的单元测试
- **集成测试 (20%)**: 模块间交互测试
- **端到端测试 (5%)**: 关键用户路径测试

## 2. 测试环境

### 2.1 环境配置
| 环境 | 用途 | URL | 数据库 |
|------|------|-----|--------|
| 开发环境 | 日常开发测试 | http://localhost:3000 | PostgreSQL (本地) |
| 集成环境 | CI/CD自动化测试 | http://test.niuma.com | PostgreSQL (测试) |
| 预发布环境 | 用户验收测试 | http://staging.niuma.com | PostgreSQL (预发布) |
| 生产环境 | 线上监控 | http://niuma.com | PostgreSQL (生产) |

### 2.2 测试工具
- **后端测试**: Jest + Supertest + TypeORM
- **前端测试**: Vitest + Testing Library + MSW
- **E2E测试**: Playwright
- **性能测试**: K6 + Artillery
- **安全测试**: OWASP ZAP + Snyk
- **API测试**: Postman + Newman

### 2.3 测试数据
- **测试用户**: 50个预设测试账号
- **测试内容**: 1000条模拟吐槽数据
- **测试动物**: 500个虚拟动物数据
- **测试问题**: 20道分类测试题目

## 3. 测试用例设计

### 3.1 功能测试用例

#### 3.1.1 用户认证模块
| 用例ID | 用例名称 | 优先级 | 测试步骤 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_AUTH_001 | 用户注册-正常流程 | P0 | 填写正确信息并提交 | 注册成功并自动登录 |
| TC_AUTH_002 | 用户注册-邮箱重复 | P1 | 使用已存在邮箱注册 | 提示邮箱已存在 |
| TC_AUTH_003 | 用户登录-正确凭据 | P0 | 输入正确邮箱密码 | 登录成功跳转首页 |
| TC_AUTH_004 | 用户登录-错误密码 | P1 | 输入错误密码 | 提示密码错误 |
| TC_AUTH_005 | 密码找回流程 | P2 | 使用邮箱找回密码 | 发送重置邮件 |

#### 3.1.2 打工人测试模块
| 用例ID | 用例名称 | 优先级 | 测试步骤 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_TEST_001 | 获取测试问题 | P0 | 点击开始测试 | 显示20道题目 |
| TC_TEST_002 | 提交测试答案 | P0 | 完成所有题目并提交 | 生成动物分类结果 |
| TC_TEST_003 | 测试进度保存 | P1 | 中途退出再进入 | 恢复之前进度 |
| TC_TEST_004 | 重新测试功能 | P2 | 点击重新测试 | 清空之前结果 |
| TC_TEST_005 | 生成分享证书 | P1 | 测试完成后生成证书 | 显示个性化证书 |

#### 3.1.3 3D动物园模块
| 用例ID | 用例名称 | 优先级 | 测试步骤 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_ZOO_001 | 进入动物园场景 | P0 | 访问动物园页面 | 加载3D场景 |
| TC_ZOO_002 | 动物交互-点赞 | P1 | 点击动物进行点赞 | 点赞数增加 |
| TC_ZOO_003 | 动物交互-投喂 | P1 | 选择食物投喂动物 | 播放投喂动画 |
| TC_ZOO_004 | 场景控制-缩放 | P2 | 滚轮缩放场景 | 视角远近调整 |
| TC_ZOO_005 | 动物筛选功能 | P2 | 按类型筛选动物 | 只显示对应类型 |

#### 3.1.4 社交功能模块
| 用例ID | 用例名称 | 优先级 | 测试步骤 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_SOCIAL_001 | 发布吐槽内容 | P0 | 填写内容并发布 | 内容成功发布 |
| TC_SOCIAL_002 | 吐槽内容点赞 | P1 | 点击其他用户吐槽 | 点赞数增加 |
| TC_SOCIAL_003 | 评论功能 | P1 | 对吐槽内容评论 | 评论成功显示 |
| TC_SOCIAL_004 | 内容举报功能 | P2 | 举报不当内容 | 触发审核流程 |
| TC_SOCIAL_005 | 匿名发布功能 | P2 | 选择匿名发布 | 隐藏用户信息 |

### 3.2 性能测试用例

#### 3.2.1 负载测试
| 场景 | 并发用户数 | 持续时间 | 成功率要求 | 响应时间要求 |
|------|------------|----------|------------|-------------|
| 正常负载 | 100 | 10分钟 | >99% | P95<500ms |
| 高负载 | 500 | 15分钟 | >95% | P95<1000ms |
| 峰值负载 | 1000 | 5分钟 | >90% | P95<2000ms |
| 压力测试 | 2000 | 10分钟 | >80% | P95<5000ms |

#### 3.2.2 3D渲染性能
| 测试项 | 目标值 | 测试环境 |
|--------|--------|----------|
| 帧率 | >30 FPS | 移动设备 |
| 帧率 | >60 FPS | 桌面设备 |
| 内存使用 | <200MB | 移动设备 |
| GPU使用率 | <70% | 中等配置 |

### 3.3 安全测试用例

#### 3.3.1 常见漏洞检查
| 漏洞类型 | 测试方法 | 检查点 |
|----------|----------|---------|
| SQL注入 | 输入恶意SQL语句 | 登录、搜索、表单输入 |
| XSS攻击 | 输入脚本代码 | 用户输入、内容发布 |
| CSRF攻击 | 伪造请求 | 状态改变操作 |
| 文件上传漏洞 | 上传恶意文件 | 头像上传、文件分享 |
| 权限绕过 | 越权访问 | API端点、管理功能 |

## 4. 测试执行计划

### 4.1 测试阶段划分
| 阶段 | 时间安排 | 测试类型 | 参与人员 |
|------|----------|----------|----------|
| 单元测试 | 开发期间 | 功能验证 | 开发工程师 |
| 集成测试 | 功能开发完成后 | 模块集成 | 测试工程师 |
| 系统测试 | 集成测试通过后 | 端到端验证 | 测试团队 |
| 性能测试 | 系统测试并行 | 性能验证 | 性能测试工程师 |
| 安全测试 | 系统测试并行 | 安全验证 | 安全测试工程师 |
| 用户验收测试 | 所有测试通过后 | 用户体验 | 产品经理、用户代表 |

### 4.2 测试时间安排
```
第1-2周: 单元测试和开发
第3周: 集成测试
第4周: 系统测试和性能测试
第5周: 安全测试和缺陷修复
第6周: 用户验收测试和发布准备
```

### 4.3 里程碑和检查点
- **里程碑1**: 单元测试覆盖率达到90%
- **里程碑2**: 集成测试全部通过
- **里程碑3**: 性能测试达到目标指标
- **里程碑4**: 安全测试无严重漏洞
- **里程碑5**: 用户验收测试通过

## 5. 缺陷管理

### 5.1 缺陷等级定义
| 等级 | 定义 | 示例 | 修复时间 |
|------|------|------|----------|
| P0-严重 | 系统崩溃、数据丢失 | 登录失败、支付异常 | 24小时内 |
| P1-高 | 主要功能不可用 | 测试无法提交、3D场景不显示 | 3天内 |
| P2-中 | 功能部分异常 | 界面显示错误、性能下降 | 1周内 |
| P3-低 | 界面美观、易用性 | 文字错误、按钮样式 | 下个版本 |

### 5.2 缺陷跟踪流程
1. **发现** → 2. **提交** → 3. **确认** → 4. **分配** → 5. **修复** → 6. **验证** → 7. **关闭**

### 5.3 缺陷报告模板
```
缺陷ID: BUG-XXXX
缺陷标题: [简短描述]
发现人员: [姓名]
发现时间: [日期时间]
测试环境: [环境信息]
重现步骤: 
1. 步骤1
2. 步骤2
3. 步骤3
预期结果: [期望的行为]
实际结果: [实际的行为]
缺陷等级: [P0/P1/P2/P3]
附件: [截图、日志等]
```

## 6. 测试报告

### 6.1 测试报告结构
1. **执行摘要**
2. **测试概述**
3. **测试结果统计**
4. **缺陷分析**
5. **性能测试结果**
6. **安全测试结果**
7. **测试结论和建议**
8. **附录**

### 6.2 关键指标
- **功能测试通过率**: >95%
- **性能测试通过率**: >90%
- **安全测试通过率**: 100%
- **代码覆盖率**: >85%
- **缺陷密度**: <2个/KLOC

## 7. 风险与应对

### 7.1 测试风险
| 风险 | 影响 | 概率 | 应对策略 |
|------|------|------|----------|
| 测试环境不稳定 | 高 | 中 | 准备备用环境 |
| 3D功能测试复杂 | 中 | 高 | 专项技术调研 |
| 性能测试工具限制 | 中 | 中 | 多工具并行 |
| 测试数据不足 | 低 | 低 | 自动生成工具 |

### 7.2 质量风险
| 风险 | 应对措施 |
|------|----------|
| 用户体验不佳 | 增加可用性测试 |
| 3D性能问题 | 专项性能优化 |
| 安全漏洞 | 代码安全审查 |
| 兼容性问题 | 扩大测试范围 |

## 8. 附录

### 8.1 测试环境配置清单
- Node.js 18+
- PostgreSQL 14+
- Redis 7+
- Playwright浏览器驱动
- K6性能测试工具
- OWASP ZAP安全扫描

### 8.2 测试数据文件
- `test-users.json`: 测试用户数据
- `test-questions.json`: 测试题目数据
- `test-animals.json`: 虚拟动物数据
- `test-posts.json`: 模拟吐槽内容

### 8.3 自动化测试脚本
- `/backend/src/**/*.spec.ts`: 后端单元测试
- `/frontend/src/**/*.test.tsx`: 前端组件测试
- `/e2e/tests/`: 端到端测试脚本
- `/performance-tests/`: 性能测试脚本
- `/security-tests/`: 安全测试脚本