
> niuma-zoo-backend@1.0.0 start:dev
> nest start --watch

[2J[3J[H[[90m10:38:25[0m] Starting compilation in watch mode...

[96msrc/config/database.config.ts[0m:[93m18[0m:[93m3[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'timezone' does not exist in type '{ retryAttempts?: number; retryDelay?: number; toRetry?: (err: any) => boolean; autoLoadEntities?: boolean; keepConnectionAlive?: boolean; verboseRetryLog?: boolean; manualInitialization?: boolean; } & Partial<...>'.

[7m18[0m   timezone: 'UTC',
[7m  [0m [91m  ~~~~~~~~[0m

[96msrc/config/redis.config.ts[0m:[93m5[0m:[93m3[0m - [91merror[0m[90m TS2322: [0mType 'typeof import("/Users/<USER>/WorkSpace/niuma/backend/node_modules/cache-manager-redis-store/dist/index")' is not assignable to type 'string | CacheStoreFactory | CacheStore'.

[7m5[0m   store: redisStore,
[7m [0m [91m  ~~~~~[0m

  [96mnode_modules/@nestjs/cache-manager/dist/interfaces/cache-manager.interface.d.ts[0m:[93m63[0m:[93m5[0m
    [7m63[0m     store?: string | CacheStoreFactory | CacheStore;
    [7m  [0m [96m    ~~~~~[0m
    The expected type comes from property 'store' which is declared here on type 'CacheModuleOptions'

[96msrc/main.ts[0m:[93m29[0m:[93m11[0m - [91merror[0m[90m TS2349: [0mThis expression is not callable.
  Type 'typeof import("/Users/<USER>/WorkSpace/niuma/backend/node_modules/helmet/index")' has no call signatures.

[7m29[0m   app.use(helmet());
[7m  [0m [91m          ~~~~~~[0m

[96msrc/main.ts[0m:[93m55[0m:[93m24[0m - [91merror[0m[90m TS2554: [0mExpected 1 arguments, but got 0.

[7m55[0m   app.useGlobalFilters(new HttpExceptionFilter());
[7m  [0m [91m                       ~~~~~~~~~~~~~~~~~~~~~~~~~[0m

  [96msrc/common/filters/http-exception.filter.ts[0m:[93m16[0m:[93m5[0m
    [7m16[0m     @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    [7m  [0m [96m    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    An argument for 'logger' was not provided.

[96msrc/main.ts[0m:[93m59[0m:[93m5[0m - [91merror[0m[90m TS2554: [0mExpected 1 arguments, but got 0.

[7m59[0m     new LoggingInterceptor(),
[7m  [0m [91m    ~~~~~~~~~~~~~~~~~~~~~~~~[0m

  [96msrc/common/interceptors/logging.interceptor.ts[0m:[93m16[0m:[93m5[0m
    [7m16[0m     @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    [7m  [0m [96m    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    An argument for 'logger' was not provided.

[96msrc/modules/ai/ai.service.ts[0m:[93m851[0m:[93m50[0m - [91merror[0m[90m TS2345: [0mArgument of type '({ text: string; inlineData?: undefined; } | { inlineData: { mimeType: string; data: string; }; text?: undefined; })[]' is not assignable to parameter of type 'string | GenerateContentRequest | (string | Part)[]'.
  Type '({ text: string; inlineData?: undefined; } | { inlineData: { mimeType: string; data: string; }; text?: undefined; })[]' is not assignable to type '(string | Part)[]'.
    Type '{ text: string; inlineData?: undefined; } | { inlineData: { mimeType: string; data: string; }; text?: undefined; }' is not assignable to type 'string | Part'.
      Type '{ text: string; inlineData?: undefined; }' is not assignable to type 'string | Part'.
        Property 'codeExecutionResult' is missing in type '{ text: string; inlineData?: undefined; }' but required in type 'CodeExecutionResultPart'.

[7m851[0m       const result = await model.generateContent(parts);
[7m   [0m [91m                                                 ~~~~~[0m

  [96mnode_modules/@google/generative-ai/dist/generative-ai.d.ts[0m:[93m199[0m:[93m5[0m
    [7m199[0m     codeExecutionResult: CodeExecutionResult;
    [7m   [0m [96m    ~~~~~~~~~~~~~~~~~~~[0m
    'codeExecutionResult' is declared here.

[96msrc/modules/auth/auth.controller.ts[0m:[93m144[0m:[93m23[0m - [91merror[0m[90m TS2693: [0m'CurrentUserInfo' only refers to a type, but is being used as a value here.

[7m144[0m   @ApiResponseWrapper(CurrentUserInfo, 200, '获取成功')
[7m   [0m [91m                      ~~~~~~~~~~~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m72[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type '"hashed_password"' is not assignable to parameter of type 'never'.

[7m72[0m       mockedBcrypt.hash.mockResolvedValue(hashedPassword);
[7m  [0m [91m                                          ~~~~~~~~~~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m116[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'confirmPassword' does not exist in type 'Partial<RegisterDto>'.

[7m116[0m         confirmPassword: 'password2',
[7m   [0m [91m        ~~~~~~~~~~~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m129[0m:[93m25[0m - [91merror[0m[90m TS2339: [0mProperty 'email' does not exist on type 'LoginDto'.

[7m129[0m         email: loginDto.email,
[7m   [0m [91m                        ~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m134[0m:[93m46[0m - [91merror[0m[90m TS2345: [0mArgument of type 'true' is not assignable to parameter of type 'never'.

[7m134[0m       mockedBcrypt.compare.mockResolvedValue(true);
[7m   [0m [91m                                             ~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m142[0m:[93m34[0m - [91merror[0m[90m TS2339: [0mProperty 'email' does not exist on type 'LoginDto'.

[7m142[0m         where: { email: loginDto.email },
[7m   [0m [91m                                 ~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m168[0m:[93m25[0m - [91merror[0m[90m TS2339: [0mProperty 'email' does not exist on type 'LoginDto'.

[7m168[0m         email: loginDto.email,
[7m   [0m [91m                        ~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m173[0m:[93m46[0m - [91merror[0m[90m TS2345: [0mArgument of type 'false' is not assignable to parameter of type 'never'.

[7m173[0m       mockedBcrypt.compare.mockResolvedValue(false);
[7m   [0m [91m                                             ~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m191[0m:[93m46[0m - [91merror[0m[90m TS2345: [0mArgument of type 'true' is not assignable to parameter of type 'never'.

[7m191[0m       mockedBcrypt.compare.mockResolvedValue(true);
[7m   [0m [91m                                             ~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m194[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'validateUser' does not exist on type 'AuthService'.

[7m194[0m       const result = await service.validateUser(email, password);
[7m   [0m [91m                                   ~~~~~~~~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m216[0m:[93m46[0m - [91merror[0m[90m TS2345: [0mArgument of type 'false' is not assignable to parameter of type 'never'.

[7m216[0m       mockedBcrypt.compare.mockResolvedValue(false);
[7m   [0m [91m                                             ~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m219[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'validateUser' does not exist on type 'AuthService'.

[7m219[0m       const result = await service.validateUser(email, password);
[7m   [0m [91m                                   ~~~~~~~~~~~~[0m

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m241[0m:[93m36[0m - [91merror[0m[90m TS2551: [0mProperty 'refreshToken' does not exist on type 'AuthService'. Did you mean 'refreshTokens'?

[7m241[0m       const result = await service.refreshToken(refreshToken);
[7m   [0m [91m                                   ~~~~~~~~~~~~[0m

  [96msrc/modules/auth/auth.service.ts[0m:[93m157[0m:[93m9[0m
    [7m157[0m   async refreshTokens(refreshToken: string): Promise<AuthResponseDto> {
    [7m   [0m [96m        ~~~~~~~~~~~~~[0m
    'refreshTokens' is declared here.

[96msrc/modules/auth/auth.service.spec.ts[0m:[93m261[0m:[93m28[0m - [91merror[0m[90m TS2551: [0mProperty 'refreshToken' does not exist on type 'AuthService'. Did you mean 'refreshTokens'?

[7m261[0m       await expect(service.refreshToken(refreshToken)).rejects.toThrow(UnauthorizedException);
[7m   [0m [91m                           ~~~~~~~~~~~~[0m

  [96msrc/modules/auth/auth.service.ts[0m:[93m157[0m:[93m9[0m
    [7m157[0m   async refreshTokens(refreshToken: string): Promise<AuthResponseDto> {
    [7m   [0m [96m        ~~~~~~~~~~~~~[0m
    'refreshTokens' is declared here.

[96msrc/modules/content/schemas/system-config.schema.ts[0m:[93m125[0m:[93m8[0m - [91merror[0m[90m TS2304: [0mCannot find name 'AnimalSpecies'.

[7m125[0m       [AnimalSpecies.QILIN_DEER]: {
[7m   [0m [91m       ~~~~~~~~~~~~~[0m

[96msrc/modules/content/schemas/system-config.schema.ts[0m:[93m132[0m:[93m8[0m - [91merror[0m[90m TS2304: [0mCannot find name 'AnimalSpecies'.

[7m132[0m       [AnimalSpecies.OX]: {
[7m   [0m [91m       ~~~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m399[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 3, '(entityLikeArray: DeepPartial<GeneratedAvatar>[]): GeneratedAvatar[]', gave the following error.
    Object literal may only specify known properties, and 'userId' does not exist in type 'DeepPartial<GeneratedAvatar>[]'.

[7m399[0m         userId,
[7m   [0m [91m        ~~~~~~[0m


[96msrc/modules/drawing/drawing.service.ts[0m:[93m403[0m:[93m39[0m - [91merror[0m[90m TS2339: [0mProperty 'imageBase64' does not exist on type 'AIFusionResult'.

[7m403[0m         avatarImageData: fusionResult.imageBase64 || '',
[7m   [0m [91m                                      ~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m404[0m:[93m38[0m - [91merror[0m[90m TS2339: [0mProperty 'imageUrl' does not exist on type 'AIFusionResult'.

[7m404[0m         avatarImageUrl: fusionResult.imageUrl,
[7m   [0m [91m                                     ~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m407[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'fusionDetails' does not exist on type 'AIFusionResult'.

[7m407[0m           fusion: fusionResult.fusionDetails.method,
[7m   [0m [91m                               ~~~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m410[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'fusionDetails' does not exist on type 'AIFusionResult'.

[7m410[0m           animalType: fusionResult.fusionDetails.animalType,
[7m   [0m [91m                                   ~~~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m416[0m:[93m40[0m - [91merror[0m[90m TS2339: [0mProperty 'generationMethod' does not exist on type 'AIFusionResult'.

[7m416[0m         generationMethod: fusionResult.generationMethod || 'AI Fusion',
[7m   [0m [91m                                       ~~~~~~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m421[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'fusionDetails' does not exist on type 'AIFusionResult'.

[7m421[0m           fusionType: fusionResult.fusionDetails.method,
[7m   [0m [91m                                   ~~~~~~~~~~~~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m428[0m:[93m49[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'GeneratedAvatar[]'.

[7m428[0m       this.logger.log(`AI融合头像已保存: ${savedAvatar.id}, 用户: ${userId}, 动物类型: ${fusionDto.animalType}`);
[7m   [0m [91m                                                ~~[0m

[96msrc/modules/drawing/drawing.service.ts[0m:[93m430[0m:[93m7[0m - [91merror[0m[90m TS2740: [0mType 'GeneratedAvatar[]' is missing the following properties from type 'GeneratedAvatar': id, userId, user, animalType, and 8 more.

[7m430[0m       return savedAvatar;
[7m   [0m [91m      ~~~~~~[0m

[96msrc/modules/drawing/services/ai-fusion.service.ts[0m:[93m209[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'requestOptions' does not exist in type 'ModelParams'.

[7m209[0m       requestOptions: {
[7m   [0m [91m      ~~~~~~~~~~~~~~[0m

[96msrc/modules/health/health.service.ts[0m:[93m21[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'host' does not exist in type 'RedisClientOptions<RedisModules, Record<string, never>, Record<string, never>>'.

[7m21[0m       host: this.configService.get('REDIS_HOST'),
[7m  [0m [91m      ~~~~[0m

[96msrc/modules/zoo/services/animal-behavior.service.ts[0m:[93m487[0m:[93m11[0m - [91merror[0m[90m TS2741: [0mProperty 'area' is missing in type '{ x: number; y: number; z: number; }' but required in type '{ x: number; y: number; z: number; area: string; }'.

[7m487[0m           animal.position = behavior.targetPosition;
[7m   [0m [91m          ~~~~~~~~~~~~~~~[0m

  [96msrc/modules/zoo/entities/zoo-animal.entity.ts[0m:[93m83[0m:[93m5[0m
    [7m83[0m     area: string;
    [7m  [0m [96m    ~~~~[0m
    'area' is declared here.

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m103[0m:[93m14[0m - [91merror[0m[90m TS2678: [0mType '"DIVINE_BEAST"' is not comparable to type 'AnimalCategory'.

[7m103[0m         case 'DIVINE_BEAST':
[7m   [0m [91m             ~~~~~~~~~~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m106[0m:[93m14[0m - [91merror[0m[90m TS2678: [0mType '"WORKING_ANIMAL"' is not comparable to type 'AnimalCategory'.

[7m106[0m         case 'WORKING_ANIMAL':
[7m   [0m [91m             ~~~~~~~~~~~~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m107[0m:[93m15[0m - [91merror[0m[90m TS2367: [0mThis comparison appears to be unintentional because the types 'InteractionType' and '"WORK_TOGETHER"' have no overlap.

[7m107[0m           if (interactionType === 'WORK_TOGETHER') {
[7m   [0m [91m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m111[0m:[93m14[0m - [91merror[0m[90m TS2678: [0mType '"PET"' is not comparable to type 'AnimalCategory'.

[7m111[0m         case 'PET':
[7m   [0m [91m             ~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m112[0m:[93m15[0m - [91merror[0m[90m TS2367: [0mThis comparison appears to be unintentional because the types 'InteractionType' and '"PET"' have no overlap.

[7m112[0m           if (interactionType === 'PET' || interactionType === 'PLAY') {
[7m   [0m [91m              ~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m112[0m:[93m44[0m - [91merror[0m[90m TS2367: [0mThis comparison appears to be unintentional because the types 'InteractionType' and '"PLAY"' have no overlap.

[7m112[0m           if (interactionType === 'PET' || interactionType === 'PLAY') {
[7m   [0m [91m                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m380[0m:[93m7[0m - [91merror[0m[90m TS2322: [0mType '{ scoreType: string; }' is not assignable to type 'FindOptionsWhere<UserScore> | FindOptionsWhere<UserScore>[]'.
  Type '{ scoreType: string; }' is not assignable to type 'FindOptionsWhere<UserScore>'.
    Types of property 'scoreType' are incompatible.
      Type 'string' is not assignable to type 'NonNullable<"TOTAL" | "DAILY" | "WEEKLY" | "MONTHLY"> | FindOperator<NonNullable<"TOTAL" | "DAILY" | "WEEKLY" | "MONTHLY">>'.

[7m380[0m       where: whereClause,
[7m   [0m [91m      ~~~~~[0m

  [96mnode_modules/typeorm/find-options/FindOneOptions.d.ts[0m:[93m23[0m:[93m5[0m
    [7m23[0m     where?: FindOptionsWhere<Entity>[] | FindOptionsWhere<Entity>;
    [7m  [0m [96m    ~~~~~[0m
    The expected type comes from property 'where' which is declared here on type 'FindManyOptions<UserScore>'

[96msrc/modules/zoo/services/scoring.service.ts[0m:[93m390[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'profileImage' does not exist on type 'User'.

[7m390[0m       avatarUrl: score.user?.profileImage,
[7m   [0m [91m                             ~~~~~~~~~~~~[0m

[96msrc/modules/zoo/zoo.service.ts[0m:[93m252[0m:[93m66[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 3, '(entityLikeArray: DeepPartial<AnimalInteractionRecord>[]): AnimalInteractionRecord[]', gave the following error.
    Object literal may only specify known properties, and 'userId' does not exist in type 'DeepPartial<AnimalInteractionRecord>[]'.
  Overload 2 of 3, '(entityLike: DeepPartial<AnimalInteractionRecord>): AnimalInteractionRecord', gave the following error.
    Type '{ condition: string; temperature: number; humidity: number; description: string; }' is not assignable to type 'string'.

[7m252[0m       const interactionRecord = this.interactionRecordRepository.create({
[7m   [0m [91m                                                                 ~~~~~~[0m

  [96msrc/modules/zoo/entities/animal-interaction-record.entity.ts[0m:[93m111[0m:[93m5[0m
    [7m111[0m     weather?: string;
    [7m   [0m [96m    ~~~~~~~[0m
    The expected type comes from property 'weather' which is declared here on type 'DeepPartial<{ weather?: string; timeOfDay?: number; otherParticipants?: string[]; specialEvents?: string[]; }>'

[96msrc/modules/zoo/zoo.service.ts[0m:[93m277[0m:[93m59[0m - [91merror[0m[90m TS2345: [0mArgument of type 'AnimalInteractionRecord[]' is not assignable to parameter of type 'AnimalInteractionRecord'.
  Type 'AnimalInteractionRecord[]' is missing the following properties from type 'AnimalInteractionRecord': id, userId, user, animalId, and 12 more.

[7m277[0m         await this.scoringService.processInteractionScore(savedInteractionRecord);
[7m   [0m [91m                                                          ~~~~~~~~~~~~~~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m14[0m:[93m36[0m - [91merror[0m[90m TS2551: [0mProperty 'userName' does not exist on type 'InternetModule'. Did you mean 'username'?

[7m14[0m     user.username = faker.internet.userName();
[7m  [0m [91m                                   ~~~~~~~~[0m

  [96mnode_modules/@faker-js/faker/dist/airline-CHFQMWko.d.ts[0m:[93m4184[0m:[93m5[0m
    [7m4184[0m     username(options?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    'username' is declared here.

[96mtest/utils/test-data-factory.ts[0m:[93m17[0m:[93m10[0m - [91merror[0m[90m TS2339: [0mProperty 'isActive' does not exist on type 'User'.

[7m17[0m     user.isActive = true;
[7m  [0m [91m         ~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m18[0m:[93m10[0m - [91merror[0m[90m TS2339: [0mProperty 'isVerified' does not exist on type 'User'.

[7m18[0m     user.isVerified = false;
[7m  [0m [91m         ~~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m31[0m:[93m13[0m - [91merror[0m[90m TS2551: [0mProperty 'userId' does not exist on type 'UserProfile'. Did you mean 'user'?

[7m31[0m     profile.userId = userId;
[7m  [0m [91m            ~~~~~~[0m

  [96msrc/modules/user/entities/user-profile.entity.ts[0m:[93m76[0m:[93m3[0m
    [7m76[0m   user: User;
    [7m  [0m [96m  ~~~~[0m
    'user' is declared here.

[96mtest/utils/test-data-factory.ts[0m:[93m32[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'nickname' does not exist on type 'UserProfile'.

[7m32[0m     profile.nickname = faker.person.firstName();
[7m  [0m [91m            ~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m33[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'avatar' does not exist on type 'UserProfile'.

[7m33[0m     profile.avatar = faker.image.avatar();
[7m  [0m [91m            ~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m34[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'bio' does not exist on type 'UserProfile'.

[7m34[0m     profile.bio = faker.person.bio();
[7m  [0m [91m            ~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m35[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'animalType' does not exist on type 'UserProfile'.

[7m35[0m     profile.animalType = faker.helpers.arrayElement(['神兽', '宠物', '牛马']);
[7m  [0m [91m            ~~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m36[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'animalSubtype' does not exist on type 'UserProfile'.

[7m36[0m     profile.animalSubtype = faker.animal.type();
[7m  [0m [91m            ~~~~~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m37[0m:[93m13[0m - [91merror[0m[90m TS2339: [0mProperty 'testScore' does not exist on type 'UserProfile'.

[7m37[0m     profile.testScore = faker.number.int({ min: 0, max: 100 });
[7m  [0m [91m            ~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m49[0m:[93m32[0m - [91merror[0m[90m TS2551: [0mProperty 'userName' does not exist on type 'InternetModule'. Did you mean 'username'?

[7m49[0m       username: faker.internet.userName(),
[7m  [0m [91m                               ~~~~~~~~[0m

  [96mnode_modules/@faker-js/faker/dist/airline-CHFQMWko.d.ts[0m:[93m4184[0m:[93m5[0m
    [7m4184[0m     username(options?: {
    [7m    [0m [96m    ~~~~~~~~[0m
    'username' is declared here.

[96mtest/utils/test-data-factory.ts[0m:[93m52[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'confirmPassword' does not exist in type 'RegisterDto'.

[7m52[0m       confirmPassword: 'Test123456!',
[7m  [0m [91m      ~~~~~~~~~~~~~~~[0m

[96mtest/utils/test-data-factory.ts[0m:[93m62[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'email' does not exist in type 'LoginDto'.

[7m62[0m       email: faker.internet.email(),
[7m  [0m [91m      ~~~~~[0m

[[90m10:38:29[0m] Found 57 errors. Watching for file changes.

