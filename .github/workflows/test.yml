name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '14'
  REDIS_VERSION: '7'

jobs:
  # 静态代码分析
  lint:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 安装后端依赖
        working-directory: ./backend
        run: npm ci

      - name: 前端代码检查
        working-directory: ./frontend
        run: |
          npm run lint
          npm run type-check

      - name: 后端代码检查
        working-directory: ./backend
        run: |
          npm run lint
          npm run format

      - name: 安全漏洞扫描
        run: |
          cd frontend && npm audit --audit-level high
          cd backend && npm audit --audit-level high

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: lint
    
    strategy:
      matrix:
        component: [frontend, backend]
    
    steps:
      - uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        working-directory: ./${{ matrix.component }}
        run: npm ci

      - name: 运行单元测试
        working-directory: ./${{ matrix.component }}
        run: npm run test:cov

      - name: 上传测试覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          files: ./${{ matrix.component }}/coverage/lcov.info
          flags: ${{ matrix.component }}
          name: ${{ matrix.component }}-coverage

      - name: 保存测试结果
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: ${{ matrix.component }}-test-results
          path: ./${{ matrix.component }}/coverage/

  # 集成测试
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit-tests

    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: niuma_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装后端依赖
        working-directory: ./backend
        run: npm ci

      - name: 等待数据库启动
        run: |
          until pg_isready -h localhost -p 5432; do
            echo "等待PostgreSQL启动..."
            sleep 2
          done

      - name: 运行数据库迁移
        working-directory: ./backend
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/niuma_test
          REDIS_URL: redis://localhost:6379
        run: npm run db:migrate

      - name: 运行集成测试
        working-directory: ./backend
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/niuma_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
        run: npm run test:e2e

      - name: 保存集成测试结果
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: ./backend/test-results/

  # 端到端测试
  e2e-tests:
    name: E2E测试
    runs-on: ubuntu-latest
    needs: integration-tests

    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: niuma_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 安装后端依赖
        working-directory: ./backend
        run: npm ci

      - name: 安装 Playwright
        working-directory: ./e2e
        run: |
          npm ci
          npx playwright install --with-deps

      - name: 构建前端应用
        working-directory: ./frontend
        run: npm run build

      - name: 启动后端服务
        working-directory: ./backend
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/niuma_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
          NODE_ENV: test
        run: |
          npm run db:migrate
          npm run start:prod &
          sleep 10

      - name: 启动前端服务
        working-directory: ./frontend
        run: |
          npm run preview &
          sleep 5

      - name: 运行 E2E 测试
        working-directory: ./e2e
        env:
          E2E_BASE_URL: http://localhost:4173
        run: npx playwright test

      - name: 上传 E2E 测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: ./e2e/test-results/

      - name: 上传 Playwright 报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: ./e2e/playwright-report/

  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: niuma_test
        ports:
          - 5432:5432

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装 K6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: 安装后端依赖并启动服务
        working-directory: ./backend
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/niuma_test
          REDIS_URL: redis://localhost:6379
        run: |
          npm ci
          npm run db:migrate
          npm run start:prod &
          sleep 10

      - name: 运行性能测试
        working-directory: ./performance-tests
        run: |
          k6 run --out json=results.json api-performance.js
          k6 run --out json=load-test.json load-test.js

      - name: 上传性能测试结果
        uses: actions/upload-artifact@v3
        with:
          name: performance-test-results
          path: ./performance-tests/*.json

  # 安全测试
  security-tests:
    name: 安全测试
    runs-on: ubuntu-latest
    needs: integration-tests

    steps:
      - uses: actions/checkout@v4

      - name: 运行 Snyk 安全扫描
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: 运行 OWASP ZAP 基线扫描
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

  # 生成测试报告
  test-report:
    name: 生成测试报告
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    if: always()

    steps:
      - uses: actions/checkout@v4

      - name: 下载所有测试结果
        uses: actions/download-artifact@v3

      - name: 生成综合测试报告
        run: |
          mkdir -p test-reports
          echo "# 牛马动物园测试报告" > test-reports/README.md
          echo "## 测试执行时间: $(date)" >> test-reports/README.md
          echo "## 提交信息: ${{ github.sha }}" >> test-reports/README.md
          echo "" >> test-reports/README.md
          
          # 汇总测试结果
          echo "## 测试结果汇总" >> test-reports/README.md
          echo "- 单元测试: ✅" >> test-reports/README.md
          echo "- 集成测试: ✅" >> test-reports/README.md
          echo "- E2E测试: ✅" >> test-reports/README.md
          echo "" >> test-reports/README.md

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: test-reports/

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: 部署到测试环境
        run: |
          echo "部署到测试环境..."
          # 这里添加实际的部署逻辑

      - name: 健康检查
        run: |
          echo "执行健康检查..."
          # 这里添加健康检查逻辑

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, performance-tests]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - uses: actions/checkout@v4

      - name: 部署到生产环境
        run: |
          echo "部署到生产环境..."
          # 这里添加实际的部署逻辑

      - name: 生产环境健康检查
        run: |
          echo "执行生产环境健康检查..."
          # 这里添加健康检查逻辑