#!/bin/bash

# 牛马动物园开发环境快速启动（无Docker版本）

set -e

echo "🐴 牛马动物园项目启动中（开发模式）..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 创建临时的模拟后端服务
create_mock_backend() {
    echo -e "${BLUE}🚀 创建模拟后端服务...${NC}"
    
    # 创建简单的Express服务器
    cat > backend/mock-server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 健康检查
app.get('/api/v1/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        service: '牛马动物园 Mock API'
    });
});

// 用户注册
app.post('/api/v1/auth/register', (req, res) => {
    res.json({
        user: {
            id: '1',
            email: req.body.email,
            username: req.body.username,
            animalType: 'OXHORSE'
        },
        accessToken: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
    });
});

// 用户登录
app.post('/api/v1/auth/login', (req, res) => {
    res.json({
        user: {
            id: '1',
            email: req.body.email,
            username: 'testuser',
            animalType: 'OXHORSE'
        },
        accessToken: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
    });
});

// 打工人测试问题
app.get('/api/v1/test/questions', (req, res) => {
    res.json({
        questions: [
            {
                id: '1',
                text: '你每周平均加班多少小时？',
                options: [
                    { value: 'A', text: '从不加班', score: 10 },
                    { value: 'B', text: '0-10小时', score: 7 },
                    { value: 'C', text: '10-20小时', score: 4 },
                    { value: 'D', text: '20小时以上', score: 1 }
                ]
            },
            {
                id: '2',
                text: '老板给你画饼的频率？',
                options: [
                    { value: 'A', text: '从不画饼', score: 10 },
                    { value: 'B', text: '偶尔画饼', score: 7 },
                    { value: 'C', text: '经常画饼', score: 4 },
                    { value: 'D', text: '天天画饼', score: 1 }
                ]
            }
        ]
    });
});

// 提交测试结果
app.post('/api/v1/test/submit', (req, res) => {
    const score = req.body.answers.reduce((sum, a) => sum + (a.score || 5), 0);
    let animalType = 'OXHORSE';
    if (score > 15) animalType = 'DIVINE';
    else if (score > 10) animalType = 'PET';
    
    res.json({
        animalType,
        score,
        description: '恭喜你成为了牛马动物园的一员！'
    });
});

// 获取动物园数据
app.get('/api/v1/zoo/animals', (req, res) => {
    res.json({
        animals: [
            { id: '1', type: 'OXHORSE', name: '打工牛马1号', x: 0, y: 0, z: 0 },
            { id: '2', type: 'PET', name: '老板宠物', x: 10, y: 0, z: 10 },
            { id: '3', type: 'DIVINE', name: '传说神兽', x: -10, y: 0, z: -10 }
        ]
    });
});

const PORT = 3001;
app.listen(PORT, () => {
    console.log(\`✅ 模拟后端服务运行在 http://localhost:\${PORT}\`);
    console.log(\`📚 API文档: http://localhost:\${PORT}/api/docs\`);
});
EOF

    # 安装必要的依赖
    cd backend
    if [ ! -f "package.json" ]; then
        npm init -y > /dev/null 2>&1
    fi
    
    # 安装express和cors
    if [ ! -d "node_modules/express" ]; then
        echo "安装后端依赖..."
        npm install express cors --save > /dev/null 2>&1
    fi
    
    # 启动模拟服务器
    echo "启动模拟后端服务..."
    node mock-server.js > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    
    echo -e "${GREEN}✅ 模拟后端服务已启动 (PID: $BACKEND_PID)${NC}"
    cd ..
}

# 启动前端服务
start_frontend() {
    echo -e "\n${BLUE}🎨 启动前端开发服务器...${NC}"
    cd frontend
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    
    # 配置环境变量
    echo "VITE_API_URL=http://localhost:3001" > .env.development
    
    # 启动前端服务
    echo "启动 Vite 开发服务器..."
    npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    echo -e "${GREEN}✅ 前端服务启动中 (PID: $FRONTEND_PID)${NC}"
    cd ..
}

# 显示访问信息
show_info() {
    echo -e "\n${GREEN}================================${NC}"
    echo -e "${GREEN}🎉 牛马动物园开发环境启动成功！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo -e "${BLUE}📱 前端应用:${NC} http://localhost:5173"
    echo -e "${BLUE}🔌 后端 API:${NC} http://localhost:3001"
    echo -e "${BLUE}❤️  健康检查:${NC} http://localhost:3001/api/v1/health"
    echo ""
    echo -e "${YELLOW}功能说明:${NC}"
    echo "- 已启用模拟后端服务，支持基本功能测试"
    echo "- 可以注册/登录用户"
    echo "- 可以进行打工人测试"
    echo "- 可以查看动物园场景"
    echo ""
    echo -e "${YELLOW}提示:${NC}"
    echo "- 查看后端日志: tail -f backend.log"
    echo "- 查看前端日志: tail -f frontend.log"
    echo "- 停止所有服务: ./stop-project.sh"
    echo ""
    echo -e "${GREEN}开始体验打工人的自嘲乐园吧！🐴${NC}"
}

# 主流程
main() {
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js (v18+)${NC}"
        echo "访问 https://nodejs.org 下载安装"
        exit 1
    fi
    
    create_mock_backend
    sleep 2
    start_frontend
    sleep 3
    
    # 检查服务
    echo -e "\n${BLUE}🔍 检查服务状态...${NC}"
    if curl -s http://localhost:3001/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 后端服务运行正常${NC}"
    fi
    
    show_info
}

# 捕获退出信号
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; ./stop-project.sh; exit' INT TERM

# 执行主流程
main

# 保持脚本运行
echo -e "\n${YELLOW}按 Ctrl+C 停止所有服务${NC}"
wait