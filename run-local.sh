#!/bin/bash
# 本地运行脚本（不使用Docker）

echo "🐂🐴 启动牛马动物园..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查环境变量
if [ -z "$GEMINI_API_KEY" ]; then
    echo -e "${YELLOW}⚠️ 请设置GEMINI_API_KEY环境变量${NC}"
    echo "export GEMINI_API_KEY=your_api_key_here"
    exit 1
fi

# 启动后端
echo -e "${BLUE}启动后端服务...${NC}"
cd backend
export AI_GENERATION_ENABLED=true
export USE_REAL_AI=true
export PREFERRED_AI_MODEL=gemini
node simple-server.js &
BACKEND_PID=$!
echo -e "${GREEN}✅ 后端启动，PID: $BACKEND_PID${NC}"

# 启动前端
echo -e "${BLUE}启动前端服务...${NC}"
cd ../frontend
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}✅ 前端启动，PID: $FRONTEND_PID${NC}"

echo ""
echo -e "${GREEN}🎉 服务已启动！${NC}"
echo -e "前端: ${BLUE}http://localhost:3001${NC}"
echo -e "后端: ${BLUE}http://localhost:3000${NC}"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待进程
wait $BACKEND_PID $FRONTEND_PID