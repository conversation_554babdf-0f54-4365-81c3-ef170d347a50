# 牛马动物园 Docker Compose 配置
services:
  # 后端服务
  backend:
    image: node:18-alpine
    container_name: niuma-backend
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - AI_GENERATION_ENABLED=true
      - USE_REAL_AI=true
      - PREFERRED_AI_MODEL=gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./backend:/app
    command: >
      sh -c "
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev python3 make g++ &&
        npm install &&
        node simple-server.js
      "
    restart: unless-stopped
    
  # 前端服务（开发模式）
  frontend:
    image: node:18-alpine
    container_name: niuma-frontend
    working_dir: /app
    ports:
      - "3001:3001"
    volumes:
      - ./frontend:/app
    command: >
      sh -c "
        npm install &&
        npm run dev
      "
    depends_on:
      - backend
    restart: unless-stopped