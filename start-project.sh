#!/bin/bash

# 牛马动物园项目一键启动脚本

set -e

echo "🐴 牛马动物园项目启动中..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}📋 检查系统依赖...${NC}"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
        echo "访问 https://www.docker.com/get-started 下载安装"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装${NC}"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js (v18+)${NC}"
        echo "访问 https://nodejs.org 下载安装"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 所有依赖检查通过${NC}"
}

# 启动数据库服务
start_databases() {
    echo -e "\n${BLUE}🗄️  启动数据库服务...${NC}"
    cd backend
    
    # 启动 Docker Compose 服务
    echo "启动 PostgreSQL, MongoDB, Redis..."
    docker compose -f docker-compose.dev.yml up -d postgres mongo redis
    
    # 等待数据库就绪
    echo "等待数据库启动..."
    sleep 10
    
    echo -e "${GREEN}✅ 数据库服务已启动${NC}"
    cd ..
}

# 安装并启动后端服务
start_backend() {
    echo -e "\n${BLUE}🚀 启动后端 API 服务...${NC}"
    cd backend
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装后端依赖..."
        npm install
    fi
    
    # 启动后端服务（后台运行）
    echo "启动 NestJS 服务..."
    npm run start:dev > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    
    echo -e "${GREEN}✅ 后端服务启动中 (PID: $BACKEND_PID)${NC}"
    cd ..
}

# 安装并启动前端服务
start_frontend() {
    echo -e "\n${BLUE}🎨 启动前端开发服务器...${NC}"
    cd frontend
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    
    # 启动前端服务（后台运行）
    echo "启动 Vite 开发服务器..."
    npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    echo -e "${GREEN}✅ 前端服务启动中 (PID: $FRONTEND_PID)${NC}"
    cd ..
}

# 检查服务状态
check_services() {
    echo -e "\n${BLUE}🔍 检查服务状态...${NC}"
    sleep 5
    
    # 检查后端服务
    if curl -s http://localhost:3000/api/v1/health > /dev/null; then
        echo -e "${GREEN}✅ 后端 API 服务运行正常${NC}"
    else
        echo -e "${YELLOW}⚠️  后端服务正在启动，请稍等...${NC}"
    fi
    
    # 检查前端服务
    if curl -s http://localhost:5173 > /dev/null; then
        echo -e "${GREEN}✅ 前端开发服务器运行正常${NC}"
    else
        echo -e "${YELLOW}⚠️  前端服务正在启动，请稍等...${NC}"
    fi
}

# 显示访问信息
show_info() {
    echo -e "\n${GREEN}================================${NC}"
    echo -e "${GREEN}🎉 牛马动物园项目启动成功！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo -e "${BLUE}📱 前端应用:${NC} http://localhost:5173"
    echo -e "${BLUE}🔌 后端 API:${NC} http://localhost:3000"
    echo -e "${BLUE}📚 API 文档:${NC} http://localhost:3000/api/docs"
    echo -e "${BLUE}💾 PostgreSQL:${NC} localhost:5432"
    echo -e "${BLUE}🍃 MongoDB:${NC} localhost:27017"
    echo -e "${BLUE}⚡ Redis:${NC} localhost:6379"
    echo ""
    echo -e "${YELLOW}提示:${NC}"
    echo "- 查看后端日志: tail -f backend.log"
    echo "- 查看前端日志: tail -f frontend.log"
    echo "- 停止所有服务: ./stop-project.sh"
    echo ""
    echo -e "${GREEN}开始体验打工人的自嘲乐园吧！🐴${NC}"
}

# 主流程
main() {
    check_dependencies
    # start_databases
    start_backend
    start_frontend
    check_services
    show_info
}

# 捕获退出信号
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; ./stop-project.sh; exit' INT TERM

# 执行主流程
main

# 保持脚本运行
echo -e "\n${YELLOW}按 Ctrl+C 停止所有服务${NC}"
wait