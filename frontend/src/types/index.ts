// 用户相关类型定义
export interface User {
  id: string
  username: string
  email?: string
  phone?: string
  avatar?: string
  nickname?: string
  bio?: string
  status: UserStatus
  createdAt: string
  updatedAt: string
}

export enum UserStatus {
  ACTIVE = 1,
  DISABLED = 2,
  DELETED = 3,
}

export interface UserProfile extends User {
  testResult?: TestResult
  stats: UserStats
  achievements: Achievement[]
}

export interface UserStats {
  totalLikes: number
  totalFeeds: number
  totalPosts: number
  totalComments: number
  loginDays: number
  level: number
  points: number
}

// 测试相关类型定义
export interface TestResult {
  id: string
  userId: string
  animalType: AnimalType
  animalSpecies: string
  testAnswers: TestAnswer[]
  personalityScore: PersonalityScore
  testVersion: string
  createdAt: string
}

export enum AnimalType {
  DIVINE = 'divine', // 神兽
  PET = 'pet',       // 宠物  
  CATTLE = 'cattle', // 牛马
}

export interface TestAnswer {
  questionId: string
  answerId: string
  question: string
  answer: string
}

export interface PersonalityScore {
  workPressure: number    // 工作压力
  compliance: number      // 服从性
  creativity: number      // 创造力
  socialSkill: number     // 社交能力
  resilience: number      // 抗压能力
}

export interface TestQuestion {
  id: string
  question: string
  options: TestOption[]
  category: string
}

export interface TestOption {
  id: string
  text: string
  score: PersonalityScore
}

// 动物相关类型定义
export interface Animal {
  id: string
  userId: string
  type: AnimalType
  species: string
  position: Vector3D
  rotation: Vector3D
  animation: AnimationState
  mood: MoodState
  likeCount: number
  feedCount: number
  lastActive: string
}

export interface Vector3D {
  x: number
  y: number
  z: number
}

export enum AnimationState {
  IDLE = 'idle',
  WALK = 'walk',
  RUN = 'run',
  EAT = 'eat',
  SLEEP = 'sleep',
  INTERACT = 'interact',
}

export enum MoodState {
  HAPPY = 'happy',
  NORMAL = 'normal',
  SAD = 'sad',
  ANGRY = 'angry',
  TIRED = 'tired',
}

// 社交互动类型定义
export interface Interaction {
  id: string
  userId: string
  targetUserId: string
  type: InteractionType
  createdAt: string
}

export enum InteractionType {
  LIKE = 'like',
  FEED = 'feed',
  COMMENT = 'comment',
  FOLLOW = 'follow',
}

export interface Comment {
  id: string
  userId: string
  contentId: string
  contentType: string
  parentId?: string
  content: string
  likeCount: number
  replies?: Comment[]
  status: ContentStatus
  createdAt: string
  user: User
}

// 内容相关类型定义
export interface Post {
  id: string
  userId: string
  content: string
  images: string[]
  tags: string[]
  isAnonymous: boolean
  likeCount: number
  commentCount: number
  shareCount: number
  status: ContentStatus
  createdAt: string
  updatedAt: string
  user: User
  comments?: Comment[]
}

export enum ContentStatus {
  NORMAL = 1,
  REVIEWING = 2,
  HIDDEN = 3,
  DELETED = 4,
}

// 排行榜类型定义
export interface Ranking {
  id: string
  userId: string
  rankingType: RankingType
  score: number
  rankPosition: number
  period: RankingPeriod
  periodDate: string
  user: User
}

export enum RankingType {
  OVERTIME_KING = 'overtime_king',     // 加班王
  FISH_MASTER = 'fish_master',         // 摸鱼大师
  MISERY_CHAMPION = 'misery_champion', // 最惨打工人
  ACTIVE_USER = 'active_user',         // 整活达人
  POPULARITY = 'popularity',           // 人气之王
}

export enum RankingPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

// 成就系统类型定义
export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  condition: AchievementCondition
  reward: AchievementReward
  unlockedAt?: string
}

export interface AchievementCondition {
  type: string
  target: number
  current?: number
}

export interface AchievementReward {
  points: number
  badge?: string
  title?: string
}

// API 相关类型定义
export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  code: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface ApiError {
  message: string
  code: number
  details?: any
}

// 认证相关类型定义
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  username: string
  email?: string
  phone?: string
  password: string
  nickname?: string
}

export interface AuthResponse {
  accessToken: string
  refreshToken: string
  user: User
  expiresIn: number
}

export interface RefreshTokenRequest {
  refreshToken: string
}

// 分享相关类型定义
export interface ShareContent {
  type: ShareType
  title: string
  description: string
  image?: string
  url: string
}

export enum ShareType {
  TEST_RESULT = 'test_result',
  POST = 'post',
  ACHIEVEMENT = 'achievement',
  RANKING = 'ranking',
}

// 3D场景相关类型定义
export interface SceneConfig {
  cameraPosition: Vector3D
  lightIntensity: number
  fogDensity: number
  backgroundColor: string
  maxAnimals: number
}

export interface AnimalModel {
  id: string
  name: string
  type: AnimalType
  species: string
  modelPath: string
  animationPaths: Record<AnimationState, string>
  scale: Vector3D
  boundingBox: Vector3D
}

// 应用状态类型定义
export interface AppState {
  user: UserState
  auth: AuthState
  zoo: ZooState
  content: ContentState
  ui: UIState
}

export interface UserState {
  currentUser: User | null
  profile: UserProfile | null
  stats: UserStats | null
  loading: boolean
  error: string | null
}

export interface AuthState {
  isAuthenticated: boolean
  accessToken: string | null
  refreshToken: string | null
  loading: boolean
  error: string | null
}

export interface ZooState {
  animals: Animal[]
  currentScene: string
  sceneConfig: SceneConfig
  loading: boolean
  error: string | null
}

export interface ContentState {
  posts: Post[]
  currentPost: Post | null
  comments: Comment[]
  loading: boolean
  error: string | null
}

export interface UIState {
  sidebarOpen: boolean
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  createdAt: string
}

// 表单相关类型定义
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox'
  placeholder?: string
  required?: boolean
  validation?: ValidationRule[]
  options?: FormOption[]
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern'
  value?: any
  message: string
}

export interface FormOption {
  label: string
  value: string | number
}

// 路由相关类型定义
export interface RouteConfig {
  path: string
  component: React.ComponentType
  exact?: boolean
  requireAuth?: boolean
  roles?: string[]
  title?: string
  description?: string
}

// 配置相关类型定义
export interface AppConfig {
  apiBaseUrl: string
  cdnBaseUrl: string
  wsUrl: string
  version: string
  environment: 'development' | 'staging' | 'production'
  features: FeatureFlags
}

export interface FeatureFlags {
  enableThreeJS: boolean
  enablePWA: boolean
  enableAnalytics: boolean
  enableRealtime: boolean
  enableSharing: boolean
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 事件相关类型
export interface CustomEvent<T = any> {
  type: string
  payload: T
  timestamp: number
}

// 性能监控相关类型
export interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: number
}

export interface AnalyticsEvent {
  event: string
  category: string
  action: string
  label?: string
  value?: number
  userId?: string
}