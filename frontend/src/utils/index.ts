import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { AnimalType } from '@/types'

// Tailwind CSS 类名合并工具
export const cn = (...inputs: ClassValue[]) => {
  return twMerge(clsx(inputs))
}

// 时间格式化工具
export const formatTime = {
  // 相对时间格式化 (1分钟前, 1小时前, 1天前等)
  relative: (date: string | Date): string => {
    const now = new Date()
    const targetDate = new Date(date)
    const diff = now.getTime() - targetDate.getTime()

    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    const weeks = Math.floor(days / 7)
    const months = Math.floor(days / 30)
    const years = Math.floor(days / 365)

    if (seconds < 60) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    if (weeks < 4) return `${weeks}周前`
    if (months < 12) return `${months}个月前`
    return `${years}年前`
  },

  // 标准时间格式化
  standard: (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  // 友好的日期格式 (今天 12:30, 昨天 15:20, 1月15日 10:00)
  friendly: (date: string | Date): string => {
    const targetDate = new Date(date)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate())

    const diffDays = Math.floor((today.getTime() - targetDay.getTime()) / (1000 * 60 * 60 * 24))
    const timeStr = formatTime.standard(targetDate, 'HH:mm')

    if (diffDays === 0) return `今天 ${timeStr}`
    if (diffDays === 1) return `昨天 ${timeStr}`
    if (diffDays === 2) return `前天 ${timeStr}`
    if (diffDays < 7) return `${diffDays}天前 ${timeStr}`

    return formatTime.standard(targetDate, 'MM月DD日 HH:mm')
  }
}

// 数字格式化工具
export const formatNumber = {
  // 简化数字显示 (1K, 1.2M, 1.5B)
  compact: (num: number): string => {
    if (num < 1000) return num.toString()
    if (num < 1000000) return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
    if (num < 1000000000) return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
    return (num / 1000000000).toFixed(1).replace(/\.0$/, '') + 'B'
  },

  // 千分位分隔符
  withCommas: (num: number): string => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  },

  // 百分比格式化
  percentage: (num: number, decimals = 1): string => {
    return (num * 100).toFixed(decimals) + '%'
  }
}

// 文本处理工具
export const textUtils = {
  // 截断文本
  truncate: (text: string, maxLength: number, suffix = '...'): string => {
    if (text.length <= maxLength) return text
    return text.slice(0, maxLength - suffix.length) + suffix
  },

  // 转换为kebab-case
  kebabCase: (str: string): string => {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  },

  // 转换为camelCase
  camelCase: (str: string): string => {
    return str
      .toLowerCase()
      .replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
  },

  // 去除HTML标签
  stripHtml: (html: string): string => {
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
  },

  // 高亮关键词
  highlight: (text: string, keyword: string): string => {
    if (!keyword) return text
    const regex = new RegExp(`(${keyword})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }
}

// URL处理工具
export const urlUtils = {
  // 构建查询参数
  buildQuery: (params: Record<string, any>): string => {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        query.append(key, String(value))
      }
    })
    return query.toString()
  },

  // 解析查询参数
  parseQuery: (search: string): Record<string, string> => {
    const params: Record<string, string> = {}
    const query = new URLSearchParams(search)
    query.forEach((value, key) => {
      params[key] = value
    })
    return params
  },

  // 获取文件扩展名
  getFileExtension: (url: string): string => {
    const parts = url.split('.')
    return parts && parts.length > 0 ? (parts.pop()?.toLowerCase() || '') : ''
  },

  // 安全地从data URL中提取base64数据
  extractBase64: (dataUrl: string): string => {
    if (!dataUrl) return ''
    
    if (dataUrl.startsWith('data:')) {
      const parts = dataUrl.split(',')
      return parts && parts.length > 1 ? parts[1] : dataUrl
    }
    
    return dataUrl
  },

  // 判断是否为有效URL
  isValidUrl: (string: string): boolean => {
    try {
      new URL(string)
      return true
    } catch {
      return false
    }
  }
}

// 动物类型相关工具
export const animalUtils = {
  // 获取动物类型的中文名称
  getTypeName: (type: AnimalType): string => {
    const names = {
      [AnimalType.DIVINE]: '神兽',
      [AnimalType.PET]: '宠物',
      [AnimalType.CATTLE]: '牛马'
    }
    return names[type] || '未知'
  },

  // 获取动物类型的颜色
  getTypeColor: (type: AnimalType): string => {
    const colors = {
      [AnimalType.DIVINE]: 'text-yellow-600 bg-yellow-100',
      [AnimalType.PET]: 'text-pink-600 bg-pink-100',
      [AnimalType.CATTLE]: 'text-yellow-800 bg-yellow-100'
    }
    return colors[type] || 'text-gray-600 bg-gray-100'
  },

  // 获取动物类型的图标
  getTypeIcon: (type: AnimalType): string => {
    const icons = {
      [AnimalType.DIVINE]: '🦌',
      [AnimalType.PET]: '🐶',
      [AnimalType.CATTLE]: '🐄'
    }
    return icons[type] || '🐾'
  },

  // 根据分数计算动物类型
  calculateAnimalType: (scores: {
    workPressure: number
    compliance: number
    creativity: number
    socialSkill: number
    resilience: number
  }): { type: AnimalType; species: string; description: string } => {
    const { workPressure, compliance, creativity, socialSkill, resilience } = scores
    const totalScore = workPressure + compliance + creativity + socialSkill + resilience
    const averageScore = totalScore / 5

    // 神兽：低工作压力，高创造力，高抗压能力
    if (workPressure < 3 && creativity > 7 && resilience > 7) {
      return {
        type: AnimalType.DIVINE,
        species: creativity > 8 ? 'dragon' : 'unicorn',
        description: '你是传说中的神兽，拥有超凡的能力和智慧，工作对你来说游刃有余。'
      }
    }

    // 宠物：中等工作压力，高社交能力，被人喜爱
    if (workPressure < 6 && socialSkill > 6 && compliance < 7) {
      return {
        type: AnimalType.PET,
        species: socialSkill > 8 ? 'dog' : 'cat',
        description: '你是人见人爱的宠物，工作环境相对轻松，深受大家喜爱。'
      }
    }

    // 牛马：高工作压力，高服从性，默默承受
    return {
      type: AnimalType.CATTLE,
      species: workPressure > 7 ? 'horse' : 'cow',
      description: '你是勤劳的牛马，承担着繁重的工作，是团队不可缺少的力量。'
    }
  }
}

// 文件处理工具
export const fileUtils = {
  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 检查文件类型
  isImageFile: (file: File | string): boolean => {
    const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (typeof file === 'string') {
      const ext = urlUtils.getFileExtension(file)
      return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)
    }
    return imageTypes.includes(file.type)
  },

  // 压缩图片
  compressImage: (file: File, maxWidth = 1920, maxHeight = 1080, quality = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new Image()

      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height
        ctx.drawImage(img, 0, 0, width, height)

        canvas.toBlob(
          (blob) => {
            const compressedFile = new File([blob!], file.name, {
              type: file.type,
              lastModified: Date.now(),
            })
            resolve(compressedFile)
          },
          file.type,
          quality
        )
      }

      img.src = URL.createObjectURL(file)
    })
  }
}

// 本地存储工具
export const storageUtils = {
  // 设置本地存储（支持对象）
  set: (key: string, value: any): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('设置本地存储失败:', error)
    }
  },

  // 获取本地存储
  get: <T>(key: string, defaultValue: T): T => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('获取本地存储失败:', error)
      return defaultValue
    }
  },

  // 删除本地存储
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('删除本地存储失败:', error)
    }
  },

  // 清空本地存储
  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空本地存储失败:', error)
    }
  },

  // 获取存储使用情况
  getUsage: (): { used: number; total: number; percentage: number } => {
    let used = 0
    let total = 5 * 1024 * 1024 // 5MB (大致的localStorage限制)

    try {
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length
        }
      }
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
    }

    return {
      used,
      total,
      percentage: (used / total) * 100
    }
  }
}

// 设备检测工具
export const deviceUtils = {
  // 检测是否为移动设备
  isMobile: (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  },

  // 检测是否为触摸设备
  isTouchDevice: (): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },

  // 获取设备类型
  getDeviceType: (): 'mobile' | 'tablet' | 'desktop' => {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  },

  // 获取浏览器信息
  getBrowserInfo: (): { name: string; version: string } => {
    const userAgent = navigator.userAgent
    let browserName = 'Unknown'
    let browserVersion = 'Unknown'

    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome'
      browserVersion = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'Unknown'
    } else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox'
      browserVersion = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'Unknown'
    } else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari'
      browserVersion = userAgent.match(/Safari\/(\d+)/)?.[1] || 'Unknown'
    } else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge'
      browserVersion = userAgent.match(/Edge\/(\d+)/)?.[1] || 'Unknown'
    }

    return { name: browserName, version: browserVersion }
  }
}

// 防抖和节流工具
export const throttleUtils = {
  // 防抖
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func.apply(null, args), delay)
    }
  },

  // 节流
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0
    return (...args: Parameters<T>) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func.apply(null, args)
      }
    }
  }
}

// 颜色工具
export const colorUtils = {
  // 十六进制转RGB
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result && result.length >= 4
      ? {
          r: parseInt(result[1] || '0', 16),
          g: parseInt(result[2] || '0', 16),
          b: parseInt(result[3] || '0', 16),
        }
      : null
  },

  // RGB转十六进制
  rgbToHex: (r: number, g: number, b: number): string => {
    return '#' + [r, g, b].map(x => {
      const hex = x.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }).join('')
  },

  // 生成随机颜色
  randomColor: (): string => {
    return '#' + Math.floor(Math.random() * 16777215).toString(16)
  },

  // 计算颜色亮度
  getBrightness: (hex: string): number => {
    const rgb = colorUtils.hexToRgb(hex)
    if (!rgb) return 0
    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
  },

  // 判断颜色是否为深色
  isDark: (hex: string): boolean => {
    return colorUtils.getBrightness(hex) < 128
  }
}

// 表单验证工具
export const validationUtils = {
  // 邮箱验证
  isEmail: (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  },

  // 手机号验证（中国大陆）
  isPhoneNumber: (phone: string): boolean => {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  // 密码强度检查
  getPasswordStrength: (password: string): { score: number; level: string; suggestions: string[] } => {
    let score = 0
    const suggestions: string[] = []

    // 长度检查
    if (password.length >= 8) {
      score += 1
    } else {
      suggestions.push('密码至少需要8个字符')
    }

    // 大写字母
    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含至少一个大写字母')
    }

    // 小写字母
    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含至少一个小写字母')
    }

    // 数字
    if (/\d/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含至少一个数字')
    }

    // 特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含至少一个特殊字符')
    }

    const levels = ['很弱', '弱', '一般', '强', '很强']
    const level = levels[Math.min(score, 4)]

    return { score, level, suggestions }
  },

  // URL验证
  isUrl: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  // 身份证验证（简单版）
  isIdCard: (idCard: string): boolean => {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
  }
}

// 错误处理工具
export const errorUtils = {
  // 错误日志记录
  logError: (error: Error, context?: string): void => {
    console.error(`[Error${context ? ` - ${context}` : ''}]:`, {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    })

    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  },

  // 安全执行函数
  safeExecute: async <T>(
    fn: () => Promise<T>,
    fallback: T,
    context?: string
  ): Promise<T> => {
    try {
      return await fn()
    } catch (error) {
      errorUtils.logError(error as Error, context)
      return fallback
    }
  },

  // 重试机制
  retry: async <T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> => {
    let lastError: Error

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }

    throw lastError!
  }
}