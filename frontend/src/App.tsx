import { useEffect, Suspense } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'

import { useAppDispatch, useAppSelector } from '@/store'
import { initializeAuth } from '@/store/slices/authSlice'
import { initializeUI } from '@/store/slices/uiSlice'
import { fetchCurrentUser } from '@/store/slices/userSlice'

// Layout Components
import Layout from '@/components/layout/Layout'
import AuthLayout from '@/components/layout/AuthLayout'

// Route Guards
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PublicRoute from '@/components/auth/PublicRoute'

// Loading Components
import PageLoader from '@/components/ui/PageLoader'
import ErrorBoundary from '@/components/ui/ErrorBoundary'

// Page Components (Lazy loaded for better performance)
import { lazy } from 'react'

// Public Pages
const WelcomePage = lazy(() => import('@/pages/WelcomePage'))
const OnboardingPage = lazy(() => import('@/pages/OnboardingPage'))
const LoginPage = lazy(() => import('@/pages/auth/LoginPage'))
const RegisterPage = lazy(() => import('@/pages/auth/RegisterPage'))
const ForgotPasswordPage = lazy(() => import('@/pages/auth/ForgotPasswordPage'))
const ResetPasswordPage = lazy(() => import('@/pages/auth/ResetPasswordPage'))

// Protected Pages
const HomePage = lazy(() => import('@/pages/HomePage'))
const DrawingPage = lazy(() => import('@/pages/DrawingPage'))
const TestPage = lazy(() => import('@/pages/test/TestPage'))
const TestResultPage = lazy(() => import('@/pages/test/TestResultPage'))
const ZooPage = lazy(() => import('@/pages/zoo/ZooPage'))
const PostsPage = lazy(() => import('@/pages/posts/PostsPage'))
const RankingPage = lazy(() => import('@/pages/ranking/RankingPage'))
const ProfilePage = lazy(() => import('@/pages/profile/ProfilePage'))
const SettingsPage = lazy(() => import('@/pages/settings/SettingsPage'))
// const SharePage = lazy(() => import('@/pages/share/SharePage'))

// Error Pages
const NotFoundPage = lazy(() => import('@/pages/errors/NotFoundPage'))
const ServerErrorPage = lazy(() => import('@/pages/errors/ServerErrorPage'))

function App() {
  const dispatch = useAppDispatch()
  const location = useLocation()
  const { isAuthenticated, accessToken } = useAppSelector(state => state.auth)
  const { currentUser } = useAppSelector(state => state.user)

  // 应用初始化
  useEffect(() => {
    // 初始化认证状态
    dispatch(initializeAuth())
    
    // 初始化UI状态
    dispatch(initializeUI())
  }, [dispatch])

  // 获取当前用户信息
  useEffect(() => {
    if (isAuthenticated && accessToken && !currentUser) {
      dispatch(fetchCurrentUser())
    }
  }, [dispatch, isAuthenticated, accessToken, currentUser])

  // 页面切换时滚动到顶部
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [location.pathname])

  return (
    <ErrorBoundary>
      <div className="App">
        <Suspense fallback={<PageLoader />}>
          <Routes>
            {/* Public Routes */}
            <Route
              path="/welcome"
              element={
                <PublicRoute>
                  <WelcomePage />
                </PublicRoute>
              }
            />
            <Route
              path="/onboarding"
              element={
                <PublicRoute>
                  <OnboardingPage />
                </PublicRoute>
              }
            />

            {/* Auth Routes */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <AuthLayout>
                    <LoginPage />
                  </AuthLayout>
                </PublicRoute>
              }
            />
            <Route
              path="/register"
              element={
                <PublicRoute>
                  <AuthLayout>
                    <RegisterPage />
                  </AuthLayout>
                </PublicRoute>
              }
            />
            <Route
              path="/forgot-password"
              element={
                <PublicRoute>
                  <AuthLayout>
                    <ForgotPasswordPage />
                  </AuthLayout>
                </PublicRoute>
              }
            />
            <Route
              path="/reset-password"
              element={
                <PublicRoute>
                  <AuthLayout>
                    <ResetPasswordPage />
                  </AuthLayout>
                </PublicRoute>
              }
            />

            {/* Protected Routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout>
                    <HomePage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/drawing"
              element={
                <ProtectedRoute>
                  <DrawingPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/test"
              element={
                <ProtectedRoute>
                  <Layout>
                    <TestPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/test/result/:userId?"
              element={
                <ProtectedRoute>
                  <Layout>
                    <TestResultPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/zoo"
              element={
                <ProtectedRoute>
                  <Layout>
                    <ZooPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/posts"
              element={
                <ProtectedRoute>
                  <Layout>
                    <PostsPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/ranking"
              element={
                <ProtectedRoute>
                  <Layout>
                    <RankingPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile/:userId?"
              element={
                <ProtectedRoute>
                  <Layout>
                    <ProfilePage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Layout>
                    <SettingsPage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* Error Routes */}
            <Route
              path="/500"
              element={<ServerErrorPage />}
            />
            <Route
              path="/404"
              element={<NotFoundPage />}
            />

            {/* Default Redirects */}
            <Route
              path="/home"
              element={<Navigate to="/" replace />}
            />

            {/* Catch all route - 404 */}
            <Route
              path="*"
              element={<Navigate to="/404" replace />}
            />
          </Routes>
        </Suspense>

        {/* Global Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#fff',
              color: '#374151',
              border: '1px solid #e5e7eb',
              borderRadius: '12px',
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
            success: {
              style: {
                border: '1px solid #10b981',
              },
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff',
              },
            },
            error: {
              style: {
                border: '1px solid #ef4444',
              },
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </ErrorBoundary>
  )
}

export default App