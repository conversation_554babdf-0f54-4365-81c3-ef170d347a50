import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { ContentState, Post, Comment, ApiError, PaginatedResponse } from '@/types'
import { contentApi } from '@/services/api'

interface FetchPostsParams {
  page?: number
  pageSize?: number
  sortBy?: string
  tag?: string
}

interface CreatePostParams {
  content: string
  images?: string[]
  tags?: string[]
  isAnonymous?: boolean
}

const initialState: ContentState = {
  posts: [],
  currentPost: null,
  comments: [],
  loading: false,
  error: null,
}

// 异步action creators
export const fetchPosts = createAsyncThunk<
  PaginatedResponse<Post>,
  FetchPostsParams,
  { rejectValue: ApiError }
>('content/fetchPosts', async (params, { rejectWithValue }) => {
  try {
    const response = await contentApi.getPosts(params)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取帖子列表失败', code: 500 })
  }
})

export const fetchPostById = createAsyncThunk<
  Post,
  string,
  { rejectValue: ApiError }
>('content/fetchPostById', async (postId, { rejectWithValue }) => {
  try {
    const response = await contentApi.getPostById(postId)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取帖子详情失败', code: 500 })
  }
})

export const createPost = createAsyncThunk<
  Post,
  CreatePostParams,
  { rejectValue: ApiError }
>('content/createPost', async (postData, { rejectWithValue }) => {
  try {
    const response = await contentApi.createPost(postData)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '发布帖子失败', code: 500 })
  }
})

export const likePost = createAsyncThunk<
  { postId: string; likeCount: number },
  string,
  { rejectValue: ApiError }
>('content/likePost', async (postId, { rejectWithValue }) => {
  try {
    const response = await contentApi.likePost(postId)
    return { postId, likeCount: response.data.likeCount }
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '点赞失败', code: 500 })
  }
})

export const fetchComments = createAsyncThunk<
  Comment[],
  { postId: string; page?: number; pageSize?: number },
  { rejectValue: ApiError }
>('content/fetchComments', async ({ postId, page = 1, pageSize = 20 }, { rejectWithValue }) => {
  try {
    const response = await contentApi.getComments(postId, { page, pageSize })
    return response.data.items
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取评论列表失败', code: 500 })
  }
})

export const createComment = createAsyncThunk<
  Comment,
  { postId: string; content: string; parentId?: string },
  { rejectValue: ApiError }
>('content/createComment', async (commentData, { rejectWithValue }) => {
  try {
    const response = await contentApi.createComment(commentData)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '发表评论失败', code: 500 })
  }
})

export const deletePost = createAsyncThunk<
  string,
  string,
  { rejectValue: ApiError }
>('content/deletePost', async (postId, { rejectWithValue }) => {
  try {
    await contentApi.deletePost(postId)
    return postId
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '删除帖子失败', code: 500 })
  }
})

const contentSlice = createSlice({
  name: 'content',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null
    },
    setCurrentPost: (state, action: PayloadAction<Post | null>) => {
      state.currentPost = action.payload
    },
    updatePost: (state, action: PayloadAction<{ id: string; updates: Partial<Post> }>) => {
      const index = state.posts.findIndex(post => post.id === action.payload.id)
      if (index >= 0) {
        state.posts[index] = { ...state.posts[index], ...action.payload.updates }
      }
      if (state.currentPost && state.currentPost.id === action.payload.id) {
        state.currentPost = { ...state.currentPost, ...action.payload.updates }
      }
    },
    addComment: (state, action: PayloadAction<Comment>) => {
      state.comments.unshift(action.payload)
      // 更新对应帖子的评论数
      const postIndex = state.posts.findIndex(post => post.id === action.payload.contentId)
      if (postIndex >= 0) {
        state.posts[postIndex].commentCount += 1
      }
      if (state.currentPost && state.currentPost.id === action.payload.contentId) {
        state.currentPost.commentCount += 1
      }
    },
    clearPosts: state => {
      state.posts = []
    },
    clearComments: state => {
      state.comments = []
    },
  },
  extraReducers: builder => {
    builder
      // Fetch posts cases
      .addCase(fetchPosts.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchPosts.fulfilled, (state, action) => {
        state.loading = false
        state.posts = action.payload.items
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取帖子列表失败'
      })
      
      // Fetch post by id cases
      .addCase(fetchPostById.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchPostById.fulfilled, (state, action) => {
        state.loading = false
        state.currentPost = action.payload
      })
      .addCase(fetchPostById.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取帖子详情失败'
      })
      
      // Create post cases
      .addCase(createPost.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(createPost.fulfilled, (state, action) => {
        state.loading = false
        state.posts.unshift(action.payload)
      })
      .addCase(createPost.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '发布帖子失败'
      })
      
      // Like post cases
      .addCase(likePost.fulfilled, (state, action) => {
        const { postId, likeCount } = action.payload
        const postIndex = state.posts.findIndex(post => post.id === postId)
        if (postIndex >= 0) {
          state.posts[postIndex].likeCount = likeCount
        }
        if (state.currentPost && state.currentPost.id === postId) {
          state.currentPost.likeCount = likeCount
        }
      })
      .addCase(likePost.rejected, (state, action) => {
        state.error = action.payload?.message || '点赞失败'
      })
      
      // Fetch comments cases
      .addCase(fetchComments.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchComments.fulfilled, (state, action) => {
        state.loading = false
        state.comments = action.payload
      })
      .addCase(fetchComments.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取评论列表失败'
      })
      
      // Create comment cases
      .addCase(createComment.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(createComment.fulfilled, (state, action) => {
        state.loading = false
        state.comments.unshift(action.payload)
        // 更新对应帖子的评论数
        const postIndex = state.posts.findIndex(post => post.id === action.payload.contentId)
        if (postIndex >= 0) {
          state.posts[postIndex].commentCount += 1
        }
        if (state.currentPost && state.currentPost.id === action.payload.contentId) {
          state.currentPost.commentCount += 1
        }
      })
      .addCase(createComment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '发表评论失败'
      })
      
      // Delete post cases
      .addCase(deletePost.fulfilled, (state, action) => {
        state.posts = state.posts.filter(post => post.id !== action.payload)
        if (state.currentPost && state.currentPost.id === action.payload) {
          state.currentPost = null
        }
      })
      .addCase(deletePost.rejected, (state, action) => {
        state.error = action.payload?.message || '删除帖子失败'
      })
  },
})

export const {
  clearError,
  setCurrentPost,
  updatePost,
  addComment,
  clearPosts,
  clearComments,
} = contentSlice.actions

export default contentSlice.reducer

// Selectors
export const selectContent = (state: { content: ContentState }) => state.content
export const selectPosts = (state: { content: ContentState }) => state.content.posts
export const selectCurrentPost = (state: { content: ContentState }) => state.content.currentPost
export const selectComments = (state: { content: ContentState }) => state.content.comments
export const selectContentLoading = (state: { content: ContentState }) => state.content.loading
export const selectContentError = (state: { content: ContentState }) => state.content.error
export const selectPostById = (postId: string) => (state: { content: ContentState }) =>
  state.content.posts.find(post => post.id === postId)