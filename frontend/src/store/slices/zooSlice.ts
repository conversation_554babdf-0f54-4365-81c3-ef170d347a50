import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { ZooState, Animal, SceneConfig, ApiError } from '@/types'
import { zooApi } from '@/services/api'

const initialState: ZooState = {
  animals: [],
  currentScene: 'main',
  sceneConfig: {
    cameraPosition: { x: 0, y: 10, z: 20 },
    lightIntensity: 1.0,
    fogDensity: 0.01,
    backgroundColor: '#87CEEB',
    maxAnimals: 200,
  },
  loading: false,
  error: null,
}

// 异步action creators
export const fetchAnimals = createAsyncThunk<
  Animal[],
  { scene?: string; limit?: number },
  { rejectValue: ApiError }
>('zoo/fetchAnimals', async ({ scene = 'main', limit = 100 }, { rejectWithValue }) => {
  try {
    const response = await zooApi.getAnimals({ scene, limit })
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取动物列表失败', code: 500 })
  }
})

export const updateAnimalPosition = createAsyncThunk<
  Animal,
  { animalId: string; position: { x: number; y: number; z: number } },
  { rejectValue: ApiError }
>('zoo/updateAnimalPosition', async ({ animalId, position }, { rejectWithValue }) => {
  try {
    const response = await zooApi.updateAnimalPosition(animalId, position)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '更新动物位置失败', code: 500 })
  }
})

export const updateAnimalAnimation = createAsyncThunk<
  Animal,
  { animalId: string; animation: string; mood?: string },
  { rejectValue: ApiError }
>('zoo/updateAnimalAnimation', async ({ animalId, animation, mood }, { rejectWithValue }) => {
  try {
    const response = await zooApi.updateAnimalAnimation(animalId, { animation, mood })
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '更新动物动画失败', code: 500 })
  }
})

const zooSlice = createSlice({
  name: 'zoo',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null
    },
    setCurrentScene: (state, action: PayloadAction<string>) => {
      state.currentScene = action.payload
    },
    updateSceneConfig: (state, action: PayloadAction<Partial<SceneConfig>>) => {
      state.sceneConfig = { ...state.sceneConfig, ...action.payload }
    },
    addAnimal: (state, action: PayloadAction<Animal>) => {
      const existingIndex = state.animals.findIndex(animal => animal.id === action.payload.id)
      if (existingIndex >= 0) {
        state.animals[existingIndex] = action.payload
      } else {
        state.animals.push(action.payload)
      }
    },
    removeAnimal: (state, action: PayloadAction<string>) => {
      state.animals = state.animals.filter(animal => animal.id !== action.payload)
    },
    updateAnimal: (state, action: PayloadAction<{ id: string; updates: Partial<Animal> }>) => {
      const index = state.animals.findIndex(animal => animal.id === action.payload.id)
      if (index >= 0) {
        state.animals[index] = { ...state.animals[index], ...action.payload.updates }
      }
    },
    clearAnimals: state => {
      state.animals = []
    },
  },
  extraReducers: builder => {
    builder
      // Fetch animals cases
      .addCase(fetchAnimals.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAnimals.fulfilled, (state, action) => {
        state.loading = false
        state.animals = action.payload
      })
      .addCase(fetchAnimals.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取动物列表失败'
      })
      
      // Update animal position cases
      .addCase(updateAnimalPosition.fulfilled, (state, action) => {
        const index = state.animals.findIndex(animal => animal.id === action.payload.id)
        if (index >= 0) {
          state.animals[index] = action.payload
        }
      })
      .addCase(updateAnimalPosition.rejected, (state, action) => {
        state.error = action.payload?.message || '更新动物位置失败'
      })
      
      // Update animal animation cases
      .addCase(updateAnimalAnimation.fulfilled, (state, action) => {
        const index = state.animals.findIndex(animal => animal.id === action.payload.id)
        if (index >= 0) {
          state.animals[index] = action.payload
        }
      })
      .addCase(updateAnimalAnimation.rejected, (state, action) => {
        state.error = action.payload?.message || '更新动物动画失败'
      })
  },
})

export const {
  clearError,
  setCurrentScene,
  updateSceneConfig,
  addAnimal,
  removeAnimal,
  updateAnimal,
  clearAnimals,
} = zooSlice.actions

export default zooSlice.reducer

// Selectors
export const selectZoo = (state: { zoo: ZooState }) => state.zoo
export const selectAnimals = (state: { zoo: ZooState }) => state.zoo.animals
export const selectCurrentScene = (state: { zoo: ZooState }) => state.zoo.currentScene
export const selectSceneConfig = (state: { zoo: ZooState }) => state.zoo.sceneConfig
export const selectZooLoading = (state: { zoo: ZooState }) => state.zoo.loading
export const selectZooError = (state: { zoo: ZooState }) => state.zoo.error
export const selectAnimalById = (animalId: string) => (state: { zoo: ZooState }) =>
  state.zoo.animals.find(animal => animal.id === animalId)
export const selectAnimalsByType = (animalType: string) => (state: { zoo: ZooState }) =>
  state.zoo.animals.filter(animal => animal.type === animalType)