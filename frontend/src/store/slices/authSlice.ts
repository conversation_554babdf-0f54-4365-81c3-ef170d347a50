import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AuthState, LoginRequest, RegisterRequest, AuthResponse, ApiError } from '@/types'
import { authApi } from '@/services/api'

const initialState: AuthState = {
  isAuthenticated: false,
  accessToken: localStorage.getItem('accessToken'),
  refreshToken: localStorage.getItem('refreshToken'),
  loading: false,
  error: null,
}

// 异步action creators
export const login = createAsyncThunk<
  AuthResponse,
  LoginRequest,
  { rejectValue: ApiError }
>('auth/login', async (credentials, { rejectWithValue }) => {
  try {
    const response = await authApi.login(credentials)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '登录失败', code: 500 })
  }
})

export const register = createAsyncThunk<
  AuthResponse,
  RegisterRequest,
  { rejectValue: ApiError }
>('auth/register', async (userData, { rejectWithValue }) => {
  try {
    const response = await authApi.register(userData)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '注册失败', code: 500 })
  }
})

export const refreshToken = createAsyncThunk<
  AuthResponse,
  void,
  { rejectValue: ApiError }
>('auth/refreshToken', async (_, { getState, rejectWithValue }) => {
  try {
    const state = getState() as any
    const token = state.auth.refreshToken
    if (!token) {
      throw new Error('No refresh token available')
    }
    
    const response = await authApi.refreshToken({ refreshToken: token })
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '刷新token失败', code: 401 })
  }
})

export const logout = createAsyncThunk<
  void,
  void,
  { rejectValue: ApiError }
>('auth/logout', async (_, { getState, rejectWithValue }) => {
  try {
    const state = getState() as any
    const token = state.auth.refreshToken
    if (token) {
      await authApi.logout({ refreshToken: token })
    }
  } catch (error: any) {
    // 即使登出API失败，也要清除本地状态
    console.warn('Logout API failed:', error)
  }
})

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null
    },
    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string }>) => {
      state.accessToken = action.payload.accessToken
      state.refreshToken = action.payload.refreshToken
      state.isAuthenticated = true
      
      // 保存到localStorage
      localStorage.setItem('accessToken', action.payload.accessToken)
      localStorage.setItem('refreshToken', action.payload.refreshToken)
    },
    clearTokens: state => {
      state.accessToken = null
      state.refreshToken = null
      state.isAuthenticated = false
      
      // 清除localStorage
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
    },
    initializeAuth: state => {
      const accessToken = localStorage.getItem('accessToken')
      const refreshToken = localStorage.getItem('refreshToken')
      
      if (accessToken && refreshToken) {
        state.accessToken = accessToken
        state.refreshToken = refreshToken
        state.isAuthenticated = true
      }
    },
  },
  extraReducers: builder => {
    builder
      // Login cases
      .addCase(login.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false
        state.isAuthenticated = true
        state.accessToken = action.payload.accessToken
        state.refreshToken = action.payload.refreshToken
        
        // 保存到localStorage
        localStorage.setItem('accessToken', action.payload.accessToken)
        localStorage.setItem('refreshToken', action.payload.refreshToken)
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '登录失败'
        state.isAuthenticated = false
        state.accessToken = null
        state.refreshToken = null
      })
      
      // Register cases
      .addCase(register.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(register.fulfilled, (state, action) => {
        state.loading = false
        state.isAuthenticated = true
        state.accessToken = action.payload.accessToken
        state.refreshToken = action.payload.refreshToken
        
        // 保存到localStorage
        localStorage.setItem('accessToken', action.payload.accessToken)
        localStorage.setItem('refreshToken', action.payload.refreshToken)
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '注册失败'
      })
      
      // Refresh token cases
      .addCase(refreshToken.pending, state => {
        state.loading = true
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.loading = false
        state.accessToken = action.payload.accessToken
        state.refreshToken = action.payload.refreshToken
        state.isAuthenticated = true
        
        // 更新localStorage
        localStorage.setItem('accessToken', action.payload.accessToken)
        localStorage.setItem('refreshToken', action.payload.refreshToken)
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || 'Token刷新失败'
        state.isAuthenticated = false
        state.accessToken = null
        state.refreshToken = null
        
        // 清除localStorage
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      })
      
      // Logout cases
      .addCase(logout.pending, state => {
        state.loading = true
      })
      .addCase(logout.fulfilled, state => {
        state.loading = false
        state.isAuthenticated = false
        state.accessToken = null
        state.refreshToken = null
        state.error = null
        
        // 清除localStorage
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      })
      .addCase(logout.rejected, (state, action) => {
        state.loading = false
        // 即使登出失败，也要清除本地状态
        state.isAuthenticated = false
        state.accessToken = null
        state.refreshToken = null
        
        // 清除localStorage
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      })
  },
})

export const { clearError, setTokens, clearTokens, initializeAuth } = authSlice.actions
export default authSlice.reducer

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error
export const selectAccessToken = (state: { auth: AuthState }) => state.auth.accessToken