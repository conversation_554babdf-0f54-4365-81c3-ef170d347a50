import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { UIState, Notification } from '@/types'

const initialState: UIState = {
  sidebarOpen: false,
  theme: 'light',
  language: 'zh-CN',
  notifications: [],
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: state => {
      state.sidebarOpen = !state.sidebarOpen
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload
      localStorage.setItem('theme', action.payload)
    },
    setLanguage: (state, action: PayloadAction<'zh-CN' | 'en-US'>) => {
      state.language = action.payload
      localStorage.setItem('language', action.payload)
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'createdAt'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
      }
      state.notifications.unshift(notification)
      
      // 限制通知数量，最多50条
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50)
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload)
    },
    clearNotifications: state => {
      state.notifications = []
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload)
      if (notification) {
        // 可以扩展通知的read属性
        // notification.read = true
      }
    },
    initializeUI: state => {
      // 从 localStorage 初始化 UI 状态
      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
      const savedLanguage = localStorage.getItem('language') as 'zh-CN' | 'en-US' | null
      
      if (savedTheme) {
        state.theme = savedTheme
      }
      
      if (savedLanguage) {
        state.language = savedLanguage
      }
    },
  },
})

export const {
  toggleSidebar,
  setSidebarOpen,
  setTheme,
  setLanguage,
  addNotification,
  removeNotification,
  clearNotifications,
  markNotificationAsRead,
  initializeUI,
} = uiSlice.actions

export default uiSlice.reducer

// Selectors
export const selectUI = (state: { ui: UIState }) => state.ui
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen
export const selectTheme = (state: { ui: UIState }) => state.ui.theme
export const selectLanguage = (state: { ui: UIState }) => state.ui.language
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications
export const selectUnreadNotificationsCount = (state: { ui: UIState }) => {
  // 假设我们后续添加了 read 属性
  // return state.ui.notifications.filter(n => !n.read).length
  return state.ui.notifications.length
}

// 通知工具函数
export const createSuccessNotification = (title: string, message: string, duration = 5000) => ({
  type: 'success' as const,
  title,
  message,
  duration,
})

export const createErrorNotification = (title: string, message: string, duration = 8000) => ({
  type: 'error' as const,
  title,
  message,
  duration,
})

export const createWarningNotification = (title: string, message: string, duration = 6000) => ({
  type: 'warning' as const,
  title,
  message,
  duration,
})

export const createInfoNotification = (title: string, message: string, duration = 5000) => ({
  type: 'info' as const,
  title,
  message,
  duration,
})