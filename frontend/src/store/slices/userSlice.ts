import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { UserState, User, UserProfile, UserStats, ApiError } from '@/types'
import { userApi } from '@/services/api'

const initialState: UserState = {
  currentUser: null,
  profile: null,
  stats: null,
  loading: false,
  error: null,
}

// 异步action creators
export const fetchCurrentUser = createAsyncThunk<
  User,
  void,
  { rejectValue: ApiError }
>('user/fetchCurrentUser', async (_, { rejectWithValue }) => {
  try {
    const response = await userApi.getCurrentUser()
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取用户信息失败', code: 500 })
  }
})

export const fetchUserProfile = createAsyncThunk<
  UserProfile,
  string,
  { rejectValue: ApiError }
>('user/fetchUserProfile', async (userId, { rejectWithValue }) => {
  try {
    const response = await userApi.getUserProfile(userId)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取用户资料失败', code: 500 })
  }
})

export const fetchUserStats = createAsyncThunk<
  UserStats,
  string,
  { rejectValue: ApiError }
>('user/fetchUserStats', async (userId, { rejectWithValue }) => {
  try {
    const response = await userApi.getUserStats(userId)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '获取用户统计失败', code: 500 })
  }
})

export const updateProfile = createAsyncThunk<
  User,
  Partial<User>,
  { rejectValue: ApiError }
>('user/updateProfile', async (updateData, { rejectWithValue }) => {
  try {
    const response = await userApi.updateProfile(updateData)
    return response.data
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '更新个人资料失败', code: 500 })
  }
})

export const uploadAvatar = createAsyncThunk<
  string,
  File,
  { rejectValue: ApiError }
>('user/uploadAvatar', async (file, { rejectWithValue }) => {
  try {
    const response = await userApi.uploadAvatar(file)
    return response.data.url
  } catch (error: any) {
    return rejectWithValue(error.response?.data || { message: '上传头像失败', code: 500 })
  }
})

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null
    },
    updateCurrentUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload }
      }
    },
    clearUserData: state => {
      state.currentUser = null
      state.profile = null
      state.stats = null
      state.error = null
    },
    updateUserStats: (state, action: PayloadAction<Partial<UserStats>>) => {
      if (state.stats) {
        state.stats = { ...state.stats, ...action.payload }
      }
    },
  },
  extraReducers: builder => {
    builder
      // Fetch current user cases
      .addCase(fetchCurrentUser.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.loading = false
        state.currentUser = action.payload
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取用户信息失败'
      })
      
      // Fetch user profile cases
      .addCase(fetchUserProfile.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false
        state.profile = action.payload
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取用户资料失败'
      })
      
      // Fetch user stats cases
      .addCase(fetchUserStats.pending, state => {
        state.loading = true
      })
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.loading = false
        state.stats = action.payload
      })
      .addCase(fetchUserStats.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '获取用户统计失败'
      })
      
      // Update profile cases
      .addCase(updateProfile.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.loading = false
        state.currentUser = action.payload
        // 更新profile中的用户信息
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload }
        }
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '更新个人资料失败'
      })
      
      // Upload avatar cases
      .addCase(uploadAvatar.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(uploadAvatar.fulfilled, (state, action) => {
        state.loading = false
        if (state.currentUser) {
          state.currentUser.avatar = action.payload
        }
        if (state.profile) {
          state.profile.avatar = action.payload
        }
      })
      .addCase(uploadAvatar.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.message || '上传头像失败'
      })
  },
})

export const { clearError, updateCurrentUser, clearUserData, updateUserStats } = userSlice.actions
export default userSlice.reducer

// Selectors
export const selectUser = (state: { user: UserState }) => state.user
export const selectCurrentUser = (state: { user: UserState }) => state.user.currentUser
export const selectUserProfile = (state: { user: UserState }) => state.user.profile
export const selectUserStats = (state: { user: UserState }) => state.user.stats
export const selectUserLoading = (state: { user: UserState }) => state.user.loading
export const selectUserError = (state: { user: UserState }) => state.user.error