import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, Heart, Coffee, PlayCircle, Briefcase, 
  Bed, Megaphone, Gift, Star, TrendingUp 
} from 'lucide-react'
import { cn } from '@/utils'

interface Animal {
  id: string
  name: string
  species?: string
  type: 'DIVINE' | 'PET' | 'OXHORSE'
  currentState?: {
    activity: string
    mood: string
    energy: number
    happiness: number
    health: number
    workEfficiency: number
    location: string
  }
  isUserAnimal?: boolean
}

interface InteractionType {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  effects: {
    energy?: number
    happiness?: number
    health?: number
    workEfficiency?: number
  }
  cooldown?: number
  requiredMood?: string[]
}

interface AnimalInteractionModalProps {
  animal: Animal | null
  isOpen: boolean
  onClose: () => void
  onInteract: (animalId: string, interactionType: string) => void
  isLoading?: boolean
  className?: string
}

const INTERACTION_TYPES: InteractionType[] = [
  {
    id: 'FEED',
    name: '喂食',
    description: '给小家伙吃美食，补充能量和健康',
    icon: Coffee,
    color: '#10B981',
    effects: { energy: 20, health: 15, happiness: 10 },
    cooldown: 300
  },
  {
    id: 'PET',
    name: '抚摸',
    description: '温柔的抚摸，提升快乐度和亲密度',
    icon: Heart,
    color: '#EC4899',
    effects: { happiness: 25, health: 5 },
    cooldown: 60
  },
  {
    id: 'PLAY',
    name: '玩耶',
    description: '一起玩游戏，可以减压和放松',
    icon: PlayCircle,
    color: '#F59E0B',
    effects: { happiness: 30, energy: -10, workEfficiency: 10 },
    cooldown: 180
  },
  {
    id: 'WORK_TOGETHER',
    name: '协作',
    description: '一起工作，提升工作效率和成就感',
    icon: Briefcase,
    color: '#3B82F6',
    effects: { workEfficiency: 25, happiness: 15, energy: -15 },
    cooldown: 600,
    requiredMood: ['content', 'happy', 'excited']
  },
  {
    id: 'REST',
    name: '休息',
    description: '让小家伙休息一下，恢复能量和精力',
    icon: Bed,
    color: '#8B5CF6',
    effects: { energy: 35, health: 10, workEfficiency: -5 },
    cooldown: 240
  },
  {
    id: 'ENCOURAGE',
    name: '鼓励',
    description: '给予正面反馈和鼓励，提升工作积极性',
    icon: Megaphone,
    color: '#EF4444',
    effects: { happiness: 20, workEfficiency: 15, health: 5 },
    cooldown: 120
  }
]

const MOOD_COLORS = {
  happy: '#10B981',
  content: '#3B82F6',
  tired: '#F59E0B',
  stressed: '#EF4444',
  excited: '#8B5CF6',
  sad: '#6B7280'
}

export default function AnimalInteractionModal({
  animal,
  isOpen,
  onClose,
  onInteract,
  isLoading = false,
  className
}: AnimalInteractionModalProps) {
  const [selectedInteraction, setSelectedInteraction] = useState<string | null>(null)
  const [interactionHistory, setInteractionHistory] = useState<string[]>([])
  
  // 重置状态当动物变化时
  useEffect(() => {
    if (animal) {
      setSelectedInteraction(null)
    }
  }, [animal?.id])
  
  if (!animal) return null
  
  const getAnimalTypeInfo = (type: string) => {
    switch (type) {
      case 'DIVINE':
        return { name: '职场神兽', emoji: '🦌', color: '#FFD700' }
      case 'PET':
        return { name: '团宠员工', emoji: '🐱', color: '#FF69B4' }
      case 'OXHORSE':
      default:
        return { name: '勤劳牛马', emoji: '🐂', color: '#8B4513' }
    }
  }
  
  const getMoodColor = (mood: string) => {
    return MOOD_COLORS[mood as keyof typeof MOOD_COLORS] || '#6B7280'
  }
  
  const handleInteraction = (interactionId: string) => {
    if (isLoading) return
    
    onInteract(animal.id, interactionId)
    setInteractionHistory(prev => [interactionId, ...prev.slice(0, 4)])
    setSelectedInteraction(null)
  }
  
  const typeInfo = getAnimalTypeInfo(animal.type)
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {/* 背景遵罩 */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* 模态窗内容 */}
          <motion.div
            className={cn(
              'relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden',
              className
            )}
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            {/* 头部 */}
            <div 
              className="relative p-6 text-white overflow-hidden"
              style={{ 
                background: `linear-gradient(135deg, ${typeInfo.color}88 0%, ${typeInfo.color}cc 100%)` 
              }}
            >
              {/* 背景装饰 */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 right-4 text-6xl">{typeInfo.emoji}</div>
              </div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-4xl">{typeInfo.emoji}</div>
                    <div>
                      <h2 className="text-2xl font-bold">{animal.name}</h2>
                      <div className="flex items-center space-x-2 text-sm opacity-90">
                        <span>{typeInfo.name}</span>
                        {animal.isUserAnimal && (
                          <span className="px-2 py-0.5 bg-white/20 rounded-full text-xs">
                            我的
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={onClose}
                    className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                
                {/* 状态信息 */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="opacity-75">当前活动：</span>
                    <span className="font-medium">{animal.currentState?.activity || '未知'}</span>
                  </div>
                  <div>
                    <span className="opacity-75">心情：</span>
                    <span 
                      className="font-medium px-2 py-0.5 rounded-full text-xs"
                      style={{ 
                        backgroundColor: `${getMoodColor(animal.currentState?.mood || 'content')}33`,
                        color: getMoodColor(animal.currentState?.mood || 'content')
                      }}
                    >
                      {animal.currentState?.mood || '平静'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              {/* 状态栏 */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                {[
                  { key: 'energy', label: '能量', color: '#10B981', icon: TrendingUp },
                  { key: 'happiness', label: '快乐', color: '#F59E0B', icon: Heart },
                  { key: 'health', label: '健康', color: '#EF4444', icon: Star },
                  { key: 'workEfficiency', label: '效率', color: '#3B82F6', icon: Briefcase }
                ].map(({ key, label, color, icon: Icon }) => {
                  const value = animal.currentState?.[key as keyof typeof animal.currentState] as number || 0
                  
                  return (
                    <div key={key} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Icon className="w-4 h-4" style={{ color }} />
                          <span className="text-sm font-medium text-gray-700">{label}</span>
                        </div>
                        <span className="text-sm font-bold" style={{ color }}>
                          {typeof value === 'number' ? `${Math.round(value)}%` : value}
                        </span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full rounded-full"
                          style={{ backgroundColor: color }}
                          initial={{ width: 0 }}
                          animate={{ width: `${typeof value === 'number' ? value : 0}%` }}
                          transition={{ duration: 0.8, ease: 'easeOut' }}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
              
              {/* 互动选项 */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                  <Gift className="w-5 h-5" />
                  <span>与 {animal.name} 互动</span>
                </h3>
                
                <div className="grid grid-cols-2 gap-3">
                  {INTERACTION_TYPES.map((interaction) => {
                    const IconComponent = interaction.icon
                    const isSelected = selectedInteraction === interaction.id
                    const canInteract = !interaction.requiredMood || 
                      interaction.requiredMood.includes(animal.currentState?.mood || 'content')
                    
                    return (
                      <motion.button
                        key={interaction.id}
                        onClick={() => {
                          if (canInteract) {
                            setSelectedInteraction(interaction.id)
                          }
                        }}
                        disabled={!canInteract || isLoading}
                        className={cn(
                          'relative p-4 rounded-xl border-2 transition-all text-left',
                          isSelected
                            ? 'border-primary-300 bg-primary-50 shadow-md'
                            : canInteract
                            ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                            : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed',
                          'group'
                        )}
                        whileHover={canInteract ? { y: -2 } : {}}
                        whileTap={canInteract ? { scale: 0.98 } : {}}
                      >
                        <div className="flex items-start space-x-3">
                          <div 
                            className="p-2 rounded-lg"
                            style={{ 
                              backgroundColor: canInteract ? `${interaction.color}15` : '#f3f4f6',
                              color: canInteract ? interaction.color : '#9ca3af'
                            }}
                          >
                            <IconComponent className="w-5 h-5" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h4 className={cn(
                              'font-semibold text-sm mb-1',
                              canInteract ? 'text-gray-900' : 'text-gray-400'
                            )}>
                              {interaction.name}
                            </h4>
                            <p className={cn(
                              'text-xs leading-relaxed',
                              canInteract ? 'text-gray-600' : 'text-gray-400'
                            )}>
                              {interaction.description}
                            </p>
                            
                            {/* 效果预览 */}
                            <div className="flex flex-wrap gap-1 mt-2">
                              {Object.entries(interaction.effects).map(([effect, value]) => {
                                const effectNames = {
                                  energy: '能量',
                                  happiness: '快乐',
                                  health: '健康',
                                  workEfficiency: '效率'
                                }
                                
                                return (
                                  <span
                                    key={effect}
                                    className={cn(
                                      'text-xs px-1.5 py-0.5 rounded-full',
                                      value > 0 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700',
                                      !canInteract && 'opacity-50'
                                    )}
                                  >
                                    {effectNames[effect as keyof typeof effectNames]} {value > 0 ? '+' : ''}{value}
                                  </span>
                                )
                              })}
                            </div>
                          </div>
                        </div>
                        
                        {!canInteract && interaction.requiredMood && (
                          <div className="mt-2 text-xs text-gray-400">
                            需要心情：{interaction.requiredMood.join('、')}
                          </div>
                        )}
                      </motion.button>
                    )
                  })}
                </div>
                
                {/* 确认按钮 */}
                <AnimatePresence>
                  {selectedInteraction && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="border-t pt-4"
                    >
                      <div className="flex items-center justify-between space-x-3">
                        <button
                          onClick={() => setSelectedInteraction(null)}
                          className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          取消
                        </button>
                        <button
                          onClick={() => handleInteraction(selectedInteraction)}
                          disabled={isLoading}
                          className={cn(
                            'flex-1 py-2 px-4 rounded-lg font-medium transition-colors',
                            'bg-primary-500 text-white hover:bg-primary-600 disabled:opacity-50'
                          )}
                        >
                          {isLoading ? '处理中...' : `与${animal.name}互动`}
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
                
                {/* 最近互动历史 */}
                {interactionHistory.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">最近互动</h4>
                    <div className="flex space-x-2">
                      {interactionHistory.map((interactionId, index) => {
                        const interaction = INTERACTION_TYPES.find(i => i.id === interactionId)
                        if (!interaction) return null
                        
                        const IconComponent = interaction.icon
                        
                        return (
                          <div
                            key={`${interactionId}-${index}`}
                            className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full text-xs"
                            title={interaction.name}
                          >
                            <IconComponent className="w-3 h-3" style={{ color: interaction.color }} />
                            <span>{interaction.name}</span>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
