import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Settings, Eye, EyeOff, Volume2, VolumeX, 
  Zap, RefreshCw, Users, BarChart3,
  Sun, Cloud, CloudRain, Zap as Lightning,
  Play, Pause, FastForward
} from 'lucide-react'
import { cn } from '@/utils'

interface ZooControlPanelProps {
  // 显示设置
  showNames: boolean
  showStats: boolean
  showTrails: boolean
  onToggleNames: (show: boolean) => void
  onToggleStats: (show: boolean) => void
  onToggleTrails: (show: boolean) => void
  
  // 音效设置
  soundEnabled: boolean
  onToggleSound: (enabled: boolean) => void
  
  // AI控制
  aiEnabled: boolean
  aiSpeed: number
  onToggleAI: (enabled: boolean) => void
  onAISpeedChange: (speed: number) => void
  
  // 环境控制
  environment?: {
    timeOfDay: number
    weather: 'sunny' | 'cloudy' | 'rainy' | 'stormy'
    crowdLevel: 'low' | 'medium' | 'high'
  }
  onWeatherChange?: (weather: string) => void
  onCrowdLevelChange?: (level: string) => void
  
  // 事件控制
  onTriggerEvent?: (eventType: string) => void
  
  // 统计信息
  statistics?: {
    totalAnimals: number
    activeAnimals: number
    averageHappiness: number
    averageEnergy: number
  }
  
  className?: string
}

const WEATHER_OPTIONS = [
  { value: 'sunny', icon: Sun, label: '晴天', color: '#F59E0B' },
  { value: 'cloudy', icon: Cloud, label: '多云', color: '#6B7280' },
  { value: 'rainy', icon: CloudRain, label: '下雨', color: '#3B82F6' },
  { value: 'stormy', icon: Lightning, label: '暴风雨', color: '#7C3AED' }
]

const CROWD_LEVELS = [
  { value: 'low', label: '宁静', emoji: '😌', color: '#10B981' },
  { value: 'medium', label: '适中', emoji: '👥', color: '#F59E0B' },
  { value: 'high', label: '热闹', emoji: '🔥', color: '#EF4444' }
]

const EVENTS = [
  { id: 'happy_hour', name: '快乐时光', emoji: '🎉', color: '#F59E0B' },
  { id: 'work_meeting', name: '团队会议', emoji: '💼', color: '#3B82F6' },
  { id: 'lunch_time', name: '午餐时间', emoji: '🍽️', color: '#10B981' },
  { id: 'break_time', name: '休息时间', emoji: '😴', color: '#8B5CF6' }
]

export default function ZooControlPanel({
  showNames,
  showStats,
  showTrails,
  onToggleNames,
  onToggleStats,
  onToggleTrails,
  soundEnabled,
  onToggleSound,
  aiEnabled,
  aiSpeed,
  onToggleAI,
  onAISpeedChange,
  environment,
  onWeatherChange,
  onCrowdLevelChange,
  onTriggerEvent,
  statistics,
  className
}: ZooControlPanelProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [activeTab, setActiveTab] = useState<'display' | 'ai' | 'environment' | 'events'>('display')
  
  const getWeatherIcon = (weather: string) => {
    const option = WEATHER_OPTIONS.find(w => w.value === weather)
    return option ? option.icon : Sun
  }
  
  const getCrowdLevel = (level: string) => {
    return CROWD_LEVELS.find(c => c.value === level) || CROWD_LEVELS[0]
  }
  
  return (
    <motion.div
      className={cn(
        'bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20',
        'transition-all duration-300',
        isExpanded ? 'w-80' : 'w-16',
        className
      )}
      initial={false}
      animate={{ width: isExpanded ? 320 : 64 }}
    >
      {/* 折叠/展开头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <AnimatePresence>
          {isExpanded && (
            <motion.h3
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              className="text-lg font-semibold text-gray-900"
            >
              🎡 智能控制台
            </motion.h3>
          )}
        </AnimatePresence>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          title={isExpanded ? '收起控制台' : '展开控制台'}
        >
          <motion.div
            animate={{ rotate: isExpanded ? 0 : 180 }}
            transition={{ duration: 0.2 }}
          >
            <Settings className="w-5 h-5 text-gray-600" />
          </motion.div>
        </button>
      </div>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            {/* 标签导航 */}
            <div className="flex border-b border-gray-100">
              {[
                { id: 'display', label: '显示', icon: Eye },
                { id: 'ai', label: 'AI', icon: Zap },
                { id: 'environment', label: '环境', icon: Sun },
                { id: 'events', label: '事件', icon: Play }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={cn(
                    'flex-1 flex items-center justify-center py-2 px-3 text-sm font-medium transition-colors',
                    activeTab === tab.id
                      ? 'text-primary-600 border-b-2 border-primary-500 bg-primary-50'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  )}
                >
                  <tab.icon className="w-4 h-4" />
                </button>
              ))}
            </div>
            
            <div className="p-4">
              <AnimatePresence mode="wait">
                {/* 显示设置 */}
                {activeTab === 'display' && (
                  <motion.div
                    key="display"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="space-y-4"
                  >
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">显示名称</span>
                        <button
                          onClick={() => onToggleNames(!showNames)}
                          className={cn(
                            'relative w-11 h-6 rounded-full transition-colors focus:outline-none',
                            showNames ? 'bg-primary-500' : 'bg-gray-300'
                          )}
                        >
                          <motion.div
                            className="w-4 h-4 bg-white rounded-full shadow-md"
                            animate={{ x: showNames ? 26 : 2, y: 4 }}
                            transition={{ duration: 0.2 }}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">显示统计</span>
                        <button
                          onClick={() => onToggleStats(!showStats)}
                          className={cn(
                            'relative w-11 h-6 rounded-full transition-colors',
                            showStats ? 'bg-primary-500' : 'bg-gray-300'
                          )}
                        >
                          <motion.div
                            className="w-4 h-4 bg-white rounded-full shadow-md"
                            animate={{ x: showStats ? 26 : 2, y: 4 }}
                            transition={{ duration: 0.2 }}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">显示轨迹</span>
                        <button
                          onClick={() => onToggleTrails(!showTrails)}
                          className={cn(
                            'relative w-11 h-6 rounded-full transition-colors',
                            showTrails ? 'bg-primary-500' : 'bg-gray-300'
                          )}
                        >
                          <motion.div
                            className="w-4 h-4 bg-white rounded-full shadow-md"
                            animate={{ x: showTrails ? 26 : 2, y: 4 }}
                            transition={{ duration: 0.2 }}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">声音效果</span>
                        <button
                          onClick={() => onToggleSound(!soundEnabled)}
                          className={cn(
                            'p-2 rounded-lg transition-colors',
                            soundEnabled 
                              ? 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                              : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                          )}
                        >
                          {soundEnabled ? (
                            <Volume2 className="w-4 h-4" />
                          ) : (
                            <VolumeX className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    {/* 统计信息 */}
                    {statistics && (
                      <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                        <h4 className="text-sm font-semibold text-gray-800">实时统计</h4>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">动物数</span>
                            <span className="font-medium">{statistics.totalAnimals}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">活跃中</span>
                            <span className="font-medium">{statistics.activeAnimals}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">平均快乐</span>
                            <span className="font-medium">{statistics.averageHappiness}%</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">平均能量</span>
                            <span className="font-medium">{statistics.averageEnergy}%</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
                
                {/* AI控制 */}
                {activeTab === 'ai' && (
                  <motion.div
                    key="ai"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="space-y-4"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">AI行为系统</span>
                      <button
                        onClick={() => onToggleAI(!aiEnabled)}
                        className={cn(
                          'flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors',
                          aiEnabled
                            ? 'bg-green-100 text-green-700 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                        )}
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full',
                          aiEnabled ? 'bg-green-500' : 'bg-gray-400'
                        )} />
                        <span>{aiEnabled ? '开启' : '关闭'}</span>
                      </button>
                    </div>
                    
                    {aiEnabled && (
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">AI速度</span>
                            <span className="text-sm font-medium text-primary-600">{aiSpeed}x</span>
                          </div>
                          <input
                            type="range"
                            min="0.5"
                            max="3"
                            step="0.5"
                            value={aiSpeed}
                            onChange={(e) => onAISpeedChange(parseFloat(e.target.value))}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                          />
                        </div>
                        
                        <div className="bg-purple-50 rounded-lg p-3">
                          <h4 className="text-sm font-semibold text-purple-800 mb-2">🤖 AI状态</h4>
                          <div className="text-xs text-purple-700 space-y-1">
                            <p>• 自动行为系统已启用</p>
                            <p>• 智能互动正在运行</p>
                            <p>• 环境适应模式开启</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
                
                {/* 环境控制 */}
                {activeTab === 'environment' && (
                  <motion.div
                    key="environment"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="space-y-4"
                  >
                    {/* 天气控制 */}
                    <div>
                      <h4 className="text-sm font-semibold text-gray-800 mb-3">☁️ 天气设置</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {WEATHER_OPTIONS.map((weather) => {
                          const IconComponent = weather.icon
                          const isActive = environment?.weather === weather.value
                          
                          return (
                            <button
                              key={weather.value}
                              onClick={() => onWeatherChange?.(weather.value)}
                              className={cn(
                                'flex flex-col items-center p-3 rounded-lg border-2 transition-all',
                                isActive
                                  ? 'border-primary-300 bg-primary-50 shadow-sm'
                                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                              )}
                            >
                              <IconComponent 
                                className="w-6 h-6 mb-1" 
                                style={{ color: isActive ? weather.color : '#6B7280' }}
                              />
                              <span className={cn(
                                'text-xs font-medium',
                                isActive ? 'text-primary-700' : 'text-gray-600'
                              )}>
                                {weather.label}
                              </span>
                            </button>
                          )
                        })}
                      </div>
                    </div>
                    
                    {/* 人流控制 */}
                    <div>
                      <h4 className="text-sm font-semibold text-gray-800 mb-3">👥 人流水平</h4>
                      <div className="space-y-2">
                        {CROWD_LEVELS.map((level) => {
                          const isActive = environment?.crowdLevel === level.value
                          
                          return (
                            <button
                              key={level.value}
                              onClick={() => onCrowdLevelChange?.(level.value)}
                              className={cn(
                                'w-full flex items-center justify-between p-2 rounded-lg transition-colors',
                                isActive
                                  ? 'bg-primary-50 text-primary-700 border border-primary-200'
                                  : 'hover:bg-gray-50 text-gray-600'
                              )}
                            >
                              <div className="flex items-center space-x-2">
                                <span className="text-sm">{level.emoji}</span>
                                <span className="text-sm font-medium">{level.label}</span>
                              </div>
                              {isActive && (
                                <div className="w-2 h-2 rounded-full bg-primary-500" />
                              )}
                            </button>
                          )
                        })}
                      </div>
                    </div>
                    
                    {/* 时间信息 */}
                    {environment && (
                      <div className="bg-blue-50 rounded-lg p-3">
                        <h4 className="text-sm font-semibold text-blue-800 mb-2">🕰️ 环境状态</h4>
                        <div className="text-xs text-blue-700 space-y-1">
                          <p>时间：{environment.timeOfDay}:00</p>
                          <p>天气：{WEATHER_OPTIONS.find(w => w.value === environment.weather)?.label}</p>
                          <p>人流：{getCrowdLevel(environment.crowdLevel).label}</p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
                
                {/* 事件控制 */}
                {activeTab === 'events' && (
                  <motion.div
                    key="events"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="space-y-4"
                  >
                    <h4 className="text-sm font-semibold text-gray-800">🎉 事件触发</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {EVENTS.map((event) => (
                        <motion.button
                          key={event.id}
                          onClick={() => onTriggerEvent?.(event.id)}
                          className="flex flex-col items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <span className="text-lg mb-1">{event.emoji}</span>
                          <span className="text-xs font-medium text-gray-700 text-center">
                            {event.name}
                          </span>
                        </motion.button>
                      ))}
                    </div>
                    
                    <div className="bg-amber-50 rounded-lg p-3">
                      <h4 className="text-sm font-semibold text-amber-800 mb-2">📝 事件说明</h4>
                      <p className="text-xs text-amber-700">
                        触发事件将影响所有动物的行为和状态，每个事件持续1分钟。
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
