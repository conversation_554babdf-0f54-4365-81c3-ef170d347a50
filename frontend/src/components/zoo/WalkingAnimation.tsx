import React, { useRef, useEffect, useState } from 'react'

export interface Animal {
  id: number
  name: string
  type: 'DIVINE' | 'PET' | 'OXHORSE'
  imageUrl: string
  x: number
  y: number
  vx: number
  vy: number
  activity: string
  energy: number
  happiness: number
  creativity: number
  zone: string
}

interface WalkingAnimationProps {
  animals: Animal[]
  images: Record<number, HTMLImageElement>
  onAnimalClick?: (animal: Animal) => void
  canvasWidth?: number
  canvasHeight?: number
}

export const WalkingAnimation: React.FC<WalkingAnimationProps> = ({
  animals,
  images,
  onAnimalClick,
  canvasWidth = 3200,
  canvasHeight = 2400
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [camera, setCamera] = useState({ x: 0, y: 0, zoom: 0.3 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const animationFrameRef = useRef<number>()
  
  // 动物移动逻辑
  const updateAnimalPositions = (animalsToUpdate: Animal[]) => {
    return animalsToUpdate.map(animal => {
      // 边界检测
      let newX = animal.x + animal.vx
      let newY = animal.y + animal.vy
      let newVx = animal.vx
      let newVy = animal.vy
      
      // 碰到边界时反弹
      if (newX <= 0 || newX >= canvasWidth) {
        newVx = -newVx
        newX = Math.max(0, Math.min(canvasWidth, newX))
      }
      if (newY <= 0 || newY >= canvasHeight) {
        newVy = -newVy
        newY = Math.max(0, Math.min(canvasHeight, newY))
      }
      
      // 根据动物类型调整行为
      const behavior = getAnimalBehavior(animal)
      
      // 随机改变方向（模拟自然行为）
      if (Math.random() < behavior.changeDirectionProbability) {
        newVx = (Math.random() - 0.5) * behavior.maxSpeed
        newVy = (Math.random() - 0.5) * behavior.maxSpeed
      }
      
      // 限制速度
      const speed = Math.sqrt(newVx * newVx + newVy * newVy)
      if (speed > behavior.maxSpeed) {
        newVx = (newVx / speed) * behavior.maxSpeed
        newVy = (newVy / speed) * behavior.maxSpeed
      }
      
      // 更新活动状态
      let newActivity = animal.activity
      if (speed > 0.5) {
        newActivity = behavior.walkingActivity
      } else if (speed > 0.1) {
        newActivity = behavior.slowActivity
      } else {
        newActivity = behavior.idleActivity
      }
      
      return {
        ...animal,
        x: newX,
        y: newY,
        vx: newVx,
        vy: newVy,
        activity: newActivity
      }
    })
  }
  
  // 根据动物类型获取行为特征
  const getAnimalBehavior = (animal: Animal) => {
    switch (animal.type) {
      case 'DIVINE':
        return {
          maxSpeed: 1.0,
          changeDirectionProbability: 0.02,
          walkingActivity: '巡视领地',
          slowActivity: '观察环境',
          idleActivity: '沉思中'
        }
      case 'PET':
        return {
          maxSpeed: 1.5,
          changeDirectionProbability: 0.05,
          walkingActivity: '欢快散步',
          slowActivity: '探索周围',
          idleActivity: '晒太阳'
        }
      case 'OXHORSE':
      default:
        return {
          maxSpeed: 0.8,
          changeDirectionProbability: 0.03,
          walkingActivity: '寻找工作',
          slowActivity: '思考人生',
          idleActivity: '休息片刻'
        }
    }
  }
  
  // 绘制动物
  const drawAnimal = (ctx: CanvasRenderingContext2D, animal: Animal, image?: HTMLImageElement) => {
    ctx.save()
    
    // 动物大小
    const size = 64
    
    if (image) {
      // 计算行走动画帧（简单的左右摆动效果）
      const time = Date.now() * 0.002
      const walkingOffset = Math.sin(time + animal.id) * 2
      
      // 根据移动方向翻转图像
      const isMovingLeft = animal.vx < 0
      if (isMovingLeft) {
        ctx.scale(-1, 1)
        ctx.drawImage(image, -animal.x - size/2 + walkingOffset, animal.y - size/2, size, size)
      } else {
        ctx.drawImage(image, animal.x - size/2 + walkingOffset, animal.y - size/2, size, size)
      }
    } else {
      // 降级：绘制彩色圆形
      const colors = {
        'DIVINE': '#ffd700',
        'PET': '#ff69b4',
        'OXHORSE': '#8b4513'
      }
      
      ctx.fillStyle = colors[animal.type]
      ctx.beginPath()
      ctx.arc(animal.x, animal.y, size/2, 0, Math.PI * 2)
      ctx.fill()
    }
    
    // 绘制名字标签
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    ctx.fillRect(animal.x - 40, animal.y + 35, 80, 20)
    ctx.fillStyle = 'white'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(animal.name, animal.x, animal.y + 48)
    
    // 绘制状态信息
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(animal.x - 35, animal.y + 55, 70, 15)
    ctx.fillStyle = '#87ceeb'
    ctx.font = '10px Arial'
    ctx.fillText(animal.activity, animal.x, animal.y + 66)
    
    // 绘制能量条
    const energyWidth = 30
    const energyHeight = 4
    ctx.fillStyle = 'rgba(255, 0, 0, 0.5)'
    ctx.fillRect(animal.x - energyWidth/2, animal.y - 40, energyWidth, energyHeight)
    ctx.fillStyle = '#00ff00'
    ctx.fillRect(animal.x - energyWidth/2, animal.y - 40, energyWidth * (animal.energy/100), energyHeight)
    
    ctx.restore()
  }
  
  // 绘制动物园背景
  const drawBackground = (ctx: CanvasRenderingContext2D) => {
    // 清空画布
    ctx.fillStyle = '#87ceeb' // 天空蓝
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)
    
    // 绘制各个区域
    const zones = [
      { name: '工作区', x: 0, y: 0, width: 1000, height: 800, color: '#deb887' },
      { name: '管理区', x: 1000, y: 0, width: 1200, height: 600, color: '#9370db' },
      { name: '休闲区', x: 2200, y: 0, width: 1000, height: 1200, color: '#90ee90' },
      { name: '食堂区', x: 0, y: 800, width: 800, height: 600, color: '#ffefd5' },
      { name: '会议区', x: 800, y: 800, width: 1400, height: 800, color: '#f0f8ff' },
      { name: '停车场', x: 0, y: 1400, width: 2200, height: 1000, color: '#c0c0c0' },
      { name: '绿化区', x: 2200, y: 1200, width: 1000, height: 1200, color: '#98fb98' }
    ]
    
    zones.forEach(zone => {
      ctx.fillStyle = zone.color
      ctx.fillRect(zone.x, zone.y, zone.width, zone.height)
      
      // 绘制区域边界
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)'
      ctx.lineWidth = 2
      ctx.strokeRect(zone.x, zone.y, zone.width, zone.height)
      
      // 绘制区域标签
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
      ctx.font = 'bold 24px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(zone.name, zone.x + zone.width/2, zone.y + zone.height/2)
    })
  }
  
  // 主渲染循环
  const animate = () => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // 应用相机变换
    ctx.save()
    ctx.scale(camera.zoom, camera.zoom)
    ctx.translate(-camera.x, -camera.y)
    
    // 绘制背景
    drawBackground(ctx)
    
    // 绘制所有动物
    animals.forEach(animal => {
      const image = images[animal.id]
      drawAnimal(ctx, animal, image)
    })
    
    ctx.restore()
    
    animationFrameRef.current = requestAnimationFrame(animate)
  }
  
  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const rect = canvas.getBoundingClientRect()
    const x = (e.clientX - rect.left) / camera.zoom + camera.x
    const y = (e.clientY - rect.top) / camera.zoom + camera.y
    
    // 检查是否点击了动物
    const clickedAnimal = animals.find(animal => {
      const dx = x - animal.x
      const dy = y - animal.y
      return dx * dx + dy * dy < 32 * 32 // 64px直径的点击区域
    })
    
    if (clickedAnimal && onAnimalClick) {
      onAnimalClick(clickedAnimal)
    } else {
      // 开始拖拽
      setIsDragging(true)
      setDragStart({ x: e.clientX, y: e.clientY })
    }
  }
  
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging) return
    
    const deltaX = (e.clientX - dragStart.x) / camera.zoom
    const deltaY = (e.clientY - dragStart.y) / camera.zoom
    
    setCamera(prev => ({
      ...prev,
      x: Math.max(0, Math.min(canvasWidth - window.innerWidth / prev.zoom, prev.x - deltaX)),
      y: Math.max(0, Math.min(canvasHeight - window.innerHeight / prev.zoom, prev.y - deltaY))
    }))
    
    setDragStart({ x: e.clientX, y: e.clientY })
  }
  
  const handleMouseUp = () => {
    setIsDragging(false)
  }
  
  // 缩放控制
  const zoomIn = () => {
    setCamera(prev => ({ ...prev, zoom: Math.min(2.0, prev.zoom * 1.2) }))
  }
  
  const zoomOut = () => {
    setCamera(prev => ({ ...prev, zoom: Math.max(0.1, prev.zoom / 1.2) }))
  }
  
  const resetView = () => {
    setCamera({ x: 0, y: 0, zoom: 0.3 })
  }
  
  // 初始化动画
  useEffect(() => {
    animate()
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [animals, images, camera])
  
  // 定期更新动物位置
  useEffect(() => {
    const interval = setInterval(() => {
      // 这里需要父组件提供更新动物位置的回调
      // 暂时注释掉，避免无限更新
      // if (animals.length > 0) {
      //   const updatedAnimals = updateAnimalPositions(animals)
      //   onAnimalsUpdate?.(updatedAnimals)
      // }
    }, 100)
    
    return () => clearInterval(interval)
  }, [animals])
  
  return (
    <div className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        width={window.innerWidth}
        height={window.innerHeight - 200}
        className="block cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      />
      
      {/* 控制面板 */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 space-y-2">
        <div className="flex items-center space-x-2">
          <button
            onClick={zoomOut}
            className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600"
          >
            -
          </button>
          <span className="text-sm font-medium w-12 text-center">
            {Math.round(camera.zoom * 100)}%
          </span>
          <button
            onClick={zoomIn}
            className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600"
          >
            +
          </button>
        </div>
        <button
          onClick={resetView}
          className="w-full px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
        >
          重置视角
        </button>
      </div>
      
      {/* 动物统计 */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-4">
        <div className="text-sm space-y-1">
          <div>🦄 神兽: {animals.filter(a => a.type === 'DIVINE').length}</div>
          <div>🐕 宠物: {animals.filter(a => a.type === 'PET').length}</div>
          <div>🐴 牛马: {animals.filter(a => a.type === 'OXHORSE').length}</div>
          <div>👥 总数: {animals.length}</div>
        </div>
      </div>
    </div>
  )
}

export default WalkingAnimation