import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders, mockZooState } from '../../test/utils/test-utils'

// Mock Three.js 相关模块
vi.mock('@react-three/fiber', () => ({
  Canvas: ({ children, ...props }: any) => (
    <div data-testid="three-canvas" data-props={JSON.stringify(props)}>
      {children}
    </div>
  ),
  useFrame: vi.fn((callback) => {
    // 模拟动画帧回调
    callback({ clock: { getElapsedTime: () => 0 } })
  }),
  useThree: () => ({
    camera: { 
      position: { set: vi.fn(), x: 0, y: 10, z: 20 },
      lookAt: vi.fn(),
    },
    gl: { 
      domElement: { style: {} },
      setSize: vi.fn(),
    },
    scene: { background: null },
    raycaster: { setFromCamera: vi.fn() },
    mouse: { x: 0, y: 0 },
  }),
  useLoader: vi.fn(() => ({})),
}))

vi.mock('@react-three/drei', () => ({
  OrbitControls: (props: any) => (
    <div data-testid="orbit-controls" data-props={JSON.stringify(props)} />
  ),
  Environment: (props: any) => (
    <div data-testid="environment" data-props={JSON.stringify(props)} />
  ),
  Text: ({ children, ...props }: any) => (
    <div data-testid="three-text" data-props={JSON.stringify(props)}>
      {children}
    </div>
  ),
  Sphere: (props: any) => (
    <div data-testid="sphere" data-props={JSON.stringify(props)} />
  ),
  Box: (props: any) => (
    <div data-testid="box" data-props={JSON.stringify(props)} />
  ),
  Plane: (props: any) => (
    <div data-testid="plane" data-props={JSON.stringify(props)} />
  ),
}))

vi.mock('@react-three/cannon', () => ({
  Physics: ({ children }: any) => (
    <div data-testid="physics-world">{children}</div>
  ),
  useBox: () => [{ current: null }, { position: [0, 0, 0] }],
  usePlane: () => [{ current: null }, { position: [0, 0, 0] }],
  useSphere: () => [{ current: null }, { position: [0, 0, 0] }],
}))

// 创建 ZooScene 组件的 mock 实现
const ZooScene = ({ onAnimalClick }: { onAnimalClick: (animal: any) => void }) => {
  const animals = mockZooState.animals

  return (
    <div data-testid="zoo-scene">
      <div data-testid="three-canvas">
        <div data-testid="physics-world">
          <div data-testid="environment" />
          <div data-testid="orbit-controls" />
          
          {/* 地面 */}
          <div data-testid="ground-plane" />
          
          {/* 动物渲染 */}
          {animals.map((animal) => (
            <div
              key={animal.id}
              data-testid={`animal-${animal.id}`}
              data-animal-type={animal.type}
              data-animal-species={animal.species}
              onClick={() => onAnimalClick(animal)}
              style={{
                transform: `translate3d(${animal.position.x}px, ${animal.position.y}px, ${animal.position.z}px)`,
              }}
            >
              <div data-testid={`animal-model-${animal.type}`} />
              <div data-testid="animal-label">{animal.species}</div>
            </div>
          ))}
          
          {/* 天空盒 */}
          <div data-testid="skybox" />
        </div>
      </div>
    </div>
  )
}

describe('ZooScene', () => {
  const mockOnAnimalClick = vi.fn()

  beforeEach(() => {
    mockOnAnimalClick.mockClear()
  })

  it('应该渲染3D场景容器', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    expect(screen.getByTestId('zoo-scene')).toBeInTheDocument()
    expect(screen.getByTestId('three-canvas')).toBeInTheDocument()
    expect(screen.getByTestId('physics-world')).toBeInTheDocument()
  })

  it('应该渲染环境和控制器', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    expect(screen.getByTestId('environment')).toBeInTheDocument()
    expect(screen.getByTestId('orbit-controls')).toBeInTheDocument()
    expect(screen.getByTestId('ground-plane')).toBeInTheDocument()
    expect(screen.getByTestId('skybox')).toBeInTheDocument()
  })

  it('应该渲染所有动物', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    const animals = mockZooState.animals
    animals.forEach((animal) => {
      const animalElement = screen.getByTestId(`animal-${animal.id}`)
      expect(animalElement).toBeInTheDocument()
      expect(animalElement).toHaveAttribute('data-animal-type', animal.type)
      expect(animalElement).toHaveAttribute('data-animal-species', animal.species)
    })
  })

  it('应该正确设置动物位置', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    const firstAnimal = mockZooState.animals[0]
    const animalElement = screen.getByTestId(`animal-${firstAnimal.id}`)
    
    expect(animalElement.style.transform).toBe(
      `translate3d(${firstAnimal.position.x}px, ${firstAnimal.position.y}px, ${firstAnimal.position.z}px)`
    )
  })

  it('应该处理动物点击事件', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    const firstAnimal = mockZooState.animals[0]
    const animalElement = screen.getByTestId(`animal-${firstAnimal.id}`)
    
    fireEvent.click(animalElement)
    
    expect(mockOnAnimalClick).toHaveBeenCalledWith(firstAnimal)
  })

  it('应该渲染不同类型的动物模型', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    // 检查不同类型的动物模型
    expect(screen.getByTestId('animal-model-cattle')).toBeInTheDocument()
    expect(screen.getByTestId('animal-model-cat')).toBeInTheDocument()
  })

  it('应该显示动物标签', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    const animalLabels = screen.getAllByTestId('animal-label')
    expect(animalLabels).toHaveLength(mockZooState.animals.length)
    
    expect(animalLabels[0]).toHaveTextContent('cow')
    expect(animalLabels[1]).toHaveTextContent('persian')
  })

  it('应该处理空动物列表', () => {
    const emptyZooState = { ...mockZooState, animals: [] }
    
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: emptyZooState } }
    )

    expect(screen.getByTestId('zoo-scene')).toBeInTheDocument()
    expect(screen.queryByTestId(/^animal-/)).not.toBeInTheDocument()
  })

  it('应该支持多种动物动画状态', () => {
    const animatedZooState = {
      ...mockZooState,
      animals: [
        { ...mockZooState.animals[0], animation: 'walking' },
        { ...mockZooState.animals[1], animation: 'eating' },
      ],
    }

    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: animatedZooState } }
    )

    // 在实际实现中，这里会检查动画状态
    // 目前只检查动物是否正确渲染
    expect(screen.getByTestId('animal-1')).toBeInTheDocument()
    expect(screen.getByTestId('animal-2')).toBeInTheDocument()
  })

  it('应该处理动物心情状态', () => {
    const moodZooState = {
      ...mockZooState,
      animals: [
        { ...mockZooState.animals[0], mood: 'happy' },
        { ...mockZooState.animals[1], mood: 'sad' },
      ],
    }

    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: moodZooState } }
    )

    // 检查动物是否渲染（在实际实现中会根据心情显示不同效果）
    expect(screen.getByTestId('animal-1')).toBeInTheDocument()
    expect(screen.getByTestId('animal-2')).toBeInTheDocument()
  })

  it('应该响应窗口大小变化', () => {
    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: mockZooState } }
    )

    // 模拟窗口大小变化
    global.dispatchEvent(new Event('resize'))

    // 检查场景是否仍然正确渲染
    expect(screen.getByTestId('zoo-scene')).toBeInTheDocument()
  })

  it('应该处理性能优化', async () => {
    // 模拟大量动物的场景
    const largeZooState = {
      ...mockZooState,
      animals: Array.from({ length: 100 }, (_, i) => ({
        id: `animal-${i}`,
        userId: `user-${i}`,
        type: 'cattle',
        species: 'cow',
        position: { x: i * 2, y: 0, z: i * 2 },
        rotation: { x: 0, y: 0, z: 0 },
        animation: 'idle',
        mood: 'normal',
        likeCount: 0,
        feedCount: 0,
      })),
    }

    renderWithProviders(
      <ZooScene onAnimalClick={mockOnAnimalClick} />,
      { preloadedState: { zoo: largeZooState } }
    )

    // 检查是否所有动物都被渲染（在实际实现中可能会有LOD优化）
    await waitFor(() => {
      expect(screen.getAllByTestId(/^animal-/)).toHaveLength(100)
    })
  })
})