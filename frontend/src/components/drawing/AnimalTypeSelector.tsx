import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Crown, Heart, Zap, Info } from 'lucide-react'
import { cn } from '@/utils'

interface AnimalType {
  id: 'DIVINE' | 'PET' | 'OXHORSE'
  name: string
  displayName: string
  description: string
  icon: string
  color: string
  traits: string[]
  workStyle: string
  advantages: string[]
}

interface AnimalTypeSelectorProps {
  selectedType?: string
  onSelect: (type: string) => void
  className?: string
}

const ANIMAL_TYPES: AnimalType[] = [
  {
    id: 'DIVINE',
    name: 'divine',
    displayName: '职场神兽',
    description: '传说中的职场精英，拥有超凡能力和领导魅力',
    icon: '🦌',
    color: '#FFD700',
    traits: ['天赋异禀', '领导力强', '创新思维', '战略眼光'],
    workStyle: '引领团队，创造价值',
    advantages: ['决策能力出众', '影响力巨大', '创新能力强', '抗压能力极强']
  },
  {
    id: 'PET',
    name: 'pet',
    displayName: '团宠员工',
    description: '人见人爱的职场宠儿，善于沟通协调',
    icon: '🐱',
    color: '#FF69B4',
    traits: ['人缘极佳', '沟通能力强', '善解人意', '团队协作'],
    workStyle: '营造和谐，促进合作',
    advantages: ['社交能力强', '情商很高', '团队凝聚力', '工作氛围好']
  },
  {
    id: 'OXHORSE',
    name: 'oxhorse',
    displayName: '勤劳牛马',
    description: '任劳任怨的职场支柱，踏实可靠的执行者',
    icon: '🐂',
    color: '#8B4513',
    traits: ['勤奋踏实', '执行力强', '责任心重', '默默奉献'],
    workStyle: '埋头苦干，稳步前进',
    advantages: ['执行力强', '可靠性高', '学习能力强', '适应性好']
  }
]

export default function AnimalTypeSelector({ selectedType, onSelect, className }: AnimalTypeSelectorProps) {
  const [hoveredType, setHoveredType] = useState<string | null>(null)
  const [showDetails, setShowDetails] = useState<string | null>(null)

  return (
    <div className={cn('space-y-6', className)}>
      {/* 标题区域 */}
      <div className="text-center space-y-3">
        <motion.h2 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-gray-900"
        >
          选择你的职场人设
        </motion.h2>
        <motion.p 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-lg text-gray-600 max-w-2xl mx-auto"
        >
          根据你的性格和工作风格，选择最适合的打工人类型。AI将基于你的选择生成专属形象。
        </motion.p>
      </div>

      {/* 动物类型选择卡片 */}
      <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {ANIMAL_TYPES.map((type, index) => {
          const isSelected = selectedType === type.id
          const isHovered = hoveredType === type.id
          
          return (
            <motion.div
              key={type.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              <motion.div
                className={cn(
                  'relative bg-white rounded-2xl p-6 border-2 cursor-pointer transition-all duration-300 shadow-soft hover:shadow-medium',
                  isSelected ? 'border-primary-500 shadow-lg ring-4 ring-primary-100' : 'border-gray-200 hover:border-gray-300',
                  'group overflow-hidden'
                )}
                onHoverStart={() => setHoveredType(type.id)}
                onHoverEnd={() => setHoveredType(null)}
                onClick={() => onSelect(type.id)}
                whileHover={{ y: -5 }}
                whileTap={{ scale: 0.98 }}
                style={{
                  background: isSelected ? `linear-gradient(135deg, ${type.color}08 0%, ${type.color}20 100%)` : 'white'
                }}
              >
                {/* 选中指示器 */}
                <AnimatePresence>
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                      className="absolute top-4 right-4 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center"
                    >
                      <Crown className="w-4 h-4 text-white" />
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* 动物图标 */}
                <motion.div 
                  className="text-center mb-4"
                  animate={isHovered ? { scale: 1.1, rotate: [0, -5, 5, 0] } : { scale: 1, rotate: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div 
                    className="inline-flex items-center justify-center w-20 h-20 rounded-full mb-3 text-4xl"
                    style={{ backgroundColor: `${type.color}20` }}
                  >
                    {type.icon}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{type.displayName}</h3>
                </motion.div>

                {/* 基本描述 */}
                <p className="text-sm text-gray-600 text-center mb-4 leading-relaxed">
                  {type.description}
                </p>

                {/* 特征标签 */}
                <div className="flex flex-wrap gap-2 justify-center mb-4">
                  {type.traits.slice(0, 2).map((trait) => (
                    <span
                      key={trait}
                      className="px-3 py-1 text-xs rounded-full"
                      style={{ 
                        backgroundColor: `${type.color}15`,
                        color: type.color === '#FFD700' ? '#B45309' : type.color === '#FF69B4' ? '#BE185D' : '#92400E'
                      }}
                    >
                      {trait}
                    </span>
                  ))}
                </div>

                {/* 工作风格 */}
                <div className="text-center text-xs text-gray-500 mb-4">
                  <span className="font-medium">工作风格：</span>{type.workStyle}
                </div>

                {/* 详情按钮 */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowDetails(showDetails === type.id ? null : type.id)
                  }}
                  className="w-full py-2 text-sm text-gray-500 hover:text-gray-700 transition-colors border-t border-gray-100 mt-4 flex items-center justify-center space-x-1"
                >
                  <Info className="w-3 h-3" />
                  <span>{showDetails === type.id ? '收起详情' : '查看详情'}</span>
                </button>

                {/* 背景装饰 */}
                <motion.div
                  className="absolute top-0 right-0 w-32 h-32 opacity-5"
                  style={{ background: `radial-gradient(circle, ${type.color} 0%, transparent 70%)` }}
                  animate={isHovered ? { scale: 1.2, opacity: 0.1 } : { scale: 1, opacity: 0.05 }}
                />
              </motion.div>

              {/* 详细信息展开 */}
              <AnimatePresence>
                {showDetails === type.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 bg-gray-50 rounded-xl p-4 space-y-3"
                  >
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">核心特质</h4>
                      <div className="flex flex-wrap gap-1">
                        {type.traits.map((trait) => (
                          <span
                            key={trait}
                            className="px-2 py-1 text-xs bg-white rounded-md border"
                          >
                            {trait}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">职场优势</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {type.advantages.map((advantage, idx) => (
                          <li key={idx} className="flex items-center space-x-2">
                            <div className="w-1 h-1 bg-primary-500 rounded-full" />
                            <span>{advantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )
        })}
      </div>

      {/* 选择提示 */}
      <AnimatePresence>
        {!selectedType && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center p-4 bg-blue-50 rounded-xl border border-blue-200"
          >
            <div className="flex items-center justify-center space-x-2 text-blue-700">
              <Heart className="w-5 h-5" />
              <span className="font-medium">请选择一个最符合你的职场人设</span>
            </div>
            <p className="text-sm text-blue-600 mt-1">选择后AI将为你生成对应的专属形象</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 已选择反馈 */}
      <AnimatePresence>
        {selectedType && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="text-center p-4 bg-green-50 rounded-xl border border-green-200"
          >
            <div className="flex items-center justify-center space-x-2 text-green-700">
              <Zap className="w-5 h-5" />
              <span className="font-medium">
                太棒了！你选择了 {ANIMAL_TYPES.find(t => t.id === selectedType)?.displayName}
              </span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              AI将基于这个人设为你生成专属的职场形象
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
