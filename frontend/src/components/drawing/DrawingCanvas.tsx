import { useRef, useEffect, useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Brush, Eraser, Palette, RotateCcw, Download, Upload } from 'lucide-react'
import { cn } from '@/utils'
import toast from 'react-hot-toast'

interface DrawingCanvasProps {
  width?: number
  height?: number
  onDrawingChange?: (imageData: string) => void
  initialDrawing?: string
  className?: string
}

type Tool = 'brush' | 'eraser'

interface DrawingState {
  tool: Tool
  brushSize: number
  color: string
  opacity: number
}

const DEFAULT_COLORS = [
  '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
  '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
  '#FFC0CB', '#A52A2A', '#808080', '#000080', '#008000'
]

export default function DrawingCanvas({ 
  width = 400, 
  height = 400, 
  onDrawingChange,
  initialDrawing,
  className 
}: DrawingCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [lastPos, setLastPos] = useState<{ x: number; y: number } | null>(null)
  const [showTools, setShowTools] = useState(true)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [customColor, setCustomColor] = useState('#000000')
  
  const [drawingState, setDrawingState] = useState<DrawingState>({
    tool: 'brush',
    brushSize: 5,
    color: '#000000',
    opacity: 1
  })

  // 初始化画布
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布大小
    canvas.width = width
    canvas.height = height

    // 设置默认样式
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    ctx.imageSmoothingEnabled = true

    // 清空画布为白色背景
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)

    // 如果有初始绘画数据，加载它
    if (initialDrawing) {
      loadDrawingFromData(initialDrawing)
    }
  }, [width, height, initialDrawing])

  // 加载绘画数据
  const loadDrawingFromData = useCallback((imageData: string) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const img = new Image()
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.fillStyle = '#FFFFFF'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      notifyDrawingChange()
    }
    img.src = imageData
  }, [])

  // 获取鼠标/触摸位置
  const getEventPos = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    const canvas = canvasRef.current
    if (!canvas) return null

    const rect = canvas.getBoundingClientRect()
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height

    if ('touches' in e) {
      const touch = e.touches[0] || e.changedTouches[0]
      return {
        x: (touch.clientX - rect.left) * scaleX,
        y: (touch.clientY - rect.top) * scaleY
      }
    } else {
      return {
        x: (e.clientX - rect.left) * scaleX,
        y: (e.clientY - rect.top) * scaleY
      }
    }
  }, [])

  // 开始绘制
  const startDrawing = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault()
    const pos = getEventPos(e)
    if (!pos) return

    setIsDrawing(true)
    setLastPos(pos)

    // 绘制起始点
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    if (drawingState.tool === 'brush') {
      ctx.globalCompositeOperation = 'source-over'
      ctx.strokeStyle = drawingState.color
      ctx.globalAlpha = drawingState.opacity
    } else {
      ctx.globalCompositeOperation = 'destination-out'
      ctx.globalAlpha = 1
    }

    ctx.lineWidth = drawingState.brushSize
    ctx.beginPath()
    ctx.arc(pos.x, pos.y, drawingState.brushSize / 2, 0, Math.PI * 2)
    ctx.fill()
  }, [drawingState, getEventPos])

  // 绘制过程
  const draw = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    if (!isDrawing || !lastPos) return

    e.preventDefault()
    const pos = getEventPos(e)
    if (!pos) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    if (drawingState.tool === 'brush') {
      ctx.globalCompositeOperation = 'source-over'
      ctx.strokeStyle = drawingState.color
      ctx.globalAlpha = drawingState.opacity
    } else {
      ctx.globalCompositeOperation = 'destination-out'
      ctx.globalAlpha = 1
    }

    ctx.lineWidth = drawingState.brushSize
    ctx.beginPath()
    ctx.moveTo(lastPos.x, lastPos.y)
    ctx.lineTo(pos.x, pos.y)
    ctx.stroke()

    setLastPos(pos)
  }, [isDrawing, lastPos, drawingState, getEventPos])

  // 结束绘制
  const endDrawing = useCallback(() => {
    if (isDrawing) {
      setIsDrawing(false)
      setLastPos(null)
      notifyDrawingChange()
    }
  }, [isDrawing])

  // 通知绘画变化
  const notifyDrawingChange = useCallback(() => {
    if (onDrawingChange && canvasRef.current) {
      const imageData = canvasRef.current.toDataURL('image/png')
      onDrawingChange(imageData)
    }
  }, [onDrawingChange])

  // 清空画布
  const clearCanvas = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    notifyDrawingChange()
    toast.success('画布已清空')
  }, [notifyDrawingChange])

  // 下载绘画
  const downloadDrawing = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const link = document.createElement('a')
    link.download = `drawing-${Date.now()}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()
    toast.success('绘画已下载')
  }, [])

  // 上传绘画
  const uploadDrawing = useCallback(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        if (result) {
          loadDrawingFromData(result)
          toast.success('绘画已上传')
        }
      }
      reader.readAsDataURL(file)
    }
    input.click()
  }, [loadDrawingFromData])

  // 设置工具
  const setTool = useCallback((tool: Tool) => {
    setDrawingState(prev => ({ ...prev, tool }))
  }, [])

  // 设置画笔大小
  const setBrushSize = useCallback((size: number) => {
    setDrawingState(prev => ({ ...prev, brushSize: size }))
  }, [])

  // 设置颜色
  const setColor = useCallback((color: string) => {
    setDrawingState(prev => ({ ...prev, color }))
    setShowColorPicker(false)
  }, [])

  return (
    <div className={cn('relative inline-block', className)}>
      {/* 画布 */}
      <canvas
        ref={canvasRef}
        className="border-2 border-gray-300 rounded-lg shadow-sm cursor-crosshair touch-none"
        onMouseDown={startDrawing}
        onMouseMove={draw}
        onMouseUp={endDrawing}
        onMouseLeave={endDrawing}
        onTouchStart={startDrawing}
        onTouchMove={draw}
        onTouchEnd={endDrawing}
        style={{ 
          width: '100%', 
          maxWidth: `${width}px`,
          height: 'auto',
          aspectRatio: `${width}/${height}`
        }}
      />

      {/* 工具栏 */}
      <AnimatePresence>
        {showTools && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute -bottom-16 left-0 right-0 bg-white rounded-lg shadow-lg border p-3"
          >
            <div className="flex items-center justify-between space-x-4">
              {/* 工具选择 */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setTool('brush')}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    drawingState.tool === 'brush'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  )}
                  title="画笔"
                >
                  <Brush className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setTool('eraser')}
                  className={cn(
                    'p-2 rounded-lg transition-colors',
                    drawingState.tool === 'eraser'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  )}
                  title="橡皮擦"
                >
                  <Eraser className="w-4 h-4" />
                </button>
              </div>

              {/* 画笔大小 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">大小</span>
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={drawingState.brushSize}
                  onChange={(e) => setBrushSize(Number(e.target.value))}
                  className="w-16"
                />
                <span className="text-sm text-gray-600 w-6 text-center">
                  {drawingState.brushSize}
                </span>
              </div>

              {/* 颜色选择 */}
              <div className="relative">
                <button
                  onClick={() => setShowColorPicker(!showColorPicker)}
                  className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                  title="选择颜色"
                >
                  <Palette className="w-4 h-4" />
                </button>
                
                <AnimatePresence>
                  {showColorPicker && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95, y: -10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95, y: -10 }}
                      className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border p-3 z-10"
                    >
                      <div className="grid grid-cols-5 gap-2 mb-3">
                        {DEFAULT_COLORS.map((color) => (
                          <button
                            key={color}
                            onClick={() => setColor(color)}
                            className={cn(
                              'w-8 h-8 rounded-full border-2 transition-transform',
                              drawingState.color === color
                                ? 'border-gray-400 scale-110'
                                : 'border-gray-200 hover:scale-105'
                            )}
                            style={{ backgroundColor: color }}
                          />
                        ))}
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="color"
                          value={customColor}
                          onChange={(e) => setCustomColor(e.target.value)}
                          className="w-8 h-8 rounded border"
                        />
                        <button
                          onClick={() => setColor(customColor)}
                          className="text-sm bg-primary-500 text-white px-3 py-1 rounded hover:bg-primary-600"
                        >
                          使用
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={clearCanvas}
                  className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                  title="清空画布"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
                <button
                  onClick={downloadDrawing}
                  className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                  title="下载绘画"
                >
                  <Download className="w-4 h-4" />
                </button>
                <button
                  onClick={uploadDrawing}
                  className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                  title="上传绘画"
                >
                  <Upload className="w-4 h-4" />
                </button>
              </div>

              {/* 切换工具栏显示 */}
              <button
                onClick={() => setShowTools(false)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="隐藏工具栏"
              >
                ×
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 当前颜色指示器 */}
      {drawingState.tool === 'brush' && (
        <div className="absolute top-2 right-2 flex items-center space-x-2 bg-white rounded-lg shadow-sm border px-2 py-1">
          <div
            className="w-4 h-4 rounded-full border"
            style={{ backgroundColor: drawingState.color }}
          />
          <span className="text-xs text-gray-600">{drawingState.brushSize}px</span>
        </div>
      )}

      {/* 工具栏切换按钮 */}
      {!showTools && (
        <button
          onClick={() => setShowTools(true)}
          className="absolute bottom-2 left-2 p-2 bg-primary-500 text-white rounded-full shadow-lg hover:bg-primary-600 transition-colors"
          title="显示工具栏"
        >
          <Palette className="w-4 h-4" />
        </button>
      )}
    </div>
  )
}