import React, { useState, useEffect } from 'react';
import { useAppSelector } from '@/store';
import toast from 'react-hot-toast';

interface LeaderboardEntry {
  userId: string;
  username: string;
  avatarUrl?: string;
  totalScore: number;
  level: number;
  title: string;
  rank: number;
  badges: Array<{
    name: string;
    icon: string;
    rarity: string;
  }>;
  stats: {
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
  };
  animalInfo?: {
    animalType: string;
    totalInteractions: number;
  };
}

interface UserScoreStats {
  userId: string;
  totalScore: number;
  level: number;
  experience: number;
  experienceToNextLevel: number;
  title: string;
  reputation: number;
  categoryScores: {
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
  };
  rankings: {
    globalRank: number;
    dailyRank: number;
    weeklyRank: number;
    monthlyRank: number;
  };
  badges: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    rarity: string;
    earnedAt: Date;
    icon: string;
  }>;
  streaks: {
    dailyLogin: number;
    maxDailyLogin: number;
    weeklyActive: number;
    maxWeeklyActive: number;
    consecutiveWork: number;
    maxConsecutiveWork: number;
  };
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  category: string;
  rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
  icon: string;
  requirement: string;
  progress: number;
  maxProgress: number;
  reward: {
    score: number;
    experience: number;
  };
}

export default function LeaderboardPanel() {
  const [activeTab, setActiveTab] = useState<'leaderboard' | 'my-stats' | 'achievements'>('leaderboard');
  const [timePeriod, setTimePeriod] = useState<'total' | 'daily' | 'weekly' | 'monthly'>('total');
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [myStats, setMyStats] = useState<UserScoreStats | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { currentUser } = useAppSelector(state => state.user);

  // 获取排行榜数据
  const fetchLeaderboard = async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/zoo/leaderboard?period=${timePeriod}&limit=50`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setLeaderboard(data.data.leaderboard);
        }
      } else {
        throw new Error('获取排行榜失败');
      }
    } catch (error) {
      console.error('获取排行榜失败:', error);
      setError('获取排行榜数据失败');
      toast.error('获取排行榜数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取我的统计数据
  const fetchMyStats = async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/v1/zoo/my-score-stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setMyStats(data.data);
        }
      } else {
        throw new Error('获取我的数据失败');
      }
    } catch (error) {
      console.error('获取我的数据失败:', error);
      setError('获取个人数据失败');
      toast.error('获取个人数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取成就数据
  const fetchAchievements = async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/v1/zoo/achievements', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setAchievements(data.data.achievements);
        }
      } else {
        throw new Error('获取成就失败');
      }
    } catch (error) {
      console.error('获取成就失败:', error);
      setError('获取成就数据失败');
      toast.error('获取成就数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换选项卡时获取数据
  useEffect(() => {
    if (activeTab === 'leaderboard') {
      fetchLeaderboard();
    } else if (activeTab === 'my-stats') {
      fetchMyStats();
    } else if (activeTab === 'achievements') {
      fetchAchievements();
    }
  }, [activeTab, timePeriod]);

  // 获取稀有度颜色
  const getRarityColor = (rarity: string) => {
    switch (rarity.toUpperCase()) {
      case 'COMMON': return 'text-gray-600 bg-gray-100';
      case 'UNCOMMON': return 'text-green-600 bg-green-100';
      case 'RARE': return 'text-blue-600 bg-blue-100';
      case 'EPIC': return 'text-purple-600 bg-purple-100';
      case 'LEGENDARY': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取等级颜色
  const getLevelColor = (level: number) => {
    if (level >= 20) return 'text-purple-600 bg-purple-100';
    if (level >= 15) return 'text-blue-600 bg-blue-100';
    if (level >= 10) return 'text-green-600 bg-green-100';
    if (level >= 5) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (!currentUser) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 text-center">
        <p className="text-gray-500">请先登录查看排行榜</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* 选项卡头部 */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          <button
            onClick={() => setActiveTab('leaderboard')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'leaderboard'
                ? 'border-primary-500 text-primary-600 bg-primary-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            🏆 排行榜
          </button>
          <button
            onClick={() => setActiveTab('my-stats')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'my-stats'
                ? 'border-primary-500 text-primary-600 bg-primary-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            📊 我的数据
          </button>
          <button
            onClick={() => setActiveTab('achievements')}
            className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'achievements'
                ? 'border-primary-500 text-primary-600 bg-primary-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            🏅 成就系统
          </button>
        </nav>
      </div>

      {/* 内容区域 */}
      <div className="p-6">
        {/* 排行榜 */}
        {activeTab === 'leaderboard' && (
          <div>
            {/* 时间选择器 */}
            <div className="mb-6">
              <div className="flex space-x-2">
                {[
                  { value: 'total', label: '总排行' },
                  { value: 'monthly', label: '本月' },
                  { value: 'weekly', label: '本周' },
                  { value: 'daily', label: '今日' },
                ].map((period) => (
                  <button
                    key={period.value}
                    onClick={() => setTimePeriod(period.value as any)}
                    className={`px-4 py-2 text-sm rounded-lg transition-colors ${
                      timePeriod === period.value
                        ? 'bg-primary-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {period.label}
                  </button>
                ))}
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500">
                {error}
                <button
                  onClick={fetchLeaderboard}
                  className="ml-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
                >
                  重试
                </button>
              </div>
            ) : leaderboard && leaderboard.length > 0 ? (
              <div className="space-y-3">
                {leaderboard.map((entry) => (
                  <div
                    key={entry.userId}
                    className={`flex items-center space-x-4 p-4 rounded-lg border transition-colors ${
                      entry.userId === currentUser?.id
                        ? 'bg-primary-50 border-primary-200'
                        : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {/* 排名 */}
                    <div className="text-2xl font-bold w-16 text-center">
                      {getRankIcon(entry.rank)}
                    </div>

                    {/* 动物头像 */}
                    <div className="flex-shrink-0">
                      {entry.avatarUrl ? (
                        <img 
                          src={entry.avatarUrl} 
                          alt={entry.username}
                          className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-white text-lg font-bold shadow-md">
                          🐾
                        </div>
                      )}
                    </div>

                    {/* 用户信息 */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <h3 className="font-medium text-gray-900">{entry.username}</h3>
                          <p className="text-sm text-gray-500">{entry.title}</p>
                          <p className="text-xs text-gray-400">
                            互动次数: {entry.animalInfo?.totalInteractions || 0}
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(entry.level)}`}>
                          Lv.{entry.level}
                        </div>
                      </div>
                    </div>

                    {/* 徽章 */}
                    <div className="flex space-x-1">
                      {entry.badges.slice(0, 3).map((badge, index) => (
                        <div
                          key={index}
                          className={`px-2 py-1 rounded text-xs ${getRarityColor(badge.rarity)}`}
                          title={badge.name}
                        >
                          {badge.icon}
                        </div>
                      ))}
                    </div>

                    {/* 积分 */}
                    <div className="text-right">
                      <div className="text-xl font-bold text-primary-600">
                        {formatNumber(entry.totalScore)}
                      </div>
                      <div className="text-xs text-gray-500">互动积分</div>
                      <div className="text-xs text-blue-500 mt-1">
                        {entry.animalInfo?.animalType && (
                          <>
                            {entry.animalInfo.animalType === 'OXHORSE' ? '🐂 牛马' : 
                             entry.animalInfo.animalType === 'PET' ? '🐱 宠物' :
                             entry.animalInfo.animalType === 'DIVINE' ? '🦌 神兽' : '🐾 未知'}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无排行榜数据
                <button
                  onClick={fetchLeaderboard}
                  className="ml-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
                >
                  刷新
                </button>
              </div>
            )}
          </div>
        )}

        {/* 我的数据 */}
        {activeTab === 'my-stats' && (
          <div>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : myStats ? (
              <div className="space-y-6">
                {/* 基础信息 */}
                <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">{currentUser.username}</h2>
                      <p className="text-lg text-primary-600 font-medium">{myStats.title}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className={`px-3 py-1 rounded text-sm font-medium ${getLevelColor(myStats.level)}`}>
                          等级 {myStats.level}
                        </span>
                        <span className="text-sm text-gray-600">
                          声望: {myStats.reputation}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-primary-600">
                        {formatNumber(myStats.totalScore)}
                      </div>
                      <div className="text-sm text-gray-600">总积分</div>
                    </div>
                  </div>

                  {/* 经验条 */}
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>经验值</span>
                      <span>{myStats.experience}/{myStats.experience + myStats.experienceToNextLevel}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-primary-500 h-3 rounded-full transition-all"
                        style={{
                          width: `${(myStats.experience / (myStats.experience + myStats.experienceToNextLevel)) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 排名信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { label: '全球排名', value: myStats.rankings.globalRank, icon: '🌍' },
                    { label: '每月排名', value: myStats.rankings.monthlyRank, icon: '📅' },
                    { label: '每周排名', value: myStats.rankings.weeklyRank, icon: '📊' },
                    { label: '每日排名', value: myStats.rankings.dailyRank, icon: '⭐' },
                  ].map((rank) => (
                    <div key={rank.label} className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl mb-1">{rank.icon}</div>
                      <div className="text-xl font-bold text-gray-900">#{rank.value}</div>
                      <div className="text-sm text-gray-600">{rank.label}</div>
                    </div>
                  ))}
                </div>

                {/* 分类积分 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">积分分布</h3>
                  <div className="space-y-3">
                    {[
                      { label: '互动积分', value: myStats.categoryScores.interactionScore, color: 'bg-blue-500', icon: '🤝' },
                      { label: '工作表现', value: myStats.categoryScores.workPerformanceScore, color: 'bg-green-500', icon: '💼' },
                      { label: '社交积分', value: myStats.categoryScores.socialScore, color: 'bg-yellow-500', icon: '👥' },
                      { label: '成就积分', value: myStats.categoryScores.achievementScore, color: 'bg-purple-500', icon: '🏆' },
                      { label: '创造力积分', value: myStats.categoryScores.creativityScore, color: 'bg-pink-500', icon: '🎨' },
                    ].map((category) => {
                      const maxScore = Math.max(...Object.values(myStats.categoryScores));
                      const percentage = maxScore > 0 ? (category.value / maxScore) * 100 : 0;
                      return (
                        <div key={category.label}>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="flex items-center space-x-2">
                              <span>{category.icon}</span>
                              <span>{category.label}</span>
                            </span>
                            <span className="font-medium">{formatNumber(category.value)}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`${category.color} h-2 rounded-full transition-all`}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* 连续记录 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">连续记录</h3>
                  <div className="grid grid-cols-3 gap-4">
                    {[
                      { label: '每日登录', current: myStats.streaks.dailyLogin, max: myStats.streaks.maxDailyLogin, icon: '📅' },
                      { label: '每周活跃', current: myStats.streaks.weeklyActive, max: myStats.streaks.maxWeeklyActive, icon: '🔥' },
                      { label: '连续工作', current: myStats.streaks.consecutiveWork, max: myStats.streaks.maxConsecutiveWork, icon: '💪' },
                    ].map((streak) => (
                      <div key={streak.label} className="bg-gray-50 rounded-lg p-4 text-center">
                        <div className="text-xl mb-1">{streak.icon}</div>
                        <div className="text-lg font-bold text-gray-900">
                          {streak.current} / {streak.max}
                        </div>
                        <div className="text-xs text-gray-600">{streak.label}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 徽章展示 */}
                {myStats.badges.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">已获得徽章</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {myStats.badges.map((badge) => (
                        <div
                          key={badge.id}
                          className={`p-4 rounded-lg border text-center ${getRarityColor(badge.rarity)}`}
                        >
                          <div className="text-2xl mb-2">{badge.icon}</div>
                          <h4 className="font-medium text-sm">{badge.name}</h4>
                          <p className="text-xs text-gray-600 mt-1">{badge.description}</p>
                          <div className="text-xs mt-2 opacity-75">
                            {new Date(badge.earnedAt).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无数据
                <button
                  onClick={fetchMyStats}
                  className="ml-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
                >
                  刷新
                </button>
              </div>
            )}
          </div>
        )}

        {/* 成就系统 */}
        {activeTab === 'achievements' && (
          <div>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">可获得成就</h3>
                <div className="grid gap-4">
                  {achievements.map((achievement) => {
                    const progress = (achievement.progress / achievement.maxProgress) * 100;
                    const isCompleted = achievement.progress >= achievement.maxProgress;
                    
                    return (
                      <div
                        key={achievement.id}
                        className={`p-4 rounded-lg border transition-all ${
                          isCompleted 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className={`text-2xl ${isCompleted ? 'grayscale-0' : 'grayscale'}`}>
                              {achievement.icon}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <h4 className="font-medium text-gray-900">{achievement.name}</h4>
                                <span className={`px-2 py-1 rounded text-xs font-medium ${getRarityColor(achievement.rarity)}`}>
                                  {achievement.rarity}
                                </span>
                                {isCompleted && (
                                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                                    ✓ 已完成
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                              <p className="text-xs text-gray-500">{achievement.requirement}</p>
                              
                              {/* 进度条 */}
                              <div className="mt-3">
                                <div className="flex justify-between text-xs text-gray-600 mb-1">
                                  <span>进度</span>
                                  <span>{achievement.progress}/{achievement.maxProgress}</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full transition-all ${
                                      isCompleted ? 'bg-green-500' : 'bg-primary-500'
                                    }`}
                                    style={{ width: `${progress}%` }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="text-right ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {achievement.reward.score} 积分
                            </div>
                            <div className="text-xs text-gray-500">
                              {achievement.reward.experience} 经验
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}