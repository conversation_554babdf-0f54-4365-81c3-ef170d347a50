import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Share2, Download, RotateCcw, Eye, Star, Heart, MessageSquare } from 'lucide-react'
import { cn } from '@/utils'
import toast from 'react-hot-toast'

interface AIResultDisplayProps {
  result?: {
    imageUrl: string
    animalType: string
    analysis: {
      animalType: 'OXHORSE' | 'PET' | 'DIVINE'
      personality: {
        workEfficiency: number
        happiness: number
        creativity: number
        resilience: number
        compliance: number
        socialSkill: number
        workPressure: number
      }
      analysis: string
      traits: string[]
      recommendations: string[]
    }
    generationMethod: string
    timestamp: number
  }
  onRegenerate?: () => void
  onShare?: (result: any) => void
  className?: string
}

export default function AIResultDisplay({ 
  result, 
  onRegenerate, 
  onShare,
  className 
}: AIResultDisplayProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [likes, setLikes] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [views, setViews] = useState(0)

  // 从localStorage加载结果（如果没有传入result）
  useEffect(() => {
    if (!result) {
      const savedResult = localStorage.getItem('userGeneratedAvatar')
      if (savedResult) {
        try {
          const parsedResult = JSON.parse(savedResult)
          console.log('从localStorage加载AI结果:', parsedResult)
        } catch (error) {
          console.error('解析保存的AI结果失败:', error)
        }
      }
    }

    // 模拟增加浏览数
    const timer = setTimeout(() => {
      setViews(prev => prev + Math.floor(Math.random() * 10) + 1)
    }, 1000)

    return () => clearTimeout(timer)
  }, [result])

  // 获取动物类型信息
  const getAnimalTypeInfo = (type: string) => {
    switch (type) {
      case 'OXHORSE':
        return {
          name: '勤劳牛马',
          emoji: '🐂',
          color: 'from-amber-400 to-orange-500',
          description: '你是职场中最可靠的存在，任劳任怨，默默承担着重要的工作。虽然有时会感到疲惫，但内心充满责任感和使命感。'
        }
      case 'PET':
        return {
          name: '团宠员工',
          emoji: '🐱',
          color: 'from-pink-400 to-rose-500',
          description: '你是办公室里的开心果，人见人爱的存在。善于调节气氛，总能给同事们带来正能量和快乐。'
        }
      case 'DIVINE':
        return {
          name: '职场精英',
          emoji: '🦌',
          color: 'from-purple-400 to-indigo-500',
          description: '你拥有超凡的能力和智慧，是团队中的核心力量。善于解决复杂问题，引领团队走向成功。'
        }
      default:
        return {
          name: '神秘角色',
          emoji: '❓',
          color: 'from-gray-400 to-gray-500',
          description: '你是一个充满神秘色彩的存在。'
        }
    }
  }

  // 下载图片
  const handleDownload = () => {
    if (!result) return

    const link = document.createElement('a')
    link.download = `my-workmate-${Date.now()}.png`
    link.href = result.imageUrl
    link.click()
    toast.success('头像已下载')
  }

  // 分享功能
  const handleShare = () => {
    if (!result) return

    if (navigator.share) {
      navigator.share({
        title: '我的AI打工人形象',
        text: `我在牛马动物园生成了专属的${getAnimalTypeInfo(result.analysis.animalType).name}形象！`,
        url: window.location.href
      }).then(() => {
        toast.success('分享成功')
      }).catch((error) => {
        console.error('分享失败:', error)
        handleCopyLink()
      })
    } else {
      handleCopyLink()
    }

    if (onShare) {
      onShare(result)
    }
  }

  // 复制链接
  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      toast.success('链接已复制到剪贴板')
    }).catch(() => {
      toast.error('复制失败')
    })
  }

  // 点赞功能
  const handleLike = () => {
    if (isLiked) {
      setLikes(prev => prev - 1)
      setIsLiked(false)
      toast.success('取消点赞')
    } else {
      setLikes(prev => prev + 1)
      setIsLiked(true)
      toast.success('点赞成功！')
    }
  }

  // 保存到动物园
  const handleSaveToZoo = () => {
    if (!result) return

    try {
      // 获取现有的动物园数据
      const existingAnimals = JSON.parse(localStorage.getItem('zooAnimals') || '[]')
      
      // 创建新动物数据
      const newAnimal = {
        id: `result_${Date.now()}`,
        name: `我的${getAnimalTypeInfo(result.analysis.animalType).name}`,
        type: result.analysis.animalType,
        imageUrl: result.imageUrl,
        avatar: result.imageUrl,
        avatarUrl: result.imageUrl,
        position: {
          x: Math.random() * 800 + 100,
          y: Math.random() * 400 + 100
        },
        stats: {
          workEfficiency: result.analysis.personality.workEfficiency,
          happiness: result.analysis.personality.happiness,
          creativity: result.analysis.personality.creativity,
          energy: result.analysis.personality.resilience
        },
        analysis: result.analysis,
        generationMethod: result.generationMethod,
        timestamp: Date.now(),
        activity: '新来的',
        zone: '工作区',
        isUserGenerated: true,
        createdAt: new Date().toISOString()
      }

      // 检查是否已经存在用户的动物，如果存在则替换
      const userAnimalIndex = existingAnimals.findIndex((animal: any) => 
        animal.name && (animal.name.includes('我的') || animal.id === 1)
      )

      if (userAnimalIndex >= 0) {
        existingAnimals[userAnimalIndex] = newAnimal
        toast.success('已更新你的动物园角色')
      } else {
        existingAnimals.unshift(newAnimal) // 使用unshift保持一致性
        toast.success('已保存到动物园')
      }

      // 保存回localStorage
      localStorage.setItem('zooAnimals', JSON.stringify(existingAnimals))
      
      console.log('保存动物到动物园:', newAnimal)
    } catch (error) {
      console.error('保存到动物园失败:', error)
      toast.error('保存失败，请稍后重试')
    }
  }

  if (!result) {
    return (
      <div className={cn('bg-white rounded-2xl p-8 shadow-soft text-center', className)}>
        <div className="text-gray-400 text-6xl mb-4">🎭</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无结果</h3>
        <p className="text-gray-600">请先完成AI分析和生成流程</p>
      </div>
    )
  }

  const animalInfo = getAnimalTypeInfo(result.analysis.animalType)

  return (
    <div className={cn('bg-white rounded-2xl shadow-soft overflow-hidden', className)}>
      {/* 头像展示区 */}
      <div className={`bg-gradient-to-br ${animalInfo.color} p-8 text-center text-white relative`}>
        <div className="absolute top-4 right-4 flex items-center space-x-2">
          <div className="flex items-center space-x-1 bg-black bg-opacity-20 rounded-full px-2 py-1">
            <Eye className="w-3 h-3" />
            <span className="text-xs">{views}</span>
          </div>
          <button
            onClick={handleLike}
            className={cn(
              'flex items-center space-x-1 rounded-full px-2 py-1 transition-colors',
              isLiked 
                ? 'bg-red-500 bg-opacity-80' 
                : 'bg-black bg-opacity-20 hover:bg-opacity-30'
            )}
          >
            <Heart className={cn('w-3 h-3', isLiked && 'fill-current')} />
            <span className="text-xs">{likes}</span>
          </button>
        </div>

        <div className="relative inline-block mb-6">
          <img
            src={result.imageUrl}
            alt="Generated Avatar"
            className="w-48 h-60 rounded-xl shadow-2xl object-cover border-4 border-white"
          />
          <div className="absolute -bottom-3 -right-3 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg">
            <span className="text-3xl">{animalInfo.emoji}</span>
          </div>
        </div>

        <h2 className="text-3xl font-bold mb-2">{animalInfo.name}</h2>
        <p className="text-lg opacity-90 mb-4">{animalInfo.description}</p>

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-3">
          <button
            onClick={handleDownload}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm rounded-lg px-4 py-2 text-sm font-medium transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>下载</span>
          </button>
          <button
            onClick={handleShare}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm rounded-lg px-4 py-2 text-sm font-medium transition-colors flex items-center space-x-2"
          >
            <Share2 className="w-4 h-4" />
            <span>分享</span>
          </button>
          {onRegenerate && (
            <button
              onClick={onRegenerate}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm rounded-lg px-4 py-2 text-sm font-medium transition-colors flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>重新生成</span>
            </button>
          )}
        </div>
      </div>

      {/* 详细信息区 */}
      <div className="p-6">
        {/* 个性特征 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">个性分析</h3>
          <p className="text-gray-700 mb-4 leading-relaxed">
            {result.analysis.analysis}
          </p>

          {/* 特征标签 */}
          <div className="flex flex-wrap gap-2 mb-4">
            {result.analysis.traits.map((trait, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full"
              >
                {trait}
              </span>
            ))}
          </div>
        </div>

        {/* 能力雷达图 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">能力评估</h3>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(result.analysis.personality).map(([key, value]) => {
              const abilityNames: Record<string, string> = {
                workEfficiency: '工作效率',
                happiness: '快乐指数',
                creativity: '创造力',
                resilience: '抗压能力',
                compliance: '服从性',
                socialSkill: '社交能力',
                workPressure: '工作压力'
              }

              return (
                <div key={key} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">{abilityNames[key]}</span>
                    <span className="font-medium">{value}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full bg-gradient-to-r ${animalInfo.color}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${value}%` }}
                      transition={{ duration: 0.8, delay: 0.2 }}
                    />
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* 建议 */}
        {result.analysis.recommendations.length > 0 && (
          <div className="mb-6">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center space-x-2 text-lg font-semibold text-gray-900 mb-3 hover:text-primary-600 transition-colors"
            >
              <span>💡 个性化建议</span>
              <motion.div
                animate={{ rotate: showDetails ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </motion.div>
            </button>
            
            <AnimatePresence>
              {showDetails && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  {result.analysis.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                      <span className="text-primary-500 mt-1">•</span>
                      <span>{rec}</span>
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/* 底部操作 */}
        <div className="border-t pt-4 flex space-x-3">
          <button
            onClick={handleSaveToZoo}
            className="btn-primary btn-md flex-1"
          >
            🎪 保存到动物园
          </button>
          <button
            onClick={() => window.location.href = '/zoo'}
            className="btn-outline btn-md flex-1"
          >
            🏃 去动物园逛逛
          </button>
        </div>

        {/* 生成信息 */}
        <div className="mt-4 text-xs text-gray-400 text-center">
          生成方式: {result.generationMethod} | 
          生成时间: {new Date(result.timestamp).toLocaleString()}
        </div>
      </div>
    </div>
  )
}