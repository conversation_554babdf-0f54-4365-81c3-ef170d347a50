import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Brain, Wand2, <PERSON><PERSON><PERSON>, CheckCircle, Loader2, Eye, Settings } from 'lucide-react'
import { cn } from '@/utils'

interface GenerationStep {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  estimatedTime: number
  status: 'pending' | 'active' | 'completed' | 'error'
  progress?: number
  details?: string[]
}

interface GenerationProgressViewerProps {
  currentStep: string
  steps: GenerationStep[]
  overallProgress: number
  isProcessing: boolean
  error?: string | null
  onCancel?: () => void
  className?: string
}

const STEP_ANIMATIONS = {
  analyzing: {
    particles: true,
    pulseColor: '#3B82F6',
    details: [
      '正在分析线条风格和笔触特征...',
      '识别绘画中的个性表达...',
      '推断你的性格倾向和工作风格...',
      '匹配最适合的动物类型...'
    ]
  },
  generating: {
    particles: true,
    pulseColor: '#8B5CF6',
    details: [
      '构建专属的AI描述提示词...',
      '调用高级AI绘画模型...',
      '生成你的专属打工人形象...',
      '优化图像质量和细节...'
    ]
  },
  finalizing: {
    particles: false,
    pulseColor: '#10B981',
    details: [
      '正在保存你的专属形象...',
      '生成个性化特征标签...',
      '准备加入动物园世界...'
    ]
  }
}

export default function GenerationProgressViewer({
  currentStep,
  steps,
  overallProgress,
  isProcessing,
  error,
  onCancel,
  className
}: GenerationProgressViewerProps) {
  const [currentDetailIndex, setCurrentDetailIndex] = useState(0)
  const [visibleParticles, setVisibleParticles] = useState<Array<{ id: number; x: number; y: number }>>([])
  
  const currentStepData = steps.find(step => step.id === currentStep)
  const currentAnimation = currentStepData ? STEP_ANIMATIONS[currentStep as keyof typeof STEP_ANIMATIONS] : null
  
  // 轮播显示处理详情
  useEffect(() => {
    if (!isProcessing || !currentAnimation?.details) return
    
    const interval = setInterval(() => {
      setCurrentDetailIndex(prev => (prev + 1) % currentAnimation.details!.length)
    }, 2000)
    
    return () => clearInterval(interval)
  }, [isProcessing, currentAnimation])
  
  // 生成粒子效果
  useEffect(() => {
    if (!currentAnimation?.particles) {
      setVisibleParticles([])
      return
    }
    
    const generateParticles = () => {
      const particles = Array.from({ length: 8 }, (_, i) => ({
        id: Date.now() + i,
        x: Math.random() * 100,
        y: Math.random() * 100
      }))
      setVisibleParticles(particles)
    }
    
    generateParticles()
    const interval = setInterval(generateParticles, 3000)
    
    return () => clearInterval(interval)
  }, [currentAnimation?.particles])
  
  return (
    <div className={cn('bg-white rounded-2xl shadow-soft overflow-hidden', className)}>
      {/* 头部区域 */}
      <div className="relative bg-gradient-to-r from-indigo-500 to-purple-600 p-8 text-white overflow-hidden">
        {/* 背景粒子 */}
        <div className="absolute inset-0 overflow-hidden">
          {visibleParticles.map(particle => (
            <motion.div
              key={particle.id}
              className="absolute w-2 h-2 bg-white rounded-full opacity-30"
              style={{ left: `${particle.x}%`, top: `${particle.y}%` }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ 
                scale: [0, 1, 0],
                opacity: [0, 0.6, 0],
                y: [0, -20, -40]
              }}
              transition={{ duration: 2, ease: 'easeOut' }}
            />
          ))}
        </div>
        
        <div className="relative z-10 text-center">
          <motion.div
            className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-4 backdrop-blur-sm"
            animate={isProcessing ? { 
              scale: [1, 1.1, 1],
              boxShadow: [
                '0 0 0 0 rgba(255,255,255,0.3)',
                '0 0 0 20px rgba(255,255,255,0)',
                '0 0 0 0 rgba(255,255,255,0)'
              ]
            } : {}}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {currentStepData?.icon && (
              <currentStepData.icon className="w-10 h-10 text-white" />
            )}
          </motion.div>
          
          <h2 className="text-3xl font-bold mb-2">
            {isProcessing ? 'AI正在为你创作' : '创作完成'}
          </h2>
          
          <p className="text-lg opacity-90">
            {currentStepData?.description || '请稍候，传奇正在诞生...'}
          </p>
        </div>
      </div>
      
      {/* 主内容区域 */}
      <div className="p-8">
        {/* 总体进度条 */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm font-medium text-gray-700">总体进度</span>
            <span className="text-sm font-bold text-primary-600">{Math.round(overallProgress)}%</span>
          </div>
          
          <div className="h-3 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${overallProgress}%` }}
              transition={{ duration: 0.5, ease: 'easeOut' }}
            />
          </div>
        </div>
        
        {/* 步骤列表 */}
        <div className="space-y-4 mb-8">
          {steps.map((step, index) => {
            const isActive = step.id === currentStep
            const isCompleted = step.status === 'completed'
            const isError = step.status === 'error'
            
            return (
              <div key={step.id} className="relative">
                {/* 连接线 */}
                {index < steps.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-12 bg-gray-200">
                    <motion.div
                      className="w-full bg-primary-500 origin-top"
                      initial={{ height: '0%' }}
                      animate={{ 
                        height: isCompleted || (isActive && step.progress && step.progress > 50) ? '100%' : '0%'
                      }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                )}
                
                <div className="flex items-start space-x-4">
                  {/* 步骤图标 */}
                  <motion.div
                    className={cn(
                      'flex items-center justify-center w-12 h-12 rounded-full border-2 transition-colors',
                      isCompleted ? 'bg-green-500 border-green-500 text-white' :
                      isError ? 'bg-red-500 border-red-500 text-white' :
                      isActive ? 'bg-primary-500 border-primary-500 text-white' :
                      'bg-white border-gray-300 text-gray-400'
                    )}
                    animate={isActive ? {
                      scale: [1, 1.05, 1],
                      boxShadow: [
                        '0 0 0 0 rgba(59,130,246,0.3)',
                        '0 0 0 8px rgba(59,130,246,0.1)',
                        '0 0 0 0 rgba(59,130,246,0)'
                      ]
                    } : {}}
                    transition={{ duration: 1.5, repeat: isActive ? Infinity : 0 }}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : isError ? (
                      <span className="text-xl">!</span>
                    ) : isActive ? (
                      <Loader2 className="w-6 h-6 animate-spin" />
                    ) : (
                      <step.icon className="w-6 h-6" />
                    )}
                  </motion.div>
                  
                  {/* 步骤内容 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={cn(
                        'text-lg font-semibold',
                        isActive ? 'text-primary-700' : 
                        isCompleted ? 'text-green-700' :
                        isError ? 'text-red-700' : 'text-gray-500'
                      )}>
                        {step.name}
                      </h3>
                      
                      {isActive && step.progress && (
                        <span className="text-sm text-primary-600 font-medium">
                          {Math.round(step.progress)}%
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mt-1">{step.description}</p>
                    
                    {/* 步骤进度条 */}
                    {isActive && step.progress && (
                      <div className="mt-3">
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-primary-500 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${step.progress}%` }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {/* 预估时间 */}
                    {isActive && (
                      <div className="text-xs text-gray-500 mt-2">
                        预估耗时：{step.estimatedTime}秒
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {/* 实时详情 */}
        <AnimatePresence mode="wait">
          {isProcessing && currentAnimation?.details && (
            <motion.div
              key={currentDetailIndex}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-blue-50 rounded-lg p-4 border border-blue-100"
            >
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                </div>
                <p className="text-blue-800 text-sm font-medium">
                  {currentAnimation.details[currentDetailIndex]}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 错误显示 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5">
                  <span className="block text-lg">⚠️</span>
                </div>
                <div>
                  <h4 className="text-red-800 font-medium">AI生成失败</h4>
                  <p className="text-red-700 text-sm mt-1">{error}</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 取消按钮 */}
        {isProcessing && onCancel && (
          <div className="text-center mt-6">
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700 text-sm transition-colors"
            >
              取消生成
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

// 预定义的步骤配置
export const DEFAULT_GENERATION_STEPS: GenerationStep[] = [
  {
    id: 'analyzing',
    name: '分析绘画',
    description: '分析你的绘画风格和个性特征',
    icon: Brain,
    estimatedTime: 15,
    status: 'pending'
  },
  {
    id: 'generating',
    name: '生成形象',
    description: '使用AI创造你的专属打工人形象',
    icon: Wand2,
    estimatedTime: 30,
    status: 'pending'
  },
  {
    id: 'finalizing',
    name: '完成创作',
    description: '保存并优化你的专属形象',
    icon: Sparkles,
    estimatedTime: 5,
    status: 'pending'
  }
]
