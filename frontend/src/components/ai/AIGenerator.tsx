import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Brain, Wand2, <PERSON>ader2, <PERSON><PERSON>ircle, AlertCircle, RefreshCw } from 'lucide-react'
import { cn } from '@/utils'
import toast from 'react-hot-toast'
import { useAppSelector } from '@/store'

interface AIGeneratorProps {
  drawingData?: string
  onAnalysisComplete?: (analysis: AnalysisResult) => void
  onGenerationComplete?: (generation: GenerationResult) => void
  className?: string
}

interface AnalysisResult {
  animalType: 'OXHORSE' | 'PET' | 'DIVINE'
  personality: {
    workEfficiency: number
    happiness: number
    creativity: number
    resilience: number
    compliance: number
    socialSkill: number
    workPressure: number
  }
  analysis: string
  traits: string[]
  recommendations: string[]
}

interface GenerationResult {
  imageUrl: string
  animalType: string
  generationMethod: 'DALLE3' | 'REPLICATE' | 'MOCK'
  metadata: {
    prompt: string
    style: string
    quality: string
  }
}

type GenerationStep = 'idle' | 'analyzing' | 'generating' | 'completed' | 'error'

export default function AIGenerator({ 
  drawingData, 
  onAnalysisComplete, 
  onGenerationComplete,
  className 
}: AIGeneratorProps) {
  const [currentStep, setCurrentStep] = useState<GenerationStep>('idle')
  const [progress, setProgress] = useState(0)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  
  const { currentUser } = useAppSelector(state => state.user)

  // 模拟进度更新
  const updateProgress = useCallback((targetProgress: number, duration: number = 2000) => {
    const startProgress = progress
    const startTime = Date.now()
    
    const updateInterval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const ratio = Math.min(elapsed / duration, 1)
      const currentProgress = startProgress + (targetProgress - startProgress) * ratio
      
      setProgress(currentProgress)
      
      if (ratio >= 1) {
        clearInterval(updateInterval)
      }
    }, 50)
    
    return updateInterval
  }, [progress])

  // 分析绘画
  const analyzeDrawing = useCallback(async () => {
    if (!drawingData) {
      toast.error('请先完成绘画')
      return
    }

    setCurrentStep('analyzing')
    setError(null)
    setProgress(0)

    try {
      // 模拟进度更新
      const progressInterval = updateProgress(80, 3000)

      // TODO: 集成真实的Gemini API
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 模拟分析结果
      const mockAnalysis: AnalysisResult = {
        animalType: Math.random() > 0.5 ? 'OXHORSE' : Math.random() > 0.5 ? 'PET' : 'DIVINE',
        personality: {
          workEfficiency: Math.floor(Math.random() * 100),
          happiness: Math.floor(Math.random() * 100),
          creativity: Math.floor(Math.random() * 100),
          resilience: Math.floor(Math.random() * 100),
          compliance: Math.floor(Math.random() * 100),
          socialSkill: Math.floor(Math.random() * 100),
          workPressure: Math.floor(Math.random() * 100)
        },
        analysis: "基于您的绘画风格和笔触特征，我分析出您是一个富有创造力但又务实的人。您的线条流畅自然，显示出良好的表达能力，同时细节处理体现了您的耐心和专注度。",
        traits: ["富有创造力", "注重细节", "表达能力强", "耐心专注", "追求完美"],
        recommendations: [
          "适合从事需要创意思维的工作",
          "建议多参与团队协作项目",
          "可以尝试艺术相关的业余爱好",
          "注意工作与生活的平衡"
        ]
      }

      clearInterval(progressInterval)
      setProgress(100)
      setAnalysisResult(mockAnalysis)
      
      if (onAnalysisComplete) {
        onAnalysisComplete(mockAnalysis)
      }

      toast.success('分析完成！')
      
      // 自动进入生成步骤
      setTimeout(() => {
        generateAvatar(mockAnalysis)
      }, 1500)

    } catch (err) {
      setCurrentStep('error')
      setError('分析失败，请重试')
      toast.error('AI分析失败')
      console.error('Analysis error:', err)
    }
  }, [drawingData, onAnalysisComplete, updateProgress])

  // 生成头像
  const generateAvatar = useCallback(async (analysis?: AnalysisResult) => {
    const targetAnalysis = analysis || analysisResult
    if (!targetAnalysis || !drawingData) {
      toast.error('需要先完成分析')
      return
    }

    setCurrentStep('generating')
    setError(null)
    setProgress(0)

    try {
      // 模拟进度更新
      const progressInterval = updateProgress(90, 4000)

      // 调用AI融合API - 使用大模型生成
      console.log('🚀 发送AI融合请求...', {
        url: `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002'}/api/v1/avatar/ai-fusion`,
        animalType: targetAnalysis.animalType,
        hasDrawingData: !!drawingData,
        drawingDataLength: drawingData?.length || 0,
        targetAnalysis
      })
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 60000) // 60秒超时
      
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002'}/api/v1/avatar/ai-fusion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: drawingData,
          animalType: targetAnalysis.animalType,
          analysisData: {
            style: 'modern-illustration',
            expression: 'focused-professional',
            pose: 'confident-professional',
            features: targetAnalysis
          }
        }),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      console.log('📡 收到响应状态:', response.status, response.ok)
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }
      
      console.log('📦 开始解析JSON响应...')
      const result = await response.json()
      console.log('📦 响应数据解析成功:', {
        success: result.success,
        hasImageUrl: !!result.avatar?.imageUrl,
        imageUrlLength: result.avatar?.imageUrl?.length || 0,
        generationMethod: result.avatar?.generationMethod
      })
      
      let generation: GenerationResult
      
      if (result.success && result.avatar) {
        // 使用真实生成的融合图像
        generation = {
          imageUrl: result.avatar.imageUrl,
          animalType: result.avatar.animalType,
          generationMethod: result.avatar.generationMethod || 'Enhanced Fusion',
          metadata: {
            prompt: '人身动物头融合生成',
            style: 'anthropomorphic_professional',
            quality: 'high',
            fusionDetails: result.avatar.fusionDetails
          }
        }
      } else {
        // 降级到模拟生成
        console.warn('融合API失败，使用模拟生成')
        generation = {
          imageUrl: createMockAvatar(targetAnalysis),
          animalType: targetAnalysis.animalType,
          generationMethod: 'MOCK',
          metadata: {
            prompt: generatePrompt(targetAnalysis),
            style: 'anthropomorphic_professional',
            quality: 'high'
          }
        }
      }

      const mockGeneration = generation

      clearInterval(progressInterval)
      setProgress(100)
      setGenerationResult(mockGeneration)
      setCurrentStep('completed')
      
      if (onGenerationComplete) {
        onGenerationComplete(mockGeneration)
      }

      // 保存到localStorage - 确保动物园能加载用户生成的动物
      const avatarData = {
        avatarUrl: mockGeneration.imageUrl,
        animalType: mockGeneration.animalType,
        analysis: targetAnalysis,
        timestamp: Date.now()
      }
      localStorage.setItem('userGeneratedAvatar', JSON.stringify(avatarData))
      
      // 同时保存到动物园系统
      const currentAnimals = JSON.parse(localStorage.getItem('zooAnimals') || '[]')
      const userAnimal = {
        id: `ai_${Date.now()}`,
        name: 'AI绘画生成的打工人',
        type: mockGeneration.animalType,
        imageUrl: mockGeneration.imageUrl,
        avatar: mockGeneration.imageUrl,
        avatarUrl: mockGeneration.imageUrl,
        position: {
          x: 800 + Math.random() * 400,
          y: 600 + Math.random() * 300
        },
        stats: {
          energy: targetAnalysis.personality.energy || 75,
          happiness: targetAnalysis.personality.happiness || 60,
          creativity: targetAnalysis.personality.creativity || 80
        },
        activity: '刚入职',
        zone: '绘画区',
        timestamp: Date.now(),
        isUserGenerated: true,
        createdAt: new Date().toISOString()
      }
      
      // 添加到动物园动物列表
      currentAnimals.unshift(userAnimal) // 放在第一位
      localStorage.setItem('zooAnimals', JSON.stringify(currentAnimals))
      
      console.log('✅ 新生成的动物已保存到动物园:', userAnimal)

      // 注册动物到后端排行榜系统
      try {
        await fetch('/api/v1/zoo/animals/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            animalId: userAnimal.id,
            name: userAnimal.name,
            avatarUrl: userAnimal.avatarUrl,
            animalType: mockGeneration.animalType,
            createdBy: currentUser?.username || 'anonymous'
          })
        });
        console.log('✅ 动物已注册到排行榜系统');
      } catch (error) {
        console.error('❌ 注册动物到排行榜失败:', error);
      }

      toast.success('头像生成完成！')

    } catch (err) {
      setCurrentStep('error')
      setError('生成失败，请重试')
      toast.error('头像生成失败')
      console.error('Generation error:', err)
    }
  }, [analysisResult, drawingData, onGenerationComplete, updateProgress])

  // 生成AI提示词
  const generatePrompt = useCallback((analysis: AnalysisResult) => {
    const animalMap = {
      'OXHORSE': '牛马（代表勤劳踏实的打工人）',
      'PET': '宠物猫咪（代表受宠爱的员工）',
      'DIVINE': '神兽麒麟（代表能力超群的精英）'
    }

    const personalityTraits = Object.entries(analysis.personality)
      .filter(([_, value]) => value > 70)
      .map(([key, _]) => {
        const traitMap: Record<string, string> = {
          workEfficiency: '高效率',
          happiness: '快乐',
          creativity: '创造力强',
          resilience: '韧性强',
          compliance: '服从性高',
          socialSkill: '社交能力强',
          workPressure: '抗压能力强'
        }
        return traitMap[key] || key
      })

    return `一个${animalMap[analysis.animalType]}的拟人化角色，身穿现代职场服装，具有${personalityTraits.join('、')}的特征。角色应该友好、专业，体现出${analysis.traits.slice(0, 3).join('、')}的个性。背景为简洁的职场环境，整体风格现代简约，色彩温和。`
  }, [])

  // 创建模拟头像
  const createMockAvatar = useCallback((analysis: AnalysisResult) => {
    const canvas = document.createElement('canvas')
    canvas.width = 400
    canvas.height = 500
    const ctx = canvas.getContext('2d')!

    // 透明背景 - 不绘制任何背景色
    ctx.clearRect(0, 0, 400, 500)

    // 身体（西装）
    ctx.fillStyle = '#1e293b'
    ctx.fillRect(120, 250, 160, 200)

    // 衬衫
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(130, 260, 140, 120)

    // 领带
    ctx.fillStyle = '#ef4444'
    ctx.fillRect(190, 260, 20, 100)

    // 头部（根据动物类型）
    const headColors = {
      'OXHORSE': '#8b4513',
      'PET': '#ff69b4',
      'DIVINE': '#ffd700'
    }
    ctx.fillStyle = headColors[analysis.animalType]
    ctx.beginPath()
    ctx.arc(200, 180, 60, 0, Math.PI * 2)
    ctx.fill()

    // 眼睛
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.arc(185, 170, 8, 0, Math.PI * 2)
    ctx.arc(215, 170, 8, 0, Math.PI * 2)
    ctx.fill()

    // 嘴巴
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.arc(200, 185, 15, 0.2 * Math.PI, 0.8 * Math.PI)
    ctx.stroke()

    // 根据个性添加特征
    if (analysis.personality.creativity > 70) {
      // 创意帽子
      ctx.fillStyle = '#8b5cf6'
      ctx.fillRect(170, 120, 60, 20)
    }

    if (analysis.personality.workEfficiency > 80) {
      // 工作徽章
      ctx.fillStyle = '#10b981'
      ctx.beginPath()
      ctx.arc(150, 280, 10, 0, Math.PI * 2)
      ctx.fill()
    }

    // 添加文字标签
    ctx.fillStyle = '#374151'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(`${analysis.animalType === 'OXHORSE' ? '勤劳牛马' : analysis.animalType === 'PET' ? '团宠员工' : '职场精英'}`, 200, 470)
    
    return canvas.toDataURL('image/png')
  }, [])

  // 重试
  const retry = useCallback(() => {
    setIsRetrying(true)
    setCurrentStep('idle')
    setError(null)
    setProgress(0)
    setTimeout(() => {
      setIsRetrying(false)
      if (analysisResult) {
        generateAvatar()
      } else {
        analyzeDrawing()
      }
    }, 500)
  }, [analysisResult, analyzeDrawing, generateAvatar])

  // 重新开始
  const restart = useCallback(() => {
    setCurrentStep('idle')
    setProgress(0)
    setAnalysisResult(null)
    setGenerationResult(null)
    setError(null)
  }, [])

  return (
    <div className={cn('bg-white rounded-2xl p-6 shadow-soft', className)}>
      {/* 头部 */}
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-primary rounded-full mb-4">
          {currentStep === 'analyzing' ? (
            <Brain className="w-8 h-8 text-white" />
          ) : (
            <Wand2 className="w-8 h-8 text-white" />
          )}
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">AI 打工人生成器</h2>
        <p className="text-gray-600">
          {currentStep === 'idle' && '点击开始，让AI分析你的绘画并生成专属打工人形象'}
          {currentStep === 'analyzing' && '正在分析你的绘画风格和个性特征...'}
          {currentStep === 'generating' && '正在为你生成专属的打工人头像...'}
          {currentStep === 'completed' && '你的专属打工人头像已生成完成！'}
          {currentStep === 'error' && '生成过程中出现了问题'}
        </p>
      </div>

      {/* 进度区域 */}
      <AnimatePresence mode="wait">
        {currentStep === 'idle' && (
          <motion.div
            key="idle"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center"
          >
            <button
              onClick={analyzeDrawing}
              disabled={!drawingData || isRetrying}
              className={cn(
                'btn-primary btn-lg px-8',
                (!drawingData || isRetrying) && 'opacity-50 cursor-not-allowed'
              )}
            >
              {isRetrying ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  重试中...
                </>
              ) : (
                <>
                  <Brain className="w-5 h-5 mr-2" />
                  开始AI分析
                </>
              )}
            </button>
            {!drawingData && (
              <p className="text-sm text-gray-500 mt-2">请先完成你的自画像绘制</p>
            )}
          </motion.div>
        )}

        {(currentStep === 'analyzing' || currentStep === 'generating') && (
          <motion.div
            key="processing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            {/* 进度条 */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div
                className="bg-gradient-primary h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900 mb-2">
                {currentStep === 'analyzing' ? '🧠 AI正在分析中...' : '🎨 正在生成头像...'}
              </p>
              <p className="text-sm text-gray-600">{Math.round(progress)}% 完成</p>
            </div>

            {/* 处理步骤 */}
            <div className="space-y-2">
              <div className={cn(
                'flex items-center text-sm',
                currentStep === 'analyzing' ? 'text-primary-600' : 'text-green-600'
              )}>
                {currentStep === 'analyzing' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                分析绘画风格和个性特征
              </div>
              <div className={cn(
                'flex items-center text-sm',
                currentStep === 'generating' ? 'text-primary-600' : 'text-gray-400'
              )}>
                {currentStep === 'generating' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <div className="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full" />
                )}
                生成专属打工人形象
              </div>
            </div>
          </motion.div>
        )}

        {currentStep === 'completed' && analysisResult && generationResult && (
          <motion.div
            key="completed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* 生成结果 */}
            <div className="text-center">
              <img
                src={generationResult.imageUrl}
                alt="Generated Avatar"
                className="w-48 h-60 mx-auto rounded-lg shadow-lg object-cover mb-4"
              />
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                你是一只 {analysisResult.animalType === 'OXHORSE' ? '🐂 勤劳牛马' : 
                          analysisResult.animalType === 'PET' ? '🐱 团宠员工' : '🦌 职场精英'}
              </h3>
            </div>

            {/* 个性分析 */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h4 className="font-semibold text-gray-900">个性分析</h4>
              <p className="text-sm text-gray-700">{analysisResult.analysis}</p>
              <div className="flex flex-wrap gap-2">
                {analysisResult.traits.map((trait, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                  >
                    {trait}
                  </span>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              <button
                onClick={restart}
                className="btn-outline btn-md flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重新生成
              </button>
              <button
                onClick={() => {
                  // 跳转到动物园查看
                  window.location.href = '/zoo'
                }}
                className="btn-primary btn-md flex-1"
              >
                去动物园看看 🎪
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'error' && (
          <motion.div
            key="error"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-4"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">生成失败</h3>
            <p className="text-gray-600">{error}</p>
            <div className="flex space-x-3">
              <button
                onClick={retry}
                className="btn-primary btn-md"
                disabled={isRetrying}
              >
                {isRetrying ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    重试中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重试
                  </>
                )}
              </button>
              <button
                onClick={restart}
                className="btn-outline btn-md"
              >
                重新开始
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}