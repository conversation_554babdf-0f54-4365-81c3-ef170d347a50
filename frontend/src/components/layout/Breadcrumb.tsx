import { Link, useLocation } from 'react-router-dom'

interface BreadcrumbItem {
  name: string
  href: string
  icon?: string
}

const pathMap: Record<string, BreadcrumbItem> = {
  '/': { name: '首页', href: '/', icon: '🏠' },
  '/test': { name: '人格测试', href: '/test', icon: '🧠' },
  '/zoo': { name: '动物园', href: '/zoo', icon: '🎪' },
  '/profile': { name: '个人资料', href: '/profile', icon: '👤' },
  '/settings': { name: '设置', href: '/settings', icon: '⚙️' },
  '/posts': { name: '社区', href: '/posts', icon: '💬' },
  '/ranking': { name: '排行榜', href: '/ranking', icon: '🏆' },
  '/login': { name: '登录', href: '/login', icon: '🔑' },
  '/register': { name: '注册', href: '/register', icon: '📝' }
}

export default function Breadcrumb() {
  const location = useLocation()
  const currentPath = location.pathname

  // 如果是首页，不显示面包屑
  if (currentPath === '/') {
    return null
  }

  // 生成面包屑路径
  const pathSegments = currentPath.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = [
    { name: '首页', href: '/', icon: '🏠' }
  ]

  let currentHref = ''
  pathSegments.forEach(segment => {
    currentHref += `/${segment}`
    const item = pathMap[currentHref]
    if (item) {
      breadcrumbs.push(item)
    }
  })

  return (
    <nav className="bg-gray-50 border-b border-gray-200 px-4 py-3">
      <div className="max-w-7xl mx-auto">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((item, index) => (
            <li key={item.href} className="flex items-center">
              {index > 0 && (
                <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="flex items-center space-x-1 text-gray-900 font-medium">
                  <span>{item.icon}</span>
                  <span>{item.name}</span>
                </span>
              ) : (
                <Link
                  to={item.href}
                  className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <span>{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  )
}