import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAppSelector } from '@/store'
import toast from 'react-hot-toast'

export default function QuickAccess() {
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)
  const [isExpanded, setIsExpanded] = useState(false)

  const quickGoToZoo = () => {
    const userAvatar = localStorage.getItem('userGeneratedAvatar')
    const hasAvatar = !!userAvatar || !!currentUser?.avatar
    
    if (!hasAvatar) {
      toast.error('请先完成人格测试并生成你的打工人形象！')
      navigate('/test')
      return
    }
    navigate('/zoo')
  }

  const quickActions = [
    {
      icon: '🎪',
      label: '动物园',
      action: quickGoToZoo,
      shortcut: 'Z'
    },
    {
      icon: '🧠',
      label: '测试',
      action: () => navigate('/test'),
      shortcut: 'T'
    },
    {
      icon: '💬',
      label: '社区',
      action: () => navigate('/posts'),
      shortcut: 'P'
    },
    {
      icon: '🏆',
      label: '排行',
      action: () => navigate('/ranking'),
      shortcut: 'R'
    }
  ]

  return (
    <>
      {/* 快速访问悬浮球 */}
      <div className="fixed bottom-6 right-6 z-40">
        <div className="relative">
          {/* 快速操作按钮 */}
          {isExpanded && (
            <div className="absolute bottom-16 right-0 space-y-3">
              {quickActions.map((action, index) => (
                <div
                  key={action.label}
                  className="flex items-center space-x-2 animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <span className="bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-75">
                    {action.label} (Alt+{action.shortcut})
                  </span>
                  <button
                    onClick={action.action}
                    className="w-12 h-12 bg-white rounded-full shadow-lg border-2 border-gray-200 flex items-center justify-center text-xl hover:scale-110 transition-transform"
                    title={`${action.label} (Alt+${action.shortcut})`}
                  >
                    {action.icon}
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* 主按钮 */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`w-14 h-14 bg-indigo-600 rounded-full shadow-lg flex items-center justify-center text-white text-xl hover:bg-indigo-700 transition-all duration-300 ${
              isExpanded ? 'rotate-45' : ''
            }`}
            title="快速访问菜单"
          >
            {isExpanded ? '✕' : '⚡'}
          </button>
        </div>
      </div>

      {/* 点击外部关闭 */}
      {isExpanded && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* 添加CSS动画 */}
      <style>{`
        @keyframes slide-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-slide-up {
          animation: slide-up 0.3s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </>
  )
}