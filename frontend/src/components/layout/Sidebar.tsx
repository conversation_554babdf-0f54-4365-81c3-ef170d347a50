import { Link, useLocation } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '@/store'
import { toggleSidebar } from '@/store/slices/uiSlice'

interface SidebarProps {
  isOpen: boolean
}

export default function Sidebar({ isOpen }: SidebarProps) {
  const location = useLocation()
  const dispatch = useAppDispatch()
  const { currentUser } = useAppSelector(state => state.user)

  const handleCloseSidebar = () => {
    dispatch(toggleSidebar())
  }

  const isActivePath = (path: string) => location.pathname === path

  const navItems = [
    { path: '/', icon: '🏠', label: '首页' },
    { path: '/test', icon: '🧠', label: '人格测试' },
    { path: '/zoo', icon: '🎪', label: '动物园', highlight: true },
    { path: '/posts', icon: '💬', label: '社区' },
    { path: '/ranking', icon: '🏆', label: '排行榜' },
    { path: '/profile', icon: '👤', label: '个人资料' },
    { path: '/settings', icon: '⚙️', label: '设置' },
  ]

  return (
    <>
      {/* 遮罩层 */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleCloseSidebar}
        />
      )}

      {/* 侧边栏 */}
      <div className={`
        fixed top-16 left-0 h-full w-64 glass-effect border-r-4 border-white/30 transform transition-transform duration-300 ease-in-out z-50 shadow-xl
        md:relative md:top-0 md:translate-x-0 md:shadow-none
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* 用户信息区域 */}
          <div className="p-6 bg-gradient-to-br from-purple-100/80 to-pink-100/80 backdrop-blur-sm border-b-4 border-white/30">
            <div className="flex items-center space-x-4 animate-slide-up">
              <div className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg shadow-lg animate-pulse-glow">
                {currentUser?.username?.charAt(0).toUpperCase() || '🐂'}
              </div>
              <div>
                <p className="text-base font-bold text-purple-800 drop-shadow-sm">
                  {currentUser?.username || '打工人'}
                </p>
                <p className="text-sm text-purple-600 animate-wiggle">
                  🌟 在线打工中... ✨
                </p>
              </div>
            </div>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                onClick={handleCloseSidebar}
                className={`
                  group flex items-center px-4 py-3 text-sm font-bold rounded-2xl transition-all duration-300 transform hover:scale-105
                  ${isActivePath(item.path)
                    ? 'bg-gradient-to-r from-purple-400 to-pink-400 text-white shadow-lg animate-pulse-glow'
                    : 'text-purple-700 hover:bg-white/60 hover:text-purple-800 hover:shadow-md'
                  }
                  ${item.highlight ? 'ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-100 to-orange-100 animate-wiggle' : ''}
                `}
              >
                <span className="text-xl mr-3 animate-bounce-gentle">{item.icon}</span>
                <span className="drop-shadow-sm">{item.label}</span>
                
                {/* 动物园特殊标识 */}
                {item.highlight && (
                  <span className="ml-auto">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-md animate-pulse-glow">
                      🔥HOT
                    </span>
                  </span>
                )}
                
                {/* 活跃路径指示器 */}
                {isActivePath(item.path) && (
                  <span className="ml-auto">
                    <div className="h-3 w-3 bg-white rounded-full shadow-lg animate-bounce-gentle"></div>
                  </span>
                )}
              </Link>
            ))}
          </nav>

          {/* 快速操作区域 */}
          <div className="p-4 bg-gradient-to-br from-pink-100/80 to-purple-100/80 backdrop-blur-sm border-t-4 border-white/30">
            <div className="space-y-3">
              <Link
                to="/zoo"
                onClick={handleCloseSidebar}
                className="btn btn-primary btn-sm w-full animate-wiggle"
              >
                <span className="mr-2 text-lg">🎪</span>
                快速进入动物园
              </Link>
              
              <Link
                to="/test"
                onClick={handleCloseSidebar}
                className="btn btn-secondary btn-sm w-full animate-float"
              >
                <span className="mr-2 text-lg">🧠</span>
                开始人格测试
              </Link>
            </div>
          </div>

          {/* 版本信息 */}
          <div className="p-4 text-center text-xs text-purple-600 bg-gradient-to-r from-purple-50 to-pink-50 backdrop-blur-sm animate-pulse-glow">
            <p className="font-bold">🎪 牛马动物园 v1.0 ✨</p>
            <p className="animate-wiggle">© 2024 打工人专属 🐂</p>
          </div>
        </div>
      </div>
    </>
  )
}