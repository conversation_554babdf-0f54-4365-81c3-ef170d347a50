import { ReactNode } from 'react'
import { Outlet } from 'react-router-dom'

import Header from './Header'
import Sidebar from './Sidebar'
import Footer from './Footer'
import Breadcrumb from './Breadcrumb'
import QuickAccess from './QuickAccess'
import { useAppSelector } from '@/store'

interface LayoutProps {
  children?: ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { sidebarOpen } = useAppSelector(state => state.ui)

  return (
    <div className="min-h-screen bg-light-contrast">
      {/* 顶部导航栏 */}
      <Header />
      
      <div className="flex">
        {/* 侧边栏 */}
        <Sidebar isOpen={sidebarOpen} />
        
        {/* 主内容区域 */}
        <main className="flex-1 min-h-screen pt-16">
          {/* 面包屑导航 */}
          <Breadcrumb />
          
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {children || <Outlet />}
          </div>
        </main>
      </div>
      
      {/* 底部信息 */}
      <Footer />
      
      {/* 快速访问工具栏 */}
      <QuickAccess />
    </div>
  )
}

export default Layout