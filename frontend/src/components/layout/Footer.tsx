import { Link } from 'react-router-dom'

export default function Footer() {
  return (
    <footer className="glass-effect border-t-4 border-white/30 mt-20">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo和描述 */}
          <div className="col-span-1 md:col-span-2 animate-slide-up">
            <div className="flex items-center space-x-3 mb-6">
              <span className="text-4xl animate-bounce-gentle">🎪</span>
              <span className="text-2xl font-bold text-purple-800 drop-shadow-lg animate-float">✨ 牛马动物园</span>
            </div>
            <p className="text-purple-700 text-base max-w-md leading-relaxed drop-shadow-sm">
              🌟 专为打工人打造的虚拟动物园，测试你的人格，生成专属形象，在虚拟世界中与其他牛马一起工作生活。✨
            </p>
          </div>

          {/* 快速链接 */}
          <div className="animate-slide-up">
            <h3 className="text-lg font-bold text-purple-800 mb-6 animate-wiggle">
              🚀 快速导航
            </h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  🏠 首页
                </Link>
              </li>
              <li>
                <Link to="/test" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  🧠 人格测试
                </Link>
              </li>
              <li>
                <Link to="/zoo" className="text-green-600 hover:text-green-800 text-sm font-bold animate-pulse-glow transition-all duration-300 hover:scale-105 inline-block">
                  🎪 动物园 ⭐
                </Link>
              </li>
              <li>
                <Link to="/posts" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  💬 社区
                </Link>
              </li>
              <li>
                <Link to="/ranking" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  🏆 排行榜
                </Link>
              </li>
            </ul>
          </div>

          {/* 支持 */}
          <div className="animate-slide-up">
            <h3 className="text-lg font-bold text-purple-800 mb-6 animate-float">
              🛠️ 支持中心
            </h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  📖 使用指南
                </a>
              </li>
              <li>
                <a href="#" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  💡 常见问题
                </a>
              </li>
              <li>
                <a href="#" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  📞 联系我们
                </a>
              </li>
              <li>
                <a href="#" className="text-purple-600 hover:text-purple-800 text-sm font-medium transition-all duration-300 hover:scale-105 inline-block">
                  🐛 反馈问题
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* 分割线 */}
        <div className="mt-12 border-t-4 border-white/30 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center animate-slide-up">
            <p className="text-purple-700 text-base font-medium drop-shadow-sm animate-pulse-glow">
              © 2024 🎪 牛马动物园 ✨ 所有权利保留 🐂 专为打工人设计 💼
            </p>
            
            {/* 快速操作按钮 */}
            <div className="mt-6 md:mt-0 flex space-x-4">
              <Link
                to="/zoo"
                className="btn btn-primary btn-md animate-wiggle"
              >
                🎪 进入动物园
              </Link>
              <Link
                to="/test"
                className="btn btn-secondary btn-md animate-float"
              >
                🧠 开始测试
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}