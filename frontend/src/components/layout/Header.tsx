import { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '@/store'
import { logout } from '@/store/slices/authSlice'
import { toggleSidebar } from '@/store/slices/uiSlice'
import toast from 'react-hot-toast'

export default function Header() {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()
  const { currentUser } = useAppSelector(state => state.user)
  const { isAuthenticated } = useAppSelector(state => state.auth)
  const [showUserMenu, setShowUserMenu] = useState(false)

  const handleLogout = () => {
    dispatch(logout())
    toast.success('已退出登录')
    navigate('/login')
  }

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar())
  }

  const isActivePath = (path: string) => location.pathname === path

  return (
    <header className="bg-white/10 backdrop-blur-md border-b-4 border-white/20 fixed top-0 left-0 right-0 z-50 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo和标题 */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handleToggleSidebar}
              className="p-3 rounded-full text-white hover:bg-white/20 md:hidden transition-all duration-300"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <Link to="/" className="flex items-center space-x-3 animate-float">
              <span className="text-4xl animate-bounce-gentle">🎪</span>
              <span className="text-2xl font-bold text-white drop-shadow-lg">✨ 牛马动物园</span>
            </Link>
          </div>

          {/* 导航菜单 */}
          <nav className="hidden md:flex space-x-8">
            <Link
              to="/"
              className={`text-sm font-medium transition-colors ${
                isActivePath('/') 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-500 hover:text-gray-900'
              } pb-4 px-2`}
            >
              🏠 首页
            </Link>
            
            <Link
              to="/test"
              className={`text-sm font-medium transition-colors ${
                isActivePath('/test') 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-500 hover:text-gray-900'
              } pb-4 px-2`}
            >
              🧠 人格测试
            </Link>
            
            <Link
              to="/zoo"
              className={`text-sm font-medium transition-colors ${
                isActivePath('/zoo') 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-500 hover:text-gray-900'
              } pb-4 px-2`}
            >
              🎪 动物园
            </Link>
            
            <Link
              to="/posts"
              className={`text-sm font-medium transition-colors ${
                isActivePath('/posts') 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-500 hover:text-gray-900'
              } pb-4 px-2`}
            >
              💬 社区
            </Link>
            
            <Link
              to="/ranking"
              className={`text-sm font-medium transition-colors ${
                isActivePath('/ranking') 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-500 hover:text-gray-900'
              } pb-4 px-2`}
            >
              🏆 排行榜
            </Link>
          </nav>

          {/* 用户菜单 */}
          <div className="flex items-center space-x-4">
            {isAuthenticated && currentUser ? (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 p-2 hover:bg-gray-100"
                >
                  <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center text-white font-semibold">
                    {currentUser.username?.charAt(0).toUpperCase() || '🐂'}
                  </div>
                  <span className="hidden md:block text-gray-700">{currentUser.username}</span>
                  <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* 用户下拉菜单 */}
                {showUserMenu && (
                  <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                    <div className="py-1">
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        👤 个人资料
                      </Link>
                      <Link
                        to="/settings"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        ⚙️ 设置
                      </Link>
                      <Link
                        to="/zoo"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 font-semibold"
                        onClick={() => setShowUserMenu(false)}
                      >
                        🎪 我的动物园
                      </Link>
                      <hr className="my-1" />
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          handleLogout()
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                      >
                        🚪 退出登录
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="text-sm font-medium text-gray-500 hover:text-gray-900"
                >
                  登录
                </Link>
                <Link
                  to="/register"
                  className="text-sm font-medium bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
                >
                  注册
                </Link>
              </div>
            )}

            {/* 快速进入动物园按钮 */}
            {isAuthenticated && (
              <Link
                to="/zoo"
                className="hidden sm:inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"
              >
                🎪 动物园
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* 点击外部关闭用户菜单 */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  )
}