import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../../test/utils/test-utils'
import LoginPage from '../../pages/auth/LoginPage'

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    Link: ({ children, to }: any) => <a href={to}>{children}</a>,
  }
})

describe('LoginPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear()
  })

  it('应该渲染登录表单', () => {
    renderWithProviders(<LoginPage />)

    expect(screen.getByRole('heading', { name: /登录/ })).toBeInTheDocument()
    expect(screen.getByLabelText(/邮箱/)).toBeInTheDocument()
    expect(screen.getByLabelText(/密码/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /登录/ })).toBeInTheDocument()
    expect(screen.getByText(/还没有账号/)).toBeInTheDocument()
  })

  it('应该显示表单验证错误', async () => {
    renderWithProviders(<LoginPage />)

    const submitButton = screen.getByRole('button', { name: /登录/ })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/邮箱是必填项/)).toBeInTheDocument()
      expect(screen.getByText(/密码是必填项/)).toBeInTheDocument()
    })
  })

  it('应该显示邮箱格式错误', async () => {
    renderWithProviders(<LoginPage />)

    const emailInput = screen.getByLabelText(/邮箱/)
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.blur(emailInput)

    await waitFor(() => {
      expect(screen.getByText(/邮箱格式不正确/)).toBeInTheDocument()
    })
  })

  it('应该成功提交表单', async () => {
    renderWithProviders(<LoginPage />)

    const emailInput = screen.getByLabelText(/邮箱/)
    const passwordInput = screen.getByLabelText(/密码/)
    const submitButton = screen.getByRole('button', { name: /登录/ })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'Password123!' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/')
    })
  })

  it('应该处理登录错误', async () => {
    // 设置MSW返回错误响应
    renderWithProviders(<LoginPage />)

    const emailInput = screen.getByLabelText(/邮箱/)
    const passwordInput = screen.getByLabelText(/密码/)
    const submitButton = screen.getByRole('button', { name: /登录/ })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)

    // 因为MSW handler会返回成功响应，这里模拟错误情况
    // 在实际测试中，可以使用server.use()来覆盖特定的handler
  })

  it('应该切换密码可见性', () => {
    renderWithProviders(<LoginPage />)

    const passwordInput = screen.getByLabelText(/密码/) as HTMLInputElement
    const toggleButton = screen.getByRole('button', { name: /显示密码/ })

    expect(passwordInput.type).toBe('password')

    fireEvent.click(toggleButton)
    expect(passwordInput.type).toBe('text')

    fireEvent.click(toggleButton)
    expect(passwordInput.type).toBe('password')
  })

  it('应该显示记住我选项', () => {
    renderWithProviders(<LoginPage />)

    const rememberCheckbox = screen.getByLabelText(/记住我/)
    expect(rememberCheckbox).toBeInTheDocument()
    expect(rememberCheckbox).not.toBeChecked()

    fireEvent.click(rememberCheckbox)
    expect(rememberCheckbox).toBeChecked()
  })

  it('应该有忘记密码链接', () => {
    renderWithProviders(<LoginPage />)

    const forgotPasswordLink = screen.getByText(/忘记密码/)
    expect(forgotPasswordLink).toBeInTheDocument()
    expect(forgotPasswordLink.getAttribute('href')).toBe('/auth/forgot-password')
  })

  it('应该有注册链接', () => {
    renderWithProviders(<LoginPage />)

    const registerLink = screen.getByText(/立即注册/)
    expect(registerLink).toBeInTheDocument()
    expect(registerLink.getAttribute('href')).toBe('/auth/register')
  })

  it('应该在提交时显示加载状态', async () => {
    renderWithProviders(<LoginPage />)

    const emailInput = screen.getByLabelText(/邮箱/)
    const passwordInput = screen.getByLabelText(/密码/)
    const submitButton = screen.getByRole('button', { name: /登录/ })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'Password123!' } })
    fireEvent.click(submitButton)

    // 检查按钮是否显示加载状态
    expect(submitButton).toBeDisabled()
    expect(screen.getByText(/登录中.../)).toBeInTheDocument()
  })
})