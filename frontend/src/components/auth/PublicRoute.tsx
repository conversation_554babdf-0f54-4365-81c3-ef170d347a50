import { Navigate, useLocation } from 'react-router-dom'
import { useAppSelector } from '@/store'

interface PublicRouteProps {
  children: React.ReactNode
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const location = useLocation()
  const { isAuthenticated } = useAppSelector(state => state.auth)

  // 如果已经认证，则跳转到目标页面或首页
  if (isAuthenticated) {
    const from = (location.state as any)?.from || '/'
    return <Navigate to={from} replace />
  }

  return <>{children}</>
}

export default PublicRoute