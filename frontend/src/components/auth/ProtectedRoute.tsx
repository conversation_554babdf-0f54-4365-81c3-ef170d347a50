import { Navigate, useLocation } from 'react-router-dom'
import { useAppSelector } from '@/store'
import PageLoader from '@/components/ui/PageLoader'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback = <PageLoader /> 
}) => {
  const location = useLocation()
  const { isAuthenticated, loading } = useAppSelector(state => state.auth)

  // 正在验证认证状态时显示加载器
  if (loading) {
    return <>{fallback}</>
  }

  // 未认证则跳转到登录页，并保存当前页面路径
  if (!isAuthenticated) {
    return (
      <Navigate 
        to="/login" 
        state={{ from: location.pathname + location.search }} 
        replace 
      />
    )
  }

  return <>{children}</>
}

export default ProtectedRoute