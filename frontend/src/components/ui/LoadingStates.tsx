import { motion, AnimatePresence } from 'framer-motion'
import { Loader2, Refresh<PERSON><PERSON>, AlertCircle, CheckCircle } from 'lucide-react'
import { cn } from '@/utils'

// 基本加载状态组件
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  color?: string
  className?: string
}

export function LoadingSpinner({ size = 'md', color, className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  }
  
  return (
    <Loader2 
      className={cn(
        'animate-spin',
        sizeClasses[size],
        className
      )}
      style={{ color }}
    />
  )
}

// 验证状态组件
interface ValidationStateProps {
  state: 'idle' | 'validating' | 'valid' | 'invalid'
  message?: string
  className?: string
}

export function ValidationState({ state, message, className }: ValidationStateProps) {
  const getIcon = () => {
    switch (state) {
      case 'validating':
        return <LoadingSpinner size="sm" />
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'invalid':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return null
    }
  }
  
  const getMessageColor = () => {
    switch (state) {
      case 'valid':
        return 'text-green-600'
      case 'invalid':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }
  
  return (
    <AnimatePresence mode="wait">
      {(state !== 'idle' || message) && (
        <motion.div
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -5 }}
          className={cn('flex items-center space-x-2 mt-1', className)}
        >
          {getIcon()}
          {message && (
            <span className={cn('text-sm', getMessageColor())}>
              {message}
            </span>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 按钮加载状态
interface LoadingButtonProps {
  isLoading?: boolean
  loadingText?: string
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingButton({
  isLoading = false,
  loadingText,
  children,
  onClick,
  disabled,
  variant = 'primary',
  size = 'md',
  className
}: LoadingButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 disabled:bg-primary-300',
    secondary: 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-500 disabled:bg-gray-300',
    outline: 'border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500 disabled:border-primary-300 disabled:text-primary-300'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        'disabled:cursor-not-allowed',
        className
      )}
    >
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center space-x-2"
          >
            <LoadingSpinner size={size === 'lg' ? 'md' : 'sm'} />
            <span>{loadingText || '加载中...'}</span>
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </button>
  )
}

// 页面加载状态
interface PageLoadingProps {
  title?: string
  description?: string
  showSpinner?: boolean
  className?: string
}

export function PageLoading({ 
  title = '加载中...',
  description,
  showSpinner = true,
  className 
}: PageLoadingProps) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center min-h-64 p-8',
      className
    )}>
      {showSpinner && (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          className="mb-4"
        >
          <LoadingSpinner size="lg" color="#3B82F6" />
        </motion.div>
      )}
      
      <motion.h2
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-xl font-semibold text-gray-900 mb-2"
      >
        {title}
      </motion.h2>
      
      {description && (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-gray-600 text-center max-w-md"
        >
          {description}
        </motion.p>
      )}
    </div>
  )
}

// 列表加载状态
interface ListLoadingProps {
  itemCount?: number
  itemHeight?: number
  className?: string
}

export function ListLoading({ itemCount = 5, itemHeight = 60, className }: ListLoadingProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: itemCount }, (_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-lg p-4 shadow-sm border"
          style={{ height: itemHeight }}
        >
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

// 卡片加载状态
interface CardLoadingProps {
  cardCount?: number
  showImage?: boolean
  className?: string
}

export function CardLoading({ cardCount = 3, showImage = true, className }: CardLoadingProps) {
  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: cardCount }, (_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-lg shadow-sm border overflow-hidden"
        >
          {showImage && (
            <div className="h-48 bg-gray-200 animate-pulse" />
          )}
          <div className="p-6 space-y-4">
            <div className="h-6 bg-gray-200 rounded animate-pulse" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse" />
            </div>
            <div className="flex space-x-2">
              <div className="h-8 bg-gray-200 rounded w-20 animate-pulse" />
              <div className="h-8 bg-gray-200 rounded w-16 animate-pulse" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

// 进度条加载状态
interface ProgressLoadingProps {
  progress: number
  title?: string
  description?: string
  showPercentage?: boolean
  color?: string
  className?: string
}

export function ProgressLoading({
  progress,
  title,
  description,
  showPercentage = true,
  color = '#3B82F6',
  className
}: ProgressLoadingProps) {
  const clampedProgress = Math.min(Math.max(progress, 0), 100)
  
  return (
    <div className={cn('w-full', className)}>
      {(title || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {title && (
            <span className="text-sm font-medium text-gray-700">{title}</span>
          )}
          {showPercentage && (
            <span className="text-sm text-gray-500">{Math.round(clampedProgress)}%</span>
          )}
        </div>
      )}
      
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <motion.div
          className="h-full rounded-full"
          style={{ backgroundColor: color }}
          initial={{ width: 0 }}
          animate={{ width: `${clampedProgress}%` }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        />
      </div>
      
      {description && (
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      )}
    </div>
  )
}

// 数据获取状态
interface DataLoadingStateProps {
  state: 'loading' | 'success' | 'error' | 'empty'
  title?: string
  message?: string
  onRetry?: () => void
  retryText?: string
  emptyStateIllustration?: React.ReactNode
  className?: string
}

export function DataLoadingState({
  state,
  title,
  message,
  onRetry,
  retryText = '重试',
  emptyStateIllustration,
  className
}: DataLoadingStateProps) {
  const getContent = () => {
    switch (state) {
      case 'loading':
        return (
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {title || '加载数据中...'}
            </h3>
            {message && (
              <p className="text-gray-600">{message}</p>
            )}
          </div>
        )
        
      case 'error':
        return (
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {title || '加载失败'}
            </h3>
            {message && (
              <p className="text-gray-600 mb-4">{message}</p>
            )}
            {onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                {retryText}
              </button>
            )}
          </div>
        )
        
      case 'empty':
        return (
          <div className="text-center">
            {emptyStateIllustration || (
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-3xl text-gray-400">📄</span>
              </div>
            )}
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {title || '暂无数据'}
            </h3>
            {message && (
              <p className="text-gray-600">{message}</p>
            )}
          </div>
        )
        
      default:
        return null
    }
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn('flex items-center justify-center min-h-64 p-8', className)}
    >
      {getContent()}
    </motion.div>
  )
}
