import { useState, useEffect, ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, X, ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/utils'

interface ResponsiveLayoutProps {
  children: ReactNode
  sidebar?: ReactNode
  header?: ReactNode
  footer?: ReactNode
  className?: string
  sidebarClassName?: string
  contentClassName?: string
}

interface BreakpointState {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  width: number
  height: number
}

const BREAKPOINTS = {
  mobile: 640,
  tablet: 1024,
  desktop: 1280
}

export default function ResponsiveLayout({
  children,
  sidebar,
  header,
  footer,
  className,
  sidebarClassName,
  contentClassName
}: ResponsiveLayoutProps) {
  const [breakpoint, setBreakpoint] = useState<BreakpointState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    width: 0,
    height: 0
  })
  
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  
  // 响应式断点检测
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setBreakpoint({
        isMobile: width < BREAKPOINTS.mobile,
        isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet,
        isDesktop: width >= BREAKPOINTS.desktop,
        width,
        height
      })
      
      // 移动端默认收起侧边栏
      if (width < BREAKPOINTS.mobile) {
        setSidebarOpen(false)
        setSidebarCollapsed(false)
      }
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  // 自动关闭移动端侧边栏
  useEffect(() => {
    if (breakpoint.isMobile && sidebarOpen) {
      const handleClickOutside = (e: MouseEvent) => {
        const target = e.target as Element
        if (!target.closest('.sidebar') && !target.closest('.sidebar-toggle')) {
          setSidebarOpen(false)
        }
      }
      
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [breakpoint.isMobile, sidebarOpen])
  
  const getSidebarWidth = () => {
    if (breakpoint.isMobile) {
      return sidebarOpen ? '280px' : '0px'
    }
    if (sidebarCollapsed) {
      return '64px'
    }
    return '320px'
  }
  
  const getContentMargin = () => {
    if (breakpoint.isMobile) {
      return '0px'
    }
    if (sidebarCollapsed) {
      return '64px'
    }
    return '320px'
  }
  
  return (
    <div className={cn('min-h-screen bg-gray-50 flex flex-col', className)}>
      {/* 顶部导航栏 */}
      {header && (
        <motion.header 
          className={cn(
            'sticky top-0 z-40 bg-white shadow-sm border-b',
            !breakpoint.isMobile && 'transition-all duration-300'
          )}
          style={{
            marginLeft: !breakpoint.isMobile ? getContentMargin() : '0px'
          }}
        >
          <div className="flex items-center justify-between px-4 py-3">
            {/* 移动端菜单按钮 */}
            {breakpoint.isMobile && sidebar && (
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="sidebar-toggle p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {sidebarOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </button>
            )}
            
            <div className="flex-1">{header}</div>
          </div>
        </motion.header>
      )}
      
      <div className="flex flex-1 relative">
        {/* 侧边栏 */}
        <AnimatePresence>
          {sidebar && (
            <>
              {/* 移动端背景遮罩 */}
              {breakpoint.isMobile && sidebarOpen && (
                <motion.div
                  className="fixed inset-0 z-30 bg-black/50"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={() => setSidebarOpen(false)}
                />
              )}
              
              <motion.aside
                className={cn(
                  'sidebar bg-white border-r shadow-sm z-40',
                  breakpoint.isMobile
                    ? 'fixed left-0 top-0 h-full'
                    : 'sticky top-0 h-screen flex-shrink-0',
                  sidebarClassName
                )}
                initial={false}
                animate={{
                  width: getSidebarWidth(),
                  x: breakpoint.isMobile && !sidebarOpen ? '-100%' : '0%'
                }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                {/* 桁面端折叠按钮 */}
                {!breakpoint.isMobile && (
                  <button
                    onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                    className="absolute -right-3 top-1/2 -translate-y-1/2 w-6 h-6 bg-white border rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors z-50"
                  >
                    {sidebarCollapsed ? (
                      <ChevronRight className="w-3 h-3" />
                    ) : (
                      <ChevronLeft className="w-3 h-3" />
                    )}
                  </button>
                )}
                
                <div className={cn(
                  'h-full overflow-hidden',
                  sidebarCollapsed && 'px-2'
                )}>
                  {sidebar}
                </div>
              </motion.aside>
            </>
          )}
        </AnimatePresence>
        
        {/* 主内容区域 */}
        <motion.main 
          className={cn(
            'flex-1 flex flex-col min-h-0',
            contentClassName
          )}
          animate={{
            marginLeft: !breakpoint.isMobile ? getContentMargin() : '0px'
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          <div className="flex-1 overflow-auto">
            {children}
          </div>
          
          {/* 底部 */}
          {footer && (
            <footer className="bg-white border-t mt-auto">
              {footer}
            </footer>
          )}
        </motion.main>
      </div>
    </div>
  )
}

// 自定义Hook用于获取响应式状态
export function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<BreakpointState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    width: 0,
    height: 0
  })
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setBreakpoint({
        isMobile: width < BREAKPOINTS.mobile,
        isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet,
        isDesktop: width >= BREAKPOINTS.desktop,
        width,
        height
      })
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  return breakpoint
}

// 响应式网格组件
interface ResponsiveGridProps {
  children: ReactNode
  cols?: {
    mobile?: number
    tablet?: number
    desktop?: number
  }
  gap?: number
  className?: string
}

export function ResponsiveGrid({ 
  children, 
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 6,
  className 
}: ResponsiveGridProps) {
  const { isMobile, isTablet } = useResponsive()
  
  const getGridCols = () => {
    if (isMobile) return cols.mobile || 1
    if (isTablet) return cols.tablet || 2
    return cols.desktop || 3
  }
  
  return (
    <div 
      className={cn('grid', className)}
      style={{
        gridTemplateColumns: `repeat(${getGridCols()}, minmax(0, 1fr))`,
        gap: `${gap * 4}px`
      }}
    >
      {children}
    </div>
  )
}

// 响应式容器组件
interface ResponsiveContainerProps {
  children: ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: {
    mobile?: number
    tablet?: number
    desktop?: number
  }
  className?: string
}

const MAX_WIDTH_CLASSES = {
  sm: 'max-w-sm',
  md: 'max-w-md', 
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  full: 'max-w-full'
}

export function ResponsiveContainer({
  children,
  maxWidth = 'full',
  padding = { mobile: 4, tablet: 6, desktop: 8 },
  className
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useResponsive()
  
  const getPadding = () => {
    if (isMobile) return `${(padding.mobile || 4) * 4}px`
    if (isTablet) return `${(padding.tablet || 6) * 4}px`
    return `${(padding.desktop || 8) * 4}px`
  }
  
  return (
    <div 
      className={cn(
        'mx-auto w-full',
        MAX_WIDTH_CLASSES[maxWidth],
        className
      )}
      style={{ padding: getPadding() }}
    >
      {children}
    </div>
  )
}
