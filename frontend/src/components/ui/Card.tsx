import { HTMLAttributes, forwardRef } from 'react'
import { clsx } from 'clsx'

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'interactive' | 'primary' | 'success' | 'warning' | 'error'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ 
    className, 
    variant = 'default', 
    padding = 'md',
    children, 
    ...props 
  }, ref) => {
    return (
      <div
        className={clsx(
          'card',
          {
            'card-elevated': variant === 'elevated',
            'card-interactive': variant === 'interactive',
            'card-primary': variant === 'primary',
            'card-success': variant === 'success',
            'card-warning': variant === 'warning',
            'card-error': variant === 'error',
            'p-0': padding === 'none',
            'p-4': padding === 'sm',
            'p-6': padding === 'md',
            'p-8': padding === 'lg',
          },
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

// Card Header Component
interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  title?: string
  subtitle?: string
}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, title, subtitle, children, ...props }, ref) => {
    return (
      <div
        className={clsx('border-b border-neutral-200 pb-4 mb-6', className)}
        ref={ref}
        {...props}
      >
        {title && (
          <h3 className="text-lg font-medium text-neutral-900 mb-1">
            {title}
          </h3>
        )}
        {subtitle && (
          <p className="text-sm text-neutral-600">
            {subtitle}
          </p>
        )}
        {children}
      </div>
    )
  }
)

CardHeader.displayName = 'CardHeader'

// Card Content Component
const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={clsx('', className)}
        ref={ref}
        {...props}
      />
    )
  }
)

CardContent.displayName = 'CardContent'

// Card Footer Component
const CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={clsx('border-t border-neutral-200 pt-4 mt-6', className)}
        ref={ref}
        {...props}
      />
    )
  }
)

CardFooter.displayName = 'CardFooter'

export { Card, CardHeader, CardContent, CardFooter }
export default Card
