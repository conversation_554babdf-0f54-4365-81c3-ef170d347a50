import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Al<PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('错误边界捕获到错误:', error, errorInfo)
    this.setState({ error, errorInfo })
    
    // 这里可以上报错误到监控服务
    // errorUtils.logError(error, 'ErrorBoundary')
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  handleRefresh = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用自定义的
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
          <div className="max-w-md w-full text-center">
            {/* 错误图标 */}
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-10 h-10 text-red-500" />
              </div>
              <div className="text-6xl mb-4">😵</div>
            </div>

            {/* 错误信息 */}
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              哎呀，出现了一些问题
            </h1>
            <p className="text-gray-600 mb-6">
              牛马动物园遇到了一个意外错误，请尝试刷新页面或联系我们。
            </p>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={this.handleReset}
                className="btn-primary btn-md w-full"
              >
                <RefreshCw className="w-5 h-5 mr-2" />
                重试
              </button>
              
              <button
                onClick={this.handleRefresh}
                className="btn-outline btn-md w-full"
              >
                刷新页面
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="btn-ghost btn-md w-full"
              >
                <Home className="w-5 h-5 mr-2" />
                返回首页
              </button>
            </div>

            {/* 开发环境下显示错误详情 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  查看错误详情
                </summary>
                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-xs text-red-700 font-mono text-left overflow-auto">
                  <div className="mb-2">
                    <strong>错误信息：</strong>
                    <br />
                    {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <strong>错误堆栈：</strong>
                      <br />
                      <pre className="whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                  )}
                  {this.state.errorInfo && (
                    <div className="mt-2">
                      <strong>组件堆栈：</strong>
                      <br />
                      <pre className="whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary