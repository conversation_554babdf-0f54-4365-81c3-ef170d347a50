import { Loader2 } from 'lucide-react'

interface PageLoaderProps {
  size?: 'sm' | 'md' | 'lg'
  message?: string
  className?: string
}

const PageLoader: React.FC<PageLoaderProps> = ({ 
  size = 'lg', 
  message = '正在加载...',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12'
  }

  return (
    <div className={`flex flex-col items-center justify-center min-h-screen ${className}`}>
      <div className="text-center">
        {/* 动物图标 + 加载动画 */}
        <div className="relative mb-6">
          <div className="animate-pulse text-6xl mb-4">🐄</div>
          <Loader2 className={`${sizeClasses[size]} text-primary-500 animate-spin mx-auto`} />
        </div>
        
        {/* 加载文本 */}
        <p className="text-gray-600 text-lg font-medium mb-2">{message}</p>
        <p className="text-gray-400 text-sm">牛马动物园正在准备中...</p>
        
        {/* 加载进度条 */}
        <div className="mt-6 w-64 mx-auto">
          <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full animate-pulse" 
                 style={{ width: '60%' }} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageLoader