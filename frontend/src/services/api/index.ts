import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios'
import { 
  ApiResponse, 
  PaginatedResponse,
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  RefreshTokenRequest,
  User,
  UserProfile,
  UserStats,
  Post,
  Comment,
  Animal,
  TestResult,
  TestQuestion,
  Ranking
} from '@/types'

// 创建 axios 实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器 - 添加认证token
  client.interceptors.request.use(
    config => {
      const token = localStorage.getItem('accessToken')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    error => Promise.reject(error)
  )

  // 响应拦截器 - 处理错误和token刷新
  client.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as any

      // Token 过期，尝试刷新
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            const response = await axios.post('/api/auth/refresh', {
              refreshToken,
            })

            const { accessToken, refreshToken: newRefreshToken } = response.data.data
            localStorage.setItem('accessToken', accessToken)
            localStorage.setItem('refreshToken', newRefreshToken)

            // 重试原始请求
            originalRequest.headers.Authorization = `Bearer ${accessToken}`
            return client(originalRequest)
          }
        } catch (refreshError) {
          // 刷新失败，清除本地token并跳转到登录页
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )

  return client
}

const apiClient = createApiClient()

// 通用API响应处理
const handleApiResponse = <T>(response: AxiosResponse<ApiResponse<T>>): ApiResponse<T> => {
  return response.data
}

// 认证相关API
export const authApi = {
  login: (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> =>
    apiClient.post('/api/auth/login', credentials).then(handleApiResponse),

  register: (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> =>
    apiClient.post('/api/auth/register', userData).then(handleApiResponse),

  refreshToken: (data: RefreshTokenRequest): Promise<ApiResponse<AuthResponse>> =>
    apiClient.post('/api/auth/refresh', data).then(handleApiResponse),

  logout: (data: RefreshTokenRequest): Promise<ApiResponse<void>> =>
    apiClient.post('/api/auth/logout', data).then(handleApiResponse),

  forgotPassword: (email: string): Promise<ApiResponse<void>> =>
    apiClient.post('/api/auth/forgot-password', { email }).then(handleApiResponse),

  resetPassword: (token: string, password: string): Promise<ApiResponse<void>> =>
    apiClient.post('/api/auth/reset-password', { token, password }).then(handleApiResponse),
}

// 用户相关API
export const userApi = {
  getCurrentUser: (): Promise<ApiResponse<User>> =>
    apiClient.get('/api/users/profile').then(handleApiResponse),

  getUserProfile: (userId: string): Promise<ApiResponse<UserProfile>> =>
    apiClient.get(`/users/${userId}`).then(handleApiResponse),

  getUserStats: (userId: string): Promise<ApiResponse<UserStats>> =>
    apiClient.get(`/users/${userId}/stats`).then(handleApiResponse),

  updateProfile: (userData: Partial<User>): Promise<ApiResponse<User>> =>
    apiClient.put('/api/users/profile', userData).then(handleApiResponse),

  uploadAvatar: (file: File): Promise<ApiResponse<{ url: string }>> => {
    const formData = new FormData()
    formData.append('avatar', file)
    return apiClient
      .post('/users/upload-avatar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      .then(handleApiResponse)
  },

  changePassword: (oldPassword: string, newPassword: string): Promise<ApiResponse<void>> =>
    apiClient
      .post('/users/change-password', { oldPassword, newPassword })
      .then(handleApiResponse),

  deleteAccount: (): Promise<ApiResponse<void>> =>
    apiClient.delete('/users/account').then(handleApiResponse),
}

// 测试相关API
export const testApi = {
  getQuestions: (): Promise<ApiResponse<TestQuestion[]>> =>
    apiClient.get('/test/questions').then(handleApiResponse),

  submitTest: (answers: { questionId: string; answerId: string }[]): Promise<ApiResponse<TestResult>> =>
    apiClient.post('/test/submit', { answers }).then(handleApiResponse),

  getTestResult: (userId: string): Promise<ApiResponse<TestResult>> =>
    apiClient.get(`/test/result/${userId}`).then(handleApiResponse),

  retakeTest: (): Promise<ApiResponse<void>> =>
    apiClient.post('/test/retake').then(handleApiResponse),
}

// 动物园相关API
export const zooApi = {
  getAnimals: (params: { scene?: string; limit?: number }): Promise<ApiResponse<Animal[]>> =>
    apiClient.get('/zoo/animals', { params }).then(handleApiResponse),

  getAnimalById: (animalId: string): Promise<ApiResponse<Animal>> =>
    apiClient.get(`/zoo/animals/${animalId}`).then(handleApiResponse),

  updateAnimalPosition: (
    animalId: string,
    position: { x: number; y: number; z: number }
  ): Promise<ApiResponse<Animal>> =>
    apiClient.put(`/zoo/animals/${animalId}/position`, position).then(handleApiResponse),

  updateAnimalAnimation: (
    animalId: string,
    data: { animation: string; mood?: string }
  ): Promise<ApiResponse<Animal>> =>
    apiClient.put(`/zoo/animals/${animalId}/animation`, data).then(handleApiResponse),

  likeAnimal: (animalId: string): Promise<ApiResponse<{ likeCount: number }>> =>
    apiClient.post(`/zoo/animals/${animalId}/like`).then(handleApiResponse),

  feedAnimal: (animalId: string): Promise<ApiResponse<{ feedCount: number }>> =>
    apiClient.post(`/zoo/animals/${animalId}/feed`).then(handleApiResponse),
}

// 内容相关API
export const contentApi = {
  getPosts: (params: {
    page?: number
    pageSize?: number
    sortBy?: string
    tag?: string
  }): Promise<ApiResponse<PaginatedResponse<Post>>> =>
    apiClient.get('/content/posts', { params }).then(handleApiResponse),

  getPostById: (postId: string): Promise<ApiResponse<Post>> =>
    apiClient.get(`/content/posts/${postId}`).then(handleApiResponse),

  createPost: (postData: {
    content: string
    images?: string[]
    tags?: string[]
    isAnonymous?: boolean
  }): Promise<ApiResponse<Post>> =>
    apiClient.post('/content/posts', postData).then(handleApiResponse),

  updatePost: (postId: string, postData: Partial<Post>): Promise<ApiResponse<Post>> =>
    apiClient.put(`/content/posts/${postId}`, postData).then(handleApiResponse),

  deletePost: (postId: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/content/posts/${postId}`).then(handleApiResponse),

  likePost: (postId: string): Promise<ApiResponse<{ likeCount: number }>> =>
    apiClient.post(`/content/posts/${postId}/like`).then(handleApiResponse),

  sharePost: (postId: string): Promise<ApiResponse<{ shareCount: number }>> =>
    apiClient.post(`/content/posts/${postId}/share`).then(handleApiResponse),

  getComments: (
    postId: string,
    params: { page?: number; pageSize?: number }
  ): Promise<ApiResponse<PaginatedResponse<Comment>>> =>
    apiClient.get(`/content/posts/${postId}/comments`, { params }).then(handleApiResponse),

  createComment: (commentData: {
    postId: string
    content: string
    parentId?: string
  }): Promise<ApiResponse<Comment>> =>
    apiClient.post('/content/comments', commentData).then(handleApiResponse),

  updateComment: (commentId: string, content: string): Promise<ApiResponse<Comment>> =>
    apiClient.put(`/content/comments/${commentId}`, { content }).then(handleApiResponse),

  deleteComment: (commentId: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/content/comments/${commentId}`).then(handleApiResponse),

  likeComment: (commentId: string): Promise<ApiResponse<{ likeCount: number }>> =>
    apiClient.post(`/content/comments/${commentId}/like`).then(handleApiResponse),

  uploadImage: (file: File): Promise<ApiResponse<{ url: string }>> => {
    const formData = new FormData()
    formData.append('image', file)
    return apiClient
      .post('/content/upload-image', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      .then(handleApiResponse)
  },
}

// 排行榜相关API
export const rankingApi = {
  getRankings: (params: {
    type: string
    period: string
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<Ranking>>> =>
    apiClient.get('/ranking', { params }).then(handleApiResponse),

  getUserRanking: (
    userId: string,
    type: string,
    period: string
  ): Promise<ApiResponse<Ranking | null>> =>
    apiClient.get(`/ranking/user/${userId}`, { params: { type, period } }).then(handleApiResponse),

  getRankingTypes: (): Promise<ApiResponse<string[]>> =>
    apiClient.get('/ranking/types').then(handleApiResponse),
}

// 社交互动相关API
export const socialApi = {
  followUser: (userId: string): Promise<ApiResponse<void>> =>
    apiClient.post(`/social/follow/${userId}`).then(handleApiResponse),

  unfollowUser: (userId: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/social/follow/${userId}`).then(handleApiResponse),

  getFollowers: (userId: string, params?: { page?: number; pageSize?: number }): Promise<ApiResponse<PaginatedResponse<User>>> =>
    apiClient.get(`/social/followers/${userId}`, { params }).then(handleApiResponse),

  getFollowing: (userId: string, params?: { page?: number; pageSize?: number }): Promise<ApiResponse<PaginatedResponse<User>>> =>
    apiClient.get(`/social/following/${userId}`, { params }).then(handleApiResponse),

  blockUser: (userId: string): Promise<ApiResponse<void>> =>
    apiClient.post(`/social/block/${userId}`).then(handleApiResponse),

  unblockUser: (userId: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/social/block/${userId}`).then(handleApiResponse),

  reportUser: (userId: string, reason: string): Promise<ApiResponse<void>> =>
    apiClient.post(`/social/report/${userId}`, { reason }).then(handleApiResponse),
}

// 通知相关API
export const notificationApi = {
  getNotifications: (params?: { page?: number; pageSize?: number }): Promise<ApiResponse<PaginatedResponse<Notification>>> =>
    apiClient.get('/notifications', { params }).then(handleApiResponse),

  markAsRead: (notificationId: string): Promise<ApiResponse<void>> =>
    apiClient.put(`/notifications/${notificationId}/read`).then(handleApiResponse),

  markAllAsRead: (): Promise<ApiResponse<void>> =>
    apiClient.put('/notifications/read-all').then(handleApiResponse),

  deleteNotification: (notificationId: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/notifications/${notificationId}`).then(handleApiResponse),

  getUnreadCount: (): Promise<ApiResponse<{ count: number }>> =>
    apiClient.get('/notifications/unread-count').then(handleApiResponse),
}

// 分享相关API
export const shareApi = {
  generateCertificate: (userId: string): Promise<ApiResponse<{ imageUrl: string }>> =>
    apiClient.post(`/share/certificate/${userId}`).then(handleApiResponse),

  getShareUrl: (type: string, id: string): Promise<ApiResponse<{ url: string }>> =>
    apiClient.get('/share/url', { params: { type, id } }).then(handleApiResponse),

  recordShare: (type: string, id: string, platform: string): Promise<ApiResponse<void>> =>
    apiClient.post('/share/record', { type, id, platform }).then(handleApiResponse),
}

// 系统相关API
export const systemApi = {
  getSystemInfo: (): Promise<ApiResponse<{ version: string; features: Record<string, boolean> }>> =>
    apiClient.get('/system/info').then(handleApiResponse),

  reportBug: (data: {
    title: string
    description: string
    userAgent: string
    url: string
  }): Promise<ApiResponse<void>> =>
    apiClient.post('/system/bug-report', data).then(handleApiResponse),

  getFeedback: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/system/feedback').then(handleApiResponse),

  submitFeedback: (data: {
    type: string
    content: string
    rating?: number
  }): Promise<ApiResponse<void>> =>
    apiClient.post('/system/feedback', data).then(handleApiResponse),
}

// 导出所有API
export {
  apiClient,
  handleApiResponse,
}

// 工具函数
export const isApiError = (error: any): error is AxiosError => {
  return error.isAxiosError === true
}

export const getErrorMessage = (error: any): string => {
  if (isApiError(error)) {
    return error.response?.data?.message || error.message || '网络请求失败'
  }
  return error.message || '发生未知错误'
}