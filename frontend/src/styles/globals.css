@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* 全局字体优化 - 卡通风格 */
  * {
    @apply box-border;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply text-slate-800;
    /* 改为深色渐变背景，提供更好的对比度 */
    background: linear-gradient(135deg, #1e293b 0%, #312e81 50%, #581c87 100%);
    min-height: 100vh;
    font-family: 'Comic Sans MS', cursive, system-ui, -apple-system, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 移除默认的outline并提供更好的focus样式 */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    @apply outline-none ring-2 ring-blue-400 ring-opacity-70;
  }
}

@layer components {
  /* 卡通化按钮组件样式 - 重新设计配色 */
  .btn {
    @apply inline-flex items-center justify-center rounded-full font-bold transition-all duration-300;
    @apply focus:outline-none focus:ring-4 focus:ring-offset-2 transform hover:scale-105;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .btn-primary {
    @apply btn text-white font-extrabold;
    /* 鲜明的蓝色渐变 */
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    @apply hover:shadow-xl active:scale-95 disabled:bg-gray-400 disabled:cursor-not-allowed;
    @apply focus:ring-blue-300;
  }
  
  .btn-secondary {
    @apply btn text-white font-bold;
    /* 温暖的橙色渐变 */
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    @apply hover:shadow-xl focus:ring-orange-300;
  }
  
  .btn-outline {
    @apply btn border-2 text-slate-700 bg-white backdrop-blur-sm font-bold;
    border-color: #3b82f6;
    @apply hover:bg-blue-50 hover:text-blue-700 focus:ring-blue-300;
  }
  
  .btn-ghost {
    @apply btn text-white bg-white/20 backdrop-blur-sm hover:bg-white/30 font-semibold;
    @apply focus:ring-white/50;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-md {
    @apply px-4 py-2 text-base;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  
  /* 卡通化卡片组件样式 - 高对比度版本 */
  .card {
    @apply bg-white/95 backdrop-blur-sm rounded-3xl border-4 border-white/70;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:-translate-y-2 hover:rotate-1;
    @apply hover:shadow-2xl hover:scale-105;
  }
  
  .card-gradient {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    @apply border-4 border-white/80;
  }
  
  /* 状态卡片 - 专门的颜色方案 */
  .status-card {
    @apply bg-gradient-to-br from-blue-50 to-indigo-100 border-4 border-blue-200 rounded-3xl;
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
  }
  
  /* 卡通化输入框样式 */
  .input {
    @apply w-full px-6 py-4 rounded-full bg-white backdrop-blur-sm border-2;
    border-color: #94a3b8;
    @apply placeholder:text-slate-500 transition-all duration-300 text-slate-700;
    @apply focus:border-blue-400 focus:ring-4 focus:ring-blue-200 focus:bg-white;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
  
  .input-error {
    @apply input border-red-400 focus:border-red-500 focus:ring-red-200;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 202, 202, 0.3) 100%);
  }
  
  /* 文本样式 - 更好的对比度 */
  .text-primary {
    @apply text-slate-800 font-semibold;
  }
  
  .text-secondary {
    @apply text-slate-600;
  }
  
  .text-accent {
    @apply text-blue-600 font-bold;
  }
  
  .text-on-dark {
    @apply text-white font-medium;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold;
  }
  
  .text-heading {
    @apply font-display font-bold text-slate-800;
  }
  
  /* 动画样式 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }
  
  .animate-bounce-gentle {
    animation: bounceGentle 2s infinite;
  }
  
  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }
  
  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }
  
  /* 3D场景相关样式 */
  .scene-container {
    @apply relative w-full h-full overflow-hidden rounded-xl;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-blue-600;
  }
  
  /* 移动端适配 */
  @screen sm {
    .btn-responsive {
      @apply btn-md;
    }
  }
  
  @screen lg {
    .btn-responsive {
      @apply btn-lg;
    }
  }
  
  /* 可访问性增强 */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
  
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .btn-primary {
      @apply border-2 border-black;
    }
    
    .card {
      @apply border-2 border-gray-600;
    }
    
    body {
      background: #000;
      @apply text-white;
    }
  }
  
  /* 减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer utilities {
  /* 动画关键帧 */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes bounceGentle {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes wiggle {
    0%, 7% { transform: rotateZ(0); }
    15% { transform: rotateZ(-15deg); }
    20% { transform: rotateZ(10deg); }
    25% { transform: rotateZ(-10deg); }
    30% { transform: rotateZ(6deg); }
    35% { transform: rotateZ(-4deg); }
    40%, 100% { transform: rotateZ(0); }
  }
  
  @keyframes float {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg); 
    }
    33% { 
      transform: translateY(-10px) rotate(1deg); 
    }
    66% { 
      transform: translateY(-5px) rotate(-1deg); 
    }
  }
  
  @keyframes pulseGlow {
    0% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
    100% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    }
  }
  
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }
  
  /* 渐变背景工具类 */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-blue-700;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-orange-500 to-orange-700;
  }
  
  .bg-gradient-success {
    @apply bg-gradient-to-r from-green-500 to-green-700;
  }
  
  .bg-gradient-warning {
    @apply bg-gradient-to-r from-yellow-500 to-yellow-600;
  }
  
  .bg-gradient-divine {
    @apply bg-gradient-to-r from-yellow-400 to-yellow-600;
  }
  
  .bg-gradient-pet {
    @apply bg-gradient-to-r from-pink-400 to-pink-600;
  }
  
  .bg-gradient-cattle {
    @apply bg-gradient-to-r from-amber-600 to-amber-800;
  }
  
  /* 特殊效果工具类 */
  .glass-effect {
    @apply backdrop-blur-md bg-white/80 border-2 border-white/30;
  }
  
  .shadow-glow {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
  }
  
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-dark {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  }
  
  /* 响应式间距 */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* 新增：高对比度背景类 */
  .bg-dark-contrast {
    background: linear-gradient(135deg, #1e293b 0%, #312e81 100%);
  }
  
  .bg-light-contrast {
    @apply bg-white;
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #1d4ed8;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply border border-gray-400 shadow-none;
  }
}