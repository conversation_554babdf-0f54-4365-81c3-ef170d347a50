@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* 专业而温暖的全局样式 */
  * {
    @apply box-border;
  }

  html {
    @apply scroll-smooth;
    font-size: 16px;
  }

  body {
    @apply text-neutral-800 font-sans;
    /* 简约而优雅的背景 - 温暖的浅灰色 */
    background: linear-gradient(135deg, #fafaf9 0%, #f5f5f4 100%);
    min-height: 100vh;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* 专业的焦点样式 */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    @apply outline-none ring-2 ring-primary-500/20 ring-offset-2 ring-offset-white;
  }

  /* 标题层级 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold text-neutral-900;
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  h1 { @apply text-3xl lg:text-4xl; }
  h2 { @apply text-2xl lg:text-3xl; }
  h3 { @apply text-xl lg:text-2xl; }
  h4 { @apply text-lg lg:text-xl; }
  h5 { @apply text-base lg:text-lg; }
  h6 { @apply text-sm lg:text-base; }
}

@layer components {
  /* 专业而温暖的按钮系统 */
  .btn {
    @apply inline-flex items-center justify-center font-medium transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white;
    @apply hover:bg-primary-700;
    @apply focus:ring-primary-500;
    @apply active:bg-primary-800;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-secondary {
    @apply btn bg-secondary-500 text-white;
    @apply hover:bg-secondary-600;
    @apply focus:ring-secondary-500;
    @apply active:bg-secondary-700;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-outline {
    @apply btn border border-neutral-300 bg-white text-neutral-700;
    @apply hover:bg-neutral-50 hover:border-neutral-400;
    @apply focus:ring-neutral-500;
    @apply active:bg-neutral-100;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .btn-ghost {
    @apply btn text-neutral-600 bg-transparent;
    @apply hover:bg-neutral-100 hover:text-neutral-900;
    @apply focus:ring-neutral-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm rounded-lg;
  }

  .btn-md {
    @apply px-4 py-2.5 text-sm rounded-lg;
  }

  .btn-lg {
    @apply px-6 py-3 text-base rounded-xl;
  }
  
  /* 简约而精致的卡片系统 */
  .card {
    @apply bg-white border border-neutral-200 shadow-soft;
    @apply transition-all duration-200;
  }

  .card-hover {
    @apply card hover:shadow-medium hover:-translate-y-1;
    @apply hover:border-neutral-300;
  }

  .card-elevated {
    @apply card shadow-large border-neutral-100;
  }

  .card-interactive {
    @apply card-hover cursor-pointer;
    @apply active:scale-[0.99] active:shadow-soft;
  }

  /* 功能性卡片变体 */
  .card-primary {
    @apply card border-primary-200 bg-primary-50/50;
  }

  .card-success {
    @apply card border-success-200 bg-success-50/50;
  }

  .card-warning {
    @apply card border-warning-200 bg-warning-50/50;
  }

  .card-error {
    @apply card border-error-200 bg-error-50/50;
  }
  
  /* 专业的表单输入样式 */
  .input {
    @apply w-full px-4 py-2.5 bg-white border border-neutral-300 shadow-subtle;
    @apply placeholder:text-neutral-500 text-neutral-900;
    @apply transition-all duration-200;
    @apply focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20;
    @apply hover:border-neutral-400;
  }

  .input-error {
    @apply input border-error-500 focus:border-error-500 focus:ring-error-500/20;
    @apply bg-error-50/50;
  }

  .input-success {
    @apply input border-success-500 focus:border-success-500 focus:ring-success-500/20;
    @apply bg-success-50/50;
  }
  
  /* 专业的文本层级系统 */
  .text-primary {
    @apply text-neutral-900 font-medium;
  }

  .text-secondary {
    @apply text-neutral-600;
  }

  .text-tertiary {
    @apply text-neutral-500;
  }

  .text-accent {
    @apply text-primary-600 font-medium;
  }

  .text-success {
    @apply text-success-600 font-medium;
  }

  .text-warning {
    @apply text-warning-600 font-medium;
  }

  .text-error {
    @apply text-error-600 font-medium;
  }

  .text-muted {
    @apply text-neutral-500 text-sm;
  }

  .text-subtle {
    @apply text-neutral-400 text-xs;
  }
  
  /* 专业的动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.4s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out forwards;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out forwards;
  }

  .animate-pulse-subtle {
    animation: pulseSubtle 2s ease-in-out infinite;
  }
  
  /* 3D场景相关样式 */
  .scene-container {
    @apply relative w-full h-full overflow-hidden rounded-xl;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-blue-600;
  }
  
  /* 移动端适配 */
  @screen sm {
    .btn-responsive {
      @apply btn-md;
    }
  }
  
  @screen lg {
    .btn-responsive {
      @apply btn-lg;
    }
  }
  
  /* 可访问性增强 */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
  
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .btn-primary {
      @apply border-2 border-black;
    }
    
    .card {
      @apply border-2 border-gray-600;
    }
    
    body {
      background: #000;
      @apply text-white;
    }
  }
  
  /* 减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer utilities {
  /* 动画关键帧 */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulseSubtle {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  /* 专业的渐变背景工具类 */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600;
  }

  .bg-gradient-success {
    @apply bg-gradient-to-r from-success-500 to-success-600;
  }

  .bg-gradient-warning {
    @apply bg-gradient-to-r from-warning-500 to-warning-600;
  }

  .bg-gradient-error {
    @apply bg-gradient-to-r from-error-500 to-error-600;
  }

  .bg-gradient-neutral {
    @apply bg-gradient-to-r from-neutral-100 to-neutral-200;
  }

  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-neutral-50 to-neutral-100;
  }
  
  /* 特殊效果工具类 */
  .glass-effect {
    @apply backdrop-blur-md bg-white/80 border-2 border-white/30;
  }
  
  .shadow-glow {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
  }
  
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-dark {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  }
  
  /* 响应式间距 */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* 新增：高对比度背景类 */
  .bg-dark-contrast {
    background: linear-gradient(135deg, #1e293b 0%, #312e81 100%);
  }
  
  .bg-light-contrast {
    @apply bg-white;
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #1d4ed8;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply border border-gray-400 shadow-none;
  }
}