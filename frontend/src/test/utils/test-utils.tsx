import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { configureStore } from '@reduxjs/toolkit'

import { authSlice } from '../../store/slices/authSlice'
import { uiSlice } from '../../store/slices/uiSlice'
import { zooSlice } from '../../store/slices/zooSlice'
import { contentSlice } from '../../store/slices/contentSlice'
import { userSlice } from '../../store/slices/userSlice'

interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any
  store?: any
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: ReactElement,
  {
    preloadedState = {},
    store = configureStore({
      reducer: {
        auth: authSlice.reducer,
        ui: uiSlice.reducer,
        zoo: zooSlice.reducer,
        content: contentSlice.reducer,
        user: userSlice.reducer,
      },
      preloadedState,
    }),
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          staleTime: Infinity,
        },
      },
    }),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: { children?: React.ReactNode }) {
    return (
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter>{children}</BrowserRouter>
        </QueryClientProvider>
      </Provider>
    )
  }

  return { store, queryClient, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// 创建mock store
export function createMockStore(initialState: any = {}) {
  return configureStore({
    reducer: {
      auth: authSlice.reducer,
      ui: uiSlice.reducer,
      zoo: zooSlice.reducer,
      content: contentSlice.reducer,
      user: userSlice.reducer,
    },
    preloadedState: initialState,
  })
}

// 创建mock query client
export function createMockQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: Infinity,
      },
      mutations: {
        retry: false,
      },
    },
  })
}

// 模拟用户交互
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  avatar: '',
  animalType: 'cattle',
  animalSubtype: 'cow',
  isActive: true,
  isVerified: true,
  createdAt: new Date().toISOString(),
  profile: {
    id: '1',
    nickname: 'Test User',
    bio: 'I am a test user',
    animalType: 'cattle',
    animalSubtype: 'cow',
    testScore: 75,
    likeCount: 10,
    feedCount: 5,
  },
}

// 模拟认证状态
export const mockAuthState = {
  user: mockUser,
  accessToken: 'mock_access_token',
  refreshToken: 'mock_refresh_token',
  isAuthenticated: true,
  isLoading: false,
  error: null,
}

// 模拟动物园状态
export const mockZooState = {
  animals: [
    {
      id: '1',
      userId: '1',
      type: 'cattle',
      species: 'cow',
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      animation: 'idle',
      mood: 'happy',
      likeCount: 5,
      feedCount: 3,
    },
    {
      id: '2',
      userId: '2',
      type: 'cat',
      species: 'persian',
      position: { x: 10, y: 0, z: 10 },
      rotation: { x: 0, y: 45, z: 0 },
      animation: 'walking',
      mood: 'normal',
      likeCount: 8,
      feedCount: 2,
    },
  ],
  selectedAnimal: null,
  cameraPosition: { x: 0, y: 10, z: 20 },
  cameraTarget: { x: 0, y: 0, z: 0 },
  isLoading: false,
  error: null,
}

// 模拟内容状态
export const mockContentState = {
  posts: [
    {
      id: '1',
      userId: '1',
      content: '今天又是996的一天...',
      images: [],
      likeCount: 5,
      commentCount: 2,
      shareCount: 1,
      isAnonymous: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ],
  currentPost: null,
  comments: [],
  isLoading: false,
  error: null,
}

// 工具函数：等待异步操作完成
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0))

// 工具函数：模拟用户点击
export const mockUserEvent = {
  click: (element: HTMLElement) => {
    element.click()
  },
  type: (element: HTMLInputElement, text: string) => {
    element.value = text
    element.dispatchEvent(new Event('input', { bubbles: true }))
  },
  submit: (form: HTMLFormElement) => {
    form.dispatchEvent(new Event('submit', { bubbles: true }))
  },
}

// re-export everything
export * from '@testing-library/react'