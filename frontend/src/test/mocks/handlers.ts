import { rest } from 'msw'
import { createMockUser, createMockPost, createMockAnimal } from '../setup'

const API_BASE_URL = 'http://localhost:3000/api/v1'

export const handlers = [
  // Auth endpoints
  rest.post(`${API_BASE_URL}/auth/register`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          user: createMockUser(),
          accessToken: 'mock_access_token',
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/auth/login`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          user: createMockUser(),
          accessToken: 'mock_access_token',
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/auth/logout`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: '登出成功',
      })
    )
  }),

  rest.get(`${API_BASE_URL}/auth/profile`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: createMockUser(),
      })
    )
  }),

  // Test endpoints
  rest.get(`${API_BASE_URL}/test/questions`, (req, res, ctx) => {
    const questions = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      question: `测试问题 ${i + 1}`,
      options: [
        { id: 1, text: '选项A', score: 1 },
        { id: 2, text: '选项B', score: 2 },
        { id: 3, text: '选项C', score: 3 },
        { id: 4, text: '选项D', score: 4 },
      ],
    }))

    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          questions,
          totalQuestions: 20,
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/test/submit`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          result: {
            id: 'test-result-1',
            userId: '1',
            totalScore: 75,
            animalType: '牛马',
            animalSubtype: '社畜牛',
            personality: '勤劳踏实型',
            description: '你是一头勤劳的社畜牛，虽然工作辛苦，但始终保持着乐观的心态。',
            ranking: {
              percentage: 60,
              rank: 1000,
              total: 2500,
            },
            createdAt: new Date().toISOString(),
          },
        },
      })
    )
  }),

  rest.get(`${API_BASE_URL}/test/certificate`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          certificate: {
            imageUrl: 'https://example.com/certificate.png',
            shareUrl: 'https://example.com/share/certificate',
            metadata: {
              username: 'testuser',
              animalType: '牛马',
              animalSubtype: '社畜牛',
              generatedAt: new Date().toISOString(),
            },
          },
        },
      })
    )
  }),

  // Zoo endpoints
  rest.get(`${API_BASE_URL}/zoo/animals`, (req, res, ctx) => {
    const animals = Array.from({ length: 50 }, (_, i) => createMockAnimal())
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          animals,
          total: 1000,
          pagination: {
            page: 1,
            limit: 50,
            totalPages: 20,
          },
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/zoo/animals/:id/like`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          likeCount: 1,
          isLiked: true,
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/zoo/animals/:id/feed`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          feedCount: 1,
          message: '投喂成功！',
        },
      })
    )
  }),

  // Social endpoints
  rest.get(`${API_BASE_URL}/social/posts`, (req, res, ctx) => {
    const posts = Array.from({ length: 20 }, (_, i) => createMockPost())
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          posts,
          pagination: {
            page: 1,
            limit: 20,
            totalPages: 10,
          },
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/social/posts`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: createMockPost(),
      })
    )
  }),

  rest.post(`${API_BASE_URL}/social/posts/:id/like`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          likeCount: 1,
          isLiked: true,
        },
      })
    )
  }),

  rest.post(`${API_BASE_URL}/social/posts/:id/comments`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          id: 'comment-1',
          content: '测试评论',
          userId: '1',
          createdAt: new Date().toISOString(),
        },
      })
    )
  }),

  // Health check
  rest.get(`${API_BASE_URL}/health`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: 3600,
          memory: {
            used: 50,
            total: 100,
          },
          database: 'connected',
          redis: 'connected',
        },
      })
    )
  }),

  // Error scenarios for testing
  rest.get(`${API_BASE_URL}/test/error`, (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        success: false,
        message: '服务器内部错误',
        code: 'INTERNAL_SERVER_ERROR',
      })
    )
  }),

  rest.get(`${API_BASE_URL}/test/unauthorized`, (req, res, ctx) => {
    return res(
      ctx.status(401),
      ctx.json({
        success: false,
        message: '未授权访问',
        code: 'UNAUTHORIZED',
      })
    )
  }),
]