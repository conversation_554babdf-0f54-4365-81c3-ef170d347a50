import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

export default function ComponentTestPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            shadcn/ui 组件测试
          </h1>
          <p className="text-muted-foreground">
            测试所有shadcn/ui组件是否正常工作
          </p>
        </div>

        {/* Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>按钮组件</CardTitle>
            <CardDescription>不同变体和大小的按钮</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button>默认按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">轮廓按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
              <Button variant="destructive">危险按钮</Button>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button size="sm">小按钮</Button>
              <Button size="default">默认按钮</Button>
              <Button size="lg">大按钮</Button>
            </div>
          </CardContent>
        </Card>

        {/* Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>卡片标题</CardTitle>
              <CardDescription>这是卡片的描述文本</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                这是卡片的内容区域，可以放置任何内容。
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle>悬停效果</CardTitle>
              <CardDescription>鼠标悬停时会有阴影效果</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2">
                <Badge>标签1</Badge>
                <Badge variant="secondary">标签2</Badge>
                <Badge variant="outline">标签3</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>表单元素</CardTitle>
              <CardDescription>输入框和文本域</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input placeholder="请输入文本" />
              <Textarea placeholder="请输入多行文本" rows={3} />
            </CardContent>
          </Card>
        </div>

        {/* Color Test */}
        <Card>
          <CardHeader>
            <CardTitle>颜色测试</CardTitle>
            <CardDescription>测试主题颜色是否正确应用</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 bg-primary text-primary-foreground rounded-lg text-center">
                Primary
              </div>
              <div className="p-4 bg-secondary text-secondary-foreground rounded-lg text-center">
                Secondary
              </div>
              <div className="p-4 bg-muted text-muted-foreground rounded-lg text-center">
                Muted
              </div>
              <div className="p-4 bg-accent text-accent-foreground rounded-lg text-center">
                Accent
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Test */}
        <Card>
          <CardHeader>
            <CardTitle>状态测试</CardTitle>
            <CardDescription>测试不同状态的显示效果</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-foreground">在线状态</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-foreground">等待状态</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-foreground">错误状态</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
