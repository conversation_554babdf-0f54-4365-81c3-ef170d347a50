import { Link } from 'react-router-dom'
import { useAppSelector } from '@/store'

export default function ProfilePage() {
  const { currentUser } = useAppSelector(state => state.user)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8">
          <div className="h-20 w-20 rounded-full bg-indigo-500 flex items-center justify-center text-white font-semibold text-2xl mx-auto mb-4">
            {currentUser?.username?.charAt(0).toUpperCase() || '🐂'}
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {currentUser?.username || '打工人'}
          </h1>
          <p className="text-gray-600 mb-6">
            你的专属牛马形象已在动物园等待
          </p>
          
          <div className="space-y-4">
            <Link
              to="/zoo"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              🎪 查看我的牛马形象
            </Link>
            
            <div>
              <Link
                to="/test"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                🔮 重新测试人格
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}