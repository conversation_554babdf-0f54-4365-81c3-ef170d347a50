import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, UserPlus, Loader2, CheckCircle2, Clock } from 'lucide-react'
import toast from 'react-hot-toast'

import { useAppDispatch, useAppSelector } from '@/store'
import { register as registerUser } from '@/store/slices/authSlice'
import { cn, validationUtils } from '@/utils'

// 注册表单验证schema
const registerSchema = z.object({
  username: z
    .string()
    .min(3, '用户名至少需要3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和连字符'),
  phone: z
    .string()
    .optional()
    .refine((phone) => !phone || validationUtils.isPhoneNumber(phone), {
      message: '请输入有效的手机号码',
    }),
  email: z
    .string()
    .optional()
    .refine((email) => !email || validationUtils.isEmail(email), {
      message: '请输入有效的邮箱地址',
    }),
  password: z
    .string()
    .min(8, '密码至少需要8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  confirmPassword: z.string(),
  verificationCode: z.string().optional(),
  nickname: z.string().max(50, '昵称不能超过50个字符').optional(),
  agreement: z.boolean().refine(val => val === true, '请同意用户协议和隐私政策'),
})
.refine((data) => data.phone || data.email, {
  message: '请至少填写手机号或邮箱地址',
  path: ['phone'],
})
.refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

type RegisterFormData = z.infer<typeof registerSchema>

const RegisterPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [registrationType, setRegistrationType] = useState<'phone' | 'email'>('phone')
  const [isCodeSent, setIsCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)

  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { loading, error } = useAppSelector(state => state.auth)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setError,
    trigger,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      phone: '',
      email: '',
      password: '',
      confirmPassword: '',
      verificationCode: '',
      nickname: '',
      agreement: false,
    },
  })

  const password = watch('password')
  const phone = watch('phone')
  const email = watch('email')

  // 获取密码强度
  const passwordStrength = password ? validationUtils.getPasswordStrength(password) : null

  // 发送验证码
  const sendVerificationCode = async () => {
    const contactInfo = registrationType === 'phone' ? phone : email
    
    if (!contactInfo) {
      toast.error(`请先输入${registrationType === 'phone' ? '手机号' : '邮箱地址'}`)
      return
    }

    // 验证格式
    const isValid = registrationType === 'phone' 
      ? validationUtils.isPhoneNumber(contactInfo)
      : validationUtils.isEmail(contactInfo)

    if (!isValid) {
      toast.error(`请输入有效的${registrationType === 'phone' ? '手机号' : '邮箱地址'}`)
      return
    }

    try {
      // TODO: 调用发送验证码API
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
      
      setIsCodeSent(true)
      setCountdown(60)
      toast.success(`验证码已发送到您的${registrationType === 'phone' ? '手机' : '邮箱'}`)

      // 开始倒计时
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error) {
      toast.error('发送验证码失败，请稍后重试')
    }
  }

  const onSubmit = async (data: RegisterFormData) => {
    try {
      const registerData = {
        username: data.username,
        password: data.password,
        ...(data.phone && { phone: data.phone }),
        ...(data.email && { email: data.email }),
        ...(data.nickname && { nickname: data.nickname }),
      }

      const result = await dispatch(registerUser(registerData))
      
      if (registerUser.fulfilled.match(result)) {
        // 注册成功
        toast.success('注册成功，欢迎加入牛马动物园！')
        navigate('/', { replace: true })
      } else if (registerUser.rejected.match(result)) {
        // 注册失败
        const errorMessage = result.payload?.message || '注册失败，请稍后重试'
        
        // 根据错误类型设置具体的表单错误
        if (errorMessage.includes('用户名')) {
          setError('username', { message: errorMessage })
        } else if (errorMessage.includes('手机号')) {
          setError('phone', { message: errorMessage })
        } else if (errorMessage.includes('邮箱')) {
          setError('email', { message: errorMessage })
        } else {
          toast.error(errorMessage)
        }
      }
    } catch (error) {
      toast.error('注册过程中发生错误，请稍后重试')
    }
  }

  const handleSocialRegister = (provider: 'wechat' | 'qq' | 'apple') => {
    toast.info(`${provider}注册功能即将上线`)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 px-4 sm:px-6 lg:px-8 py-12">
      <div className="max-w-md w-full">
        {/* Logo和欢迎信息 */}
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-4">
            <span className="text-2xl">🐄</span>
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-2">加入牛马大家庭</h1>
          <p className="text-muted-foreground">创建你的专属账号，开始牛马之旅</p>
        </div>

        {/* 注册表单 */}
        <div className="card p-6 sm:p-8">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* 注册方式切换 */}
            <div className="flex rounded-lg bg-gray-100 p-1">
              <button
                type="button"
                onClick={() => setRegistrationType('phone')}
                className={cn(
                  'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all',
                  registrationType === 'phone'
                    ? 'bg-background text-primary shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                )}
              >
                📱 手机注册
              </button>
              <button
                type="button"
                onClick={() => setRegistrationType('email')}
                className={cn(
                  'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all',
                  registrationType === 'email'
                    ? 'bg-background text-primary shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                )}
              >
                📧 邮箱注册
              </button>
            </div>

            {/* 联系方式输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                {registrationType === 'phone' ? '手机号' : '邮箱地址'}
              </label>
              {registrationType === 'phone' ? (
                <div className="flex">
                  <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-200 bg-gray-50 text-gray-500 text-sm">
                    +86
                  </span>
                  <input
                    {...register('phone')}
                    type="tel"
                    placeholder="请输入手机号码"
                    className={cn(
                      'input rounded-l-none',
                      errors.phone && 'input-error'
                    )}
                  />
                </div>
              ) : (
                <input
                  {...register('email')}
                  type="email"
                  placeholder="请输入邮箱地址"
                  className={cn(
                    'input',
                    errors.email && 'input-error'
                  )}
                />
              )}
              {(errors.phone || errors.email) && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.phone?.message || errors.email?.message}
                </p>
              )}
            </div>

            {/* 验证码输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                验证码
              </label>
              <div className="flex gap-2">
                <input
                  {...register('verificationCode')}
                  type="text"
                  placeholder="请输入验证码"
                  className="input flex-1"
                  maxLength={6}
                />
                <button
                  type="button"
                  onClick={sendVerificationCode}
                  disabled={countdown > 0}
                  className={cn(
                    'btn-outline btn-sm whitespace-nowrap px-4',
                    countdown > 0 && 'opacity-50 cursor-not-allowed'
                  )}
                >
                  {countdown > 0 ? (
                    <>
                      <Clock className="w-4 h-4 mr-1" />
                      {countdown}s
                    </>
                  ) : isCodeSent ? (
                    '重新发送'
                  ) : (
                    '获取验证码'
                  )}
                </button>
              </div>
            </div>

            {/* 用户名输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                用户名
              </label>
              <input
                {...register('username')}
                type="text"
                placeholder="请输入用户名"
                className={cn(
                  'input',
                  errors.username && 'input-error'
                )}
                autoComplete="username"
              />
              {errors.username && (
                <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>
              )}
            </div>

            {/* 昵称输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                昵称 (可稍后设置)
              </label>
              <input
                {...register('nickname')}
                type="text"
                placeholder="给自己起个好听的名字"
                className="input"
              />
            </div>

            {/* 密码输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                密码
              </label>
              <div className="relative">
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码"
                  className={cn(
                    'input pr-10',
                    errors.password && 'input-error'
                  )}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              
              {/* 密码强度指示器 */}
              {password && passwordStrength && (
                <div className="mt-2">
                  <div className="flex items-center gap-2 text-xs">
                    <span className="text-muted-foreground">密码强度:</span>
                    <span className={cn(
                      'font-medium',
                      passwordStrength.score <= 2 ? 'text-red-500' :
                      passwordStrength.score === 3 ? 'text-yellow-500' :
                      'text-green-500'
                    )}>
                      {passwordStrength.level}
                    </span>
                  </div>
                  <div className="mt-1 flex gap-1">
                    {[1, 2, 3, 4, 5].map(i => (
                      <div
                        key={i}
                        className={cn(
                          'h-1 flex-1 rounded',
                          i <= passwordStrength.score
                            ? passwordStrength.score <= 2 ? 'bg-red-500' :
                              passwordStrength.score === 3 ? 'bg-yellow-500' :
                              'bg-green-500'
                            : 'bg-gray-200'
                        )}
                      />
                    ))}
                  </div>
                </div>
              )}
              
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            {/* 确认密码输入框 */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                确认密码
              </label>
              <div className="relative">
                <input
                  {...register('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="请再次输入密码"
                  className={cn(
                    'input pr-10',
                    errors.confirmPassword && 'input-error'
                  )}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>
              )}
            </div>

            {/* 用户协议同意 */}
            <div>
              <div className="flex items-start">
                <input
                  {...register('agreement')}
                  type="checkbox"
                  className="h-4 w-4 mt-0.5 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 text-sm text-muted-foreground">
                  同意{' '}
                  <Link to="/terms" className="text-primary-600 hover:underline">
                    《用户协议》
                  </Link>{' '}
                  和{' '}
                  <Link to="/privacy" className="text-primary-600 hover:underline">
                    《隐私政策》
                  </Link>
                </label>
              </div>
              {errors.agreement && (
                <p className="text-red-500 text-sm mt-1">{errors.agreement.message}</p>
              )}
            </div>

            {/* 全局错误信息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* 注册按钮 */}
            <button
              type="submit"
              disabled={loading || isSubmitting}
              className={cn(
                'btn-primary btn-lg w-full',
                (loading || isSubmitting) && 'opacity-50 cursor-not-allowed'
              )}
            >
              {loading || isSubmitting ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  注册中...
                </>
              ) : (
                <>
                  <UserPlus className="w-5 h-5 mr-2" />
                  立即注册并开始测试
                </>
              )}
            </button>
          </form>

          {/* 分割线 */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-background text-muted-foreground">或其他方式注册</span>
              </div>
            </div>

            {/* 第三方注册 */}
            <div className="mt-4 grid grid-cols-3 gap-3">
              <button
                onClick={() => handleSocialRegister('wechat')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-green-500 text-xl mr-1">微</span>
                微信
              </button>
              <button
                onClick={() => handleSocialRegister('qq')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-blue-500 text-xl mr-1">Q</span>
                QQ
              </button>
              <button
                onClick={() => handleSocialRegister('apple')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-foreground text-xl mr-1"></span>
                Apple
              </button>
            </div>
          </div>

          {/* 登录链接 */}
          <div className="mt-6 text-center">
            <p className="text-muted-foreground">
              已有账号？{' '}
              <Link
                to="/login"
                className="text-primary-600 hover:text-primary-500 font-medium transition-colors"
              >
                立即登录
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RegisterPage