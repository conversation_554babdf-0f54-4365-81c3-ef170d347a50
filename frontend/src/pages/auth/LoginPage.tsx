import { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, LogIn, Loader2 } from 'lucide-react'
import toast from 'react-hot-toast'

import { useAppDispatch, useAppSelector } from '@/store'
import { login } from '@/store/slices/authSlice'
import { cn } from '@/utils'

// 登录表单验证schema
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名/邮箱/手机号'),
  password: z.string().min(1, '请输入密码'),
  rememberMe: z.boolean().default(false),
})

type LoginFormData = z.infer<typeof loginSchema>

const LoginPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()
  const { loading, error } = useAppSelector(state => state.auth)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    try {
      const result = await dispatch(login(data))
      
      if (login.fulfilled.match(result)) {
        // 登录成功
        toast.success('登录成功，欢迎回来！')
        
        // 跳转到目标页面或首页
        const from = (location.state as any)?.from || '/'
        navigate(from, { replace: true })
      } else if (login.rejected.match(result)) {
        // 登录失败
        const errorMessage = result.payload?.message || '登录失败，请检查用户名和密码'
        
        // 根据错误类型设置具体的表单错误
        if (errorMessage.includes('用户名') || errorMessage.includes('账号')) {
          setError('username', { message: errorMessage })
        } else if (errorMessage.includes('密码')) {
          setError('password', { message: errorMessage })
        } else {
          toast.error(errorMessage)
        }
      }
    } catch (error) {
      toast.error('登录过程中发生错误，请稍后重试')
    }
  }

  const handleSocialLogin = (provider: 'wechat' | 'qq' | 'apple') => {
    // 这里处理第三方登录
    toast.info(`${provider}登录功能即将上线`)
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full animate-slide-up">
        {/* Logo和欢迎信息 */}
        <div className="text-center mb-8">
          <div className="mx-auto w-20 h-20 bg-gradient-to-r from-pink-400 to-purple-500 rounded-3xl flex items-center justify-center mb-6 shadow-2xl animate-pulse-glow">
            <span className="text-4xl animate-bounce-gentle">🎪</span>
          </div>
          <h1 className="text-4xl font-bold text-white mb-4 drop-shadow-lg animate-float">🌟 欢迎回家 ✨</h1>
          <p className="text-white/90 text-lg drop-shadow-sm animate-wiggle">🐂 登录你的牛马动物园账号 🎨</p>
        </div>

        {/* 登录表单 */}
        <div className="card p-6 sm:p-8">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* 用户名输入框 */}
            <div>
              <label className="block text-base font-bold text-purple-800 mb-3 animate-wiggle">
                📱 手机号/邮箱/用户名 ✨
              </label>
              <div className="relative">
                <input
                  {...register('username')}
                  type="text"
                  placeholder="请输入账号"
                  className={cn(
                    'input',
                    errors.username && 'input-error'
                  )}
                  autoComplete="username"
                />
              </div>
              {errors.username && (
                <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>
              )}
            </div>

            {/* 密码输入框 */}
            <div>
              <label className="block text-base font-bold text-purple-800 mb-3 animate-float">
                🔐 密码 🌟
              </label>
              <div className="relative">
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码"
                  className={cn(
                    'input pr-10',
                    errors.password && 'input-error'
                  )}
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            {/* 记住我 & 忘记密码 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  {...register('rememberMe')}
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-foreground">
                  7天内免登录
                </label>
              </div>
              <Link
                to="/forgot-password"
                className="text-sm text-primary-600 hover:text-primary-500 transition-colors"
              >
                忘记密码？
              </Link>
            </div>

            {/* 全局错误信息 */}
            {error && !errors.username && !errors.password && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={loading || isSubmitting}
              className={cn(
                'btn-primary btn-lg w-full',
                (loading || isSubmitting) && 'opacity-50 cursor-not-allowed'
              )}
            >
              {loading || isSubmitting ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  登录中...
                </>
              ) : (
                <>
                  <LogIn className="w-5 h-5 mr-2" />
                  🚀 登录 ✨
                </>
              )}
            </button>
          </form>

          {/* 分割线 */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-background text-muted-foreground">快速登录</span>
              </div>
            </div>

            {/* 第三方登录 */}
            <div className="mt-4 grid grid-cols-3 gap-3">
              <button
                onClick={() => handleSocialLogin('wechat')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-green-500 text-xl mr-1">微</span>
                微信
              </button>
              <button
                onClick={() => handleSocialLogin('qq')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-blue-500 text-xl mr-1">Q</span>
                QQ
              </button>
              <button
                onClick={() => handleSocialLogin('apple')}
                className="btn-ghost btn-sm w-full flex items-center justify-center"
              >
                <span className="text-foreground text-xl mr-1"></span>
                Apple
              </button>
            </div>
          </div>

          {/* 注册链接 */}
          <div className="mt-6 text-center">
            <p className="text-muted-foreground">
              还没账号？{' '}
              <Link
                to="/register"
                className="text-primary-600 hover:text-primary-500 font-medium transition-colors"
              >
                立即注册
              </Link>
            </p>
          </div>
        </div>

        {/* 用户协议 */}
        <p className="mt-6 text-center text-xs text-muted-foreground">
          登录即表示你同意我们的{' '}
          <Link to="/terms" className="text-primary-600 hover:underline">
            用户协议
          </Link>{' '}
          和{' '}
          <Link to="/privacy" className="text-primary-600 hover:underline">
            隐私政策
          </Link>
        </p>
      </div>
    </div>
  )
}

export default LoginPage