import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../../test/utils/test-utils'
import TestPage from './TestPage'

// Mock Three.js components
vi.mock('@react-three/fiber', () => ({
  Canvas: ({ children }: any) => <div data-testid="three-canvas">{children}</div>,
  useFrame: vi.fn(),
  useThree: () => ({
    camera: { position: { set: vi.fn() } },
    gl: { domElement: { style: {} } },
  }),
}))

vi.mock('@react-three/drei', () => ({
  OrbitControls: () => <div data-testid="orbit-controls" />,
  Environment: () => <div data-testid="environment" />,
  Text: ({ children }: any) => <div data-testid="three-text">{children}</div>,
}))

describe('TestPage', () => {
  it('应该渲染测试介绍页面', () => {
    renderWithProviders(<TestPage />)

    expect(screen.getByText(/打工人身份测试/)).toBeInTheDocument()
    expect(screen.getByText(/通过20道有趣的问题/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /开始测试/ })).toBeInTheDocument()
  })

  it('应该开始测试并显示第一个问题', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    await waitFor(() => {
      expect(screen.getByText(/测试问题 1/)).toBeInTheDocument()
      expect(screen.getByText(/问题 1 \/ 20/)).toBeInTheDocument()
    })
  })

  it('应该显示进度条', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    await waitFor(() => {
      const progressBar = screen.getByRole('progressbar')
      expect(progressBar).toBeInTheDocument()
      expect(progressBar).toHaveAttribute('aria-valuenow', '5') // 1/20 * 100 = 5%
    })
  })

  it('应该能够选择答案并进入下一题', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    await waitFor(() => {
      const firstOption = screen.getByText(/选项A/)
      expect(firstOption).toBeInTheDocument()
    })

    // 选择第一个选项
    const firstOption = screen.getByText(/选项A/)
    fireEvent.click(firstOption)

    // 点击下一题按钮
    const nextButton = screen.getByRole('button', { name: /下一题/ })
    expect(nextButton).toBeEnabled()
    fireEvent.click(nextButton)

    await waitFor(() => {
      expect(screen.getByText(/测试问题 2/)).toBeInTheDocument()
      expect(screen.getByText(/问题 2 \/ 20/)).toBeInTheDocument()
    })
  })

  it('应该能够返回上一题', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    await waitFor(() => {
      const firstOption = screen.getByText(/选项A/)
      fireEvent.click(firstOption)
    })

    // 到第二题
    fireEvent.click(screen.getByRole('button', { name: /下一题/ }))

    await waitFor(() => {
      expect(screen.getByText(/测试问题 2/)).toBeInTheDocument()
    })

    // 返回上一题
    const prevButton = screen.getByRole('button', { name: /上一题/ })
    fireEvent.click(prevButton)

    await waitFor(() => {
      expect(screen.getByText(/测试问题 1/)).toBeInTheDocument()
      // 之前选择的答案应该被保留
      expect(screen.getByText(/选项A/)).toHaveClass('selected')
    })
  })

  it('应该在完成所有题目后显示结果', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    // 模拟完成所有20道题
    for (let i = 0; i < 20; i++) {
      await waitFor(() => {
        const option = screen.getByText(/选项A/)
        fireEvent.click(option)
      })

      if (i < 19) {
        const nextButton = screen.getByRole('button', { name: /下一题/ })
        fireEvent.click(nextButton)
      } else {
        const submitButton = screen.getByRole('button', { name: /提交测试/ })
        fireEvent.click(submitButton)
      }
    }

    await waitFor(() => {
      expect(screen.getByText(/测试完成/)).toBeInTheDocument()
      expect(screen.getByText(/你是一头勤劳的社畜牛/)).toBeInTheDocument()
      expect(screen.getByText(/牛马/)).toBeInTheDocument()
    })
  })

  it('应该显示测试结果详情', async () => {
    renderWithProviders(<TestPage />)

    // 快速完成测试到结果页面
    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    // 模拟提交测试（简化流程）
    // 在实际测试中，这里会模拟完整的测试流程
    // 为了测试结果页面，我们可以直接模拟状态变化

    await waitFor(() => {
      expect(screen.getByText(/总分：75分/)).toBeInTheDocument()
      expect(screen.getByText(/超过了60%的用户/)).toBeInTheDocument()
      expect(screen.getByText(/排名：1000 \/ 2500/)).toBeInTheDocument()
    })
  })

  it('应该能够生成和分享证书', async () => {
    // 模拟已完成测试的状态
    const preloadedState = {
      user: {
        testResult: {
          id: 'test-result-1',
          animalType: '牛马',
          animalSubtype: '社畜牛',
          totalScore: 75,
        },
      },
    }

    renderWithProviders(<TestPage />, { preloadedState })

    await waitFor(() => {
      const generateButton = screen.getByRole('button', { name: /生成证书/ })
      expect(generateButton).toBeInTheDocument()
      fireEvent.click(generateButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/证书生成成功/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /分享到微信/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /分享到微博/ })).toBeInTheDocument()
    })
  })

  it('应该能够重新测试', async () => {
    // 模拟已完成测试的状态
    const preloadedState = {
      user: {
        testResult: {
          id: 'test-result-1',
          animalType: '牛马',
          animalSubtype: '社畜牛',
          totalScore: 75,
        },
      },
    }

    renderWithProviders(<TestPage />, { preloadedState })

    await waitFor(() => {
      const retakeButton = screen.getByRole('button', { name: /重新测试/ })
      expect(retakeButton).toBeInTheDocument()
      fireEvent.click(retakeButton)
    })

    await waitFor(() => {
      // 应该回到测试介绍页面
      expect(screen.getByText(/打工人身份测试/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /开始测试/ })).toBeInTheDocument()
    })
  })

  it('应该处理网络错误', async () => {
    // 使用MSW模拟网络错误
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    // 如果API调用失败，应该显示错误信息
    await waitFor(() => {
      if (screen.queryByText(/网络错误/)) {
        expect(screen.getByText(/网络错误/)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /重试/ })).toBeInTheDocument()
      }
    })
  })

  it('应该保存测试进度', async () => {
    renderWithProviders(<TestPage />)

    const startButton = screen.getByRole('button', { name: /开始测试/ })
    fireEvent.click(startButton)

    await waitFor(() => {
      const firstOption = screen.getByText(/选项A/)
      fireEvent.click(firstOption)
    })

    // 模拟页面刷新或重新进入
    // 检查是否保存了测试进度
    expect(localStorage.getItem('test-progress')).toBeTruthy()
  })

  it('应该显示3D动物预览', async () => {
    renderWithProviders(<TestPage />)

    await waitFor(() => {
      expect(screen.getByTestId('three-canvas')).toBeInTheDocument()
      expect(screen.getByTestId('orbit-controls')).toBeInTheDocument()
    })
  })
})