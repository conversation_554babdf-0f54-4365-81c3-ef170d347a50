import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { motion } from 'framer-motion'
import { CheckCircle, Download, Share2, RotateCcw } from 'lucide-react'
import toast from 'react-hot-toast'

import { useAppSelector } from '@/store'

interface TestResult {
  animalType: 'OXHORSE' | 'PET' | 'DIVINE'
  personality: string
  traits: string[]
  description: string
  avatarUrl?: string
  confidence: number
}

export default function TestResultPage() {
  const navigate = useNavigate()
  const params = useParams()
  const { currentUser } = useAppSelector(state => state.user)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isGeneratingAvatar, setIsGeneratingAvatar] = useState(false)
  const [result, setResult] = useState<TestResult | null>(null)
  const [avatarGenerated, setAvatarGenerated] = useState(false)

  useEffect(() => {
    // 模拟测试结果生成
    const generateTestResult = async () => {
      setIsLoading(true)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 随机生成结果
      const animalTypes: TestResult['animalType'][] = ['OXHORSE', 'PET', 'DIVINE']
      const randomType = animalTypes[Math.floor(Math.random() * animalTypes.length)]
      
      const mockResult: TestResult = {
        animalType: randomType,
        personality: getPersonalityByType(randomType),
        traits: getTraitsByType(randomType),
        description: getDescriptionByType(randomType),
        confidence: Math.floor(Math.random() * 30) + 70 // 70-100%
      }
      
      setResult(mockResult)
      setIsLoading(false)
      
      // 自动开始生成头像
      setTimeout(() => {
        generateAvatar(mockResult)
      }, 1000)
    }
    
    generateTestResult()
  }, [])

  const getPersonalityByType = (type: TestResult['animalType']): string => {
    switch (type) {
      case 'OXHORSE':
        return '勤劳牛马型'
      case 'PET':
        return '可爱宠物型'
      case 'DIVINE':
        return '神兽领导型'
      default:
        return '未知类型'
    }
  }

  const getTraitsByType = (type: TestResult['animalType']): string[] => {
    switch (type) {
      case 'OXHORSE':
        return ['任劳任怨', '默默承受', '勤劳踏实', '服从性强', '抗压能力强']
      case 'PET':
        return ['可爱萌萌', '社交能力强', '讨人喜欢', '情商较高', '善于撒娇']
      case 'DIVINE':
        return ['领导能力强', '威严神秘', '独当一面', '决策果断', '气场强大']
      default:
        return []
    }
  }

  const getDescriptionByType = (type: TestResult['animalType']): string => {
    switch (type) {
      case 'OXHORSE':
        return '你是办公室里最可靠的存在，任何艰难的任务都能默默承担。虽然有时会被压榨，但你的坚韧和毅力让同事们都很依赖你。'
      case 'PET':
        return '你拥有很强的人际交往能力，总能在职场中如鱼得水。可爱的外表下隐藏着聪明的头脑，善于化解各种职场矛盾。'
      case 'DIVINE':
        return '天生的领导者气质让你在团队中脱颖而出。你有着敏锐的洞察力和决断力，是天生的管理者材料。'
      default:
        return ''
    }
  }

  const generateAvatar = async (testResult: TestResult) => {
    setIsGeneratingAvatar(true)
    
    try {
      // 首先检查是否有用户绘画数据
      const canvasData = localStorage.getItem('userDrawing')
      if (!canvasData) {
        toast.error('未找到绘画数据，请重新进行测试')
        navigate('/test')
        return
      }

      // 调用后端API生成头像 - 使用AI融合
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002'}/api/v1/avatar/ai-fusion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: canvasData,
          animalType: testResult.animalType,
          analysisData: {
            style: 'modern-illustration',
            expression: 'focused-professional',
            pose: 'confident-professional'
          },
          userId: currentUser?.id || 'anonymous'
        })
      })

      if (!response.ok) {
        throw new Error('头像生成失败')
      }

      const data = await response.json()
      
      // 更新结果中的头像URL
      const avatarUrl = data.avatar?.imageUrl || data.avatarUrl
      setResult(prev => prev ? { ...prev, avatarUrl } : null)
      
      // 保存到localStorage，标记用户已生成头像
      const avatarData = {
        animalType: testResult.animalType,
        avatarUrl,
        generatedAt: new Date().toISOString(),
        userId: currentUser?.id,
        personality: testResult.personality,
        traits: testResult.traits
      }
      localStorage.setItem('userGeneratedAvatar', JSON.stringify(avatarData))
      
      // 将新生成的打工人加入动物园
      const zooAnimals = JSON.parse(localStorage.getItem('zooAnimals') || '[]')
      const newAnimal = {
        id: `test_${Date.now()}`,
        type: testResult.animalType,
        name: currentUser?.username || '测试生成的打工人',
        position: {
          x: Math.random() * 800 + 100,
          y: Math.random() * 400 + 100
        },
        imageUrl: avatarUrl,
        avatar: avatarUrl,
        avatarUrl: avatarUrl,
        personality: testResult.personality,
        traits: testResult.traits,
        stats: {
          energy: 75,
          happiness: 80,
          creativity: 70
        },
        activity: '个性测试完成',
        zone: '测试区',
        timestamp: Date.now(),
        isUserGenerated: true,
        createdAt: new Date().toISOString()
      }
      zooAnimals.unshift(newAnimal) // 使用unshift保持一致性
      localStorage.setItem('zooAnimals', JSON.stringify(zooAnimals))
      
      setAvatarGenerated(true)
      toast.success('头像生成成功！')
      
    } catch (error) {
      console.error('头像生成失败:', error)
      toast.error('头像生成失败，请稍后重试')
    } finally {
      setIsGeneratingAvatar(false)
    }
  }

  const handleGoToZoo = () => {
    navigate('/zoo')
  }

  const handleRetakeTest = () => {
    // 清除之前的数据
    localStorage.removeItem('userDrawing')
    localStorage.removeItem('userGeneratedAvatar')
    navigate('/test')
  }

  const handleDownloadAvatar = () => {
    if (result?.avatarUrl) {
      const link = document.createElement('a')
      link.href = result.avatarUrl
      link.download = `${result.personality}_avatar.png`
      link.click()
    }
  }

  const handleShareResult = () => {
    if (navigator.share && result) {
      navigator.share({
        title: '我的打工人测试结果',
        text: `我是${result.personality}！${result.description}`,
        url: window.location.href
      })
    } else {
      // 复制到剪贴板
      const text = `我的打工人测试结果：${result?.personality} - ${result?.description}`
      navigator.clipboard.writeText(text).then(() => {
        toast.success('结果已复制到剪贴板')
      })
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="mb-8"
        >
          <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-full"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">分析中...</h1>
          <p className="text-gray-600">正在基于你的答案生成专属结果</p>
        </motion.div>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="text-red-500 text-xl">生成结果失败，请重新测试</div>
        <button onClick={() => navigate('/test')} className="btn-primary mt-4">
          重新测试
        </button>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* 结果标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="w-12 h-12 text-green-500" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">测试完成！</h1>
        <p className="text-gray-600">你的专属打工人形象已生成</p>
      </motion.div>

      {/* 结果展示 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-2xl p-8 shadow-soft mb-6"
      >
        {/* 动物类型和头像 */}
        <div className="text-center mb-6">
          <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gray-100 overflow-hidden">
            {isGeneratingAvatar ? (
              <div className="w-full h-full flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                  className="w-8 h-8 border-4 border-gray-200 border-t-gray-500 rounded-full"
                />
              </div>
            ) : result.avatarUrl ? (
              <img 
                src={result.avatarUrl} 
                alt="Generated Avatar" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-4xl">
                {result.animalType === 'OXHORSE' ? '🐂' : 
                 result.animalType === 'PET' ? '🐱' : '🦌'}
              </div>
            )}
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {result.personality}
          </h2>
          <p className="text-sm text-gray-500 mb-4">
            匹配度: {result.confidence}%
          </p>
          
          {isGeneratingAvatar && (
            <p className="text-blue-600 text-sm">正在生成专属头像...</p>
          )}
        </div>

        {/* 性格描述 */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-900 mb-2">性格分析</h3>
          <p className="text-gray-600 leading-relaxed">{result.description}</p>
        </div>

        {/* 特征标签 */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-900 mb-3">你的特征</h3>
          <div className="flex flex-wrap gap-2">
            {result.traits.map((trait, index) => (
              <motion.span
                key={trait}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
              >
                {trait}
              </motion.span>
            ))}
          </div>
        </div>
      </motion.div>

      {/* 操作按钮 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        {/* 主要操作 */}
        <div className="flex space-x-4">
          <button
            onClick={handleGoToZoo}
            disabled={!avatarGenerated}
            className={`flex-1 btn-primary btn-lg ${
              !avatarGenerated ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            🎪 进入动物园
          </button>
          
          <button
            onClick={handleRetakeTest}
            className="btn-outline btn-lg"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            重新测试
          </button>
        </div>

        {/* 次要操作 */}
        <div className="flex space-x-4">
          <button
            onClick={handleDownloadAvatar}
            disabled={!result.avatarUrl}
            className={`flex-1 btn-ghost btn-md ${
              !result.avatarUrl ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <Download className="w-4 h-4 mr-2" />
            下载头像
          </button>
          
          <button
            onClick={handleShareResult}
            className="flex-1 btn-ghost btn-md"
          >
            <Share2 className="w-4 h-4 mr-2" />
            分享结果
          </button>
        </div>

        {/* 提示信息 */}
        {!avatarGenerated && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
            <p className="text-yellow-800 text-sm">
              ⏳ 头像生成完成后即可进入动物园与其他打工人互动
            </p>
          </div>
        )}
      </motion.div>
    </div>
  )
}