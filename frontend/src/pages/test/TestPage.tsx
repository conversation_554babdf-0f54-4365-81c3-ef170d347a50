import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ChevronLeft, ChevronRight, RotateCcw, CheckCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

import { useAppSelector } from '@/store'
import { TestQuestion, TestAnswer } from '@/types'
import { cn } from '@/utils'

// 模拟测试数据
const mockQuestions: TestQuestion[] = [
  {
    id: '1',
    question: '你一般几点到达公司？',
    category: 'work_schedule',
    options: [
      { id: 'a', text: '提前30分钟，做好准备', score: { workPressure: 3, compliance: 8, creativity: 5, socialSkill: 6, resilience: 7 } },
      { id: 'b', text: '准时到达，不早不晚', score: { workPressure: 5, compliance: 6, creativity: 6, socialSkill: 6, resilience: 6 } },
      { id: 'c', text: '经常迟到5-10分钟', score: { workPressure: 7, compliance: 4, creativity: 7, socialSkill: 5, resilience: 5 } },
      { id: 'd', text: '迟到是常态，反正没人管', score: { workPressure: 9, compliance: 2, creativity: 8, socialSkill: 4, resilience: 3 } }
    ]
  },
  {
    id: '2',
    question: '面对老板的无理要求，你会？',
    category: 'workplace_pressure',
    options: [
      { id: 'a', text: '默默承受，完成任务', score: { workPressure: 8, compliance: 9, creativity: 3, socialSkill: 4, resilience: 6 } },
      { id: 'b', text: '礼貌地提出异议', score: { workPressure: 5, compliance: 6, creativity: 7, socialSkill: 8, resilience: 7 } },
      { id: 'c', text: '直接拒绝，据理力争', score: { workPressure: 3, compliance: 2, creativity: 8, socialSkill: 6, resilience: 9 } },
      { id: 'd', text: '阳奉阴违，表面答应', score: { workPressure: 6, compliance: 4, creativity: 8, socialSkill: 7, resilience: 5 } }
    ]
  },
  {
    id: '3',
    question: '工作中遇到困难时，你倾向于？',
    category: 'problem_solving',
    options: [
      { id: 'a', text: '自己琢磨，死磕到底', score: { workPressure: 6, compliance: 5, creativity: 8, socialSkill: 3, resilience: 8 } },
      { id: 'b', text: '主动向同事请教', score: { workPressure: 4, compliance: 6, creativity: 6, socialSkill: 8, resilience: 7 } },
      { id: 'c', text: '直接找领导汇报', score: { workPressure: 7, compliance: 8, creativity: 4, socialSkill: 6, resilience: 5 } },
      { id: 'd', text: '能躲就躲，能拖就拖', score: { workPressure: 8, compliance: 3, creativity: 5, socialSkill: 4, resilience: 3 } }
    ]
  },
  {
    id: '4',
    question: '你的摸鱼技能等级是？',
    category: 'work_efficiency',
    options: [
      { id: 'a', text: '青铜：偶尔看看手机', score: { workPressure: 5, compliance: 7, creativity: 6, socialSkill: 6, resilience: 6 } },
      { id: 'b', text: '黄金：摸鱼看剧样样通', score: { workPressure: 6, compliance: 4, creativity: 7, socialSkill: 7, resilience: 6 } },
      { id: 'c', text: '钻石：上班摸鱼下班躺', score: { workPressure: 7, compliance: 3, creativity: 8, socialSkill: 6, resilience: 4 } },
      { id: 'd', text: '王者：摸鱼我是专业的', score: { workPressure: 8, compliance: 2, creativity: 9, socialSkill: 8, resilience: 3 } }
    ]
  },
  {
    id: '5',
    question: '加班对你来说是？',
    category: 'overtime_attitude',
    options: [
      { id: 'a', text: '家常便饭，习以为常', score: { workPressure: 9, compliance: 8, creativity: 4, socialSkill: 5, resilience: 7 } },
      { id: 'b', text: '能避免就避免', score: { workPressure: 6, compliance: 5, creativity: 7, socialSkill: 6, resilience: 6 } },
      { id: 'c', text: '坚决不加班', score: { workPressure: 3, compliance: 3, creativity: 8, socialSkill: 7, resilience: 8 } },
      { id: 'd', text: '看心情，看钱', score: { workPressure: 5, compliance: 4, creativity: 7, socialSkill: 7, resilience: 6 } }
    ]
  }
]

const TestPage: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<TestAnswer[]>([])
  const [isStarted, setIsStarted] = useState(false)
  const [isCompleted, setIsCompleted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)

  const currentQuestion = mockQuestions[currentQuestionIndex]
  const progress = isStarted ? ((currentQuestionIndex + 1) / mockQuestions.length) * 100 : 0
  const canGoNext = answers.find(a => a.questionId === currentQuestion?.id)
  const canGoPrevious = currentQuestionIndex > 0

  // 开始测试
  const handleStart = () => {
    setIsStarted(true)
    setAnswers([])
    setCurrentQuestionIndex(0)
  }

  // 随机分配
  const handleRandomAssign = () => {
    toast.info('随机分配功能即将上线')
  }

  // 选择答案
  const handleSelectAnswer = (option: { id: string; text: string; score: any }) => {
    const newAnswer: TestAnswer = {
      questionId: currentQuestion.id,
      answerId: option.id,
      question: currentQuestion.question,
      answer: option.text
    }

    setAnswers(prev => {
      const existingIndex = prev.findIndex(a => a.questionId === currentQuestion.id)
      if (existingIndex >= 0) {
        const updated = [...prev]
        updated[existingIndex] = newAnswer
        return updated
      }
      return [...prev, newAnswer]
    })
  }

  // 下一题
  const handleNext = () => {
    if (currentQuestionIndex < mockQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    } else {
      handleComplete()
    }
  }

  // 上一题
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  // 完成测试
  const handleComplete = async () => {
    setIsCompleted(true)
    setIsSubmitting(true)

    try {
      // 生成模拟绘画数据（稍后可以替换为真实的绘画组件）
      const mockDrawingData = generateMockDrawingData(answers)
      localStorage.setItem('userDrawing', mockDrawingData)
      
      // TODO: 调用提交测试结果API
      await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
      
      toast.success('测试完成！正在生成你的动物形象...')
      navigate('/test/result')
    } catch (error) {
      toast.error('提交测试结果失败，请稍后重试')
      setIsSubmitting(false)
    }
  }

  // 生成模拟绘画数据
  const generateMockDrawingData = (answers: TestAnswer[]) => {
    // 基于用户答案生成不同复杂度的模拟绘画数据
    const complexity = answers.length * 10000 + Math.random() * 20000
    const mockCanvasData = 'data:image/png;base64,' + 'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'.repeat(Math.floor(complexity / 100))
    return mockCanvasData
  }

  // 重新开始
  const handleRestart = () => {
    setIsStarted(false)
    setIsCompleted(false)
    setCurrentQuestionIndex(0)
    setAnswers([])
  }

  // 获取当前问题的答案
  const getCurrentAnswer = () => {
    return answers.find(a => a.questionId === currentQuestion?.id)
  }

  if (!isStarted) {
    return (
      <div className="min-h-screen">
        <div className="max-w-2xl mx-auto">
          {/* 测试介绍 */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <div className="w-32 h-32 mx-auto rounded-full flex items-center justify-center mb-6 animate-bounce-gentle">
                <span className="text-8xl">🧪</span>
              </div>
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-5xl font-extrabold text-white mb-6 animate-float drop-shadow-lg"
            >
              ✨ 打工人分类测试 ✨
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-xl text-white/90 mb-8 drop-shadow-md"
            >
              🎭 通过回答{mockQuestions.length}个趣味问题，<br />
              我们将为你匹配最适合的动物形象和性格分析 🎪
            </motion.p>
          </div>

          {/* 动物转换动画 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="card-gradient p-10 mb-8 animate-slide-up"
          >
            <div className="flex items-center justify-center space-x-12 text-8xl">
              <motion.span
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="animate-float"
              >
                🧑‍💼
              </motion.span>
              <span className="text-2xl text-gray-400">→</span>
              <span className="text-2xl text-gray-400">?</span>
              <span className="text-2xl text-gray-400">→</span>
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex space-x-2"
              >
                <span>🐄</span>
                <span>🐶</span>
                <span>🦌</span>
              </motion.div>
            </div>
          </motion.div>

        {/* 测试说明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 mb-8 shadow-soft"
        >
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">⏱️</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">预计用时</h3>
              <p className="text-sm text-gray-600">3-5分钟</p>
            </div>
            <div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">答题建议</h3>
              <p className="text-sm text-gray-600">答案没有对错，请诚实回答</p>
            </div>
            <div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🔒</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">隐私保护</h3>
              <p className="text-sm text-gray-600">所有数据仅用于个性分析</p>
            </div>
          </div>
        </motion.div>

        {/* 开始按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center space-y-4"
        >
          <button
            onClick={handleStart}
            className="btn btn-primary btn-lg px-12 animate-pulse-glow"
          >
            🚀 开始测试
          </button>
          
          <button
            onClick={handleRandomAssign}
            className="btn btn-outline btn-md block mx-auto animate-float"
          >
            🎲 或者随机分配一个
          </button>
        </motion.div>
        </div>
      </div>
    )
  }

  if (isCompleted) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="mb-8"
        >
          <div className="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="w-12 h-12 text-green-500" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">测试完成！</h1>
          <p className="text-gray-600">正在分析你的答案，生成专属动物形象...</p>
        </motion.div>

        <div className="bg-white rounded-2xl p-8 shadow-soft">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="w-16 h-16 mx-auto mb-4"
          >
            <div className="w-full h-full border-4 border-primary-200 border-t-primary-500 rounded-full" />
          </motion.div>
          <p className="text-lg font-medium text-gray-700">请稍等片刻...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* 头部信息 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={() => navigate(-1)}
            className="btn-ghost btn-sm"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            退出
          </button>
          <div className="text-sm text-gray-500">
            第{currentQuestionIndex + 1}题 / {mockQuestions.length}题
          </div>
          <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
        </div>

        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-gradient-primary h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>

      {/* 问题卡片 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-2xl p-8 shadow-soft mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            {currentQuestion.question}
          </h2>

          <div className="space-y-3">
            {currentQuestion.options.map((option) => {
              const isSelected = getCurrentAnswer()?.answerId === option.id
              
              return (
                <motion.button
                  key={option.id}
                  onClick={() => handleSelectAnswer(option)}
                  className={cn(
                    'w-full p-4 text-left rounded-xl border-2 transition-all duration-200',
                    'hover:bg-gray-50 hover:border-primary-200',
                    isSelected
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 text-gray-700'
                  )}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center">
                    <div className={cn(
                      'w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center',
                      isSelected
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-300'
                    )}>
                      {isSelected && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="w-2 h-2 bg-white rounded-full"
                        />
                      )}
                    </div>
                    <span className="font-medium">{option.text}</span>
                  </div>
                </motion.button>
              )
            })}
          </div>
        </motion.div>
      </AnimatePresence>

      {/* 导航按钮 */}
      <div className="flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={!canGoPrevious}
          className={cn(
            'btn-outline btn-md',
            !canGoPrevious && 'opacity-50 cursor-not-allowed'
          )}
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          上一题
        </button>

        <div className="flex space-x-3">
          <button
            onClick={handleRestart}
            className="btn-ghost btn-md"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            重新开始
          </button>

          <button
            onClick={handleNext}
            disabled={!canGoNext}
            className={cn(
              'btn-primary btn-md',
              !canGoNext && 'opacity-50 cursor-not-allowed'
            )}
          >
            {currentQuestionIndex === mockQuestions.length - 1 ? '完成测试' : '下一题'}
            {currentQuestionIndex < mockQuestions.length - 1 && (
              <ChevronRight className="w-4 h-4 ml-1" />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default TestPage