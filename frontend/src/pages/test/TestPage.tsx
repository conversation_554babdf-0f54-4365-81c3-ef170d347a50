import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Home, RotateCcw } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

interface TestAnswer {
  questionId: number
  answerId: string
  score: {
    workPressure: number
    compliance: number
    creativity: number
    socialSkill: number
    resilience: number
  }
}

const questions = [
  {
    id: 1,
    question: '你对加班的态度是？',
    answers: [
      { id: 'a', text: '能不加班就不加班', score: { workPressure: 2, compliance: 3, creativity: 8, socialSkill: 6, resilience: 4 } },
      { id: 'b', text: '适当加班可以接受', score: { workPressure: 6, compliance: 7, creativity: 5, socialSkill: 7, resilience: 7 } },
      { id: 'c', text: '经常加班，习以为常', score: { workPressure: 9, compliance: 8, creativity: 3, socialSkill: 4, resilience: 8 } },
      { id: 'd', text: '主动加班，展现积极性', score: { workPressure: 8, compliance: 9, creativity: 4, socialSkill: 8, resilience: 6 } }
    ]
  },
  {
    id: 2,
    question: '面对领导的不合理要求，你会？',
    answers: [
      { id: 'a', text: '直接拒绝或提出异议', score: { workPressure: 3, compliance: 2, creativity: 9, socialSkill: 4, resilience: 8 } },
      { id: 'b', text: '委婉地表达不同意见', score: { workPressure: 5, compliance: 5, creativity: 7, socialSkill: 8, resilience: 6 } },
      { id: 'c', text: '默默执行，心里抱怨', score: { workPressure: 8, compliance: 8, creativity: 3, socialSkill: 3, resilience: 4 } },
      { id: 'd', text: '立即执行，不问原因', score: { workPressure: 7, compliance: 9, creativity: 2, socialSkill: 6, resilience: 5 } }
    ]
  },
  {
    id: 3,
    question: '在团队合作中，你通常扮演什么角色？',
    answers: [
      { id: 'a', text: '领导者，喜欢主导方向', score: { workPressure: 6, compliance: 4, creativity: 8, socialSkill: 9, resilience: 7 } },
      { id: 'b', text: '协调者，平衡各方意见', score: { workPressure: 5, compliance: 6, creativity: 6, socialSkill: 9, resilience: 6 } },
      { id: 'c', text: '执行者，按要求完成任务', score: { workPressure: 7, compliance: 8, creativity: 4, socialSkill: 5, resilience: 6 } },
      { id: 'd', text: '观察者，很少主动发言', score: { workPressure: 8, compliance: 7, creativity: 5, socialSkill: 3, resilience: 4 } }
    ]
  },
  {
    id: 4,
    question: '你如何看待职场中的竞争？',
    answers: [
      { id: 'a', text: '积极竞争，力争上游', score: { workPressure: 7, compliance: 5, creativity: 7, socialSkill: 7, resilience: 8 } },
      { id: 'b', text: '适度竞争，保持平衡', score: { workPressure: 5, compliance: 6, creativity: 6, socialSkill: 7, resilience: 6 } },
      { id: 'c', text: '避免竞争，求稳为主', score: { workPressure: 8, compliance: 8, creativity: 3, socialSkill: 4, resilience: 5 } },
      { id: 'd', text: '无所谓，随遇而安', score: { workPressure: 6, compliance: 5, creativity: 5, socialSkill: 5, resilience: 4 } }
    ]
  },
  {
    id: 5,
    question: '面对工作压力时，你的反应是？',
    answers: [
      { id: 'a', text: '越挫越勇，迎难而上', score: { workPressure: 4, compliance: 5, creativity: 8, socialSkill: 6, resilience: 9 } },
      { id: 'b', text: '冷静分析，寻找解决方案', score: { workPressure: 5, compliance: 6, creativity: 8, socialSkill: 7, resilience: 8 } },
      { id: 'c', text: '感到焦虑，但还是硬撑', score: { workPressure: 9, compliance: 7, creativity: 4, socialSkill: 4, resilience: 5 } },
      { id: 'd', text: '容易崩溃，需要他人安慰', score: { workPressure: 9, compliance: 6, creativity: 5, socialSkill: 6, resilience: 2 } }
    ]
  }
]

const TestPage = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<TestAnswer[]>([])
  const [isCompleted, setIsCompleted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  const navigate = useNavigate()

  const currentQuestion = questions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100

  const handleAnswerSelect = async (answerId: string) => {
    const selectedAnswer = currentQuestion.answers.find(a => a.id === answerId)
    if (!selectedAnswer) return

    const newAnswer: TestAnswer = {
      questionId: currentQuestion.id,
      answerId,
      score: selectedAnswer.score
    }

    const newAnswers = [...answers, newAnswer]
    setAnswers(newAnswers)

    if (currentQuestionIndex < questions.length - 1) {
      // 不是最后一题，继续下一题
      setTimeout(() => {
        setCurrentQuestionIndex(currentQuestionIndex + 1)
      }, 500)
    } else {
      // 最后一题，计算结果
      setIsSubmitting(true)
      
      try {
        // 计算总分
        const totalScores = newAnswers.reduce((acc, answer) => {
          acc.workPressure += answer.score.workPressure
          acc.compliance += answer.score.compliance
          acc.creativity += answer.score.creativity
          acc.socialSkill += answer.score.socialSkill
          acc.resilience += answer.score.resilience
          return acc
        }, {
          workPressure: 0,
          compliance: 0,
          creativity: 0,
          socialSkill: 0,
          resilience: 0
        })

        // 根据分数确定动物类型
        let animalType = '社畜羊'
        let description = '温顺听话，任劳任怨的典型社畜'
        
        if (totalScores.creativity >= 30 && totalScores.resilience >= 30) {
          animalType = '创意猫'
          description = '独立思考，富有创造力，不轻易妥协'
        } else if (totalScores.workPressure >= 35) {
          animalType = '加班狗'
          description = '勤劳忠诚，承受压力强，是老板的好帮手'
        } else if (totalScores.socialSkill >= 35) {
          animalType = '社交蝶'
          description = '善于沟通，人际关系良好，职场如鱼得水'
        } else if (totalScores.compliance >= 35) {
          animalType = '听话牛'
          description = '服从性强，执行力好，是团队的稳定力量'
        }

        const result = {
          animalType,
          description,
          scores: totalScores,
          traits: [
            `工作压力承受: ${totalScores.workPressure}/50`,
            `服从度: ${totalScores.compliance}/50`,
            `创造力: ${totalScores.creativity}/50`,
            `社交能力: ${totalScores.socialSkill}/50`,
            `抗压能力: ${totalScores.resilience}/50`
          ]
        }

        setTestResult(result)
        setIsCompleted(true)
        toast.success('测试完成！发现你的职场动物形象')
      } catch (error) {
        toast.error('测试提交失败，请重试')
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  const handleRestart = () => {
    setCurrentQuestionIndex(0)
    setAnswers([])
    setIsCompleted(false)
    setTestResult(null)
  }

  const handleGoHome = () => {
    navigate('/')
  }

  if (isCompleted && testResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 p-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <Button variant="ghost" onClick={handleGoHome} className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              返回首页
            </Button>
            <Button variant="outline" onClick={handleRestart} className="flex items-center gap-2">
              <RotateCcw className="w-4 h-4" />
              重新测试
            </Button>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <Card className="text-center">
              <CardHeader>
                <CardTitle className="text-3xl text-purple-600">🎉 测试完成！</CardTitle>
                <CardDescription>你的职场动物形象是...</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-6xl mb-4">
                  {testResult.animalType === '创意猫' && '🐱'}
                  {testResult.animalType === '加班狗' && '🐶'}
                  {testResult.animalType === '社交蝶' && '🦋'}
                  {testResult.animalType === '听话牛' && '🐮'}
                  {testResult.animalType === '社畜羊' && '🐑'}
                </div>
                <h2 className="text-2xl font-bold text-gray-800">{testResult.animalType}</h2>
                <p className="text-gray-600">{testResult.description}</p>
                
                <div className="grid grid-cols-1 gap-2 mt-6">
                  {testResult.traits.map((trait: string, index: number) => (
                    <Badge key={index} variant="secondary" className="justify-center py-2">
                      {trait}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 p-4">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={handleGoHome} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            返回首页
          </Button>
          <div className="text-sm text-gray-600">
            {currentQuestionIndex + 1} / {questions.length}
          </div>
        </div>

        <div className="mb-6">
          <Progress value={progress} className="h-2" />
          <p className="text-center text-sm text-gray-600 mt-2">
            完成进度: {Math.round(progress)}%
          </p>
        </div>

        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">
                  问题 {currentQuestionIndex + 1}
                </CardTitle>
                <CardDescription className="text-lg">
                  {currentQuestion.question}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {currentQuestion.answers.map((answer) => (
                    <Button
                      key={answer.id}
                      variant="outline"
                      className="w-full text-left justify-start h-auto p-4 hover:bg-purple-50 hover:border-purple-300"
                      onClick={() => handleAnswerSelect(answer.id)}
                      disabled={isSubmitting}
                    >
                      <span className="font-medium mr-3">{answer.id.toUpperCase()}.</span>
                      {answer.text}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}

export default TestPage
