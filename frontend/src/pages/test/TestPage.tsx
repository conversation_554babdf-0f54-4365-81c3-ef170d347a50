import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, RotateCcw, CheckCircle, Trophy, Home, Brain } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import { useAppSelector } from '@/store'
import { TestQuestion, TestAnswer } from '@/types'
import { cn } from '@/utils'

// 模拟测试数据
const mockQuestions: TestQuestion[] = [
  {
    id: '1',
    question: '你一般几点到达公司？',
    category: 'work_schedule',
    options: [
      { id: 'a', text: '提前30分钟，做好准备', score: { workPressure: 3, compliance: 8, creativity: 5, socialSkill: 6, resilience: 7 } },
      { id: 'b', text: '准时到达，不早不晚', score: { workPressure: 5, compliance: 6, creativity: 6, socialSkill: 6, resilience: 6 } },
      { id: 'c', text: '经常迟到5-10分钟', score: { workPressure: 7, compliance: 4, creativity: 7, socialSkill: 5, resilience: 5 } },
      { id: 'd', text: '迟到是常态，反正没人管', score: { workPressure: 9, compliance: 2, creativity: 8, socialSkill: 4, resilience: 3 } }
    ]
  },
  {
    id: '2',
    question: '面对老板的无理要求，你会？',
    category: 'workplace_pressure',
    options: [
      { id: 'a', text: '默默承受，完成任务', score: { workPressure: 8, compliance: 9, creativity: 3, socialSkill: 4, resilience: 6 } },
      { id: 'b', text: '礼貌地提出异议', score: { workPressure: 5, compliance: 6, creativity: 7, socialSkill: 8, resilience: 7 } },
      { id: 'c', text: '直接拒绝，据理力争', score: { workPressure: 3, compliance: 2, creativity: 8, socialSkill: 6, resilience: 9 } },
      { id: 'd', text: '阳奉阴违，表面答应', score: { workPressure: 6, compliance: 4, creativity: 8, socialSkill: 7, resilience: 5 } }
    ]
  },
  {
    id: '3',
    question: '工作中遇到困难时，你倾向于？',
    category: 'problem_solving',
    options: [
      { id: 'a', text: '自己琢磨，死磕到底', score: { workPressure: 6, compliance: 5, creativity: 8, socialSkill: 3, resilience: 8 } },
      { id: 'b', text: '主动向同事请教', score: { workPressure: 4, compliance: 6, creativity: 6, socialSkill: 8, resilience: 7 } },
      { id: 'c', text: '直接找领导汇报', score: { workPressure: 7, compliance: 8, creativity: 4, socialSkill: 6, resilience: 5 } },
      { id: 'd', text: '能躲就躲，能拖就拖', score: { workPressure: 8, compliance: 3, creativity: 5, socialSkill: 4, resilience: 3 } }
    ]
  },
  {
    id: '4',
    question: '你的摸鱼技能等级是？',
    category: 'work_efficiency',
    options: [
      { id: 'a', text: '青铜：偶尔看看手机', score: { workPressure: 5, compliance: 7, creativity: 6, socialSkill: 6, resilience: 6 } },
      { id: 'b', text: '黄金：摸鱼看剧样样通', score: { workPressure: 6, compliance: 4, creativity: 7, socialSkill: 7, resilience: 6 } },
      { id: 'c', text: '钻石：上班摸鱼下班躺', score: { workPressure: 7, compliance: 3, creativity: 8, socialSkill: 6, resilience: 4 } },
      { id: 'd', text: '王者：摸鱼我是专业的', score: { workPressure: 8, compliance: 2, creativity: 9, socialSkill: 8, resilience: 3 } }
    ]
  },
  {
    id: '5',
    question: '加班对你来说是？',
    category: 'overtime_attitude',
    options: [
      { id: 'a', text: '家常便饭，习以为常', score: { workPressure: 9, compliance: 8, creativity: 4, socialSkill: 5, resilience: 7 } },
      { id: 'b', text: '能避免就避免', score: { workPressure: 6, compliance: 5, creativity: 7, socialSkill: 6, resilience: 6 } },
      { id: 'c', text: '坚决不加班', score: { workPressure: 3, compliance: 3, creativity: 8, socialSkill: 7, resilience: 8 } },
      { id: 'd', text: '看心情，看钱', score: { workPressure: 5, compliance: 4, creativity: 7, socialSkill: 7, resilience: 6 } }
    ]
  }
]

const TestPage: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<TestAnswer[]>([])
  const [isCompleted, setIsCompleted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)

  const currentQuestion = mockQuestions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / mockQuestions.length) * 100
  const canGoNext = answers.find(a => a.questionId === currentQuestion?.id)
  const canGoPrevious = currentQuestionIndex > 0
  const isLastQuestion = currentQuestionIndex === mockQuestions.length - 1

  // 选择答案
  const handleSelectAnswer = (option: { id: string; text: string; score: any }) => {
    const newAnswer: TestAnswer = {
      questionId: currentQuestion.id,
      answerId: option.id,
      question: currentQuestion.question,
      answer: option.text
    }

    setAnswers(prev => {
      const existingIndex = prev.findIndex(a => a.questionId === currentQuestion.id)
      if (existingIndex >= 0) {
        const updated = [...prev]
        updated[existingIndex] = newAnswer
        return updated
      }
      return [...prev, newAnswer]
    })

    // 如果是最后一题，自动完成测试
    if (isLastQuestion) {
      setTimeout(() => handleComplete(), 500)
    }
  }

  // 下一题
  const handleNext = () => {
    if (currentQuestionIndex < mockQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }

  // 上一题
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  // 完成测试
  const handleComplete = async () => {
    setIsCompleted(true)
    setIsSubmitting(true)

    try {
      // 计算测试结果
      const result = calculateTestResult(answers)
      setTestResult(result)

      // 保存结果到本地存储
      localStorage.setItem('testResult', JSON.stringify(result))

      toast.success('测试完成！你的打工人类型已生成！')
    } catch (error) {
      toast.error('生成结果失败，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 重新开始测试
  const handleRestart = () => {
    setCurrentQuestionIndex(0)
    setAnswers([])
    setIsCompleted(false)
    setTestResult(null)
  }

  // 计算测试结果
  const calculateTestResult = (answers: TestAnswer[]) => {
    // 这里应该有实际的算法来计算结果
    // 暂时返回模拟结果
    const animalTypes = ['牛马', '社畜', '打工人', '职场精英', '摸鱼大师']
    const randomType = animalTypes[Math.floor(Math.random() * animalTypes.length)]

    return {
      animalType: randomType,
      description: `你是一个典型的${randomType}，具有独特的职场特征。`,
      traits: ['勤奋', '负责', '有创意'],
      score: Math.floor(Math.random() * 100) + 1
    }
  }

  // 获取当前问题的答案
  const getCurrentAnswer = () => {
    return answers.find(a => a.questionId === currentQuestion?.id)
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate(-1)}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回
          </Button>

          <div className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">职场人格测试</h1>
          </div>

          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="gap-2"
          >
            <Home className="w-4 h-4" />
            首页
          </Button>
        </div>

        {!isCompleted ? (
          <>
            {/* 进度条 */}
            <Card className="mb-8">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium text-foreground">
                    问题 {currentQuestionIndex + 1} / {mockQuestions.length}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {Math.round(progress)}% 完成
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <motion.div
                    className="bg-primary h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 问题卡片 */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="text-xl text-foreground">
                  {currentQuestion.question}
                </CardTitle>
                <CardDescription>
                  请选择最符合你情况的选项
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {currentQuestion.options.map((option, index) => {
                    const isSelected = getCurrentAnswer()?.answerId === option.id
                    return (
                      <motion.div
                        key={option.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Button
                          variant={isSelected ? "default" : "outline"}
                          className={cn(
                            "w-full justify-start text-left h-auto p-4",
                            isSelected && "ring-2 ring-primary"
                          )}
                          onClick={() => handleSelectAnswer(option)}
                        >
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                              isSelected ? "bg-primary-foreground border-primary-foreground" : "border-muted-foreground"
                            )}>
                              {isSelected && <CheckCircle className="w-4 h-4 text-primary" />}
                            </div>
                            <span className="flex-1">{option.text}</span>
                          </div>
                        </Button>
                      </motion.div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* 导航按钮 */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={!canGoPrevious}
                className="gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                上一题
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  onClick={handleRestart}
                  className="gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  重新开始
                </Button>

                {!isLastQuestion && (
                  <Button
                    onClick={handleNext}
                    disabled={!canGoNext}
                    className="gap-2"
                  >
                    下一题
                    <ArrowLeft className="w-4 h-4 rotate-180" />
                  </Button>
                )}
              </div>
            </div>
          </>
        ) : (
          /* 测试结果 */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto text-center"
          >
            <Card>
              <CardHeader>
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                  <Trophy className="w-10 h-10 text-primary-foreground" />
                </div>
                <CardTitle className="text-2xl text-foreground">
                  测试完成！
                </CardTitle>
                <CardDescription>
                  你的打工人类型已生成
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isSubmitting ? (
                  <div className="py-8">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-8 h-8 mx-auto mb-4"
                    >
                      <Brain className="w-8 h-8 text-primary" />
                    </motion.div>
                    <p className="text-muted-foreground">正在分析你的答案...</p>
                  </div>
                ) : testResult ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <Badge variant="secondary" className="text-lg px-4 py-2 mb-4">
                        {testResult.animalType}
                      </Badge>
                      <p className="text-foreground mb-4">{testResult.description}</p>
                      <div className="flex flex-wrap justify-center gap-2 mb-6">
                        {testResult.traits.map((trait: string, index: number) => (
                          <Badge key={index} variant="outline">
                            {trait}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-4 justify-center">
                      <Button onClick={handleRestart} variant="outline">
                        重新测试
                      </Button>
                      <Button onClick={() => navigate('/')}>
                        返回首页
                      </Button>
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )

export default TestPage