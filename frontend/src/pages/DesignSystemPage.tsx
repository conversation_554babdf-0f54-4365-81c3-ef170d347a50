import { Button } from '@/components/ui/button'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

export default function DesignSystemPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-display font-semibold text-neutral-900 mb-4">
            设计系统展示
          </h1>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            专业而不失温度 · 简约而不简单 · 成熟而不沉闷
          </p>
        </div>

        <div className="space-y-16">
          {/* 色彩系统 */}
          <section>
            <h2 className="text-2xl font-display font-semibold text-neutral-900 mb-8">色彩系统</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* 主色调 */}
              <Card>
                <CardHeader title="主色调" subtitle="专业而温暖的蓝色系" />
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-12 bg-primary-500 rounded-lg flex items-center justify-center text-white font-medium">
                      Primary 500
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="h-8 bg-primary-300 rounded text-xs flex items-center justify-center text-white">300</div>
                      <div className="h-8 bg-primary-600 rounded text-xs flex items-center justify-center text-white">600</div>
                      <div className="h-8 bg-primary-800 rounded text-xs flex items-center justify-center text-white">800</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 辅助色 */}
              <Card>
                <CardHeader title="辅助色" subtitle="温暖的橙色系" />
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-12 bg-secondary-500 rounded-lg flex items-center justify-center text-white font-medium">
                      Secondary 500
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="h-8 bg-secondary-300 rounded text-xs flex items-center justify-center text-white">300</div>
                      <div className="h-8 bg-secondary-600 rounded text-xs flex items-center justify-center text-white">600</div>
                      <div className="h-8 bg-secondary-800 rounded text-xs flex items-center justify-center text-white">800</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 中性色 */}
              <Card>
                <CardHeader title="中性色" subtitle="温暖的灰色系" />
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-12 bg-neutral-500 rounded-lg flex items-center justify-center text-white font-medium">
                      Neutral 500
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="h-8 bg-neutral-200 rounded text-xs flex items-center justify-center text-neutral-700">200</div>
                      <div className="h-8 bg-neutral-600 rounded text-xs flex items-center justify-center text-white">600</div>
                      <div className="h-8 bg-neutral-800 rounded text-xs flex items-center justify-center text-white">800</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* 按钮系统 */}
          <section>
            <h2 className="text-2xl font-display font-semibold text-neutral-900 mb-8">按钮系统</h2>
            <Card>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  <div className="space-y-4">
                    <h3 className="font-medium text-neutral-900">主要按钮</h3>
                    <div className="space-y-3">
                      <Button size="sm">小按钮</Button>
                      <Button size="md">中按钮</Button>
                      <Button size="lg">大按钮</Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-medium text-neutral-900">次要按钮</h3>
                    <div className="space-y-3">
                      <Button variant="secondary" size="sm">小按钮</Button>
                      <Button variant="secondary" size="md">中按钮</Button>
                      <Button variant="secondary" size="lg">大按钮</Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-medium text-neutral-900">轮廓按钮</h3>
                    <div className="space-y-3">
                      <Button variant="outline" size="sm">小按钮</Button>
                      <Button variant="outline" size="md">中按钮</Button>
                      <Button variant="outline" size="lg">大按钮</Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-medium text-neutral-900">幽灵按钮</h3>
                    <div className="space-y-3">
                      <Button variant="ghost" size="sm">小按钮</Button>
                      <Button variant="ghost" size="md">中按钮</Button>
                      <Button variant="ghost" size="lg">大按钮</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* 卡片系统 */}
          <section>
            <h2 className="text-2xl font-display font-semibold text-neutral-900 mb-8">卡片系统</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader title="默认卡片" subtitle="基础样式" />
                <CardContent>
                  <p className="text-neutral-600">这是一个默认样式的卡片，具有简洁的边框和阴影效果。</p>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardHeader title="悬浮卡片" subtitle="增强阴影" />
                <CardContent>
                  <p className="text-neutral-600">这是一个悬浮样式的卡片，具有更明显的阴影效果。</p>
                </CardContent>
              </Card>

              <Card variant="interactive">
                <CardHeader title="交互卡片" subtitle="可点击" />
                <CardContent>
                  <p className="text-neutral-600">这是一个交互式卡片，鼠标悬停时会有动画效果。</p>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* 表单系统 */}
          <section>
            <h2 className="text-2xl font-display font-semibold text-neutral-900 mb-8">表单系统</h2>
            <Card>
              <CardHeader title="输入框样式" subtitle="各种状态展示" />
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <Input 
                      label="默认输入框" 
                      placeholder="请输入内容"
                      helperText="这是帮助文本"
                      fullWidth
                    />
                    <Input 
                      label="成功状态" 
                      placeholder="输入正确"
                      success="输入格式正确"
                      fullWidth
                    />
                  </div>
                  <div className="space-y-6">
                    <Input 
                      label="错误状态" 
                      placeholder="输入错误"
                      error="请输入有效的内容"
                      fullWidth
                    />
                    <Input 
                      label="禁用状态" 
                      placeholder="禁用输入"
                      disabled
                      fullWidth
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </div>
    </div>
  )
}
