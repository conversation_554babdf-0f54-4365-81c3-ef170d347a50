import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Home, RefreshCw, AlertTriangle } from 'lucide-react'

const ServerErrorPage: React.FC = () => {
  const navigate = useNavigate()

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-100 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 动画数字 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500 mb-4">
            500
          </div>
          <motion.div
            animate={{ rotate: [0, 15, -15, 0] }}
            transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }}
            className="text-6xl"
          >
            🔥
          </motion.div>
        </motion.div>

        {/* 标题和描述 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            服务器开小差了...
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            我们的服务器正在经历一些技术问题
          </p>
          <p className="text-gray-500">
            请稍后再试，或者联系技术支持
          </p>
        </motion.div>

        {/* 错误信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-8 shadow-lg mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-orange-500" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">可能的情况：</h2>
          <div className="grid md:grid-cols-2 gap-4 text-left text-gray-600">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
              <span>服务器正在维护</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
              <span>数据库连接异常</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
              <span>服务器负载过高</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
              <span>网络连接问题</span>
            </div>
          </div>
        </motion.div>

        {/* 解决方案 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-blue-50 rounded-2xl p-8 mb-8"
        >
          <h3 className="text-lg font-semibold text-blue-900 mb-4">您可以尝试：</h3>
          <div className="grid md:grid-cols-2 gap-4 text-left text-blue-700">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <span>等待几分钟后刷新页面</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <span>检查您的网络连接</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <span>清除浏览器缓存</span>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <span>尝试使用其他浏览器</span>
            </div>
          </div>
        </motion.div>

        {/* 操作按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
        >
          <button
            onClick={handleRefresh}
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white font-medium rounded-lg hover:from-red-600 hover:to-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            刷新页面
          </button>
          
          <button
            onClick={() => navigate('/')}
            className="inline-flex items-center px-6 py-3 bg-white text-gray-700 font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm"
          >
            <Home className="w-5 h-5 mr-2" />
            回到首页
          </button>
        </motion.div>

        {/* 服务状态 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0 }}
          className="text-center"
        >
          <div className="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm mb-4">
            <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse" />
            我们正在修复这个问题
          </div>
          <p className="text-gray-500 text-sm">
            预计修复时间：5-10分钟
          </p>
        </motion.div>

        {/* 联系信息 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2 }}
          className="mt-12 p-6 bg-gray-50 rounded-2xl"
        >
          <h4 className="font-semibold text-gray-900 mb-2">需要帮助？</h4>
          <p className="text-gray-600 text-sm mb-4">
            如果问题持续存在，请联系我们的技术团队
          </p>
          <div className="flex flex-wrap justify-center gap-3">
            <span className="px-3 py-1 bg-white rounded-full text-xs text-gray-600">
              📧 <EMAIL>
            </span>
            <span className="px-3 py-1 bg-white rounded-full text-xs text-gray-600">
              📱 400-123-4567
            </span>
            <span className="px-3 py-1 bg-white rounded-full text-xs text-gray-600">
              💬 在线客服
            </span>
          </div>
        </motion.div>

        {/* 错误代码 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.4 }}
          className="mt-8 text-xs text-gray-400"
        >
          错误代码: 500 | 时间戳: {new Date().toLocaleString()}
        </motion.div>
      </div>
    </div>
  )
}

export default ServerErrorPage