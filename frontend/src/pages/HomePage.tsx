import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppSelector } from '@/store'
import toast from 'react-hot-toast'

export default function HomePage() {
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)
  const [hasGeneratedAvatar, setHasGeneratedAvatar] = useState(false)
  const [isCheckingAvatar, setIsCheckingAvatar] = useState(true)
  
  useEffect(() => {
    // 检查用户是否已经生成过头像
    const checkAvatar = async () => {
      setIsCheckingAvatar(true)
      const userAvatar = localStorage.getItem('userGeneratedAvatar')
      const userDrawing = localStorage.getItem('userDrawing')
      const hasAvatar = !!userAvatar || !!currentUser?.avatar
      
      console.log('头像检查:', { userAvatar: !!userAvatar, userDrawing: !!userDrawing, currentUserAvatar: !!currentUser?.avatar, hasAvatar })
      
      setHasGeneratedAvatar(hasAvatar)
      setIsCheckingAvatar(false)
    }
    checkAvatar()
  }, [currentUser])

  const handleGoToTest = () => {
    navigate('/test')
  }

  const handleGoToDrawing = () => {
    navigate('/drawing')
  }

  const handleGoToZoo = () => {
    console.log('尝试进入动物园:', { hasGeneratedAvatar })
    if (!hasGeneratedAvatar) {
      console.log('没有头像，重定向到测试页面')
      toast.error('请先完成人格测试并生成你的打工人形象！')
      navigate('/test')
      return
    }
    console.log('进入动物园')
    navigate('/zoo')
  }

  const handleGoToRanking = () => {
    navigate('/ranking')
  }

  const handleGoToPosts = () => {
    navigate('/posts')
  }

  // 调试功能：模拟生成头像
  const handleDebugGenerateAvatar = () => {
    const mockAvatarData = {
      animalType: 'OXHORSE',
      avatarUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIyNSIgeT0iNjAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzQ2ODJCNCIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjQwIiByPSIyMCIgZmlsbD0iIzhCNDUxMyIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI0NSIgY3k9IjM1IiByPSIzIiBmaWxsPSIjMDAwIi8+PGNpcmNsZSBjeD0iNTUiIGN5PSIzNSIgcj0iMyIgZmlsbD0iIzAwMCIvPjxwYXRoIGQ9Ik0gNDAgNDUgUSA1MCA1MCA2MCA0NSIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz48cmVjdCB4PSI1IiB5PSI2NSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjgiIGZpbGw9IiNGREJDQjQiLz48cmVjdCB4PSI3NSIgeT0iNjUiIHdpZHRoPSIyMCIgaGVpZ2h0PSI4IiBmaWxsPSIjRkRCQ0I0Ii8+PHJlY3QgeD0iMzAiIHk9IjEyMCIgd2lkdGg9IjgiIGhlaWdodD0iMjAiIGZpbGw9IiMwMDAiLz48cmVjdCB4PSI2MiIgeT0iMTIwIiB3aWR0aD0iOCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==',
      generatedAt: new Date().toISOString(),
      userId: currentUser?.id || 'debug-user'
    }
    
    localStorage.setItem('userGeneratedAvatar', JSON.stringify(mockAvatarData))
    localStorage.setItem('userDrawing', 'data:image/png;base64,mock-drawing-data')
    
    setHasGeneratedAvatar(true)
    toast.success('调试：模拟头像生成完成！')
  }

  // 调试功能：清除头像数据
  const handleDebugClearAvatar = () => {
    localStorage.removeItem('userGeneratedAvatar')
    localStorage.removeItem('userDrawing')
    setHasGeneratedAvatar(false)
    toast.info('调试：头像数据已清除')
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="pt-10 mx-auto max-w-7xl px-4 sm:pt-12 sm:px-6 md:pt-16 lg:pt-20 lg:px-8 xl:pt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-on-dark sm:text-5xl md:text-6xl animate-float">
                  <span className="block xl:inline text-shadow-dark">🌟 欢迎来到</span>{' '}
                  <span className="block text-yellow-300 xl:inline text-shadow-dark animate-pulse-glow">✨牛马动物园✨</span>
                </h1>
                <p className="mt-3 text-base text-on-dark sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0 text-shadow-dark">
                  🎭 测试你的人格，生成专属的打工人牛马形象，在动物园中和其他牛马一起生活工作！ 🎪
                </p>
                
                {/* 用户状态显示 */}
                <div className="mt-8 status-card p-6 animate-slide-up">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-primary">
                        👋 你好，{currentUser?.username || '打工人'}
                      </h3>
                      <p className="text-secondary font-medium">
                        {isCheckingAvatar 
                          ? '🔄 检查头像状态中...'
                          : hasGeneratedAvatar 
                            ? '✅ 你已经拥有专属的牛马形象' 
                            : '❌ 还没有生成专属形象'}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-4xl animate-bounce-gentle">
                        {isCheckingAvatar ? '⏳' : hasGeneratedAvatar ? '🐂' : '❓'}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="mt-8 sm:mt-8 sm:flex sm:justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-4">
                  <button
                    onClick={hasGeneratedAvatar ? handleGoToZoo : handleGoToDrawing}
                    className="btn btn-primary btn-lg w-full sm:w-auto animate-wiggle"
                  >
                    {hasGeneratedAvatar ? '🎪 进入动物园' : '🎨 画出自己'}
                  </button>
                  <button
                    onClick={handleGoToTest}
                    className="btn btn-secondary btn-lg w-full sm:w-auto"
                  >
                    🔮 人格测试
                  </button>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600 opacity-15 animate-pulse"></div>
            <div className="text-8xl animate-float z-10 drop-shadow-2xl">
              🎪
            </div>
            <div className="absolute top-10 left-10 text-4xl animate-bounce-gentle drop-shadow-xl">
              🎠
            </div>
            <div className="absolute bottom-10 right-10 text-3xl animate-wiggle drop-shadow-xl">
              🎭
            </div>
            <div className="absolute top-1/2 left-1/4 text-2xl animate-float drop-shadow-lg" style={{animationDelay: '1s'}}>
              ✨
            </div>
            <div className="absolute bottom-1/4 left-1/2 text-2xl animate-float drop-shadow-lg" style={{animationDelay: '2s'}}>
              🌟
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 relative">
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="lg:text-center">
            <h2 className="text-lg text-yellow-300 font-bold tracking-wide uppercase animate-pulse-glow text-shadow-dark">🎮 功能介绍</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-on-dark sm:text-4xl animate-slide-up text-shadow-dark">
              🦄 成为独一无二的打工牛马 🦄
            </p>
            <p className="mt-4 max-w-2xl text-xl text-on-dark lg:mx-auto text-shadow-dark">
              🎭 通过人格测试，生成专属的动物形象，在虚拟动物园中体验不同的工作生活 ✨
            </p>
          </div>

          <div className="mt-10">
            <div className="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3">
              {/* Feature 1: 人格测试 */}
              <div className="card-hover cursor-pointer animate-slide-up p-8" onClick={handleGoToTest} style={{animationDelay: '0.1s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-bounce-gentle mx-auto">
                  🧠
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">✨ AI人格测试</h3>
                <p className="text-secondary text-center leading-relaxed">
                  通过先进的AI分析，深入了解你的人格特征，为你量身定制专属的动物形象 🎭
                </p>
              </div>

              {/* Feature 2: 动物园 */}
              <div className="card-hover cursor-pointer animate-slide-up p-8" onClick={handleGoToZoo} style={{animationDelay: '0.2s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-float mx-auto">
                  🎪
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">🎠 虚拟动物园</h3>
                <p className="text-secondary text-center leading-relaxed">
                  在3D虚拟动物园中，与其他打工人牛马互动，体验不同区域的工作和生活 🌟
                </p>
              </div>

              {/* Feature 3: 社区 */}
              <div className="card-hover cursor-pointer animate-slide-up p-8" onClick={handleGoToPosts} style={{animationDelay: '0.3s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-wiggle mx-auto">
                  💬
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">🤝 打工人社区</h3>
                <p className="text-secondary text-center leading-relaxed">
                  分享你的打工心得，与其他牛马交流经验，建立属于打工人的社交网络 💫
                </p>
              </div>

              {/* Feature 4: 排行榜 */}
              <div className="card-hover cursor-pointer animate-slide-up p-8" onClick={handleGoToRanking} style={{animationDelay: '0.4s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-pulse-glow mx-auto">
                  🏆
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">🌟 牛马排行榜</h3>
                <p className="text-secondary text-center leading-relaxed">
                  查看最勤奋的打工牛马，比较工作效率，争当最佳员工 🚀
                </p>
              </div>

              {/* Feature 5: 个性化 */}
              <div className="card-hover cursor-pointer animate-slide-up p-8" onClick={handleGoToDrawing} style={{animationDelay: '0.5s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-float mx-auto">
                  🎨
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">🎨 AI绘画生成</h3>
                <p className="text-secondary text-center leading-relaxed">
                  画出你的自画像，AI将分析你的绘画风格并生成专属的打工人形象 ✨
                </p>
              </div>

              {/* Feature 6: 实时互动 */}
              <div className="card-hover animate-slide-up p-8" style={{animationDelay: '0.6s'}}>
                <div className="flex items-center justify-center h-16 w-16 rounded-full text-4xl mb-6 animate-bounce-gentle mx-auto">
                  ⚡
                </div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">💫 实时互动</h3>
                <p className="text-secondary text-center leading-relaxed">
                  在动物园中实时移动，与其他玩家互动，体验真实的社交乐趣 🎮
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Debug Section - 仅开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="py-12 relative">
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
          <div className="max-w-4xl mx-auto text-center px-4 relative">
            <h3 className="text-xl font-bold text-yellow-300 mb-4 animate-pulse-glow text-shadow-dark">🔧 调试工具</h3>
            <div className="flex justify-center space-x-4 flex-wrap gap-4">
              <button
                onClick={handleDebugGenerateAvatar}
                className="btn btn-outline btn-sm"
              >
                模拟生成头像
              </button>
              <button
                onClick={handleDebugClearAvatar}
                className="btn btn-outline btn-sm"
              >
                清除头像数据
              </button>
              <button
                onClick={handleGoToZoo}
                className="btn btn-outline btn-sm"
              >
                强制进入动物园
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="relative py-20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 opacity-30"></div>
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative">
          <h2 className="text-4xl font-extrabold text-white sm:text-5xl animate-float">
            <span className="block drop-shadow-lg">🎉 准备好成为打工牛马了吗？ 🎉</span>
          </h2>
          <p className="mt-4 text-xl leading-6 text-white/90 drop-shadow-md animate-slide-up">
            🌟 加入数百万打工人，在虚拟动物园中找到属于你的位置 ✨
          </p>
          <div className="mt-12 flex justify-center space-x-6 flex-wrap gap-4">
            <button
              onClick={hasGeneratedAvatar ? handleGoToZoo : handleGoToDrawing}
              className="btn btn-primary btn-lg animate-bounce-gentle"
            >
              {hasGeneratedAvatar ? '🎪 立即进入动物园' : '🎨 开始绘画'}
            </button>
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="btn btn-outline btn-lg animate-float"
            >
              📖 了解更多
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}