import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppSelector } from '@/store'
import toast from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function HomePage() {
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)
  const [hasGeneratedAvatar, setHasGeneratedAvatar] = useState(false)
  const [isCheckingAvatar, setIsCheckingAvatar] = useState(true)
  
  useEffect(() => {
    // 检查用户是否已经生成过头像
    const checkAvatar = async () => {
      setIsCheckingAvatar(true)
      const userAvatar = localStorage.getItem('userGeneratedAvatar')
      const userDrawing = localStorage.getItem('userDrawing')
      const hasAvatar = !!userAvatar || !!currentUser?.avatar
      
      console.log('头像检查:', { userAvatar: !!userAvatar, userDrawing: !!userDrawing, currentUserAvatar: !!currentUser?.avatar, hasAvatar })
      
      setHasGeneratedAvatar(hasAvatar)
      setIsCheckingAvatar(false)
    }
    checkAvatar()
  }, [currentUser])

  const handleGoToTest = () => {
    navigate('/test')
  }

  const handleGoToDrawing = () => {
    navigate('/drawing')
  }

  const handleGoToZoo = () => {
    console.log('尝试进入动物园:', { hasGeneratedAvatar })
    if (!hasGeneratedAvatar) {
      console.log('没有头像，重定向到测试页面')
      toast.error('请先完成人格测试并生成你的打工人形象！')
      navigate('/test')
      return
    }
    console.log('进入动物园')
    navigate('/zoo')
  }

  const handleGoToRanking = () => {
    navigate('/ranking')
  }

  const handleGoToPosts = () => {
    navigate('/posts')
  }

  // 调试功能：模拟生成头像
  const handleDebugGenerateAvatar = () => {
    const mockAvatarData = {
      animalType: 'OXHORSE',
      avatarUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIyNSIgeT0iNjAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzQ2ODJCNCIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjQwIiByPSIyMCIgZmlsbD0iIzhCNDUxMyIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI0NSIgY3k9IjM1IiByPSIzIiBmaWxsPSIjMDAwIi8+PGNpcmNsZSBjeD0iNTUiIGN5PSIzNSIgcj0iMyIgZmlsbD0iIzAwMCIvPjxwYXRoIGQ9Ik0gNDAgNDUgUSA1MCA1MCA2MCA0NSIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz48cmVjdCB4PSI1IiB5PSI2NSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjgiIGZpbGw9IiNGREJDQjQiLz48cmVjdCB4PSI3NSIgeT0iNjUiIHdpZHRoPSIyMCIgaGVpZ2h0PSI4IiBmaWxsPSIjRkRCQ0I0Ii8+PHJlY3QgeD0iMzAiIHk9IjEyMCIgd2lkdGg9IjgiIGhlaWdodD0iMjAiIGZpbGw9IiMwMDAiLz48cmVjdCB4PSI2MiIgeT0iMTIwIiB3aWR0aD0iOCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==',
      generatedAt: new Date().toISOString(),
      userId: currentUser?.id || 'debug-user'
    }
    
    localStorage.setItem('userGeneratedAvatar', JSON.stringify(mockAvatarData))
    localStorage.setItem('userDrawing', 'data:image/png;base64,mock-drawing-data')
    
    setHasGeneratedAvatar(true)
    toast.success('调试：模拟头像生成完成！')
  }

  // 调试功能：清除头像数据
  const handleDebugClearAvatar = () => {
    localStorage.removeItem('userGeneratedAvatar')
    localStorage.removeItem('userDrawing')
    setHasGeneratedAvatar(false)
    toast.info('调试：头像数据已清除')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div className="space-y-8 animate-fade-in">
              <div>
                <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight text-shadow">
                  欢迎来到
                  <span className="text-primary block animate-bounce-gentle">牛马动物园</span>
                </h1>
                <p className="mt-4 text-lg text-muted-foreground leading-relaxed animate-slide-up">
                  通过专业的人格测试和AI绘画，发现你在职场中的动物形象
                </p>
              </div>

              {/* 用户状态卡片 */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className={`w-3 h-3 rounded-full ${hasGeneratedAvatar ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                        <CardTitle className="text-base">
                          {isCheckingAvatar
                            ? '检查状态中...'
                            : hasGeneratedAvatar
                              ? '已完成人格测试'
                              : '待完成人格测试'}
                        </CardTitle>
                      </div>
                      <CardDescription>
                        {isCheckingAvatar
                          ? '正在验证你的测试结果'
                          : hasGeneratedAvatar
                            ? '你已经拥有专属的职场形象，可以开始探索了'
                            : '完成测试后，你将获得独特的职场动物形象'}
                      </CardDescription>
                    </div>
                    <div className="ml-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        hasGeneratedAvatar ? 'bg-green-100' : 'bg-yellow-100'
                      }`}>
                        <span className="text-xl">
                          {isCheckingAvatar ? '⏳' : hasGeneratedAvatar ? '✅' : '📝'}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
                
              {/* 主要操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={hasGeneratedAvatar ? handleGoToZoo : handleGoToDrawing}
                  size="lg"
                  className="flex-1 sm:flex-none"
                >
                  {hasGeneratedAvatar ? '🎪 进入动物园' : '🎨 开始绘制'}
                </Button>
                <Button
                  onClick={handleGoToTest}
                  variant="outline"
                  size="lg"
                  className="flex-1 sm:flex-none"
                >
                  🧪 人格测试
                </Button>
              </div>
            </div>

            {/* 右侧视觉区域 */}
            <div className="hidden lg:block animate-float">
              <div className="relative">
                <Card className="w-full h-96 bg-gradient-to-br from-primary/10 to-secondary/10 border-0 shadow-2xl">
                  <CardContent className="h-full flex items-center justify-center p-8">
                    <div className="text-center space-y-4">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center shadow-lg animate-bounce-gentle">
                        <span className="text-4xl text-white">🎯</span>
                      </div>
                    <div className="space-y-2">
                      <h3 className="font-medium text-neutral-900">发现你的职场人格</h3>
                      <p className="text-sm text-neutral-600">专业测评 · 个性形象 · 社交互动</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              探索更多功能
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              通过专业的人格测试，发现你的职场特质，与志同道合的伙伴建立连接
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Feature 1: 人格测试 */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleGoToTest}>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">📊</span>
                </div>
                <CardTitle className="text-base mb-2">人格测试</CardTitle>
                <CardDescription>
                  专业的心理学测评，深入了解你的职场特质
                </CardDescription>
              </CardContent>
            </Card>

            {/* Feature 2: 动物园 */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleGoToZoo}>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-secondary/10 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">🎯</span>
                </div>
                <CardTitle className="text-base mb-2">虚拟动物园</CardTitle>
                <CardDescription>
                  3D互动环境，与其他用户进行社交互动
                </CardDescription>
              </CardContent>
            </Card>

            {/* Feature 3: 社区 */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleGoToPosts}>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">💬</span>
                </div>
                <CardTitle className="text-base mb-2">社区交流</CardTitle>
                <CardDescription>
                  分享经验，与志同道合的伙伴建立连接
                </CardDescription>
              </CardContent>
            </Card>

            {/* Feature 4: 排行榜 */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleGoToRanking}>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">🏆</span>
                </div>
                <CardTitle className="text-base mb-2">牛马排行榜</CardTitle>
                <CardDescription>
                  查看最勤奋的打工牛马，比较工作效率
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Debug Section - 仅开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="py-12 bg-muted/50">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h3 className="text-xl font-bold text-foreground mb-4">🔧 调试工具</h3>
            <div className="flex justify-center space-x-4 flex-wrap gap-4">
              <Button
                onClick={handleDebugGenerateAvatar}
                variant="outline"
                size="sm"
              >
                模拟生成头像
              </Button>
              <Button
                onClick={handleDebugClearAvatar}
                variant="outline"
                size="sm"
              >
                清除头像数据
              </Button>
              <Button
                onClick={handleGoToZoo}
                variant="outline"
                size="sm"
              >
                强制进入动物园
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="relative py-20 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-foreground sm:text-5xl">
            🎉 准备好成为打工牛马了吗？ 🎉
          </h2>
          <p className="mt-4 text-xl text-muted-foreground">
            🌟 加入数百万打工人，在虚拟动物园中找到属于你的位置 ✨
          </p>
          <div className="mt-12 flex justify-center space-x-6 flex-wrap gap-4">
            <Button
              onClick={hasGeneratedAvatar ? handleGoToZoo : handleGoToDrawing}
              size="lg"
              className="text-lg"
            >
              {hasGeneratedAvatar ? '🎪 立即进入动物园' : '🎨 开始绘画'}
            </Button>
            <Button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              variant="outline"
              size="lg"
              className="text-lg"
            >
              📖 了解更多
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}