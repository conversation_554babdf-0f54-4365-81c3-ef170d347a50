import { Link } from 'react-router-dom'

export default function WelcomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 flex items-center justify-center px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* 简约的视觉标识 */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-large">
            <span className="text-4xl text-white">🎯</span>
          </div>
        </div>

        {/* 专业而温暖的标题 */}
        <h1 className="text-4xl lg:text-5xl font-display font-semibold text-neutral-900 mb-6 leading-tight">
          发现你的
          <span className="text-primary-600"> 职场人格</span>
        </h1>

        {/* 简洁的描述 */}
        <p className="text-lg text-neutral-600 mb-12 max-w-2xl mx-auto leading-relaxed">
          通过专业的人格测试，了解你的工作风格，
          <br className="hidden sm:block" />
          找到属于你的职场动物形象
        </p>

        {/* 精致的操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="/register"
            className="btn btn-primary btn-lg w-full sm:w-auto min-w-[160px]"
          >
            开始测试
          </Link>
          <Link
            to="/login"
            className="btn btn-outline btn-lg w-full sm:w-auto min-w-[160px]"
          >
            已有账号
          </Link>
        </div>

        {/* 简约的特性说明 */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-primary-100 rounded-xl flex items-center justify-center">
              <span className="text-xl text-primary-600">📊</span>
            </div>
            <h3 className="font-medium text-neutral-900 mb-2">专业测评</h3>
            <p className="text-sm text-neutral-600">科学的人格分析模型</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-secondary-100 rounded-xl flex items-center justify-center">
              <span className="text-xl text-secondary-600">🎨</span>
            </div>
            <h3 className="font-medium text-neutral-900 mb-2">个性形象</h3>
            <p className="text-sm text-neutral-600">独特的动物形象生成</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-success-100 rounded-xl flex items-center justify-center">
              <span className="text-xl text-success-600">🤝</span>
            </div>
            <h3 className="font-medium text-neutral-900 mb-2">社交互动</h3>
            <p className="text-sm text-neutral-600">与同类型伙伴交流</p>
          </div>
        </div>
      </div>
    </div>
  )
}