import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, ArrowRight, Save, Eye, EyeOff } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { useAppSelector } from '@/store'
import DrawingCanvas from '@/components/drawing/DrawingCanvas'
import AIGenerator from '@/components/ai/AIGenerator'

type DrawingStep = 'intro' | 'drawing' | 'preview' | 'ai-generation'

export default function DrawingPage() {
  const [currentStep, setCurrentStep] = useState<DrawingStep>('intro')
  const [drawingData, setDrawingData] = useState<string>('')
  const [showPreview, setShowPreview] = useState(true)
  
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)

  // 绘画数据变化处理
  const handleDrawingChange = useCallback((imageData: string) => {
    setDrawingData(imageData)
  }, [])

  // 步骤导航
  const goToStep = useCallback((step: DrawingStep) => {
    setCurrentStep(step)
  }, [])

  // 下一步
  const nextStep = useCallback(() => {
    if (currentStep === 'intro') {
      setCurrentStep('drawing')
    } else if (currentStep === 'drawing') {
      if (!drawingData) {
        toast.error('请先完成你的自画像绘制')
        return
      }
      setCurrentStep('preview')
    } else if (currentStep === 'preview') {
      setCurrentStep('ai-generation')
    }
  }, [currentStep, drawingData])

  // 上一步
  const prevStep = useCallback(() => {
    if (currentStep === 'drawing') {
      setCurrentStep('intro')
    } else if (currentStep === 'preview') {
      setCurrentStep('drawing')
    } else if (currentStep === 'ai-generation') {
      setCurrentStep('preview')
    }
  }, [currentStep])

  // 保存绘画
  const saveDrawing = useCallback(() => {
    if (drawingData) {
      localStorage.setItem('userDrawing', drawingData)
      toast.success('绘画已保存')
    }
  }, [drawingData])

  // 获取步骤进度
  const getStepProgress = useCallback(() => {
    const steps = ['intro', 'drawing', 'preview', 'ai-generation']
    return ((steps.indexOf(currentStep) + 1) / steps.length) * 100
  }, [currentStep])

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8 animate-slide-up">
          <button
            onClick={() => navigate(-1)}
            className="btn btn-ghost btn-sm animate-wiggle"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            🔙 返回
          </button>
          
          {/* 进度指示器 */}
          <div className="flex-1 max-w-md mx-8 animate-float">
            <div className="flex items-center justify-between text-sm text-purple-600 mb-3 font-bold">
              <span className={currentStep === 'intro' ? 'text-pink-600 animate-pulse-glow' : ''}>
                📖 介绍
              </span>
              <span className={currentStep === 'drawing' ? 'text-pink-600 animate-pulse-glow' : ''}>
                🎨 绘画
              </span>
              <span className={currentStep === 'preview' ? 'text-pink-600 animate-pulse-glow' : ''}>
                👀 预览
              </span>
              <span className={currentStep === 'ai-generation' ? 'text-pink-600 animate-pulse-glow' : ''}>
                🤖 AI生成
              </span>
            </div>
            <div className="w-full bg-white/60 rounded-full h-3 shadow-md backdrop-blur-sm">
              <motion.div
                className="bg-gradient-to-r from-pink-400 to-purple-500 h-3 rounded-full shadow-lg animate-pulse-glow"
                initial={{ width: 0 }}
                animate={{ width: `${getStepProgress()}%` }}
                transition={{ duration: 0.5, type: "spring" }}
              />
            </div>
          </div>

          <div className="text-sm text-purple-700 font-bold animate-bounce-gentle">
            👤 {currentUser?.username || '匿名访客'} ✨
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="max-w-6xl mx-auto">
          <AnimatePresence mode="wait">
            {/* 介绍步骤 */}
            {currentStep === 'intro' && (
              <motion.div
                key="intro"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="text-center space-y-8"
              >
                <div className="max-w-2xl mx-auto">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: 'spring' }}
                    className="w-40 h-40 mx-auto mb-8 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl animate-pulse-glow"
                  >
                    <span className="text-8xl animate-bounce-gentle">🎨</span>
                  </motion.div>

                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-5xl font-bold text-white mb-8 drop-shadow-lg animate-float"
                  >
                    🌟 画出你的打工人自画像 ✨
                  </motion.h1>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-xl text-white/90 mb-10 leading-relaxed drop-shadow-md animate-slide-up"
                  >
                    🎪 欢迎来到AI打工人生成器！请用画笔画出你心中的自己，<br />
                    🤖 AI将基于你的绘画风格分析你的性格特征，<br />
                    ✨ 并为你生成专属的拟人化打工人形象。🐂
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="card-gradient p-8 mb-10 animate-slide-up"
                  >
                    <h2 className="text-2xl font-bold text-purple-800 mb-6 text-center animate-wiggle">🎯 绘画提示</h2>
                    <div className="grid md:grid-cols-3 gap-6 text-left">
                      <div className="flex items-start space-x-4 animate-float">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg animate-bounce-gentle">
                          <span className="text-2xl">👤</span>
                        </div>
                        <div>
                          <h3 className="font-bold text-purple-800 mb-2 text-lg">🎭 画出你的脸</h3>
                          <p className="text-purple-600 leading-relaxed">包括眼睛、鼻子、嘴巴等基本特征 ✨</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-4 animate-float" style={{ animationDelay: "0.2s" }}>
                        <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg animate-bounce-gentle">
                          <span className="text-2xl">✍️</span>
                        </div>
                        <div>
                          <h3 className="font-bold text-purple-800 mb-2 text-lg">🚀 自由发挥</h3>
                          <p className="text-purple-600 leading-relaxed">用你习惯的方式和风格来绘画 🎨</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-4 animate-float" style={{ animationDelay: "0.4s" }}>
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg animate-bounce-gentle">
                          <span className="text-2xl">🎯</span>
                        </div>
                        <div>
                          <h3 className="font-bold text-purple-800 mb-2 text-lg">🌟 表达个性</h3>
                          <p className="text-purple-600 leading-relaxed">让绘画反映出你的个性特点 💫</p>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    onClick={nextStep}
                    className="btn btn-primary btn-lg px-10 animate-wiggle text-xl"
                  >
                    🎨 开始绘画 ✨
                    <ArrowRight className="w-6 h-6 ml-3" />
                  </motion.button>
                </div>
              </motion.div>
            )}

            {/* 绘画步骤 */}
            {currentStep === 'drawing' && (
              <motion.div
                key="drawing"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                className="grid lg:grid-cols-2 gap-8 items-start"
              >
                {/* 绘画区域 */}
                <div className="space-y-6">
                  <div className="text-center lg:text-left">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">开始你的创作</h2>
                    <p className="text-gray-600">在下面的画布上画出你的自画像</p>
                  </div>

                  <div className="bg-white rounded-2xl p-6 shadow-soft">
                    <DrawingCanvas
                      width={400}
                      height={400}
                      onDrawingChange={handleDrawingChange}
                      className="w-full"
                    />
                  </div>

                  <div className="flex space-x-4">
                    <button
                      onClick={prevStep}
                      className="btn-outline btn-md flex-1"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      上一步
                    </button>
                    <button
                      onClick={saveDrawing}
                      disabled={!drawingData}
                      className="btn-ghost btn-md"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      保存
                    </button>
                    <button
                      onClick={nextStep}
                      disabled={!drawingData}
                      className="btn-primary btn-md flex-1"
                    >
                      下一步
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>

                {/* 参考示例 */}
                <div className="space-y-6">
                  <div className="bg-white rounded-2xl p-6 shadow-soft">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">绘画示例</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {[
                        { style: '简约风格', emoji: '😊', desc: '简单的线条和形状' },
                        { style: '详细风格', emoji: '🧑‍💼', desc: '丰富的细节和特征' },
                        { style: '卡通风格', emoji: '😄', desc: '可爱的卡通形象' },
                        { style: '抽象风格', emoji: '🎨', desc: '创意的抽象表达' }
                      ].map((example, index) => (
                        <div
                          key={index}
                          className="text-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          <div className="text-3xl mb-2">{example.emoji}</div>
                          <h4 className="font-medium text-gray-900 mb-1">{example.style}</h4>
                          <p className="text-xs text-gray-600">{example.desc}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                    <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 绘画技巧</h3>
                    <ul className="space-y-2 text-sm text-blue-800">
                      <li>• 不用担心画得不好，AI会理解你的风格</li>
                      <li>• 可以尝试不同的颜色和画笔大小</li>
                      <li>• 画错了可以使用橡皮擦工具修改</li>
                      <li>• 让绘画体现出你的个人特色</li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}

            {/* 预览步骤 */}
            {currentStep === 'preview' && (
              <motion.div
                key="preview"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="max-w-4xl mx-auto space-y-8"
              >
                <div className="text-center">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">预览你的作品</h2>
                  <p className="text-lg text-gray-600">确认无误后，AI将开始分析你的绘画并生成专属形象</p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {/* 绘画预览 */}
                  <div className="bg-white rounded-2xl p-6 shadow-soft">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold text-gray-900">你的绘画</h3>
                      <button
                        onClick={() => setShowPreview(!showPreview)}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        {showPreview ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    
                    {showPreview && drawingData && (
                      <div className="text-center">
                        <img
                          src={drawingData}
                          alt="Your Drawing"
                          className="max-w-full h-auto rounded-lg shadow-sm border-2 border-gray-200"
                          style={{ maxHeight: '400px' }}
                        />
                      </div>
                    )}
                    
                    {!showPreview && (
                      <div className="text-center py-20">
                        <div className="text-6xl mb-4">👁️‍🗨️</div>
                        <p className="text-gray-500">绘画已隐藏</p>
                      </div>
                    )}
                  </div>

                  {/* 即将发生的事情 */}
                  <div className="space-y-6">
                    <div className="bg-white rounded-2xl p-6 shadow-soft">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">接下来会发生什么？</h3>
                      <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <span className="text-blue-600">🧠</span>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">AI分析</h4>
                            <p className="text-sm text-gray-600">
                              AI将分析你的绘画风格、线条特征、颜色使用等，推断你的性格特征
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <span className="text-green-600">🎭</span>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">角色匹配</h4>
                            <p className="text-sm text-gray-600">
                              根据分析结果，为你匹配最合适的打工人动物类型（牛马、宠物、神兽）
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <span className="text-purple-600">🎨</span>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">形象生成</h4>
                            <p className="text-sm text-gray-600">
                              使用先进的AI绘画技术，生成你的专属拟人化打工人形象
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-100">
                      <h3 className="text-lg font-semibold text-amber-900 mb-3">⚡ 处理时间</h3>
                      <p className="text-sm text-amber-800 mb-3">
                        AI分析和生成通常需要30-60秒，请耐心等待
                      </p>
                      <div className="text-xs text-amber-700">
                        💡 生成过程中请不要关闭页面
                      </div>
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-between">
                  <button
                    onClick={prevStep}
                    className="btn-outline btn-lg"
                  >
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    重新绘画
                  </button>
                  <button
                    onClick={nextStep}
                    className="btn-primary btn-lg px-8"
                  >
                    开始AI生成
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </button>
                </div>
              </motion.div>
            )}

            {/* AI生成步骤 */}
            {currentStep === 'ai-generation' && (
              <motion.div
                key="ai-generation"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="max-w-4xl mx-auto"
              >
                <AIGenerator
                  drawingData={drawingData}
                  onAnalysisComplete={(analysis) => {
                    console.log('Analysis completed:', analysis)
                  }}
                  onGenerationComplete={(generation) => {
                    console.log('Generation completed:', generation)
                    toast.success('🎉 你的打工人形象生成完成！')
                  }}
                />

                {/* 底部导航 */}
                <div className="mt-8 flex justify-between">
                  <button
                    onClick={prevStep}
                    className="btn-outline btn-md"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    返回预览
                  </button>
                  <div className="text-sm text-gray-500 flex items-center">
                    AI正在为你生成专属形象...
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}