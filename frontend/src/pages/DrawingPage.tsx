import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Save, Palette, Sparkles, Home } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import { useAppSelector } from '@/store'
import DrawingCanvas from '@/components/drawing/DrawingCanvas'
import AIGenerator from '@/components/ai/AIGenerator'

function DrawingPage() {
  const [drawingData, setDrawingData] = useState<string>('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<string>('')

  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)

  // 绘画数据变化处理
  const handleDrawingChange = useCallback((imageData: string) => {
    setDrawingData(imageData)
  }, [])

  // AI生成处理
  const handleAIGenerate = useCallback(async () => {
    if (!drawingData) {
      toast.error('请先完成你的自画像绘制')
      return
    }

    setIsGenerating(true)
    try {
      // 模拟AI生成过程
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 这里应该调用实际的AI生成API
      const mockGeneratedImage = drawingData // 暂时使用原图
      setGeneratedImage(mockGeneratedImage)

      toast.success('AI形象生成成功！')
    } catch (error) {
      toast.error('生成失败，请重试')
    } finally {
      setIsGenerating(false)
    }
  }, [drawingData])

  // 保存结果
  const handleSave = useCallback(() => {
    if (!generatedImage) {
      toast.error('请先生成AI形象')
      return
    }

    // 保存到本地存储
    localStorage.setItem('user_avatar', generatedImage)
    toast.success('形象已保存！')

    // 跳转到首页或其他页面
    navigate('/')
  }, [generatedImage, navigate])

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate(-1)}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回
          </Button>

          <div className="flex items-center gap-2">
            <Palette className="w-5 h-5 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">AI绘画生成</h1>
          </div>

          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="gap-2"
          >
            <Home className="w-4 h-4" />
            首页
          </Button>
        </div>

        {/* 说明卡片 */}
        <Card className="mb-8 bg-gradient-to-r from-primary/10 to-secondary/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-foreground">
              <Sparkles className="w-5 h-5" />
              创建你的专属打工人形象
            </CardTitle>
            <CardDescription>
              在左侧画板上绘制你的自画像，然后点击"AI生成"按钮，让AI为你创造独特的打工人形象！
            </CardDescription>
          </CardHeader>
        </Card>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：绘画区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Palette className="w-5 h-5" />
                绘画区域
              </CardTitle>
              <CardDescription>
                在下方画板上绘制你的自画像
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-4">
                <DrawingCanvas
                  onDrawingChange={handleDrawingChange}
                  className="w-full h-96 bg-white rounded-lg"
                />
              </div>
              <div className="flex gap-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 清空画板的逻辑
                    setDrawingData('')
                  }}
                >
                  清空画板
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (drawingData) {
                      localStorage.setItem('userDrawing', drawingData)
                      toast.success('绘画已保存')
                    }
                  }}
                >
                  <Save className="w-4 h-4 mr-2" />
                  保存绘画
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 右侧：AI生成区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Sparkles className="w-5 h-5" />
                AI生成结果
              </CardTitle>
              <CardDescription>
                AI将基于你的绘画生成专属形象
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 预览区域 */}
                <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-4 h-96 flex items-center justify-center bg-muted/10">
                  {generatedImage ? (
                    <img
                      src={generatedImage}
                      alt="AI生成的形象"
                      className="max-w-full max-h-full object-contain rounded-lg"
                    />
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <Sparkles className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>AI生成的形象将在这里显示</p>
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  <Button
                    onClick={handleAIGenerate}
                    disabled={!drawingData || isGenerating}
                    className="flex-1"
                  >
                    {isGenerating ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 mr-2"
                        >
                          <Sparkles className="w-4 h-4" />
                        </motion.div>
                        生成中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        AI生成
                      </>
                    )}
                  </Button>

                  {generatedImage && (
                    <Button
                      onClick={handleSave}
                      variant="outline"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      保存形象
                    </Button>
                  )}
                </div>

                {/* 状态提示 */}
                {!drawingData && (
                  <div className="text-sm text-muted-foreground text-center p-4 bg-muted/20 rounded-lg">
                    💡 请先在左侧画板上绘制你的自画像
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default DrawingPage