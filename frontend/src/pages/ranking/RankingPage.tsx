import { Link } from 'react-router-dom';
import LeaderboardPanel from '@/components/leaderboard/LeaderboardPanel';

export default function RankingPage() {
  return (
    <div className="min-h-screen">
      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* 页面头部 */}
        <div className="text-center mb-12 animate-slide-up">
          <div className="text-8xl mb-6 animate-bounce-gentle">🏆</div>
          <h1 className="text-4xl font-extrabold text-white mb-6 animate-float drop-shadow-lg">
            ✨ 打工人排行榜 ✨
          </h1>
          <p className="text-xl text-white/90 mb-8 drop-shadow-md animate-slide-up">
            🌟 查看最勤奋的打工人们的积分表现和成就 🌟
          </p>
          <div className="flex justify-center space-x-6 flex-wrap gap-4">
            <Link
              to="/zoo"
              className="btn btn-primary btn-lg animate-wiggle"
            >
              🎪 去动物园互动
            </Link>
            <Link
              to="/drawing"
              className="btn btn-secondary btn-lg animate-float"
            >
              🎨 生成新角色
            </Link>
          </div>
        </div>

      {/* 排行榜面板 */}
      <div className="mb-8">
        <LeaderboardPanel />
      </div>

        {/* 说明文档 */}
        <div className="card-gradient p-8 animate-slide-up">
          <h2 className="text-2xl font-bold text-purple-800 mb-6 text-center animate-pulse-glow">
            🎯 积分说明
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="font-bold text-purple-700 mb-4 text-lg">💫 如何获得积分</h3>
              <ul className="text-purple-600 space-y-3 leading-relaxed">
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🤝</span>
                  <span>与动物互动：喂食、抚摸、玩耍等</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">💼</span>
                  <span>协作工作：与工作动物一起努力</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">👥</span>
                  <span>社交活动：帮助其他打工人</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🎖️</span>
                  <span>完成成就：达成各种里程碑</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🎨</span>
                  <span>创意表现：生成独特的动物头像</span>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-bold text-purple-700 mb-4 text-lg">⭐ 加分要素</h3>
              <ul className="text-purple-600 space-y-3 leading-relaxed">
                <li className="flex items-center space-x-2">
                  <span className="text-xl">⚡</span>
                  <span>互动效果：高效互动获得更多积分</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🦄</span>
                  <span>动物类型：神兽互动有额外加成</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🔥</span>
                  <span>连击奖励：连续互动获得倍数奖励</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">⏰</span>
                  <span>持续时间：长时间互动效果更佳</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="text-xl">🚀</span>
                  <span>升级奖励：每次升级获得额外积分</span>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 card p-6 bg-gradient-to-r from-yellow-100 to-pink-100">
            <p className="text-purple-700 leading-relaxed">
              <strong className="text-purple-800">✨ 提示：</strong>
              积分不仅代表你的努力程度，还反映了你在动物园社区中的贡献。
              通过多样化的互动和持续的参与，你可以快速提升排名并解锁更多成就！ 🌟
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}