import { Link } from 'react-router-dom'

export default function SettingsPage() {
  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h1 className="text-lg font-medium text-gray-900">设置</h1>
        </div>
        
        <div className="p-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">动物园设置</h3>
              <p className="text-gray-600 mb-4">
                自定义你在动物园中的体验
              </p>
              <Link
                to="/zoo"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                🎪 进入动物园配置
              </Link>
            </div>
            
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">账户设置</h3>
              <p className="text-gray-600">
                更多设置功能正在开发中...
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}