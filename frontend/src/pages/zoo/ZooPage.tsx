import { useEffect, useRef, useState } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { useAppSelector } from '@/store'
import toast from 'react-hot-toast'
import WalkingAnimation from '../../components/zoo/WalkingAnimation'

interface Animal {
  id: string
  name: string
  species: string
  category: 'DIVINE_BEAST' | 'PET' | 'WORKING_ANIMAL'
  type: 'DIVINE' | 'PET' | 'OXHORSE' // 保留兼容性
  imageUrl?: string
  avatarUrl?: string
  avatarBase64?: string
  x: number
  y: number
  vx: number
  vy: number
  targetX?: number
  targetY?: number
  currentState: {
    activity: string
    mood: string
    energy: number
    happiness: number
    health: number
    workEfficiency: number
    location: string
  }
  // 保留兼容性字段
  activity?: string
  energy?: number
  happiness?: number
  creativity?: number
  zone?: string
  lastStepPhase?: number
  // AI行为相关
  behaviorState?: {
    currentBehavior?: string
    isActive: boolean
    timeRemaining?: number
  }
  lastInteractionAt?: Date
  isUserAnimal?: boolean
}

interface EnvironmentState {
  timeOfDay: number
  weather: {
    condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy'
    temperature: number
    humidity: number
  }
  crowdLevel: 'low' | 'medium' | 'high'
  events: Array<{
    id: string
    name: string
    type: string
    startTime: string
    duration: number
  }>
}

interface ZooStatistics {
  totalAnimals: number
  activeAnimals: number
  averageHappiness: number
  averageEnergy: number
  activeEvents: number
}

export default function ZooPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const updateIntervalRef = useRef<NodeJS.Timeout>()
  const animalsRef = useRef<Animal[]>([])
  
  // 基础状态
  const [animals, setAnimals] = useState<Animal[]>([])
  const [camera, setCamera] = useState({ x: 0, y: 0, zoom: 1.0 })
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(null)
  const [showNames, setShowNames] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [images, setImages] = useState<{ [key: string]: HTMLImageElement }>({})
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null)
  
  // AI和环境状态
  const [environment, setEnvironment] = useState<EnvironmentState | null>(null)
  const [statistics, setStatistics] = useState<ZooStatistics | null>(null)
  const [aiEnabled, setAiEnabled] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)
  
  const worldWidth = 3200
  const worldHeight = 2400

  // 辅助函数：获取动物颜色
  const getAnimalColor = (type: string) => {
    switch (type) {
      case 'DIVINE': return '#FFD700' // 金色
      case 'PET': return '#FF69B4'    // 粉色
      case 'OXHORSE': return '#8B4513' // 棕色
      default: return '#808080'       // 灰色
    }
  }

  // 辅助函数：获取动物表情符号
  const getAnimalEmoji = (type: string) => {
    switch (type) {
      case 'DIVINE': return '🦌'
      case 'PET': return '🐱'
      case 'OXHORSE': return '🐂'
      default: return '❓'
    }
  }

  // 辅助函数：获取动物类型名称
  const getAnimalTypeName = (type: string) => {
    switch (type) {
      case 'DIVINE': return '神兽'
      case 'PET': return '宠物'
      case 'OXHORSE': return '牛马'
      default: return '未知'
    }
  }

  // 辅助函数：根据X坐标确定区域
  const determineZone = (x: number) => {
    if (x < worldWidth / 3) return '工作区'
    if (x < worldWidth * 2 / 3) return '管理区'
    return '休闲区'
  }

  // 加载背景图
  useEffect(() => {
    const bgImg = new Image()
    bgImg.onload = () => {
      setBackgroundImage(bgImg)
    }
    bgImg.src = '/zoo.png' // 使用项目根目录下的zoo.png
  }, [])

  // API调用函数
  const fetchZooScene = async () => {
    try {
      const response = await fetch('/api/v1/zoo/scene', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          return data.data
        }
      }
    } catch (error) {
      console.error('获取动物园场景失败:', error)
    }
    return null
  }

  const fetchEnvironment = async () => {
    try {
      const response = await fetch('/api/v1/zoo/environment', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setEnvironment(data)
      }
    } catch (error) {
      console.error('获取环境状态失败:', error)
    }
  }

  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/v1/zoo/statistics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setStatistics(data)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const triggerEvent = async (eventType: string) => {
    try {
      const response = await fetch('/api/v1/zoo/events/trigger', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type: eventType, duration: 60000 }),
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success(`事件"${eventType}"已触发！`)
        } else {
          toast.error('事件触发失败')
        }
      }
    } catch (error) {
      console.error('触发事件失败:', error)
      toast.error('触发事件失败')
    }
  }

  const interactWithAnimal = async (animalId: string, interactionType: string) => {
    console.log('开始互动:', animalId, interactionType)
    try {
      const response = await fetch(`/api/v1/zoo/animals/${animalId}/interact`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interactionType,
          intensity: 5,
          duration: 10000,
          message: `与${selectedAnimal?.name}进行${interactionType}互动`
        }),
      })
      
      console.log('互动响应状态:', response.status)
      
      if (response.ok) {
        const data = await response.json()
        console.log('互动响应数据:', data)
        if (data.success) {
          toast.success(data.message || '互动成功！')
          // 显示积分奖励
          if (data.data?.scoreGain) {
            toast.success(`获得 ${data.data.scoreGain} 积分！`, {
              duration: 3000,
              icon: '🎉'
            })
          }
          // 更新动物数据
          await loadZooData()
        } else {
          toast.error(data.message || '互动失败')
        }
      } else {
        const errorData = await response.json().catch(() => null)
        console.error('互动失败响应:', errorData)
        toast.error(errorData?.message || `互动失败: ${response.status}`)
      }
    } catch (error) {
      console.error('动物互动失败:', error)
      toast.error('互动失败: 网络错误')
    }
  }

  // 从后端加载动物园数据
  const loadZooData = async () => {
    setIsLoading(true)
    try {
      // 尝试从后端API获取动物园场景数据
      const sceneData = await fetchZooScene()
      
      if (sceneData && sceneData.animals && sceneData.animals.length > 0) {
        console.log('从后端API加载动物园数据:', sceneData.animals.length, '个动物')
        
        const apiAnimals: Animal[] = sceneData.animals.map((apiAnimal: any) => ({
          id: apiAnimal.id,
          name: apiAnimal.name || '未命名动物',
          species: apiAnimal.species,
          category: apiAnimal.category,
          type: mapCategoryToType(apiAnimal.category),
          imageUrl: apiAnimal.avatarUrl,
          avatarUrl: apiAnimal.avatarUrl,
          avatarBase64: apiAnimal.avatarBase64,
          x: apiAnimal.position?.x || Math.random() * (worldWidth - 200) + 100,
          y: apiAnimal.position?.y || Math.random() * (worldHeight - 200) + 100,
          vx: (Math.random() - 0.5) * 20 + (Math.random() > 0.5 ? 5 : -5),  // 合理的移动速度
          vy: (Math.random() - 0.5) * 20 + (Math.random() > 0.5 ? 5 : -5),  // 合理的移动速度
          currentState: apiAnimal.state || {
            activity: 'idle',
            mood: 'content',
            energy: 70,
            happiness: 60,
            health: 90,
            workEfficiency: 50,
            location: 'main_area'
          },
          // 保留兼容性
          activity: apiAnimal.state?.activity || 'idle',
          energy: apiAnimal.state?.energy || 70,
          happiness: apiAnimal.state?.happiness || 60,
          creativity: apiAnimal.state?.workEfficiency || 50,
          zone: determineZone(apiAnimal.position?.x || Math.random() * worldWidth),
          isUserAnimal: apiAnimal.isUserAnimal || false
        }))
        
        setAnimals(apiAnimals)
        
        // 预加载图像
        const imagePromises = apiAnimals.map(animal => {
          return new Promise<void>((resolve) => {
            const img = new Image()
            img.onload = () => {
              console.log(`动物 ${animal.name} 的图像加载成功:`, animal.imageUrl)
              setImages(prev => ({ ...prev, [animal.id]: img }))
              resolve()
            }
            img.onerror = (error) => {
              console.error(`动物 ${animal.name} 的图像加载失败:`, animal.imageUrl, error)
              resolve()
            }
            img.src = animal.imageUrl
          })
        })
        
        await Promise.all(imagePromises)
        
        // 获取环境和统计数据
        await fetchEnvironment()
        await fetchStatistics()
        
        setLastUpdate(new Date())
        return
      }
    } catch (error) {
      console.error('从后端加载数据失败:', error)
    }
    
    // 如果后端数据加载失败，回退到本地数据
    await loadAnimalsFromLocal()
    setIsLoading(false)
  }

  // 映射动物类别到兼容类型
  const mapCategoryToType = (category: string): 'DIVINE' | 'PET' | 'OXHORSE' => {
    switch (category) {
      case 'DIVINE_BEAST': return 'DIVINE'
      case 'PET': return 'PET'
      case 'WORKING_ANIMAL': return 'OXHORSE'
      default: return 'OXHORSE'
    }
  }


  // 加载动物数据和图像
  const loadAnimalsFromLocal = async () => {
    // 首先尝试从localStorage加载保存的动物
      const savedAnimalsStr = localStorage.getItem('zooAnimals')
      const userAvatarStr = localStorage.getItem('userGeneratedAvatar')
      
      // Helper function to fix URL port
      const fixImageUrl = (url: string): string => {
        if (!url || url.startsWith('/') || url.startsWith('data:')) return url
        return url.replace('http://localhost:3002/', 'http://localhost:3000/')
      }
      
      let animalsToLoad: Animal[] = []
      
      if (savedAnimalsStr) {
        try {
          const savedAnimals = JSON.parse(savedAnimalsStr)
          console.log('从localStorage加载保存的动物:', savedAnimals.length, '个')
          
          animalsToLoad = savedAnimals.map((saved: any, index: number) => ({
            id: saved.id || Date.now() + index,
            name: saved.name || `动物${index + 1}`,
            type: saved.type || 'OXHORSE',
            imageUrl: fixImageUrl(saved.avatar || saved.imageUrl || saved.avatarUrl || '/single_oxhorse_test.png'),
            x: saved.position?.x || saved.x || Math.random() * (worldWidth - 200) + 100,
            y: saved.position?.y || saved.y || Math.random() * (worldHeight - 200) + 100,
            vx: (Math.random() - 0.5) * 20 + (Math.random() > 0.5 ? 5 : -5),  // 合理的移动速度
            vy: (Math.random() - 0.5) * 20 + (Math.random() > 0.5 ? 5 : -5),  // 合理的移动速度
            activity: saved.activity || '闲逛中',
            energy: saved.stats?.energy || saved.energy || Math.random() * 100,
            happiness: saved.stats?.happiness || saved.happiness || Math.random() * 100,
            creativity: saved.stats?.creativity || saved.creativity || Math.random() * 100,
            zone: saved.zone || determineZone(saved.x || Math.random() * worldWidth)
          }))
        } catch (error) {
          console.error('解析保存的动物数据失败:', error)
        }
      }
      
      // 如果没有保存的动物，但有用户生成的头像，创建用户动物
      if (animalsToLoad.length === 0 && userAvatarStr) {
        try {
          const userAvatarData = JSON.parse(userAvatarStr)
          console.log('创建用户动物:', userAvatarData)
          
          const userAnimal: Animal = {
            id: '1',
            name: currentUser?.username || '我的打工人',
            species: userAvatarData.animalType === 'OXHORSE' ? '牛马' : userAvatarData.animalType === 'PET' ? '宠物' : '神兽',
            category: userAvatarData.animalType === 'OXHORSE' ? 'WORKING_ANIMAL' : userAvatarData.animalType === 'PET' ? 'PET' : 'DIVINE_BEAST',
            type: userAvatarData.animalType || 'OXHORSE',
            imageUrl: fixImageUrl(userAvatarData.avatarUrl),
            x: 800,
            y: 600,
            vx: 20 + Math.random() * 20,  // 快速移动
            vy: 15 + Math.random() * 15,  // 快速移动
            activity: '工作中',
            energy: userAvatarData.analysis?.personality?.energy || 75,
            happiness: userAvatarData.analysis?.personality?.happiness || 60,
            creativity: userAvatarData.analysis?.personality?.creativity || 80,
            zone: '工作区',
            currentState: {
              activity: '工作中',
              mood: 'focused',
              energy: userAvatarData.analysis?.personality?.energy || 75,
              happiness: userAvatarData.analysis?.personality?.happiness || 60,
              health: 80,
              workEfficiency: 80,
              location: '工作区'
            }
          }
          
          animalsToLoad.push(userAnimal)
          
          // 添加一些测试伙伴
          const companions: Animal[] = [
            {
              id: '2',
              name: '勤劳牛马小王',
              species: '牛马',
              category: 'WORKING_ANIMAL' as const,
              type: 'OXHORSE',
              imageUrl: '/single_oxhorse_test.png',
              x: 1200,
              y: 800,
              vx: -(20 + Math.random() * 15),  // 快速移动
              vy: 20 + Math.random() * 15,      // 快速移动
              activity: '加班中',
              energy: 45,
              happiness: 30,
              creativity: 20,
              zone: '工作区',
              currentState: {
                activity: '加班中',
                mood: 'tired',
                energy: 45,
                happiness: 30,
                health: 60,
                workEfficiency: 65,
                location: '工作区'
              }
            },
            {
              id: '3',
              name: '团宠小猫咪',
              species: '宠物',
              category: 'PET' as const,
              type: 'PET',
              imageUrl: '/single_oxhorse_test.png',
              x: 2000,
              y: 1200,
              vx: 15 + Math.random() * 15,   // 快速移动
              vy: -(15 + Math.random() * 15), // 快速移动
              activity: '摸鱼中',
              energy: 80,
              happiness: 95,
              creativity: 85,
              zone: '休闲区',
              currentState: {
                activity: '摸鱼中',
                mood: 'happy',
                energy: 80,
                happiness: 95,
                health: 90,
                workEfficiency: 40,
                location: '休闲区'
              }
            },
            {
              id: '4',
              name: '职场神兽',
              species: '神兽',
              category: 'DIVINE_BEAST' as const,
              type: 'DIVINE',
              imageUrl: '/single_oxhorse_test.png',
              x: 1600,
              y: 400,
              vx: -(15 + Math.random() * 15),
              vy: 10 + Math.random() * 15,
              activity: '指导工作',
              energy: 100,
              happiness: 90,
              creativity: 100,
              zone: '管理区',
              currentState: {
                activity: '指导工作',
                mood: 'confident',
                energy: 100,
                happiness: 90,
                health: 100,
                workEfficiency: 95,
                location: '管理区'
              }
            }
          ]
          
          animalsToLoad.push(...companions)
        } catch (error) {
          console.error('解析用户头像数据失败:', error)
        }
      }
      
      // 如果还是没有动物，创建一些默认测试动物
      if (animalsToLoad.length === 0) {
        console.log('创建默认测试动物')
        animalsToLoad = [
          {
            id: '1',
            name: currentUser?.username || '访客牛马',
            species: '牛马',
            category: 'WORKING_ANIMAL' as const,
            type: 'OXHORSE',
            imageUrl: '/single_oxhorse_test.png',
            x: 600,
            y: 400,
            vx: 20 + Math.random() * 20,  // 快速移动
            vy: 15 + Math.random() * 15,  // 快速移动
            activity: '寻找工作',
            energy: 75,
            happiness: 60,
            creativity: 80,
            zone: '工作区',
            currentState: {
              activity: '寻找工作',
              mood: 'determined',
              energy: 75,
              happiness: 60,
              health: 75,
              workEfficiency: 70,
              location: '工作区'
            }
          },
          {
            id: '2',
            name: '示例神兽',
            species: '神兽',
            category: 'DIVINE_BEAST' as const,
            type: 'DIVINE',
            imageUrl: '/single_oxhorse_test.png',
            x: 1200,
            y: 600,
            vx: -(20 + Math.random() * 20),  // 快速移动
            vy: 20 + Math.random() * 15,     // 快速移动
            activity: '巡视中',
            energy: 90,
            happiness: 85,
            creativity: 95,
            zone: '管理区',
            currentState: {
              activity: '巡视中',
              mood: 'majestic',
              energy: 90,
              happiness: 85,
              health: 95,
              workEfficiency: 90,
              location: '管理区'
            }
          },
          {
            id: '3',
            name: '示例宠物',
            species: '宠物',
            category: 'PET' as const,
            type: 'PET',
            imageUrl: '/single_oxhorse_test.png',
            x: 1800,
            y: 800,
            vx: 15 + Math.random() * 15,   // 快速移动
            vy: -(10 + Math.random() * 15), // 快速移动
            activity: '休息中',
            energy: 50,
            happiness: 90,
            creativity: 70,
            zone: '休闲区',
            currentState: {
              activity: '休息中',
              mood: 'relaxed',
              energy: 50,
              happiness: 90,
              health: 85,
              workEfficiency: 55,
              location: '休闲区'
            }
          }
        ]
      }
      
      console.log('准备加载', animalsToLoad.length, '个动物')
      setAnimals(animalsToLoad)
      animalsRef.current = animalsToLoad  // 同步到ref
      
      // 预加载图像
      const imagePromises = animalsToLoad.map(animal => {
        return new Promise<void>((resolve) => {
          const img = new Image()
          img.onload = () => {
            console.log(`动物 ${animal.name} 的图像加载成功:`, animal.imageUrl, `尺寸: ${img.naturalWidth}x${img.naturalHeight}`)
            setImages(prev => ({ ...prev, [animal.id]: img }))
            resolve()
          }
          img.onerror = (error) => {
            console.error(`动物 ${animal.name} 的图像加载失败:`, animal.imageUrl, error)
            // 尝试使用备用图像或者直接解决
            resolve()
          }
          console.log(`开始加载动物 ${animal.name} 的图像:`, animal.imageUrl)
          // 添加时间戳避免缓存问题
          if (animal.imageUrl.startsWith('data:')) {
            // 对于data URL，直接使用
            img.src = animal.imageUrl
          } else {
            // 对于普通URL，添加时间戳避免缓存问题
            img.src = animal.imageUrl + (animal.imageUrl.includes('?') ? '&' : '?') + '_t=' + Date.now()
          }
        })
      })
      
      await Promise.all(imagePromises)
    }

  // 同步animals到animalsRef
  useEffect(() => {
    animalsRef.current = animals
  }, [animals])

  // 使用useEffect加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await loadAnimalsFromLocal()
      setIsLoading(false)
    }
    loadData()
  }, [currentUser])

  // 定期更新数据
  useEffect(() => {
    const updateInterval = setInterval(async () => {
      if (!isLoading) {
        await fetchEnvironment()
        await fetchStatistics()
      }
    }, 10000) // 每10秒更新环境和统计数据

    updateIntervalRef.current = updateInterval
    
    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current)
      }
    }
  }, [isLoading])

  // 动画循环
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const animate = () => {
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 保存状态
      ctx.save()
      
      // 应用相机变换
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.scale(camera.zoom, camera.zoom)
      ctx.translate(-camera.x, -camera.y)
      
      // 绘制背景
      drawBackground(ctx)
      
      // 更新和绘制动物
      updateAnimals()
      drawAnimals(ctx)
      
      // 恢复状态
      ctx.restore()
      
      // 绘制UI
      drawUI(ctx)
      
      animationRef.current = requestAnimationFrame(animate)
    }
    
    animate()
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [camera, images, selectedAnimal, showNames, backgroundImage])

  // 绘制背景
  const drawBackground = (ctx: CanvasRenderingContext2D) => {
    if (backgroundImage) {
      // 使用zoo.png作为背景
      ctx.drawImage(backgroundImage, 0, 0, worldWidth, worldHeight)
    } else {
      // 备用渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, worldHeight)
      gradient.addColorStop(0, '#87CEEB')
      gradient.addColorStop(1, '#E0F6FF')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, worldWidth, worldHeight)
    }
    
    // 绘制区域标签（可选）
    drawZones(ctx)
    
    // 绘制网格（调试用）
    if (false) { // 可以开启网格辅助
      ctx.strokeStyle = 'rgba(0,0,0,0.1)'
      ctx.lineWidth = 1
      for (let x = 0; x <= worldWidth; x += 200) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, worldHeight)
        ctx.stroke()
      }
      for (let y = 0; y <= worldHeight; y += 200) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(worldWidth, y)
        ctx.stroke()
      }
    }
  }

  // 绘制区域
  const drawZones = (ctx: CanvasRenderingContext2D) => {
    // 工作区
    ctx.fillStyle = 'rgba(100, 100, 200, 0.1)'
    ctx.fillRect(0, 0, worldWidth / 3, worldHeight)
    ctx.fillStyle = '#666'
    ctx.font = 'bold 48px Arial'
    ctx.fillText('工作区', 100, 100)
    
    // 神兽区
    ctx.fillStyle = 'rgba(255, 215, 0, 0.1)'
    ctx.fillRect(worldWidth / 3, 0, worldWidth / 3, worldHeight)
    ctx.fillStyle = '#666'
    ctx.fillText('神兽区', worldWidth / 3 + 100, 100)
    
    // 休闲区
    ctx.fillStyle = 'rgba(100, 200, 100, 0.1)'
    ctx.fillRect(worldWidth * 2 / 3, 0, worldWidth / 3, worldHeight)
    ctx.fillStyle = '#666'
    ctx.fillText('休闲区', worldWidth * 2 / 3 + 100, 100)
  }

  // 更新动物位置
  const updateAnimals = () => {
    // 更新ref中的动物位置
    animalsRef.current = animalsRef.current.map(animal => {
      let newX = animal.x + animal.vx
      let newY = animal.y + animal.vy
      let newVx = animal.vx
      let newVy = animal.vy
      
      // 边界反弹
      if (newX < 100 || newX > worldWidth - 100) {
        newVx = -newVx
        newX = Math.max(100, Math.min(worldWidth - 100, newX))
      }
      if (newY < 100 || newY > worldHeight - 100) {
        newVy = -newVy
        newY = Math.max(100, Math.min(worldHeight - 100, newY))
      }
      
      // 随机改变方向（降低频率）
      if (Math.random() < 0.008) {
        // 使用更合理的速度范围
        newVx = (Math.random() - 0.5) * 30 + (newVx > 0 ? 5 : -5)  // 保持一定的基础速度
        newVy = (Math.random() - 0.5) * 30 + (newVy > 0 ? 5 : -5)  // 保持一定的基础速度
      }
      
      return {
        ...animal,
        x: newX,
        y: newY,
        vx: newVx,
        vy: newVy
      }
    })
    
    // 同步更新state（降低频率避免过多渲染）
    setAnimals([...animalsRef.current])
  }

  // 绘制动物
  const drawAnimals = (ctx: CanvasRenderingContext2D) => {
    // 使用ref中的动物数据绘制
    animalsRef.current.forEach(animal => {
      drawAnimal(ctx, animal)
    })
  }

  // 绘制单个动物（完整画像+走路动画+AI状态）
  const drawAnimal = (ctx: CanvasRenderingContext2D, animal: Animal) => {
    const img = images[animal.id]
    
    ctx.save()
    ctx.translate(animal.x, animal.y)
    
    // 绘制阴影（调整大小匹配新的图像尺寸）
    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
    ctx.beginPath()
    ctx.ellipse(0, 150, 80, 25, 0, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制用户动物的特殊标记
    if (animal.isUserAnimal) {
      ctx.strokeStyle = '#FFD700'
      ctx.lineWidth = 4
      ctx.setLineDash([5, 5])
      ctx.beginPath()
      ctx.arc(0, -120, 100, 0, Math.PI * 2)
      ctx.stroke()
      ctx.setLineDash([])
    }
    
    // 走路动画参数
    const walkSpeed = Math.sqrt(animal.vx * animal.vx + animal.vy * animal.vy)
    const walkCycle = Date.now() / (300 - walkSpeed * 50)
    const isMoving = walkSpeed > 0.1
    
    if (isMoving) {
      // 走动动画效果（减少摇摆幅度）
      const stepPhase = walkCycle % (Math.PI * 2)
      const wobble = Math.sin(walkCycle) * Math.min(3, walkSpeed * 0.1)  // 限制摇摆幅度
      const bounce = Math.abs(Math.sin(walkCycle * 2)) * Math.min(5, walkSpeed * 0.15)  // 限制弹跳幅度
      
      ctx.translate(wobble, -bounce)
      ctx.rotate(Math.sin(walkCycle) * 0.01)  // 减少旋转幅度
      
      // 记录步伐（可用于添加脚印等效果）
      if (Math.floor(stepPhase) !== Math.floor(animal.lastStepPhase || 0)) {
        animal.lastStepPhase = stepPhase
      }
    }
    
    // 绘制完整画像（更大尺寸确保明显）
    const imageWidth = 240
    const imageHeight = 300
    
    if (img && img.complete && img.naturalWidth > 0) {
      // 成功加载的图像
      ctx.drawImage(img, -imageWidth/2, -imageHeight + 20, imageWidth, imageHeight)
    } else {
      // 占位符或加载失败时显示大尺寸的动物图标
      ctx.fillStyle = getAnimalColor(animal.type)
      ctx.fillRect(-imageWidth/2, -imageHeight + 20, imageWidth, imageHeight)
      
      // 绘制边框
      ctx.strokeStyle = '#333'
      ctx.lineWidth = 3
      ctx.strokeRect(-imageWidth/2, -imageHeight + 20, imageWidth, imageHeight)
      
      // 绘制大图标（增大字体尺寸）
      ctx.fillStyle = '#fff'
      ctx.font = 'bold 72px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(getAnimalEmoji(animal.type), 0, -imageHeight/2 + 80)
      
      // 绘制类型文字（增大字体尺寸）
      ctx.fillStyle = '#fff'
      ctx.font = 'bold 32px Arial'
      ctx.fillText(getAnimalTypeName(animal.type), 0, -imageHeight/2 + 20)
      
      // 绘制加载状态
      if (img && !img.complete) {
        ctx.fillStyle = '#fff'
        ctx.font = '16px Arial'
        ctx.fillText('加载中...', 0, -imageHeight/2 + 100)
      } else if (img && img.naturalWidth === 0) {
        ctx.fillStyle = '#fff'
        ctx.font = '16px Arial'
        ctx.fillText('图像加载失败', 0, -imageHeight/2 + 100)
      }
    }
    
    // 显示名称
    if (showNames) {
      ctx.fillStyle = '#333'
      ctx.font = 'bold 20px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(animal.name, 0, -imageHeight + 10)
    }
    
    // 高亮选中的动物（调整选择框尺寸）
    if (selectedAnimal?.id === animal.id) {
      ctx.strokeStyle = '#FFD700'
      ctx.lineWidth = 6
      ctx.strokeRect(-imageWidth/2 - 15, -imageHeight + 5, imageWidth + 30, imageHeight + 30)
    }
    
    ctx.restore()
  }

  // 绘制UI
  const drawUI = (ctx: CanvasRenderingContext2D) => {
    // 绘制小地图等UI元素
  }

  // 处理画布大小 - 全屏显示
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        canvasRef.current.width = window.innerWidth
        canvasRef.current.height = window.innerHeight // 使用完整屏幕高度
      }
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 鼠标控制
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX + camera.x, y: e.clientY + camera.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setCamera(prev => ({
        ...prev,
        x: dragStart.x - e.clientX,
        y: dragStart.y - e.clientY
      }))
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleWheel = (e: React.WheelEvent) => {
    const zoomSpeed = 0.001
    const newZoom = Math.max(0.2, Math.min(2, camera.zoom - e.deltaY * zoomSpeed))
    setCamera(prev => ({ ...prev, zoom: newZoom }))
  }

  const handleClick = (e: React.MouseEvent) => {
    if (!isDragging) {
      const rect = canvasRef.current?.getBoundingClientRect()
      if (!rect) return
      
      const x = (e.clientX - rect.left - rect.width / 2) / camera.zoom + camera.x
      const y = (e.clientY - rect.top - rect.height / 2) / camera.zoom + camera.y
      
      // 检查是否点击了动物（调整点击范围匹配新的图像尺寸）
      const clickedAnimal = animals.find(animal => {
        const dist = Math.sqrt((animal.x - x) ** 2 + (animal.y - y) ** 2)
        return dist < 150  // 增大点击范围
      })
      
      setSelectedAnimal(clickedAnimal || null)
    }
  }

  return (
    <div className="zoo-page h-screen bg-gray-100 overflow-hidden">
      <div className="relative h-full w-full">
        <canvas
          ref={canvasRef}
          className="absolute inset-0 cursor-move w-full h-full"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
          onClick={handleClick}
        />
        
        {/* 控制面板 */}
        <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 space-y-3 max-w-xs max-h-[90vh] overflow-y-auto">
          <h2 className="text-xl font-bold text-gray-800">🎪 智能动物园</h2>
          
          {/* 环境信息 */}
          {environment && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <h3 className="text-sm font-bold text-blue-800 mb-2">环境状态</h3>
              <div className="text-xs text-blue-700 space-y-1">
                <p>时间: {environment.timeOfDay}:00</p>
                <p>天气: {environment.weather.condition === 'sunny' ? '☀️ 晴朗' : 
                         environment.weather.condition === 'cloudy' ? '☁️ 多云' : 
                         environment.weather.condition === 'rainy' ? '🌧️ 下雨' : '⛈️ 暴风雨'}</p>
                <p>温度: {environment.weather.temperature.toFixed(1)}°C</p>
                <p>人流: {environment.crowdLevel === 'high' ? '🔥 繁忙' : 
                        environment.crowdLevel === 'medium' ? '👥 适中' : '😌 悠闲'}</p>
                {environment.events.length > 0 && (
                  <p className="text-orange-600">🎉 进行中事件: {environment.events[0].name}</p>
                )}
              </div>
            </div>
          )}

          {/* 统计信息 */}
          {statistics && (
            <div className="bg-green-50 p-3 rounded-lg">
              <h3 className="text-sm font-bold text-green-800 mb-2">统计信息</h3>
              <div className="text-xs text-green-700 space-y-1">
                <p>总动物: {statistics.totalAnimals}</p>
                <p>活跃中: {statistics.activeAnimals}</p>
                <p>平均幸福度: {statistics.averageHappiness}%</p>
                <p>平均能量: {statistics.averageEnergy}%</p>
                <p>进行中事件: {statistics.activeEvents}</p>
              </div>
            </div>
          )}

          {/* AI控制 */}
          <div className="bg-purple-50 p-3 rounded-lg">
            <h3 className="text-sm font-bold text-purple-800 mb-2">AI控制</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={aiEnabled}
                  onChange={(e) => setAiEnabled(e.target.checked)}
                  className="rounded"
                />
                <span className="text-xs text-purple-700">AI行为系统</span>
              </div>
              <div className="flex flex-wrap gap-1">
                <button
                  onClick={() => triggerEvent('快乐时光')}
                  className="px-2 py-1 bg-yellow-400 text-yellow-800 text-xs rounded hover:bg-yellow-500"
                >
                  🎉 快乐时光
                </button>
                <button
                  onClick={() => triggerEvent('工作集会')}
                  className="px-2 py-1 bg-blue-400 text-blue-800 text-xs rounded hover:bg-blue-500"
                >
                  💼 工作集会
                </button>
                <button
                  onClick={() => triggerEvent('午休时间')}
                  className="px-2 py-1 bg-green-400 text-green-800 text-xs rounded hover:bg-green-500"
                >
                  😴 午休时间
                </button>
              </div>
            </div>
          </div>
          
          {/* 显示设置 */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-1">
                <input
                  type="checkbox"
                  checked={showNames}
                  onChange={(e) => setShowNames(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">显示名称</span>
              </label>
            </div>
          </div>
          
          {/* 基础统计 */}
          <div className="text-sm text-gray-600 space-y-1">
            <p>在线动物: <span className="font-medium text-gray-900">{animals.length}</span></p>
            <p>缩放倍数: <span className="font-medium text-gray-900">{(camera.zoom * 100).toFixed(0)}%</span></p>
            <p>已加载图像: <span className="font-medium text-gray-900">{Object.keys(images).length}</span></p>
            {selectedAnimal && (
              <p className="text-primary-600">已选中: {selectedAnimal.name}</p>
            )}
            {lastUpdate && (
              <p className="text-xs text-gray-400">
                最后更新: {lastUpdate.toLocaleTimeString()}
              </p>
            )}
          </div>
          
          {/* 积分和排名快速显示 */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg border border-yellow-200">
            <h3 className="text-sm font-bold text-orange-800 mb-2">💯 我的积分</h3>
            <div className="text-xs text-orange-700 space-y-1">
              <p>每次互动都会获得积分奖励！</p>
              <div className="flex justify-between items-center mt-2">
                <Link
                  to="/ranking"
                  className="px-2 py-1 bg-yellow-400 text-yellow-800 text-xs rounded hover:bg-yellow-500 transition-colors"
                >
                  🏆 查看排行榜
                </Link>
              </div>
            </div>
          </div>
          
          {/* 动物类型统计 */}
          <div className="text-xs text-gray-500">
            <div className="flex justify-between">
              <span>🐂 牛马: {animals.filter(a => a.type === 'OXHORSE').length}</span>
              <span>🐱 宠物: {animals.filter(a => a.type === 'PET').length}</span>
              <span>🦌 神兽: {animals.filter(a => a.type === 'DIVINE').length}</span>
            </div>
          </div>
          
          {/* 缩放控制 */}
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-700">视角控制</div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCamera(prev => ({ ...prev, zoom: Math.max(0.2, prev.zoom - 0.2) }))}
                className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                title="缩小"
              >
                ➖
              </button>
              <button
                onClick={() => setCamera({ x: 0, y: 0, zoom: 1.0 })}
                className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                title="重置视角"
              >
                🎯 重置
              </button>
              <button
                onClick={() => setCamera(prev => ({ ...prev, zoom: Math.min(3.0, prev.zoom + 0.2) }))}
                className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                title="放大"
              >
                ➕
              </button>
            </div>
          </div>
          
          {/* 快捷操作 */}
          <div className="space-y-1">
            <div className="text-sm font-medium text-gray-700">快捷操作</div>
            <button
              onClick={() => navigate('/drawing')}
              className="w-full px-2 py-1 bg-primary-500 text-white text-xs rounded hover:bg-primary-600"
            >
              🎨 创建新角色
            </button>
            <button
              onClick={() => {
                // 跟随用户动物
                const userAnimal = animals.find(a => a.name.includes(currentUser?.username || '我的'))
                if (userAnimal) {
                  setCamera(prev => ({ ...prev, x: userAnimal.x - worldWidth/2, y: userAnimal.y - worldHeight/2 }))
                  setSelectedAnimal(userAnimal)
                }
              }}
              className="w-full px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
              disabled={!animals.some(a => a.name.includes(currentUser?.username || '我的'))}
            >
              👀 找到我的角色
            </button>
          </div>
          
          {/* 调试按钮 */}
          {process.env.NODE_ENV === 'development' && (
            <div className="space-y-1">
              <div className="text-sm font-medium text-gray-700">开发工具</div>
              <button
                onClick={() => window.location.reload()}
                className="w-full px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
              >
                🔄 重新加载页面
              </button>
              <button
                onClick={() => {
                  localStorage.clear()
                  window.location.reload()
                }}
                className="w-full px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
              >
                🗑️ 清除缓存并重载
              </button>
            </div>
          )}
        </div>
        
        {/* 选中动物信息 */}
        {selectedAnimal && (
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 w-80 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold mb-2 flex items-center">
              {selectedAnimal.name}
              {selectedAnimal.isUserAnimal && (
                <span className="ml-2 px-2 py-1 bg-gold-100 text-gold-800 text-xs rounded-full">我的</span>
              )}
            </h3>
            
            <div className="space-y-3 text-sm">
              {/* 基本信息 */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <p><strong>种类:</strong> {selectedAnimal.species || (selectedAnimal.type === 'OXHORSE' ? '牛马' : selectedAnimal.type === 'DIVINE' ? '神兽' : '宠物')}</p>
                <p><strong>当前活动:</strong> {selectedAnimal.currentState?.activity || selectedAnimal.activity || '未知'}</p>
                <p><strong>心情:</strong> {selectedAnimal.currentState?.mood || '平静'}</p>
                <p><strong>位置:</strong> {selectedAnimal.currentState?.location || selectedAnimal.zone || '主区域'}</p>
              </div>

              {/* 状态条 */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>能量</span>
                  <div className="w-32 bg-gray-200 rounded h-4">
                    <div 
                      className="bg-green-500 rounded h-full" 
                      style={{ width: `${selectedAnimal.currentState?.energy || selectedAnimal.energy || 0}%` }} 
                    />
                  </div>
                  <span className="text-xs">{selectedAnimal.currentState?.energy || selectedAnimal.energy || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>快乐</span>
                  <div className="w-32 bg-gray-200 rounded h-4">
                    <div 
                      className="bg-yellow-500 rounded h-full" 
                      style={{ width: `${selectedAnimal.currentState?.happiness || selectedAnimal.happiness || 0}%` }} 
                    />
                  </div>
                  <span className="text-xs">{selectedAnimal.currentState?.happiness || selectedAnimal.happiness || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>健康</span>
                  <div className="w-32 bg-gray-200 rounded h-4">
                    <div 
                      className="bg-red-500 rounded h-full" 
                      style={{ width: `${selectedAnimal.currentState?.health || 90}%` }} 
                    />
                  </div>
                  <span className="text-xs">{selectedAnimal.currentState?.health || 90}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>工作效率</span>
                  <div className="w-32 bg-gray-200 rounded h-4">
                    <div 
                      className="bg-blue-500 rounded h-full" 
                      style={{ width: `${selectedAnimal.currentState?.workEfficiency || selectedAnimal.creativity || 0}%` }} 
                    />
                  </div>
                  <span className="text-xs">{selectedAnimal.currentState?.workEfficiency || selectedAnimal.creativity || 0}%</span>
                </div>
              </div>

              {/* 互动按钮 */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="font-bold text-blue-800 mb-2">与{selectedAnimal.name}互动</h4>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'FEED')}
                    className="px-3 py-2 bg-green-400 text-green-800 text-xs rounded hover:bg-green-500 transition-colors"
                    disabled={isLoading}
                  >
                    🍎 喂食
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'PET')}
                    className="px-3 py-2 bg-pink-400 text-pink-800 text-xs rounded hover:bg-pink-500 transition-colors"
                    disabled={isLoading}
                  >
                    ✋ 抚摸
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'PLAY')}
                    className="px-3 py-2 bg-yellow-400 text-yellow-800 text-xs rounded hover:bg-yellow-500 transition-colors"
                    disabled={isLoading}
                  >
                    🎾 玩耍
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'WORK_TOGETHER')}
                    className="px-3 py-2 bg-blue-400 text-blue-800 text-xs rounded hover:bg-blue-500 transition-colors"
                    disabled={isLoading}
                  >
                    💼 协作
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'REST')}
                    className="px-3 py-2 bg-purple-400 text-purple-800 text-xs rounded hover:bg-purple-500 transition-colors"
                    disabled={isLoading}
                  >
                    😴 休息
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'ENCOURAGE')}
                    className="px-3 py-2 bg-orange-400 text-orange-800 text-xs rounded hover:bg-orange-500 transition-colors"
                    disabled={isLoading}
                  >
                    📢 鼓励
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'COMPLAIN')}
                    className="px-3 py-2 bg-red-400 text-red-800 text-xs rounded hover:bg-red-500 transition-colors"
                    disabled={isLoading}
                  >
                    😤 吐槽
                  </button>
                  <button
                    onClick={() => interactWithAnimal(selectedAnimal.id, 'THROW_STONE')}
                    className="px-3 py-2 bg-gray-600 text-gray-100 text-xs rounded hover:bg-gray-700 transition-colors"
                    disabled={isLoading}
                  >
                    🪨 扔石头
                  </button>
                </div>
                {isLoading && (
                  <p className="text-xs text-gray-500 mt-2 text-center">处理中...</p>
                )}
              </div>

              {/* AI行为状态 */}
              {selectedAnimal.behaviorState && (
                <div className="bg-purple-50 p-3 rounded-lg">
                  <h4 className="font-bold text-purple-800 mb-2">AI行为状态</h4>
                  <p className="text-xs text-purple-700">
                    {selectedAnimal.behaviorState.isActive ? 
                      `正在执行: ${selectedAnimal.behaviorState.currentBehavior || '未知行为'}` : 
                      '空闲中'
                    }
                  </p>
                </div>
              )}

              {/* 关闭按钮 */}
              <button
                onClick={() => setSelectedAnimal(null)}
                className="w-full px-3 py-2 bg-gray-400 text-white text-xs rounded hover:bg-gray-500 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        )}
        
        {/* 操作提示 */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white rounded px-3 py-2 text-sm">
          拖动移动视角 | 滚轮缩放 | 点击选择动物
        </div>
      </div>
    </div>
  )
}