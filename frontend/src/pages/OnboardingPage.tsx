import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowRight, Check } from 'lucide-react'

const OnboardingPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const navigate = useNavigate()

  const steps = [
    {
      title: '欢迎来到牛马动物园',
      description: '在这里，每个打工人都能找到属于自己的动物形象',
      emoji: '🎪',
      content: '通过AI分析，我们将为你生成独一无二的打工人动物形象，让你在虚拟动物园中与其他牛马一起生活工作。'
    },
    {
      title: '创建你的形象',
      description: '通过绘画或测试，让AI了解你的个性',
      emoji: '🎨',
      content: '你可以选择画出你的自画像，或者完成人格测试。AI将根据你的选择分析你的性格特征，匹配最合适的动物类型。'
    },
    {
      title: '加入动物园社区',
      description: '与其他打工人牛马互动交流',
      emoji: '🤝',
      content: '在动物园中移动探索，点击其他动物查看他们的信息，分享你的工作心得，建立属于打工人的社交网络。'
    }
  ]

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      navigate('/drawing')
    }
  }

  const skip = () => {
    navigate('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
        >
          {/* 进度指示器 */}
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="text-white text-sm font-medium">
                步骤 {currentStep + 1} / {steps.length}
              </div>
              <button
                onClick={skip}
                className="text-indigo-100 hover:text-white text-sm transition-colors"
              >
                跳过引导
              </button>
            </div>
            <div className="w-full bg-indigo-400 bg-opacity-30 rounded-full h-2">
              <motion.div
                className="bg-white h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-12 text-center">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-8xl mb-8"
            >
              {steps[currentStep].emoji}
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-4xl font-bold text-gray-900 mb-4"
            >
              {steps[currentStep].title}
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl text-indigo-600 mb-8"
            >
              {steps[currentStep].description}
            </motion.p>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 text-lg leading-relaxed mb-12 max-w-2xl mx-auto"
            >
              {steps[currentStep].content}
            </motion.p>

            {/* 步骤指示器 */}
            <div className="flex justify-center space-x-3 mb-12">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index <= currentStep ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            {/* 操作按钮 */}
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              onClick={nextStep}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              {currentStep === steps.length - 1 ? (
                <>
                  <Check className="w-5 h-5 mr-2" />
                  开始创建形象
                </>
              ) : (
                <>
                  下一步
                  <ArrowRight className="w-5 h-5 ml-2" />
                </>
              )}
            </motion.button>

            {currentStep > 0 && (
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                onClick={() => setCurrentStep(currentStep - 1)}
                className="block mx-auto mt-4 text-gray-500 hover:text-gray-700 transition-colors"
              >
                上一步
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default OnboardingPage