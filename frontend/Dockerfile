# 多阶段构建 - 前端
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件并安装依赖
COPY package*.json ./
RUN npm ci && npm cache clean --force

# 复制源码并构建
COPY . .
RUN npm run build

# 生产镜像 - 使用nginx提供静态文件
FROM nginx:alpine AS production

# 复制自定义nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建非root用户
RUN addgroup -g 1001 -S nginx
RUN adduser -S frontend -u 1001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3001 || exit 1

EXPOSE 3001

CMD ["nginx", "-g", "daemon off;"]