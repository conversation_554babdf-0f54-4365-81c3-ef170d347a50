{"name": "niuma-zoo-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@react-three/cannon": "^6.5.2", "@react-three/drei": "^9.82.0", "@react-three/fiber": "^8.13.6", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "intersection-observer": "^0.12.2", "lottie-react": "^2.4.0", "lucide-react": "^0.279.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "three": "^0.155.0", "zod": "^3.22.2", "zustand": "^4.4.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/three": "^0.155.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^0.34.4", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "postcss": "^8.4.29", "prettier": "^3.0.2", "prettier-plugin-tailwindcss": "^0.5.4", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.4"}}