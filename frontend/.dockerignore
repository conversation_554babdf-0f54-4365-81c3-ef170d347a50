# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist
build
.vite

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Development
.vscode
.idea

# OS
.DS_Store
Thumbs.db

# Test
coverage
.nyc_output

# Cache
.cache
.parcel-cache

# Logs
*.log

# Git
.git
.gitignore

# Docker
Dockerfile*
.dockerignore

# Documentation
README.md
*.md