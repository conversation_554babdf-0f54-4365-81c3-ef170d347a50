#!/bin/bash

# 牛马动物园项目停止脚本

echo "🛑 停止牛马动物园项目..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 停止前端服务
if [ -f frontend.pid ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo -e "${YELLOW}停止前端服务 (PID: $FRONTEND_PID)...${NC}"
        kill $FRONTEND_PID
        rm frontend.pid
    fi
fi

# 停止后端服务
if [ -f backend.pid ]; then
    BACKEND_PID=$(cat backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        echo -e "${YELLOW}停止后端服务 (PID: $BACKEND_PID)...${NC}"
        kill $BACKEND_PID
        rm backend.pid
    fi
fi

# 停止 Docker 容器
echo -e "${YELLOW}停止数据库服务...${NC}"
cd backend
docker compose -f docker-compose.dev.yml down
cd ..

# 清理日志文件（可选）
if [ "$1" = "--clean" ]; then
    echo -e "${YELLOW}清理日志文件...${NC}"
    rm -f backend.log frontend.log
fi

echo -e "${GREEN}✅ 所有服务已停止${NC}"