import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// 自定义指标
export const errorRate = new Rate('errors');
export const responseTime = new Trend('response_time');
export const requestCount = new Counter('requests');

// 测试配置
export const options = {
  stages: [
    { duration: '2m', target: 50 },   // 预热阶段
    { duration: '5m', target: 100 },  // 正常负载
    { duration: '3m', target: 200 },  // 高负载
    { duration: '2m', target: 300 },  // 峰值负载
    { duration: '3m', target: 0 },    // 冷却阶段
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求响应时间小于500ms
    http_req_failed: ['rate<0.1'],    // 错误率小于10%
    errors: ['rate<0.1'],
    response_time: ['p(95)<500'],
  },
};

// 测试数据
const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';

// 测试用户数据
const testUsers = [
  { email: '<EMAIL>', password: 'Test123456!' },
  { email: '<EMAIL>', password: 'Test123456!' },
  { email: '<EMAIL>', password: 'Test123456!' },
];

export function setup() {
  console.log('开始性能测试设置...');
  
  // 创建测试用户并获取认证token
  const registerRes = http.post(`${BASE_URL}/auth/register`, JSON.stringify({
    username: 'perftest_user',
    email: '<EMAIL>',
    password: 'Test123456!',
    confirmPassword: 'Test123456!',
  }), {
    headers: { 'Content-Type': 'application/json' },
  });

  if (registerRes.status === 201) {
    const userData = JSON.parse(registerRes.body);
    authToken = userData.data.accessToken;
    console.log('测试用户创建成功');
  } else {
    // 如果用户已存在，尝试登录
    const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
      email: '<EMAIL>',
      password: 'Test123456!',
    }), {
      headers: { 'Content-Type': 'application/json' },
    });

    if (loginRes.status === 200) {
      const userData = JSON.parse(loginRes.body);
      authToken = userData.data.accessToken;
      console.log('测试用户登录成功');
    }
  }

  return { authToken };
}

export default function(data) {
  const token = data.authToken || authToken;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };

  group('认证API性能测试', () => {
    // 登录测试
    group('用户登录', () => {
      const loginData = testUsers[Math.floor(Math.random() * testUsers.length)];
      const startTime = Date.now();
      
      const res = http.post(`${BASE_URL}/auth/login`, JSON.stringify(loginData), {
        headers: { 'Content-Type': 'application/json' },
      });

      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '登录状态码为200': (r) => r.status === 200,
        '返回access token': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.accessToken;
          } catch (e) {
            return false;
          }
        },
        '响应时间小于500ms': () => duration < 500,
      });

      if (!success) {
        errorRate.add(1);
      }
    });

    // 获取用户信息测试
    group('获取用户信息', () => {
      const startTime = Date.now();
      
      const res = http.get(`${BASE_URL}/auth/profile`, { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为200': (r) => r.status === 200,
        '返回用户数据': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.id;
          } catch (e) {
            return false;
          }
        },
        '响应时间小于200ms': () => duration < 200,
      });

      if (!success) {
        errorRate.add(1);
      }
    });
  });

  group('测试API性能测试', () => {
    // 获取测试问题
    group('获取测试问题', () => {
      const startTime = Date.now();
      
      const res = http.get(`${BASE_URL}/test/questions`, { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为200': (r) => r.status === 200,
        '返回20个问题': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.questions && body.data.questions.length === 20;
          } catch (e) {
            return false;
          }
        },
        '响应时间小于300ms': () => duration < 300,
      });

      if (!success) {
        errorRate.add(1);
      }
    });

    // 提交测试答案
    group('提交测试答案', () => {
      const answers = [];
      for (let i = 1; i <= 20; i++) {
        answers.push({
          questionId: i,
          answerId: Math.floor(Math.random() * 4) + 1,
        });
      }

      const startTime = Date.now();
      
      const res = http.post(`${BASE_URL}/test/submit`, JSON.stringify({ answers }), { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为201或409': (r) => r.status === 201 || r.status === 409, // 409表示已经测试过
        '返回测试结果': (r) => {
          try {
            if (r.status === 201) {
              const body = JSON.parse(r.body);
              return body.data && body.data.result && body.data.result.animalType;
            }
            return true; // 409状态码表示已测试过，也算成功
          } catch (e) {
            return false;
          }
        },
        '响应时间小于1000ms': () => duration < 1000,
      });

      if (!success) {
        errorRate.add(1);
      }
    });
  });

  group('动物园API性能测试', () => {
    // 获取动物列表
    group('获取动物列表', () => {
      const startTime = Date.now();
      
      const res = http.get(`${BASE_URL}/zoo/animals?page=1&limit=50`, { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为200': (r) => r.status === 200,
        '返回动物数据': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.animals && Array.isArray(body.data.animals);
          } catch (e) {
            return false;
          }
        },
        '响应时间小于400ms': () => duration < 400,
      });

      if (!success) {
        errorRate.add(1);
      }
    });

    // 点赞动物
    group('点赞动物', () => {
      const animalId = 'test-animal-id'; // 在实际测试中应该使用真实的动物ID
      const startTime = Date.now();
      
      const res = http.post(`${BASE_URL}/zoo/animals/${animalId}/like`, '{}', { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为200或404': (r) => r.status === 200 || r.status === 404, // 404表示动物不存在
        '响应时间小于200ms': () => duration < 200,
      });

      if (!success) {
        errorRate.add(1);
      }
    });
  });

  group('社交API性能测试', () => {
    // 获取吐槽列表
    group('获取吐槽列表', () => {
      const startTime = Date.now();
      
      const res = http.get(`${BASE_URL}/social/posts?page=1&limit=20`, { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为200': (r) => r.status === 200,
        '返回吐槽数据': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.posts && Array.isArray(body.data.posts);
          } catch (e) {
            return false;
          }
        },
        '响应时间小于300ms': () => duration < 300,
      });

      if (!success) {
        errorRate.add(1);
      }
    });

    // 发布吐槽
    group('发布吐槽', () => {
      const postData = {
        content: `性能测试吐槽内容 ${Math.random()}`,
        tags: ['测试', '性能'],
        isAnonymous: Math.random() > 0.5,
      };

      const startTime = Date.now();
      
      const res = http.post(`${BASE_URL}/social/posts`, JSON.stringify(postData), { headers });
      
      const duration = Date.now() - startTime;
      responseTime.add(duration);
      requestCount.add(1);

      const success = check(res, {
        '状态码为201': (r) => r.status === 201,
        '返回创建的吐槽': (r) => {
          try {
            const body = JSON.parse(r.body);
            return body.data && body.data.id;
          } catch (e) {
            return false;
          }
        },
        '响应时间小于500ms': () => duration < 500,
      });

      if (!success) {
        errorRate.add(1);
      }
    });
  });

  // 模拟用户思考时间
  sleep(Math.random() * 2 + 1); // 1-3秒随机等待
}

export function teardown(data) {
  console.log('性能测试清理完成');
  console.log(`总请求数: ${requestCount.count}`);
  console.log(`错误率: ${(errorRate.rate * 100).toFixed(2)}%`);
}