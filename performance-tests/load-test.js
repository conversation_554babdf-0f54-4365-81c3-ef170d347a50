import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// 自定义指标
export const totalRequests = new Counter('total_requests');
export const failedRequests = new Counter('failed_requests');
export const errorRate = new Rate('error_rate');
export const responseTimeP95 = new Trend('response_time_p95');

// 负载测试配置
export const options = {
  scenarios: {
    // 常规负载测试
    normal_load: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '5m', target: 50 },
        { duration: '10m', target: 50 },
        { duration: '5m', target: 0 },
      ],
      gracefulRampDown: '2m',
    },
    
    // 压力测试
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '2m', target: 100 },
        { duration: '5m', target: 200 },
        { duration: '5m', target: 300 },
        { duration: '10m', target: 400 },
        { duration: '5m', target: 0 },
      ],
      gracefulRampDown: '3m',
      startTime: '20m', // 在normal_load结束后开始
    },

    // 峰值测试
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '2m', target: 50 },
        { duration: '1m', target: 500 }, // 突然峰值
        { duration: '2m', target: 500 },
        { duration: '1m', target: 50 },
        { duration: '2m', target: 0 },
      ],
      startTime: '47m', // 在stress_test结束后开始
    },

    // 容量测试
    capacity_test: {
      executor: 'ramping-arrival-rate',
      startRate: 10,
      timeUnit: '1s',
      stages: [
        { duration: '10m', target: 50 },
        { duration: '20m', target: 100 },
        { duration: '10m', target: 200 },
        { duration: '10m', target: 0 },
      ],
      preAllocatedVUs: 100,
      maxVUs: 500,
      startTime: '55m', // 在spike_test结束后开始
    },
  },
  
  thresholds: {
    // 性能目标
    http_req_duration: [
      'p(50)<100',   // 50%的请求在100ms内
      'p(90)<300',   // 90%的请求在300ms内  
      'p(95)<500',   // 95%的请求在500ms内
      'p(99)<1000',  // 99%的请求在1s内
    ],
    http_req_failed: ['rate<0.05'], // 错误率小于5%
    http_reqs: ['rate>100'],        // 每秒处理100个请求以上
    
    // 场景特定阈值
    'http_req_duration{scenario:normal_load}': ['p(95)<300'],
    'http_req_duration{scenario:stress_test}': ['p(95)<800'],
    'http_req_duration{scenario:spike_test}': ['p(95)<1500'],
    'http_req_duration{scenario:capacity_test}': ['p(95)<400'],
  },
};

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试场景权重
const scenarios = [
  { name: 'auth', weight: 20 },
  { name: 'test', weight: 30 },
  { name: 'zoo', weight: 35 },
  { name: 'social', weight: 15 },
];

// 预设测试用户
const testUsers = Array.from({ length: 100 }, (_, i) => ({
  email: `loadtest${i}@example.com`,
  password: 'Test123456!',
  token: null,
}));

export function setup() {
  console.log('负载测试设置开始...');
  
  // 预创建测试用户
  const createdUsers = [];
  for (let i = 0; i < 10; i++) {
    const userData = {
      username: `loaduser${i}`,
      email: `loadtest${i}@example.com`,
      password: 'Test123456!',
      confirmPassword: 'Test123456!',
    };

    const res = http.post(`${BASE_URL}/auth/register`, JSON.stringify(userData), {
      headers: { 'Content-Type': 'application/json' },
    });

    if (res.status === 201) {
      const data = JSON.parse(res.body);
      createdUsers.push({
        ...userData,
        token: data.data.accessToken,
      });
    }
  }

  console.log(`创建了 ${createdUsers.length} 个测试用户`);
  return { users: createdUsers };
}

export default function(data) {
  const scenario = selectScenario();
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  totalRequests.add(1);

  switch (scenario) {
    case 'auth':
      testAuthScenario(user);
      break;
    case 'test':
      testTestScenario(user);
      break;
    case 'zoo':
      testZooScenario(user);
      break;
    case 'social':
      testSocialScenario(user);
      break;
  }

  // 模拟用户行为间隔
  sleep(Math.random() * 3 + 1);
}

function selectScenario() {
  const random = Math.random() * 100;
  let cumulative = 0;
  
  for (const scenario of scenarios) {
    cumulative += scenario.weight;
    if (random <= cumulative) {
      return scenario.name;
    }
  }
  
  return scenarios[0].name;
}

function testAuthScenario(user) {
  group('认证场景', () => {
    // 登录
    const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
      email: user.email,
      password: user.password,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });

    const success = check(loginRes, {
      '登录成功': (r) => r.status === 200,
      '响应时间合理': (r) => r.timings.duration < 500,
    });

    if (!success) {
      failedRequests.add(1);
      errorRate.add(1);
    } else {
      responseTimeP95.add(loginRes.timings.duration);
    }

    // 获取用户信息
    if (loginRes.status === 200) {
      const authData = JSON.parse(loginRes.body);
      const token = authData.data.accessToken;
      
      const profileRes = http.get(`${BASE_URL}/auth/profile`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      check(profileRes, {
        '获取用户信息成功': (r) => r.status === 200,
      });
    }
  });
}

function testTestScenario(user) {
  group('测试场景', () => {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user.token}`,
    };

    // 获取测试问题
    const questionsRes = http.get(`${BASE_URL}/test/questions`, { headers });
    
    const success = check(questionsRes, {
      '获取问题成功': (r) => r.status === 200,
      '响应时间合理': (r) => r.timings.duration < 300,
    });

    if (!success) {
      failedRequests.add(1);
      errorRate.add(1);
      return;
    }

    // 模拟提交答案
    const answers = Array.from({ length: 20 }, (_, i) => ({
      questionId: i + 1,
      answerId: Math.floor(Math.random() * 4) + 1,
    }));

    const submitRes = http.post(`${BASE_URL}/test/submit`, JSON.stringify({ answers }), { headers });
    
    check(submitRes, {
      '提交测试成功': (r) => r.status === 201 || r.status === 409, // 409表示已测试过
      '响应时间合理': (r) => r.timings.duration < 1000,
    });
  });
}

function testZooScenario(user) {
  group('动物园场景', () => {
    const headers = {
      'Authorization': `Bearer ${user.token}`,
    };

    // 获取动物列表
    const animalsRes = http.get(`${BASE_URL}/zoo/animals?page=1&limit=50`, { headers });
    
    const success = check(animalsRes, {
      '获取动物列表成功': (r) => r.status === 200,
      '响应时间合理': (r) => r.timings.duration < 400,
    });

    if (!success) {
      failedRequests.add(1);
      errorRate.add(1);
      return;
    }

    // 模拟随机点赞和投喂
    const animalId = `animal-${Math.floor(Math.random() * 100)}`;
    
    // 点赞
    http.post(`${BASE_URL}/zoo/animals/${animalId}/like`, '{}', { headers });
    
    // 投喂
    http.post(`${BASE_URL}/zoo/animals/${animalId}/feed`, JSON.stringify({
      foodType: 'banana',
    }), {
      headers: { ...headers, 'Content-Type': 'application/json' },
    });
  });
}

function testSocialScenario(user) {
  group('社交场景', () => {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user.token}`,
    };

    // 获取吐槽列表
    const postsRes = http.get(`${BASE_URL}/social/posts?page=1&limit=20`, {
      headers: { 'Authorization': `Bearer ${user.token}` },
    });
    
    check(postsRes, {
      '获取吐槽列表成功': (r) => r.status === 200,
    });

    // 随机发布吐槽
    if (Math.random() > 0.7) { // 30%概率发布
      const postData = {
        content: `负载测试吐槽 ${Date.now()} - ${Math.random()}`,
        tags: ['负载测试', '压测'],
        isAnonymous: Math.random() > 0.5,
      };

      const createRes = http.post(`${BASE_URL}/social/posts`, JSON.stringify(postData), { headers });
      
      check(createRes, {
        '发布吐槽成功': (r) => r.status === 201,
        '响应时间合理': (r) => r.timings.duration < 500,
      });
    }

    // 随机点赞评论
    if (Math.random() > 0.5) { // 50%概率点赞
      const postId = `post-${Math.floor(Math.random() * 100)}`;
      http.post(`${BASE_URL}/social/posts/${postId}/like`, '{}', { headers });
    }
  });
}

export function teardown(data) {
  console.log('负载测试清理完成');
  console.log(`总请求数: ${totalRequests.count}`);
  console.log(`失败请求数: ${failedRequests.count}`);
  console.log(`错误率: ${(errorRate.rate * 100).toFixed(2)}%`);
  
  // 清理测试数据
  console.log('清理测试数据...');
}