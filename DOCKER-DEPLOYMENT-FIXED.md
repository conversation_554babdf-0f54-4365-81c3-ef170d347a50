# 🐂🐴 牛马动物园 Docker 部署方案

## ⚠️ 当前状况

Docker部署遇到了一些技术问题：
1. Docker凭据配置问题导致镜像拉取困难
2. 前端TypeScript编译错误需要修复
3. 容器启动时权限和网络配置问题

## 🚀 解决方案

### 方案1：使用简化的Docker Compose（推荐）

我已经创建了一个简化版的 `docker-compose.yml`，直接使用Node.js官方镜像：

```bash
# 设置环境变量
export GEMINI_API_KEY="your_api_key_here"

# 启动服务（首次运行会自动安装依赖）
docker compose up -d

# 查看日志
docker compose logs -f

# 停止服务
docker compose down
```

**特点**：
- 使用官方Node镜像，避免构建问题
- 自动安装依赖
- 挂载本地代码，支持热重载
- 简单直接，容易调试

### 方案2：本地运行（最简单）

如果Docker有问题，可以直接本地运行：

```bash
# 设置环境变量
export GEMINI_API_KEY="your_api_key_here"

# 使用提供的脚本
./run-local.sh

# 或手动运行
# 后端
cd backend
npm install
node simple-server.js

# 前端（新终端）
cd frontend
npm install
npm run dev
```

### 方案3：修复后使用完整Docker部署

需要修复的问题：
1. 前端 `TestPage.tsx` 的TypeScript错误
2. Docker凭据配置
3. 容器权限设置

## 📋 文件说明

- `docker-compose.yml` - 简化版配置，直接可用
- `docker-compose-simple.yml` - 仅后端的测试配置
- `backend/Dockerfile.dev` - 开发环境Dockerfile
- `backend/Dockerfile.simple` - 生产环境Dockerfile
- `run-local.sh` - 本地运行脚本

## 🔧 环境要求

- Node.js 18+
- Docker Desktop（如果使用Docker）
- GEMINI_API_KEY 环境变量

## 🎯 访问地址

- 前端：http://localhost:3001
- 后端API：http://localhost:3000
- API测试：http://localhost:3000/api/v1/zoo/animals

## 💡 建议

1. **开发环境**：使用 `run-local.sh` 脚本，简单快速
2. **测试环境**：使用简化的 `docker-compose.yml`
3. **生产环境**：修复所有问题后，使用完整的Docker配置

## 🛠️ 故障排除

### Docker启动失败
- 检查端口占用：`lsof -i :3000` 和 `lsof -i :3001`
- 清理Docker：`docker system prune -a`
- 重启Docker Desktop

### 前端编译错误
- 临时跳过类型检查：修改 `frontend/tsconfig.json`，添加 `"skipLibCheck": true`
- 或修复 `TestPage.tsx` 文件的语法错误

### 环境变量问题
- 确保设置了 `GEMINI_API_KEY`
- 创建 `.env` 文件：
```env
GEMINI_API_KEY=your_actual_key_here
AI_GENERATION_ENABLED=true
USE_REAL_AI=true
PREFERRED_AI_MODEL=gemini
```

## 📝 总结

虽然完整的Docker部署遇到了一些问题，但我提供了多个可行的替代方案。建议先使用本地运行脚本让项目跑起来，然后逐步解决Docker相关问题。