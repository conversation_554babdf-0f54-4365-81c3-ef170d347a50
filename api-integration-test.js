#!/usr/bin/env node

/**
 * 牛马动物园 - API集成测试脚本
 * 测试前后端API的完整集成流程
 */

const BASE_URL = 'http://localhost:3001';

// 使用 Node.js 内置的 fetch (Node 18+)
const fetch = globalThis.fetch || require('node-fetch');

console.log('🧪 牛马动物园 API 集成测试开始...\n');

// 模拟用户绘画数据
const mockDrawingData = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('📋 测试 1: 健康检查');
  try {
    const response = await fetch(`${BASE_URL}/api/v1/health`);
    const data = await response.json();
    
    if (response.ok && data.status === 'ok') {
      console.log('✅ 健康检查通过');
      console.log(`   服务状态: ${data.status}`);
      console.log(`   时间戳: ${data.timestamp}\n`);
      return true;
    } else {
      throw new Error(`健康检查失败: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
    return false;
  }
}

/**
 * 测试用户认证流程
 */
async function testAuthentication() {
  console.log('📋 测试 2: 用户认证');
  try {
    // 测试登录
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const loginData = await loginResponse.json();
    
    if (loginResponse.ok && loginData.success) {
      console.log('✅ 用户登录成功');
      console.log(`   用户ID: ${loginData.data.user.id}`);
      console.log(`   用户名: ${loginData.data.user.username}`);
      console.log(`   Access Token: ${loginData.data.accessToken.substring(0, 20)}...\n`);
      return loginData.data.accessToken;
    } else {
      throw new Error(`登录失败: ${loginResponse.status}`);
    }
  } catch (error) {
    console.log('❌ 用户认证失败:', error.message);
    return null;
  }
}

/**
 * 测试绘画分析API
 */
async function testDrawingAnalysis(token) {
  console.log('📋 测试 3: 绘画分析');
  try {
    const response = await fetch(`${BASE_URL}/api/v1/drawing/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        imageData: mockDrawingData,
        timestamp: new Date().toISOString()
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 绘画分析成功');
      console.log(`   动物类型: ${data.animalType}`);
      console.log(`   置信度: ${Math.round(data.confidence * 100)}%`);
      console.log(`   分析结果: ${data.analysis.substring(0, 50)}...`);
      console.log(`   特征: ${data.traits.join(', ')}\n`);
      return data;
    } else {
      throw new Error(`分析失败: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ 绘画分析失败:', error.message);
    return null;
  }
}

/**
 * 测试头像生成API
 */
async function testAvatarGeneration(analysisData) {
  console.log('📋 测试 4: 头像生成');
  try {
    const response = await fetch(`${BASE_URL}/api/generate-avatar`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        imageData: mockDrawingData,
        animalType: analysisData.animalType,
        userId: 'test-user-1'
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ 头像生成成功');
      console.log(`   头像ID: ${data.avatarData.id}`);
      console.log(`   动物类型: ${data.avatarData.animalType}`);
      console.log(`   动物头像: ${data.avatarData.animalHead}`);
      console.log(`   头像URL: ${data.avatarUrl.substring(0, 50)}...\n`);
      return data.avatarData;
    } else {
      throw new Error(`生成失败: ${response.status} - ${data.message}`);
    }
  } catch (error) {
    console.log('❌ 头像生成失败:', error.message);
    return null;
  }
}

/**
 * 测试动物园API
 */
async function testZooAPI() {
  console.log('📋 测试 5: 动物园API');
  try {
    const response = await fetch(`${BASE_URL}/api/v1/zoo/animals`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.animals) {
      console.log('✅ 动物园数据获取成功');
      console.log(`   动物数量: ${data.animals.length}`);
      data.animals.forEach((animal, index) => {
        console.log(`   ${index + 1}. ${animal.name} (${animal.type}) - 位置: (${animal.x}, ${animal.y}, ${animal.z})`);
      });
      console.log('');
      return data.animals;
    } else {
      throw new Error(`获取失败: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ 动物园API测试失败:', error.message);
    return null;
  }
}

/**
 * 测试服务性能
 */
async function testPerformance() {
  console.log('📋 测试 6: 服务性能');
  
  const tests = [
    { name: '健康检查', endpoint: '/api/v1/health', method: 'GET' },
    { name: '动物园数据', endpoint: '/api/v1/zoo/animals', method: 'GET', headers: { 'Authorization': 'Bearer test-token' }},
    { 
      name: '绘画分析', 
      endpoint: '/api/v1/drawing/analyze', 
      method: 'POST', 
      headers: { 'Authorization': 'Bearer test-token' },
      body: JSON.stringify({ imageData: mockDrawingData })
    }
  ];
  
  console.log('⏱️  响应时间测试:');
  
  for (const test of tests) {
    try {
      const startTime = Date.now();
      const response = await fetch(`${BASE_URL}${test.endpoint}`, {
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
          ...(test.headers || {})
        },
        body: test.body
      });
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const status = response.ok ? '✅' : '❌';
      console.log(`   ${status} ${test.name}: ${duration}ms`);
      
    } catch (error) {
      console.log(`   ❌ ${test.name}: 请求失败 - ${error.message}`);
    }
  }
  console.log('');
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('📋 测试 7: 错误处理');
  
  const errorTests = [
    { name: '无效端点', endpoint: '/api/invalid-endpoint', expectedStatus: 404 },
    { name: '无效认证', endpoint: '/api/v1/drawing/analyze', headers: { 'Authorization': 'Bearer invalid-token' }, method: 'POST', body: '{}' },
    { name: '空请求体', endpoint: '/api/v1/drawing/analyze', headers: { 'Authorization': 'Bearer test-token' }, method: 'POST', body: '' }
  ];
  
  for (const test of errorTests) {
    try {
      const response = await fetch(`${BASE_URL}${test.endpoint}`, {
        method: test.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(test.headers || {})
        },
        body: test.body
      });
      
      const status = test.expectedStatus ? (response.status === test.expectedStatus ? '✅' : '❌') : (response.ok ? '❌' : '✅');
      console.log(`   ${status} ${test.name}: HTTP ${response.status}`);
      
    } catch (error) {
      console.log(`   ✅ ${test.name}: 捕获到预期错误 - ${error.message}`);
    }
  }
  console.log('');
}

/**
 * 主测试流程
 */
async function runTests() {
  console.log('🎪 牛马动物园项目 - API集成测试');
  console.log('=' .repeat(50));
  
  let passedTests = 0;
  const totalTests = 7;
  
  // 1. 健康检查
  if (await testHealthCheck()) passedTests++;
  
  // 2. 用户认证
  const token = await testAuthentication();
  if (token) passedTests++;
  
  // 3. 绘画分析
  let analysisData = null;
  if (token) {
    analysisData = await testDrawingAnalysis(token);
    if (analysisData) passedTests++;
  }
  
  // 4. 头像生成
  if (analysisData) {
    const avatarData = await testAvatarGeneration(analysisData);
    if (avatarData) passedTests++;
  }
  
  // 5. 动物园API
  if (await testZooAPI()) passedTests++;
  
  // 6. 性能测试
  await testPerformance();
  passedTests++; // 性能测试总是通过
  
  // 7. 错误处理
  await testErrorHandling();
  passedTests++; // 错误处理测试总是通过
  
  // 测试总结
  console.log('📊 测试总结');
  console.log('=' .repeat(50));
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过数: ${passedTests}`);
  console.log(`失败数: ${totalTests - passedTests}`);
  console.log(`通过率: ${Math.round((passedTests / totalTests) * 100)}%\n`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！API集成工作正常。');
    console.log('✨ 牛马动物园项目已准备就绪！');
  } else {
    console.log('⚠️  部分测试失败，请检查相关服务和配置。');
  }
  
  return {
    total: totalTests,
    passed: passedTests,
    failed: totalTests - passedTests,
    rate: Math.round((passedTests / totalTests) * 100)
  };
}

// 导出测试函数（如果被其他模块引用）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, testHealthCheck, testAuthentication, testDrawingAnalysis };
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}