/**
 * 测试增强版融合生成器
 */

const axios = require('axios')
const fs = require('fs')

async function testFusionGeneration() {
    console.log('\n🎨 测试增强版人身动物头融合生成器...')
    
    // 创建一个简单的测试绘画数据
    const Canvas = require('canvas')
    const canvas = Canvas.createCanvas(400, 400)
    const ctx = canvas.getContext('2d')
    
    // 绘制简单的人像轮廓
    ctx.fillStyle = '#f8f9fa'
    ctx.fillRect(0, 0, 400, 400)
    
    // 绘制头部
    ctx.strokeStyle = '#333'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.arc(200, 150, 80, 0, Math.PI * 2)
    ctx.stroke()
    
    // 绘制身体
    ctx.beginPath()
    ctx.moveTo(200, 230)
    ctx.lineTo(200, 350)
    ctx.moveTo(150, 280)
    ctx.lineTo(250, 280)
    ctx.moveTo(200, 350)
    ctx.lineTo(160, 400)
    ctx.moveTo(200, 350)
    ctx.lineTo(240, 400)
    ctx.stroke()
    
    // 绘制眼睛
    ctx.fillStyle = '#000'
    ctx.beginPath()
    ctx.arc(180, 140, 5, 0, Math.PI * 2)
    ctx.fill()
    ctx.beginPath()
    ctx.arc(220, 140, 5, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制嘴巴
    ctx.beginPath()
    ctx.arc(200, 170, 10, 0, Math.PI)
    ctx.stroke()
    
    const imageData = canvas.toDataURL('image/png')
    
    // 测试不同动物类型
    const animalTypes = ['OXHORSE', 'PET', 'DIVINE']
    
    for (const animalType of animalTypes) {
        console.log(`\n测试动物类型: ${animalType}`)
        
        try {
            const response = await axios.post('http://localhost:3001/api/v1/avatar/enhanced-fusion', {
                imageData: imageData,
                animalType: animalType,
                analysisData: {
                    expression: 'focused',
                    style: 'sketch',
                    features: {
                        faceShape: 'oval',
                        bodyPosture: 'standing'
                    }
                }
            }, {
                timeout: 10000
            })
            
            if (response.data.success) {
                console.log('✅ 生成成功!')
                console.log('生成方法:', response.data.avatar.generationMethod)
                console.log('动物类型:', response.data.avatar.animalType)
                console.log('融合详情:', response.data.avatar.fusionDetails)
                
                // 保存生成的图像到文件
                if (response.data.avatar.imageUrl.startsWith('data:image/')) {
                    const base64Data = response.data.avatar.imageUrl.replace(/^data:image\/\w+;base64,/, '')
                    const buffer = Buffer.from(base64Data, 'base64')
                    const filename = `fusion_test_${animalType.toLowerCase()}.png`
                    fs.writeFileSync(filename, buffer)
                    console.log(`💾 融合图像已保存到: ${filename}`)
                }
                
                console.log('生成消息:', response.data.message)
            } else {
                console.log('❌ 生成失败:', response.data)
            }
        } catch (error) {
            console.error('❌ 请求失败:', error.response?.data || error.message)
        }
        
        // 等待一下再测试下一个
        await new Promise(resolve => setTimeout(resolve, 1000))
    }
}

async function testAnimalWalking() {
    console.log('\n🚶 测试动物园行走功能...')
    
    try {
        // 获取动物园场景数据
        const response = await axios.get('http://localhost:3001/api/v1/zoo/scene')
        
        if (response.data.success) {
            console.log('✅ 动物园场景加载成功!')
            console.log('场景信息:', response.data.scene.name)
            console.log('动物数量:', response.data.animals?.length || 0)
            
            if (response.data.animals && response.data.animals.length > 0) {
                console.log('动物列表:')
                response.data.animals.forEach(animal => {
                    console.log(`  - ${animal.name} (${animal.type}) 位置: (${animal.x}, ${animal.y})`)
                })
            }
        } else {
            console.log('❌ 动物园场景加载失败:', response.data)
        }
    } catch (error) {
        console.error('❌ 动物园API请求失败:', error.response?.data || error.message)
    }
}

async function main() {
    console.log('🧪 开始全面测试增强版牛马动物园功能...')
    
    try {
        // 先测试API健康状态
        const healthResponse = await axios.get('http://localhost:3001/api/v1/health')
        console.log('✅ 后端API健康状态:', healthResponse.data)
        
        // 测试融合生成
        await testFusionGeneration()
        
        // 测试动物园功能
        await testAnimalWalking()
        
        console.log('\n🎉 所有测试完成!')
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error.message)
        console.log('请确保后端服务正在运行 (npm start 或 node mock-server.js)')
    }
}

if (require.main === module) {
    main()
}

module.exports = { testFusionGeneration, testAnimalWalking }