version: '3.8'

services:
  # 只测试后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.simple
    container_name: niuma-backend-test
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - AI_GENERATION_ENABLED=true
      - USE_REAL_AI=true
      - PREFERRED_AI_MODEL=gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - backend_uploads_test:/app/uploads
    networks:
      - niuma-test-network
    restart: unless-stopped

volumes:
  backend_uploads_test:
    driver: local

networks:
  niuma-test-network:
    driver: bridge