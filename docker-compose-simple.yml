# 简化版Docker Compose - 只部署后端
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: niuma-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - AI_GENERATION_ENABLED=true
      - USE_REAL_AI=true
      - PREFERRED_AI_MODEL=gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY:-your_api_key}
    volumes:
      - ./backend/uploads:/app/uploads
    restart: unless-stopped