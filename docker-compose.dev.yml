version: '3.8'

services:
  # 后端开发服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.simple
    container_name: niuma-backend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - AI_GENERATION_ENABLED=true
      - USE_REAL_AI=true
      - PREFERRED_AI_MODEL=gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads_dev:/app/uploads
    networks:
      - niuma-dev-network
    restart: unless-stopped
    command: ["node", "--inspect=0.0.0.0:9229", "simple-server.js"]

  # 前端开发服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: builder
    container_name: niuma-frontend-dev
    ports:
      - "3001:3001"
      - "24678:24678" # HMR port for Vite
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - niuma-dev-network
    restart: unless-stopped
    command: ["npm", "run", "dev"]

volumes:
  backend_uploads_dev:
    driver: local

networks:
  niuma-dev-network:
    driver: bridge