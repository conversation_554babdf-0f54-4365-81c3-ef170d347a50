import { chromium, FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始E2E测试全局清理...')

  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // 清理测试数据
    console.log('🗑️ 清理测试数据...')
    await page.goto(config.use?.baseURL || 'http://localhost:5173')
    
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    // 可以在这里添加数据库清理逻辑
    // await cleanupDatabase()

    console.log('✅ 全局清理完成')
  } catch (error) {
    console.error('❌ 全局清理失败:', error)
  } finally {
    await browser.close()
  }
}

export default globalTeardown