import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 开始E2E测试全局设置...')

  // 启动浏览器进行预处理
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // 等待应用启动
    console.log('⏳ 等待应用启动...')
    await page.goto(config.use?.baseURL || 'http://localhost:5173', {
      waitUntil: 'networkidle',
      timeout: 30000,
    })

    // 清理测试数据
    console.log('🧹 清理测试数据...')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    // 预创建测试用户（如果需要）
    console.log('👤 准备测试用户...')
    await setupTestUser(page)

    console.log('✅ 全局设置完成')
  } catch (error) {
    console.error('❌ 全局设置失败:', error)
    throw error
  } finally {
    await browser.close()
  }
}

async function setupTestUser(page: any) {
  // 这里可以预创建测试用户或设置测试数据
  try {
    // 检查健康状态
    await page.goto('/api/v1/health')
    const healthCheck = await page.textContent('body')
    console.log('🏥 服务健康状态:', healthCheck?.includes('ok') ? '正常' : '异常')

    // 可以在这里添加更多的预设置逻辑
    // 比如创建测试用户、初始化测试数据等
  } catch (error) {
    console.warn('⚠️ 测试用户设置过程中出现错误:', error)
  }
}

export default globalSetup