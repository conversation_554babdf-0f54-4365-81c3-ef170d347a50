import { test, expect } from '@playwright/test'

test.describe('3D动物园功能', () => {
  let testUser: any

  test.beforeAll(async ({ browser }) => {
    // 创建并登录测试用户
    const timestamp = Date.now()
    testUser = {
      username: `zoouser_${timestamp}`,
      email: `zoo_${timestamp}@example.com`,
      password: 'Test123456!',
    }

    const page = await browser.newPage()
    await page.goto('/auth/register')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.fill('[data-testid="confirm-password-input"]', testUser.password)
    await page.check('[data-testid="terms-checkbox"]')
    await page.click('[data-testid="register-button"]')

    // 完成测试以获得动物身份
    await page.goto('/test')
    await page.click('[data-testid="start-test-button"]')
    for (let i = 1; i <= 20; i++) {
      await page.click('[data-testid="option-1"]')
      if (i < 20) {
        await page.click('[data-testid="next-button"]')
      } else {
        await page.click('[data-testid="submit-button"]')
      }
    }
    await page.close()
  })

  test.beforeEach(async ({ page }) => {
    // 登录测试用户
    await page.goto('/auth/login')
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/')
  })

  test('进入3D动物园', async ({ page }) => {
    // 访问动物园
    await page.goto('/zoo')
    
    // 验证3D场景加载
    await expect(page.locator('[data-testid="zoo-canvas"]')).toBeVisible()
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeHidden()

    // 验证控制说明
    await expect(page.locator('[data-testid="controls-hint"]')).toBeVisible()
    await expect(page.locator('[data-testid="controls-hint"]')).toContainText('拖拽旋转')
  })

  test('3D场景交互控制', async ({ page }) => {
    await page.goto('/zoo')
    await page.waitForSelector('[data-testid="zoo-canvas"]')

    const canvas = page.locator('[data-testid="zoo-canvas"]')

    // 测试鼠标拖拽旋转
    await canvas.dragTo(canvas, {
      sourcePosition: { x: 100, y: 100 },
      targetPosition: { x: 200, y: 150 },
    })

    // 测试滚轮缩放
    await canvas.hover()
    await page.mouse.wheel(0, -100) // 放大
    await page.mouse.wheel(0, 100)  // 缩小

    // 验证相机控制响应
    // 在实际实现中，这里会检查相机位置的变化
  })

  test('动物点击交互', async ({ page }) => {
    await page.goto('/zoo')
    await page.waitForSelector('[data-testid="zoo-canvas"]')

    // 等待动物加载
    await page.waitForSelector('[data-testid="animal-model"]', { timeout: 10000 })

    // 点击第一个动物
    await page.click('[data-testid="animal-model"]')

    // 验证动物信息弹窗
    await expect(page.locator('[data-testid="animal-info-modal"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-owner-name"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-type-display"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-mood-indicator"]')).toBeVisible()
  })

  test('点赞功能', async ({ page }) => {
    await page.goto('/zoo')
    await page.waitForSelector('[data-testid="animal-model"]')

    // 点击动物打开信息面板
    await page.click('[data-testid="animal-model"]')
    await expect(page.locator('[data-testid="animal-info-modal"]')).toBeVisible()

    // 获取初始点赞数
    const initialLikes = await page.locator('[data-testid="like-count"]').textContent()

    // 点击点赞按钮
    await page.click('[data-testid="like-button"]')

    // 验证点赞数增加
    await expect(page.locator('[data-testid="like-count"]')).not.toContainText(initialLikes || '')
    await expect(page.locator('[data-testid="like-button"]')).toHaveClass(/liked/)

    // 再次点击取消点赞
    await page.click('[data-testid="like-button"]')
    await expect(page.locator('[data-testid="like-count"]')).toContainText(initialLikes || '')
    await expect(page.locator('[data-testid="like-button"]')).not.toHaveClass(/liked/)
  })

  test('投喂功能', async ({ page }) => {
    await page.goto('/zoo')
    await page.waitForSelector('[data-testid="animal-model"]')

    // 打开动物信息面板
    await page.click('[data-testid="animal-model"]')
    await expect(page.locator('[data-testid="animal-info-modal"]')).toBeVisible()

    // 点击投喂按钮
    await page.click('[data-testid="feed-button"]')

    // 验证投喂选择菜单
    await expect(page.locator('[data-testid="feed-menu"]')).toBeVisible()
    await expect(page.locator('[data-testid="banana-option"]')).toBeVisible()
    await expect(page.locator('[data-testid="carrot-option"]')).toBeVisible()

    // 选择香蕉投喂
    await page.click('[data-testid="banana-option"]')

    // 验证投喂动画和效果
    await expect(page.locator('[data-testid="feed-animation"]')).toBeVisible()
    await expect(page.locator('[data-testid="feed-success-message"]')).toContainText('投喂成功')

    // 验证投喂次数增加
    const feedCount = await page.locator('[data-testid="feed-count"]').textContent()
    expect(parseInt(feedCount || '0')).toBeGreaterThan(0)
  })

  test('动物筛选功能', async ({ page }) => {
    await page.goto('/zoo')

    // 打开筛选面板
    await page.click('[data-testid="filter-button"]')
    await expect(page.locator('[data-testid="filter-panel"]')).toBeVisible()

    // 按动物类型筛选
    await page.click('[data-testid="filter-cattle"]')
    await page.click('[data-testid="apply-filter-button"]')

    // 验证只显示牛类动物
    await page.waitForTimeout(1000) // 等待筛选动画完成
    const visibleAnimals = await page.locator('[data-testid="animal-model"]').count()
    expect(visibleAnimals).toBeGreaterThan(0)

    // 清除筛选
    await page.click('[data-testid="clear-filter-button"]')
    
    // 验证所有动物重新显示
    const allAnimals = await page.locator('[data-testid="animal-model"]').count()
    expect(allAnimals).toBeGreaterThanOrEqual(visibleAnimals)
  })

  test('搜索功能', async ({ page }) => {
    await page.goto('/zoo')

    // 使用搜索功能
    await page.fill('[data-testid="search-input"]', testUser.username)
    await page.press('[data-testid="search-input"]', 'Enter')

    // 验证搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 点击搜索结果
    await page.click('[data-testid="search-result-item"]')

    // 验证相机聚焦到对应动物
    await expect(page.locator('[data-testid="animal-highlight"]')).toBeVisible()
  })

  test('动物园区域导航', async ({ page }) => {
    await page.goto('/zoo')

    // 验证区域导航菜单
    await expect(page.locator('[data-testid="zone-navigator"]')).toBeVisible()
    
    // 切换到神兽区
    await page.click('[data-testid="zone-mythical"]')
    
    // 验证相机移动到神兽区
    await page.waitForTimeout(2000) // 等待相机动画
    await expect(page.locator('[data-testid="zone-indicator"]')).toContainText('神兽区')

    // 切换到宠物区
    await page.click('[data-testid="zone-pet"]')
    await expect(page.locator('[data-testid="zone-indicator"]')).toContainText('宠物区')

    // 切换到牛马区
    await page.click('[data-testid="zone-cattle"]')
    await expect(page.locator('[data-testid="zone-indicator"]')).toContainText('牛马区')
  })

  test('实时动物行为', async ({ page }) => {
    await page.goto('/zoo')
    await page.waitForSelector('[data-testid="animal-model"]')

    // 观察动物行为变化
    const animal = page.locator('[data-testid="animal-model"]').first()
    
    // 等待并验证动物有动画行为
    await page.waitForTimeout(3000)
    
    // 验证动物状态更新
    await animal.click()
    await expect(page.locator('[data-testid="animal-activity"]')).toBeVisible()
    
    // 动物行为包括：idle, walking, eating, sleeping等
    const activity = await page.locator('[data-testid="animal-activity"]').textContent()
    expect(['idle', 'walking', 'eating', 'sleeping']).toContain(activity)
  })

  test('动物园统计信息', async ({ page }) => {
    await page.goto('/zoo')

    // 查看统计面板
    await page.click('[data-testid="stats-button"]')
    await expect(page.locator('[data-testid="stats-panel"]')).toBeVisible()

    // 验证统计数据
    await expect(page.locator('[data-testid="total-animals"]')).toBeVisible()
    await expect(page.locator('[data-testid="online-animals"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-distribution"]')).toBeVisible()

    // 验证排行榜
    await page.click('[data-testid="ranking-tab"]')
    await expect(page.locator('[data-testid="like-ranking"]')).toBeVisible()
    await expect(page.locator('[data-testid="feed-ranking"]')).toBeVisible()
  })

  test('全屏模式', async ({ page }) => {
    await page.goto('/zoo')

    // 进入全屏模式
    await page.click('[data-testid="fullscreen-button"]')
    
    // 验证全屏模式UI调整
    await expect(page.locator('[data-testid="fullscreen-ui"]')).toBeVisible()
    await expect(page.locator('[data-testid="sidebar"]')).toBeHidden()

    // 退出全屏模式
    await page.press('body', 'Escape')
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
  })

  test('性能监控', async ({ page }) => {
    await page.goto('/zoo')

    // 监控FPS
    const fpsElement = page.locator('[data-testid="fps-counter"]')
    if (await fpsElement.isVisible()) {
      const fps = await fpsElement.textContent()
      expect(parseInt(fps || '0')).toBeGreaterThan(30) // 至少30FPS
    }

    // 监控内存使用
    const memoryElement = page.locator('[data-testid="memory-usage"]')
    if (await memoryElement.isVisible()) {
      const memory = await memoryElement.textContent()
      expect(memory).toMatch(/\d+MB/) // 显示内存使用情况
    }
  })

  test('夜间模式', async ({ page }) => {
    await page.goto('/zoo')

    // 切换到夜间模式
    await page.click('[data-testid="night-mode-toggle"]')

    // 验证场景变为夜间
    await expect(page.locator('[data-testid="zoo-canvas"]')).toHaveAttribute('data-theme', 'night')
    
    // 验证动物行为在夜间的变化
    await page.waitForTimeout(2000)
    
    // 切换回日间模式
    await page.click('[data-testid="day-mode-toggle"]')
    await expect(page.locator('[data-testid="zoo-canvas"]')).toHaveAttribute('data-theme', 'day')
  })

  test('移动端3D交互', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/zoo')

    // 验证移动端触摸控制
    const canvas = page.locator('[data-testid="zoo-canvas"]')
    
    // 单指拖拽旋转
    await canvas.touchscreen.tap(100, 100)
    await canvas.dragTo(canvas, {
      sourcePosition: { x: 100, y: 100 },
      targetPosition: { x: 200, y: 100 },
    })

    // 双指缩放手势模拟
    await page.touchscreen.tap(100, 100)
    await page.touchscreen.tap(200, 200)

    // 验证移动端UI适配
    await expect(page.locator('[data-testid="mobile-controls"]')).toBeVisible()
    await expect(page.locator('[data-testid="mobile-zone-tabs"]')).toBeVisible()
  })
})