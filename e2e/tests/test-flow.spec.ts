import { test, expect } from '@playwright/test'

test.describe('打工人分类测试完整流程', () => {
  let testUser: any

  test.beforeAll(async ({ browser }) => {
    // 创建测试用户
    const timestamp = Date.now()
    testUser = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'Test123456!',
    }

    // 注册测试用户
    const page = await browser.newPage()
    await page.goto('/auth/register')
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.fill('[data-testid="confirm-password-input"]', testUser.password)
    await page.check('[data-testid="terms-checkbox"]')
    await page.click('[data-testid="register-button"]')
    await page.close()
  })

  test.beforeEach(async ({ page }) => {
    // 登录测试用户
    await page.goto('/auth/login')
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/')
  })

  test('完整的测试流程', async ({ page }) => {
    // 1. 访问测试页面
    await page.goto('/test')
    await expect(page.locator('[data-testid="test-intro"]')).toBeVisible()
    await expect(page.locator('h1')).toContainText('打工人身份测试')

    // 2. 开始测试
    await page.click('[data-testid="start-test-button"]')
    await expect(page.locator('[data-testid="test-question"]')).toBeVisible()

    // 3. 回答所有问题
    for (let i = 1; i <= 20; i++) {
      // 验证当前问题
      await expect(page.locator('[data-testid="question-number"]')).toContainText(`${i} / 20`)
      await expect(page.locator('[data-testid="progress-bar"]')).toHaveAttribute(
        'aria-valuenow',
        Math.round((i / 20) * 100).toString()
      )

      // 选择答案（选择第一个选项）
      await page.click('[data-testid="option-1"]')
      await expect(page.locator('[data-testid="option-1"]')).toHaveClass(/selected/)

      // 点击下一题（最后一题是提交）
      if (i < 20) {
        await page.click('[data-testid="next-button"]')
      } else {
        await page.click('[data-testid="submit-button"]')
      }
    }

    // 4. 验证测试结果页面
    await expect(page.locator('[data-testid="test-result"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-type"]')).toBeVisible()
    await expect(page.locator('[data-testid="test-score"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-description"]')).toBeVisible()

    // 5. 验证结果内容
    const animalType = await page.locator('[data-testid="animal-type"]').textContent()
    expect(['神兽', '宠物', '牛马']).toContain(animalType)

    const score = await page.locator('[data-testid="test-score"]').textContent()
    expect(score).toMatch(/\d+分/)

    // 6. 检查3D动物预览
    await expect(page.locator('[data-testid="animal-3d-preview"]')).toBeVisible()
  })

  test('测试进度保存功能', async ({ page }) => {
    await page.goto('/test')
    await page.click('[data-testid="start-test-button"]')

    // 回答前5个问题
    for (let i = 1; i <= 5; i++) {
      await page.click('[data-testid="option-1"]')
      if (i < 5) {
        await page.click('[data-testid="next-button"]')
      }
    }

    // 刷新页面
    await page.reload()

    // 验证进度是否保存
    await expect(page.locator('[data-testid="continue-test-button"]')).toBeVisible()
    await page.click('[data-testid="continue-test-button"]')

    // 验证回到第6题
    await expect(page.locator('[data-testid="question-number"]')).toContainText('6 / 20')
  })

  test('返回上一题功能', async ({ page }) => {
    await page.goto('/test')
    await page.click('[data-testid="start-test-button"]')

    // 回答第一题
    await page.click('[data-testid="option-1"]')
    await page.click('[data-testid="next-button"]')

    // 回答第二题
    await page.click('[data-testid="option-2"]')
    await page.click('[data-testid="next-button"]')

    // 验证在第三题
    await expect(page.locator('[data-testid="question-number"]')).toContainText('3 / 20')

    // 返回上一题
    await page.click('[data-testid="prev-button"]')
    await expect(page.locator('[data-testid="question-number"]')).toContainText('2 / 20')

    // 验证之前的答案被保留
    await expect(page.locator('[data-testid="option-2"]')).toHaveClass(/selected/)
  })

  test('测试验证和错误处理', async ({ page }) => {
    await page.goto('/test')
    await page.click('[data-testid="start-test-button"]')

    // 尝试不选择答案直接下一题
    await page.click('[data-testid="next-button"]')
    await expect(page.locator('[data-testid="validation-error"]')).toContainText('请选择一个答案')

    // 选择答案后应该可以继续
    await page.click('[data-testid="option-1"]')
    await page.click('[data-testid="next-button"]')
    await expect(page.locator('[data-testid="question-number"]')).toContainText('2 / 20')
  })

  test('生成和分享测试证书', async ({ page }) => {
    // 首先完成测试
    await completeTest(page)

    // 生成证书
    await page.click('[data-testid="generate-certificate-button"]')
    await expect(page.locator('[data-testid="certificate-modal"]')).toBeVisible()

    // 验证证书内容
    await expect(page.locator('[data-testid="certificate-username"]')).toContainText(testUser.username)
    await expect(page.locator('[data-testid="certificate-animal-type"]')).toBeVisible()
    await expect(page.locator('[data-testid="certificate-date"]')).toBeVisible()

    // 测试分享功能
    await page.click('[data-testid="share-wechat-button"]')
    // 验证分享功能被触发（在实际测试中可能需要mock分享API）

    // 下载证书
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.click('[data-testid="download-certificate-button"]')
    ])
    expect(download.suggestedFilename()).toMatch(/.*\.(png|jpg|jpeg)$/)
  })

  test('重新测试功能', async ({ page }) => {
    // 首先完成测试
    await completeTest(page)

    // 点击重新测试
    await page.click('[data-testid="retake-test-button"]')

    // 验证确认对话框
    await expect(page.locator('[data-testid="retake-confirm-modal"]')).toBeVisible()
    await page.click('[data-testid="confirm-retake-button"]')

    // 验证回到测试开始页面
    await expect(page.locator('[data-testid="test-intro"]')).toBeVisible()
    await expect(page.locator('[data-testid="start-test-button"]')).toBeVisible()
  })

  test('测试结果分享功能', async ({ page }) => {
    // 完成测试
    await completeTest(page)

    // 测试社交媒体分享
    await page.click('[data-testid="share-result-button"]')
    await expect(page.locator('[data-testid="share-modal"]')).toBeVisible()

    // 分享到微信
    await page.click('[data-testid="share-wechat"]')
    // 验证分享链接生成
    await expect(page.locator('[data-testid="share-link"]')).toBeVisible()

    // 复制分享链接
    await page.click('[data-testid="copy-link-button"]')
    // 验证复制成功提示
    await expect(page.locator('[data-testid="copy-success-toast"]')).toBeVisible()
  })

  test('测试数据统计展示', async ({ page }) => {
    // 完成测试
    await completeTest(page)

    // 查看统计数据
    await page.click('[data-testid="view-statistics-button"]')
    await expect(page.locator('[data-testid="statistics-modal"]')).toBeVisible()

    // 验证统计数据内容
    await expect(page.locator('[data-testid="total-participants"]')).toBeVisible()
    await expect(page.locator('[data-testid="animal-distribution-chart"]')).toBeVisible()
    await expect(page.locator('[data-testid="score-ranking"]')).toBeVisible()
  })

  test('移动端响应式测试', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/test')

    // 验证移动端布局
    await expect(page.locator('[data-testid="mobile-test-intro"]')).toBeVisible()
    
    // 开始测试
    await page.click('[data-testid="start-test-button"]')

    // 验证移动端问题布局
    await expect(page.locator('[data-testid="mobile-question-container"]')).toBeVisible()
    
    // 测试滑动操作
    await page.touchscreen.tap(200, 400) // 选择答案
    await page.click('[data-testid="next-button"]')

    // 验证进度条在移动端正确显示
    await expect(page.locator('[data-testid="mobile-progress-bar"]')).toBeVisible()
  })

  // 辅助函数：完成测试
  async function completeTest(page: any) {
    await page.goto('/test')
    await page.click('[data-testid="start-test-button"]')

    // 快速完成所有20题
    for (let i = 1; i <= 20; i++) {
      await page.click('[data-testid="option-1"]')
      if (i < 20) {
        await page.click('[data-testid="next-button"]')
      } else {
        await page.click('[data-testid="submit-button"]')
      }
    }

    // 等待结果页面加载
    await expect(page.locator('[data-testid="test-result"]')).toBeVisible()
  }
})