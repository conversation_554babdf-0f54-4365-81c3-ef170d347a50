import { test, expect } from '@playwright/test'

test.describe('用户认证流程', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前清理存储
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  test('用户注册流程', async ({ page }) => {
    // 生成唯一的测试用户数据
    const timestamp = Date.now()
    const testUser = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'Test123456!',
    }

    // 访问注册页面
    await page.goto('/auth/register')
    await expect(page).toHaveTitle(/注册/)

    // 填写注册表单
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.fill('[data-testid="confirm-password-input"]', testUser.password)

    // 检查密码强度指示器
    await expect(page.locator('[data-testid="password-strength"]')).toBeVisible()
    await expect(page.locator('[data-testid="password-strength"]')).toContainText('强')

    // 同意服务条款
    await page.check('[data-testid="terms-checkbox"]')

    // 提交表单
    await page.click('[data-testid="register-button"]')

    // 验证注册成功并跳转
    await expect(page).toHaveURL('/')
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
    
    // 验证用户名显示正确
    await page.click('[data-testid="user-menu"]')
    await expect(page.locator('[data-testid="username-display"]')).toContainText(testUser.username)
  })

  test('用户登录流程', async ({ page }) => {
    // 使用预设的测试用户
    const testUser = {
      email: '<EMAIL>',
      password: 'Test123456!',
    }

    // 访问登录页面
    await page.goto('/auth/login')
    await expect(page).toHaveTitle(/登录/)

    // 填写登录表单
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)

    // 选择记住我
    await page.check('[data-testid="remember-checkbox"]')

    // 提交表单
    await page.click('[data-testid="login-button"]')

    // 验证登录成功
    await expect(page).toHaveURL('/')
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
  })

  test('登录表单验证', async ({ page }) => {
    await page.goto('/auth/login')

    // 测试空表单提交
    await page.click('[data-testid="login-button"]')
    await expect(page.locator('[data-testid="email-error"]')).toContainText('邮箱是必填项')
    await expect(page.locator('[data-testid="password-error"]')).toContainText('密码是必填项')

    // 测试无效邮箱格式
    await page.fill('[data-testid="email-input"]', 'invalid-email')
    await page.blur('[data-testid="email-input"]')
    await expect(page.locator('[data-testid="email-error"]')).toContainText('邮箱格式不正确')

    // 测试密码长度不足
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', '123')
    await page.blur('[data-testid="password-input"]')
    await expect(page.locator('[data-testid="password-error"]')).toContainText('密码长度至少6位')
  })

  test('密码可见性切换', async ({ page }) => {
    await page.goto('/auth/login')

    const passwordInput = page.locator('[data-testid="password-input"]')
    const toggleButton = page.locator('[data-testid="password-toggle"]')

    // 初始状态应该是隐藏的
    await expect(passwordInput).toHaveAttribute('type', 'password')

    // 点击显示密码
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'text')

    // 再次点击隐藏密码
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('错误的登录凭据', async ({ page }) => {
    await page.goto('/auth/login')

    // 使用错误的凭据
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'wrongpassword')
    await page.click('[data-testid="login-button"]')

    // 验证错误消息
    await expect(page.locator('[data-testid="login-error"]')).toContainText('邮箱或密码错误')
    
    // 验证仍在登录页面
    await expect(page).toHaveURL('/auth/login')
  })

  test('注册表单验证', async ({ page }) => {
    await page.goto('/auth/register')

    // 测试用户名长度验证
    await page.fill('[data-testid="username-input"]', 'ab')
    await page.blur('[data-testid="username-input"]')
    await expect(page.locator('[data-testid="username-error"]')).toContainText('用户名长度至少3位')

    // 测试密码确认不匹配
    await page.fill('[data-testid="password-input"]', 'Test123456!')
    await page.fill('[data-testid="confirm-password-input"]', 'Different123!')
    await page.blur('[data-testid="confirm-password-input"]')
    await expect(page.locator('[data-testid="confirm-password-error"]')).toContainText('密码确认不匹配')

    // 测试必须同意服务条款
    await page.fill('[data-testid="username-input"]', 'testuser')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'Test123456!')
    await page.fill('[data-testid="confirm-password-input"]', 'Test123456!')
    await page.click('[data-testid="register-button"]')
    
    await expect(page.locator('[data-testid="terms-error"]')).toContainText('必须同意服务条款')
  })

  test('忘记密码功能', async ({ page }) => {
    await page.goto('/auth/login')

    // 点击忘记密码链接
    await page.click('[data-testid="forgot-password-link"]')
    await expect(page).toHaveURL('/auth/forgot-password')

    // 填写邮箱
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.click('[data-testid="send-reset-button"]')

    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toContainText('重置密码邮件已发送')
  })

  test('用户登出功能', async ({ page }) => {
    // 先登录
    await page.goto('/auth/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'Test123456!')
    await page.click('[data-testid="login-button"]')

    // 验证登录成功
    await expect(page).toHaveURL('/')
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()

    // 点击用户菜单
    await page.click('[data-testid="user-menu"]')
    
    // 点击登出
    await page.click('[data-testid="logout-button"]')

    // 验证登出成功
    await expect(page).toHaveURL('/auth/login')
    await expect(page.locator('[data-testid="user-menu"]')).not.toBeVisible()
  })

  test('第三方登录（微信）', async ({ page }) => {
    await page.goto('/auth/login')

    // 点击微信登录按钮
    await page.click('[data-testid="wechat-login-button"]')

    // 验证弹出微信授权窗口（这里只是检查是否触发了正确的事件）
    // 在实际测试中，可能需要使用mock或者专门的测试环境
    await expect(page.locator('[data-testid="wechat-qr-modal"]')).toBeVisible()
  })

  test('会话保持功能', async ({ page, context }) => {
    // 登录
    await page.goto('/auth/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'Test123456!')
    await page.check('[data-testid="remember-checkbox"]')
    await page.click('[data-testid="login-button"]')

    // 验证登录成功
    await expect(page).toHaveURL('/')

    // 创建新页面模拟重新访问
    const newPage = await context.newPage()
    await newPage.goto('/')

    // 验证会话保持，用户仍然登录
    await expect(newPage.locator('[data-testid="user-menu"]')).toBeVisible()
  })
})