# 动物园展示功能设计文档

本文档包含5个专业的Mermaid图表，全面展示牛马动物园系统的架构设计、行为模式、用户交互流程和AI服务集成。

## 1. 动物园系统架构图

```mermaid
graph TB
    %% 用户层
    subgraph "用户界面层"
        A[React Frontend] 
        A1[ZooPage - 3D Canvas显示]
        A2[AnimalInteractionModal - 互动弹窗]
        A3[ZooControlPanel - 控制面板]
        A --> A1
        A --> A2
        A --> A3
    end

    %% API网关层
    subgraph "API网关层"
        B[NestJS Backend]
        B1[Zoo Controller]
        B2[Auth Controller]
        B3[Drawing Controller]
        B --> B1
        B --> B2
        B --> B3
    end

    %% 服务层
    subgraph "核心服务层"
        C1[Zoo Service - 场景管理]
        C2[Animal Behavior Service - AI行为]
        C3[Zoo AI Scheduler - 定时任务]
        C4[Drawing Service - 头像生成]
        C5[AI Service - 外部AI集成]
        B1 --> C1
        B1 --> C2
        B1 --> C3
        B3 --> C4
        B3 --> C5
    end

    %% 数据层
    subgraph "数据持久层"
        D1[PostgreSQL Database]
        D2[Redis Cache]
        D3[File Storage]
        C1 --> D1
        C2 --> D1
        C4 --> D1
        C5 --> D2
        C4 --> D3
    end

    %% 外部服务
    subgraph "外部AI服务"
        E1[Gemini AI - 图像分析]
        E2[Replicate - 图像生成]
        E3[Animal Type Classifier]
        C5 --> E1
        C5 --> E2
        C5 --> E3
    end

    %% 实时更新
    subgraph "实时系统"
        F1[WebSocket连接]
        F2[定时更新机制]
        F3[事件驱动系统]
        A <--> F1
        C3 --> F2
        C2 --> F3
    end

    %% 数据流
    A -.->|用户绘图| B3
    B3 -.->|AI分析| C4
    C4 -.->|生成动物| C1
    A -.->|查看场景| B1
    B1 -.->|获取动物| C1
    A -.->|动物互动| B1
    B1 -.->|处理互动| C2
    
    %% 样式
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef service fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef external fill:#fce4ec
    classDef realtime fill:#f1f8e9

    class A,A1,A2,A3 frontend
    class B,B1,B2,B3 backend
    class C1,C2,C3,C4,C5 service
    class D1,D2,D3 data
    class E1,E2,E3 external
    class F1,F2,F3 realtime
```

## 2. 动物行为状态机图

```mermaid
stateDiagram-v2
    [*] --> Idle: 动物创建

    state "空闲状态" as Idle {
        [*] --> Resting
        Resting --> Observing: 环境刺激
        Observing --> Deciding: 行为权重计算
        Deciding --> Resting: 无合适行为
    }

    state "工作状态" as Working {
        [*] --> Evaluating: 评估工作能力
        Evaluating --> Productive: 能量>30
        Evaluating --> Struggling: 能量<30
        Productive --> Collaborating: 附近有其他动物
        Struggling --> Resting: 能量耗尽
        Collaborating --> Productive: 协作完成
    }

    state "社交状态" as Socializing {
        [*] --> Approaching: 寻找社交目标
        Approaching --> Interacting: 找到合适目标
        Approaching --> Rejecting: 目标不合适
        Interacting --> Bonding: 互动成功
        Interacting --> Conflicting: 互动失败
        Bonding --> Separating: 社交满足
        Conflicting --> Avoiding: 产生敌意
        Rejecting --> [*]
        Avoiding --> [*]
        Separating --> [*]
    }

    state "移动状态" as Moving {
        [*] --> Wandering: 随机漫游
        Wandering --> Targeted: 有明确目标
        Targeted --> Patrolling: 神兽巡视
        Targeted --> Seeking: 寻找资源
        Patrolling --> Wandering: 巡视完成
        Seeking --> Wandering: 目标达成
    }

    state "休息状态" as Resting_State {
        [*] --> Sleeping: 能量<20
        [*] --> Relaxing: 20<=能量<50
        [*] --> Energizing: 能量恢复中
        Sleeping --> Relaxing: 能量>20
        Relaxing --> Energizing: 能量>50
        Energizing --> [*]: 能量>70
    }

    state "玩耍状态" as Playing {
        [*] --> Exploring: 探索环境
        Exploring --> Gaming: 发现玩具/设施
        Gaming --> Enjoying: 玩耍成功
        Gaming --> Bored: 玩耍失败
        Enjoying --> [*]: 幸福度提升
        Bored --> [*]: 寻找新活动
    }

    %% 主要状态转换
    Idle --> Working: 工作时间 && 能量>30
    Idle --> Socializing: 附近有动物 && 社交欲望>60
    Idle --> Moving: 需要改变位置
    Idle --> Resting_State: 能量<50
    Idle --> Playing: 宠物类型 && 幸福度<60

    Working --> Idle: 工作完成 || 能量<20
    Working --> Resting_State: 过度疲劳
    Working --> Socializing: 工作协作需要

    Socializing --> Idle: 社交完成
    Socializing --> Playing: 社交转为玩耍
    Socializing --> Moving: 跟随其他动物

    Moving --> Idle: 到达目标位置
    Moving --> Working: 到达工作区域
    Moving --> Playing: 到达游乐设施

    Resting_State --> Idle: 休息完成
    Playing --> Idle: 玩耍结束
    Playing --> Socializing: 多动物玩耍

    %% 外部事件触发
    Idle --> Playing: 用户互动(PLAY)
    Idle --> Working: 用户互动(WORK_TOGETHER) 
    Idle --> Resting_State: 用户互动(REST)
    
    %% 特殊状态
    state "特殊事件响应" as SpecialEvent {
        [*] --> EventParticipating: 动物园事件触发
        EventParticipating --> EventCompleted: 事件结束
        EventCompleted --> [*]
    }

    Idle --> SpecialEvent: 全园事件触发
    Working --> SpecialEvent: 全园事件触发
    Socializing --> SpecialEvent: 全园事件触发
    SpecialEvent --> Idle: 事件结束

    %% AI条件说明
    note right of Idle
        AI条件评估：
        - 时间段(6-18工作时间)
        - 能量水平(0-100)
        - 幸福度(0-100)
        - 附近动物数量
        - 动物类型偏好
        - 天气条件
    end note

    note right of Working
        工作行为特征：
        - 牛马类：高效率持续工作
        - 神兽类：指导协调工作
        - 宠物类：参与度较低
    end note
```

## 3. 用户交互流程图

```mermaid
flowchart TD
    Start([用户进入动物园]) --> LoadCheck{检查登录状态}
    
    LoadCheck -->|未登录| Login[跳转登录页面]
    LoadCheck -->|已登录| LoadData[加载动物园数据]
    Login --> LoginSuccess{登录成功?}
    LoginSuccess -->|否| Login
    LoginSuccess -->|是| LoadData
    
    LoadData --> APICheck{后端API可用?}
    APICheck -->|是| LoadFromAPI[从API加载场景数据]
    APICheck -->|否| LoadLocal[加载本地/示例数据]
    
    LoadFromAPI --> ParseAnimals[解析动物数据]
    LoadLocal --> ParseAnimals
    ParseAnimals --> LoadImages[加载动物头像图片]
    LoadImages --> InitCanvas[初始化3D画布]
    
    InitCanvas --> ZooReady[动物园展示就绪]
    
    ZooReady --> UserAction{用户操作}
    
    UserAction -->|点击动物| SelectAnimal[选中动物]
    UserAction -->|拖拽画布| MoveCamera[移动视角]
    UserAction -->|滚轮操作| ZoomCamera[缩放视角]
    UserAction -->|点击控制按钮| TriggerEvent[触发动物园事件]
    UserAction -->|切换显示设置| UpdateDisplay[更新显示选项]
    
    SelectAnimal --> ShowInfo[显示动物详细信息]
    ShowInfo --> InteractChoice{选择互动类型}
    
    InteractChoice -->|喂食| FeedAction[执行喂食互动]
    InteractChoice -->|抚摸| PetAction[执行抚摸互动]
    InteractChoice -->|玩耍| PlayAction[执行玩耍互动]
    InteractChoice -->|协作| WorkAction[执行工作协作]
    InteractChoice -->|休息| RestAction[帮助动物休息]
    InteractChoice -->|鼓励| EncourageAction[鼓励动物]
    InteractChoice -->|关闭| CloseInfo[关闭信息面板]
    
    FeedAction --> ProcessInteraction[处理互动请求]
    PetAction --> ProcessInteraction
    PlayAction --> ProcessInteraction
    WorkAction --> ProcessInteraction
    RestAction --> ProcessInteraction
    EncourageAction --> ProcessInteraction
    
    ProcessInteraction --> APIRequest[发送API请求]
    APIRequest --> ValidateAuth{验证用户权限}
    ValidateAuth -->|失败| ErrorAuth[权限错误提示]
    ValidateAuth -->|成功| CheckCooldown{检查互动冷却}
    
    CheckCooldown -->|冷却中| ErrorCooldown[显示冷却时间]
    CheckCooldown -->|可互动| ExecuteInteraction[执行动物互动]
    
    ExecuteInteraction --> CalculateEffect[计算互动效果]
    CalculateEffect --> UpdateAnimalState[更新动物状态]
    UpdateAnimalState --> RecordInteraction[记录互动历史]
    RecordInteraction --> GenerateReward[生成用户奖励]
    GenerateReward --> UpdateDisplay2[更新显示内容]
    UpdateDisplay2 --> ShowSuccess[显示成功消息]
    
    ErrorAuth --> UserAction
    ErrorCooldown --> UserAction
    ShowSuccess --> UserAction
    CloseInfo --> UserAction
    MoveCamera --> UserAction
    ZoomCamera --> UserAction
    UpdateDisplay --> UserAction
    
    TriggerEvent --> EventChoice{事件类型}
    EventChoice -->|快乐时光| HappyEvent[触发快乐时光事件]
    EventChoice -->|工作集会| WorkEvent[触发工作集会事件]
    EventChoice -->|午休时间| RestEvent[触发午休事件]
    
    HappyEvent --> BroadcastEvent[广播事件到所有动物]
    WorkEvent --> BroadcastEvent
    RestEvent --> BroadcastEvent
    BroadcastEvent --> UpdateAllAnimals[批量更新动物行为]
    UpdateAllAnimals --> ShowEventSuccess[显示事件成功消息]
    ShowEventSuccess --> UserAction
    
    %% 实时更新循环
    ZooReady --> AutoUpdate[自动更新循环]
    AutoUpdate --> UpdateEnvironment[更新环境状态]
    UpdateEnvironment --> UpdateStatistics[更新统计信息]
    UpdateStatistics --> RefreshDisplay[刷新显示]
    RefreshDisplay --> Delay[等待10秒]
    Delay --> AutoUpdate
    
    %% AI行为循环
    ZooReady --> AILoop[AI行为循环]
    AILoop --> EvaluateBehaviors[评估所有动物行为]
    EvaluateBehaviors --> GenerateBehaviors[生成新的行为指令]
    GenerateBehaviors --> ApplyBehaviors[应用行为到动物]
    ApplyBehaviors --> AIDelay[等待行为完成]
    AIDelay --> AILoop
    
    %% 创建新角色流程
    UserAction -->|创建角色| NavigateDrawing[跳转绘画页面]
    NavigateDrawing --> DrawingProcess[用户绘画流程]
    DrawingProcess --> GenerateAvatar[AI生成动物头像]
    GenerateAvatar --> CreateZooAnimal[创建动物园动物]
    CreateZooAnimal --> ZooReady
    
    %% 样式定义
    classDef userAction fill:#e3f2fd
    classDef systemProcess fill:#f1f8e9  
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8
    
    class Start,UserAction,InteractChoice,EventChoice userAction
    class LoadData,ProcessInteraction,UpdateAnimalState,BroadcastEvent systemProcess
    class LoadCheck,APICheck,ValidateAuth,CheckCooldown decision
    class ErrorAuth,ErrorCooldown error
    class ShowSuccess,ShowEventSuccess success
```

## 4. 动物园场景布局图

```mermaid
graph TB
    subgraph "动物园全景布局 (3200x2400像素)"
        subgraph "工作区域 (0-1067像素)"
            W1[办公设施区]
            W2[协作工作台]  
            W3[牛马专区]
            W4[效率监控站]
            
            W1 --- W2
            W2 --- W3
            W3 --- W4
            
            subgraph "工作区设施"
                WF1[智能办公桌]
                WF2[团队协作屏]
                WF3[休息咖啡角]
                WF4[成果展示墙]
            end
        end
        
        subgraph "神兽管理区 (1067-2133像素)"
            M1[神兽圣殿]
            M2[指导中心]
            M3[冥想花园]
            M4[智慧之泉]
            
            M1 --- M2
            M2 --- M3
            M3 --- M4
            
            subgraph "管理区设施"
                MF1[决策会议室]
                MF2[战略规划台]
                MF3[神兽修炼场]
                MF4[全局监控中心]
            end
        end
        
        subgraph "休闲娱乐区 (2133-3200像素)"
            R1[宠物游乐场]
            R2[休闲咖啡厅]
            R3[娱乐活动区]
            R4[放松按摩区]
            
            R1 --- R2
            R2 --- R3
            R3 --- R4
            
            subgraph "休闲区设施"
                RF1[玩具天地]
                RF2[美食广场] 
                RF3[音乐演奏台]
                RF4[阳光草坪]
            end
        end
        
        subgraph "公共设施区域"
            subgraph "中央广场"
                P1[动物园入口]
                P2[信息发布台]
                P3[紧急医疗站]
                P4[访客观景台]
            end
            
            subgraph "基础设施"
                I1[食物补给站]
                I2[饮水设施]
                I3[清洁维护点]
                I4[安全监控点]
            end
        end
        
        %% 区域间连接通道
        W4 -.->|工作走廊| M1
        M4 -.->|管理通道| R1
        
        %% 公共设施连接
        P2 --> W2
        P2 --> M2  
        P2 --> R2
        
        I1 --> W3
        I1 --> M3
        I1 --> R3
        
        %% 动物移动路径
        W1 -->|牛马通勤路线| W2
        W2 -->|效率提升路线| W3
        M1 -->|神兽巡视路线| M2
        M2 -->|指导路线| M3
        R1 -->|宠物玩耍路线| R2
        R2 -->|休闲漫步路线| R3
        
        %% 跨区域交流路径
        W3 -.->|工作汇报| M2
        M3 -.->|关怀指导| W2
        M4 -.->|放松指引| R2
        R3 -.->|活力补充| W1
    end
    
    subgraph "动物分布密度图"
        subgraph "密度说明"
            D1[🔴 高密度区域: 15-25只动物]
            D2[🟡 中密度区域: 8-15只动物] 
            D3[🟢 低密度区域: 3-8只动物]
            D4[⚫ 设施专用区域: 仅特定功能]
        end
        
        %% 密度标记
        W2 --> D1
        M2 --> D1
        R2 --> D1
        
        W1 --> D2
        W3 --> D2
        M1 --> D2
        M3 --> D2
        R1 --> D2
        R3 --> D2
        
        W4 --> D3
        M4 --> D3  
        R4 --> D3
        
        P1 --> D4
        I1 --> D4
    end
    
    subgraph "环境要素"
        subgraph "光照系统"
            L1[☀️ 全日照区域: 工作区]
            L2[🌤️ 半日照区域: 管理区]  
            L3[🌙 柔和光照: 休闲区]
            L4[💡 人工照明: 室内设施]
        end
        
        subgraph "音效环境"
            S1[🔊 工作音效: 键盘声、讨论声]
            S2[🎵 自然音效: 水声、鸟鸣]
            S3[🎶 轻松音乐: 休闲背景乐]
            S4[🔇 静音区域: 冥想、休息]
        end
        
        subgraph "交互热点"
            H1[⭐ 高频交互: 食物站、工作台]
            H2[✨ 中频交互: 游乐设施、休息区]
            H3[💫 低频交互: 特殊场所、隐藏区域]
        end
    end
    
    %% 样式定义
    classDef workZone fill:#e3f2fd,stroke:#1976d2
    classDef divineZone fill:#fff3e0,stroke:#f57c00  
    classDef leisureZone fill:#e8f5e8,stroke:#388e3c
    classDef publicArea fill:#f3e5f5,stroke:#7b1fa2
    classDef facility fill:#fff8e1,stroke:#f9a825
    
    class W1,W2,W3,W4,WF1,WF2,WF3,WF4 workZone
    class M1,M2,M3,M4,MF1,MF2,MF3,MF4 divineZone
    class R1,R2,R3,R4,RF1,RF2,RF3,RF4 leisureZone  
    class P1,P2,P3,P4 publicArea
    class I1,I2,I3,I4 facility
```

## 5. AI服务集成流程图

```mermaid
sequenceDiagram
    participant User as 👤用户
    participant Frontend as 🌐前端界面
    participant Gateway as 🚪API网关
    participant ZooService as 🏢动物园服务
    participant AIService as 🤖AI服务
    participant BehaviorService as 🧠行为服务
    participant Scheduler as ⏰调度服务
    participant GeminiAPI as 💎Gemini AI
    participant ReplicateAPI as 🎨Replicate API
    participant Database as 💾数据库
    participant Cache as ⚡缓存系统

    %% 用户绘画生成动物流程
    rect rgb(240, 248, 255)
        Note over User, Cache: 🎨 AI动物生成流程
        User->>Frontend: 提交手绘图片
        Frontend->>Gateway: POST /api/drawing/analyze
        Gateway->>AIService: 分析图片内容
        
        AIService->>GeminiAPI: 发送图片分析请求
        GeminiAPI-->>AIService: 返回动物类型和特征
        
        AIService->>ReplicateAPI: 生成精美动物头像
        ReplicateAPI-->>AIService: 返回生成的头像URL
        
        AIService->>Database: 保存头像和分析结果
        AIService-->>Gateway: 返回生成结果
        Gateway-->>Frontend: 显示生成的动物
        Frontend-->>User: 展示个人专属动物
    end

    %% 动物园场景加载流程  
    rect rgb(248, 255, 248)
        Note over User, Cache: 🏞️ 智能场景加载流程
        User->>Frontend: 访问动物园页面
        Frontend->>Gateway: GET /api/zoo/scene
        Gateway->>ZooService: 获取动物园场景
        
        ZooService->>Cache: 检查场景缓存
        alt 缓存存在且有效
            Cache-->>ZooService: 返回缓存场景数据
        else 缓存过期或不存在
            ZooService->>Database: 查询所有动物数据
            ZooService->>BehaviorService: 生成动物行为状态
            ZooService->>Cache: 更新场景缓存
        end
        
        ZooService-->>Gateway: 返回完整场景数据
        Gateway-->>Frontend: 场景数据(动物+环境+统计)
        Frontend-->>User: 展示动态动物园场景
    end

    %% AI行为调度流程
    rect rgb(255, 248, 240)
        Note over User, Cache: 🤖 AI智能行为系统
        loop 每30秒执行一次
            Scheduler->>BehaviorService: 触发行为更新检查
            BehaviorService->>Database: 获取所有活跃动物
            
            loop 为每只动物生成行为
                BehaviorService->>BehaviorService: 评估动物当前状态
                BehaviorService->>BehaviorService: 分析环境条件(时间/天气/附近动物)
                BehaviorService->>BehaviorService: 计算行为权重和适用性
                BehaviorService->>BehaviorService: 选择最优行为模式
                BehaviorService->>Database: 更新动物状态和位置
            end
            
            BehaviorService->>Cache: 更新行为状态缓存
            BehaviorService-->>Frontend: WebSocket推送状态更新
        end
    end

    %% 用户互动处理流程
    rect rgb(255, 240, 255)  
        Note over User, Cache: 👥 智能交互处理流程
        User->>Frontend: 与动物互动(喂食/抚摸/玩耍)
        Frontend->>Gateway: POST /api/zoo/animals/{id}/interact
        Gateway->>ZooService: 处理互动请求
        
        ZooService->>Database: 验证动物存在性和用户权限
        ZooService->>ZooService: 检查互动冷却时间
        
        alt 互动被允许
            ZooService->>BehaviorService: 计算互动效果
            BehaviorService->>BehaviorService: 基于动物状态计算有效性
            BehaviorService->>BehaviorService: 生成动物反应和动画
            BehaviorService->>Database: 记录互动历史
            BehaviorService->>Database: 更新动物经验和状态
            ZooService->>ZooService: 计算用户奖励
            ZooService-->>Gateway: 返回互动成功结果
        else 互动被拒绝
            ZooService-->>Gateway: 返回冷却或权限错误
        end
        
        Gateway-->>Frontend: 互动结果和动物新状态
        Frontend-->>User: 显示互动效果和奖励
    end

    %% 全局事件触发流程
    rect rgb(240, 255, 240)
        Note over User, Cache: 🎪 全局事件系统流程  
        User->>Frontend: 触发动物园事件(快乐时光/工作集会)
        Frontend->>Gateway: POST /api/zoo/events/trigger
        Gateway->>ZooService: 处理事件触发请求
        
        ZooService->>Scheduler: 创建定时事件任务
        Scheduler->>BehaviorService: 广播事件到所有动物
        
        loop 影响所有动物
            BehaviorService->>BehaviorService: 根据事件类型调整行为权重
            BehaviorService->>BehaviorService: 生成事件专属动画和音效
            BehaviorService->>Database: 批量更新动物状态
        end
        
        BehaviorService->>Cache: 更新全局事件状态
        BehaviorService-->>Frontend: 实时推送事件效果
        Frontend-->>User: 显示全园动物响应事件
        
        Note over Scheduler: 事件持续指定时间后自动结束
        Scheduler->>BehaviorService: 事件结束，恢复正常行为模式
    end

    %% 性能优化和缓存策略
    rect rgb(248, 248, 255)
        Note over Frontend, Cache: ⚡ 性能优化策略
        
        par 并发数据获取
            Frontend->>Gateway: 获取动物数据
        and
            Frontend->>Gateway: 获取环境数据  
        and
            Frontend->>Gateway: 获取统计数据
        end
        
        Note over Cache: 多层缓存策略
        Note over Cache: - 场景数据: 5分钟缓存
        Note over Cache: - 动物状态: 30秒缓存
        Note over Cache: - AI生成结果: 永久缓存
        Note over Cache: - 用户互动: 实时更新
        
        par 异步后台任务
            Scheduler->>Database: 定期清理过期数据
        and  
            Scheduler->>AIService: 预生成热门动物头像
        and
            Scheduler->>BehaviorService: 预计算行为模式
        end
    end
```

## 系统设计特点总结

### 🏗️ 架构特点
- **微服务架构**: 前后端分离，服务模块化
- **实时响应**: WebSocket + 定时更新机制
- **智能化**: AI驱动的动物行为和用户交互
- **可扩展**: 支持新动物类型和行为模式扩展

### 🤖 AI集成特点  
- **多AI协作**: Gemini分析 + Replicate生成
- **智能行为**: 基于时间、环境、状态的行为决策
- **个性化**: 每只动物具有独特的行为模式和偏好
- **实时适应**: 根据用户互动动态调整动物状态

### 🎮 用户体验特点
- **沉浸式3D**: Canvas渲染的动态动物园场景  
- **多样互动**: 6种不同的动物交互方式
- **即时反馈**: 实时显示动物状态变化和用户奖励
- **社交元素**: 动物间互动和用户协作功能

### 📊 数据管理特点
- **多层缓存**: Redis缓存 + 前端状态管理
- **实时同步**: 状态变更的即时同步更新
- **历史追踪**: 完整的交互历史和动物成长记录  
- **性能优化**: 分页加载 + 异步处理 + 智能预取

这套设计文档为动物园展示功能提供了完整的技术架构指导，确保系统的高性能、智能化和良好的用户体验。