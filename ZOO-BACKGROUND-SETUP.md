# 动物园背景图片设置

## 背景图片要求

为了让动物园页面使用自定义背景图片，请准备以下文件：

### 文件要求
- **文件名**: `zoo.png`
- **尺寸**: 3200x2400 像素（与动物园世界尺寸匹配）
- **格式**: PNG 格式（支持透明度）
- **位置**: 放在项目根目录 `/Users/<USER>/WorkSpace/niuma/zoo.png`

### 推荐设计内容

动物园底图应该包含以下区域（对应代码中的区域配置）：

1. **主入口广场** (0,0 - 600x400)
   - 大门、售票处、指示牌

2. **草原区域** (600,0 - 1400x1000) 
   - 绿色草地、干草堆、水槽
   - 适合牛马类打工人

3. **森林区域** (1400,0 - 3200x500)
   - 树木、灌木、花园
   - 适合宠物类角色

4. **中央广场** (1200,500 - 2000x1000)
   - 表演台、座椅、餐饮区

5. **湖泊区域** (400,1000 - 1200x1600)
   - 蓝色湖水、喷泉、小桥

6. **山地区域** (2400,1200 - 3200x1200)
   - 山峰、洞穴、神秘元素
   - 适合神兽类角色

7. **功能区域**
   - 医疗中心 (0,400 - 400x700)
   - 餐饮区 (400,400 - 1000x700)
   - 停车场 (0,2000 - 800x400)
   - 管理区 (1000,2000 - 1800x400)

### 设计建议

```
动物园布局示意图：

0        800       1600      2400      3200
0  +--------+--------+--------+--------+ 0
   |  入口  | 草原1  | 森林1  | 森林2  |
400+--------+--------+--------+--------+ 400
   | 医疗   | 餐饮   |        |        |
   | 中心   | 区域   |   中央广场    |
800+--------+--------+--------+--------+ 800
   |     湖泊区域    | 游乐场 |        |
1200+--------+--------+--------+--------+ 1200
    | 草原2  |        |  山地  | 神兽  |
1600+--------+--------+ 区域  | 殿堂  | 1600
    | 沙漠区 | 绿洲   |        |       |
2000+--------+--------+--------+-------+ 2000
    | 停车场 | 管理区 | 仓储区 |       |
2400+--------+--------+--------+-------+ 2400
```

## 当前实现状态

### ✅ 已实现功能
1. **背景图片加载系统**
   - 自动加载 `zoo.png` 文件
   - 错误处理和备用方案
   - 图片缓存机制

2. **动态渲染**
   - 优先使用背景图片
   - 图片加载失败时回退到程序绘制
   - 实时重绘机制

3. **尺寸适配**
   - 背景图片自动缩放到世界尺寸
   - 保持图片比例
   - 支持不同分辨率

### 🔧 代码实现

```javascript
// 背景图片加载
function loadZooBackground() {
    if (!zooBackgroundImage) {
        zooBackgroundImage = new Image();
        zooBackgroundImage.onload = function() {
            console.log('动物园背景图片加载成功');
            renderZooScene(); // 重新渲染
        };
        zooBackgroundImage.onerror = function() {
            console.warn('背景图片加载失败，使用备用背景');
            zooBackgroundImage = null;
        };
        zooBackgroundImage.src = 'zoo.png';
    }
}

// 背景渲染
function renderZones() {
    if (zooBackgroundImage && zooBackgroundImage.complete) {
        // 使用背景图片
        zooCtx.drawImage(zooBackgroundImage, 0, 0, 
                        ZOO_CONFIG.CANVAS_WIDTH, 
                        ZOO_CONFIG.CANVAS_HEIGHT);
    } else {
        // 备用方案：程序绘制
        drawFallbackBackground();
    }
}
```

## 如何添加背景图片

### 方法1：直接放置文件
```bash
# 将准备好的背景图片复制到项目根目录
cp your-zoo-background.png /Users/<USER>/WorkSpace/niuma/zoo.png
```

### 方法2：在线生成
如果没有现成的背景图片，可以：

1. **使用 AI 生成**
   ```
   提示词：
   "俯视视角的卡通动物园地图，包含草地、森林、湖泊、山地等区域，
   3200x2400分辨率，适合手机游戏的风格"
   ```

2. **使用绘图软件**
   - Photoshop、GIMP、或在线工具
   - 创建 3200x2400 像素的画布
   - 绘制各个功能区域

3. **下载免费素材**
   - 搜索 "zoo map top view"
   - 确保尺寸和授权符合要求

### 方法3：程序生成（临时方案）
如果暂时没有背景图片，系统会自动使用程序绘制的区域作为备用。

## 测试方法

1. **启动项目**
   ```bash
   cd /Users/<USER>/WorkSpace/niuma
   python3 -m http.server 8000
   ```

2. **打开浏览器**
   - 访问 http://localhost:8000
   - 完成绘画和分析流程
   - 点击"进入动物园"

3. **检查背景**
   - 打开浏览器开发者工具
   - 查看控制台日志
   - 确认背景图片是否正确加载

### 预期结果
- ✅ 成功：显示 "动物园背景图片加载成功"
- ❌ 失败：显示 "背景图片加载失败，使用备用背景"

## 优化建议

1. **文件大小优化**
   - 使用图片压缩工具减小文件大小
   - 推荐使用 WebP 格式（需要修改代码）

2. **加载性能**
   - 考虑使用渐进式加载
   - 提供低分辨率预览图

3. **多分辨率支持**
   - 准备多个尺寸的背景图片
   - 根据设备性能选择合适版本

## 故障排除

### 常见问题

1. **图片不显示**
   - 检查文件路径是否正确
   - 确认文件名为 `zoo.png`
   - 查看浏览器控制台错误信息

2. **图片显示不完整**
   - 确认图片尺寸为 3200x2400
   - 检查图片是否损坏

3. **加载速度慢**
   - 压缩图片文件大小
   - 使用 CDN 或本地缓存

### 调试命令
```javascript
// 在浏览器控制台执行
console.log('背景图片状态:', zooBackgroundImage);
console.log('图片尺寸:', zooBackgroundImage?.width, 'x', zooBackgroundImage?.height);
console.log('加载状态:', zooBackgroundImage?.complete);
```

## 下一步

一旦背景图片设置完成，打工人角色将在漂亮的动物园环境中自由行走，大大提升用户体验！