#!/usr/bin/env node

/**
 * 牛马动物园 - 性能测试脚本
 * 测试前端加载时间、API响应速度、内存使用等性能指标
 */

const fetch = globalThis.fetch || require('node-fetch');

const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:3001';

console.log('⚡ 牛马动物园 - 性能测试');
console.log('=' .repeat(50));

/**
 * 测试API响应时间
 */
async function testAPIPerformance() {
  console.log('📊 API性能测试');
  console.log('-' .repeat(30));
  
  const endpoints = [
    { name: '健康检查', url: `${BACKEND_URL}/api/v1/health`, method: 'GET' },
    { name: '用户登录', url: `${BACKEND_URL}/api/auth/login`, method: 'POST', body: { email: '<EMAIL>', password: 'test' }},
    { name: '动物园数据', url: `${BACKEND_URL}/api/v1/zoo/animals`, method: 'GET', headers: { 'Authorization': 'Bearer test-token' }},
    { 
      name: '绘画分析', 
      url: `${BACKEND_URL}/api/v1/drawing/analyze`, 
      method: 'POST', 
      headers: { 'Authorization': 'Bearer test-token' },
      body: { imageData: 'data:image/png;base64,test', timestamp: new Date().toISOString() }
    }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const times = [];
    const errors = [];
    
    // 执行5次测试取平均值
    for (let i = 0; i < 5; i++) {
      try {
        const startTime = performance.now();
        
        const response = await fetch(endpoint.url, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json',
            ...(endpoint.headers || {})
          },
          body: endpoint.body ? JSON.stringify(endpoint.body) : undefined
        });
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        if (response.ok) {
          times.push(duration);
        } else {
          errors.push(`HTTP ${response.status}`);
        }
        
      } catch (error) {
        errors.push(error.message);
      }
    }
    
    const result = {
      name: endpoint.name,
      avgTime: times.length > 0 ? Math.round(times.reduce((a, b) => a + b, 0) / times.length) : null,
      minTime: times.length > 0 ? Math.round(Math.min(...times)) : null,
      maxTime: times.length > 0 ? Math.round(Math.max(...times)) : null,
      successRate: Math.round((times.length / 5) * 100),
      errors: [...new Set(errors)]
    };
    
    results.push(result);
    
    const status = result.successRate === 100 ? '✅' : result.successRate > 0 ? '⚠️ ' : '❌';
    console.log(`${status} ${result.name}:`);
    
    if (result.avgTime !== null) {
      console.log(`   平均响应时间: ${result.avgTime}ms`);
      console.log(`   响应时间范围: ${result.minTime}ms - ${result.maxTime}ms`);
    }
    
    console.log(`   成功率: ${result.successRate}%`);
    
    if (result.errors.length > 0) {
      console.log(`   错误: ${result.errors.join(', ')}`);
    }
    
    console.log('');
  }
  
  return results;
}

/**
 * 测试负载能力
 */
async function testLoadCapacity() {
  console.log('🚀 负载能力测试');
  console.log('-' .repeat(30));
  
  const testUrl = `${BACKEND_URL}/api/v1/health`;
  const concurrentRequests = [1, 5, 10, 20];
  const results = [];
  
  for (const concurrent of concurrentRequests) {
    console.log(`测试并发数: ${concurrent}`);
    
    const startTime = performance.now();
    const promises = [];
    
    for (let i = 0; i < concurrent; i++) {
      promises.push(
        fetch(testUrl).then(response => ({
          success: response.ok,
          status: response.status,
          time: performance.now()
        })).catch(error => ({
          success: false,
          error: error.message,
          time: performance.now()
        }))
      );
    }
    
    try {
      const responses = await Promise.all(promises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      const successCount = responses.filter(r => r.success).length;
      const successRate = Math.round((successCount / concurrent) * 100);
      const avgTime = Math.round(totalTime / concurrent);
      
      const result = {
        concurrent,
        totalTime: Math.round(totalTime),
        avgTime,
        successRate,
        throughput: Math.round((concurrent / totalTime) * 1000) // requests per second
      };
      
      results.push(result);
      
      const status = successRate === 100 ? '✅' : successRate > 80 ? '⚠️ ' : '❌';
      console.log(`${status} 总时间: ${result.totalTime}ms, 平均: ${result.avgTime}ms`);
      console.log(`   成功率: ${result.successRate}%, 吞吐量: ${result.throughput} req/s\n`);
      
      // 避免过快请求
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.log(`❌ 并发测试失败: ${error.message}\n`);
    }
  }
  
  return results;
}

/**
 * 测试前端资源加载
 */
async function testFrontendPerformance() {
  console.log('🌐 前端性能测试');
  console.log('-' .repeat(30));
  
  const resources = [
    { name: 'HTML首页', url: FRONTEND_URL },
    { name: 'Vite开发服务器', url: `${FRONTEND_URL}/@vite/client` }
  ];
  
  const results = [];
  
  for (const resource of resources) {
    try {
      const startTime = performance.now();
      
      const response = await fetch(resource.url, {
        method: 'HEAD' // 只获取头部信息，不下载内容
      });
      
      const endTime = performance.now();
      const loadTime = Math.round(endTime - startTime);
      
      const result = {
        name: resource.name,
        loadTime,
        status: response.status,
        success: response.ok,
        contentLength: response.headers.get('content-length'),
        contentType: response.headers.get('content-type')
      };
      
      results.push(result);
      
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.name}:`);
      console.log(`   加载时间: ${result.loadTime}ms`);
      console.log(`   状态码: ${result.status}`);
      
      if (result.contentLength) {
        const size = Math.round(parseInt(result.contentLength) / 1024);
        console.log(`   文件大小: ${size}KB`);
      }
      
      console.log(`   内容类型: ${result.contentType || 'unknown'}\n`);
      
    } catch (error) {
      console.log(`❌ ${resource.name}: ${error.message}\n`);
      results.push({
        name: resource.name,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

/**
 * 内存和性能监控
 */
function getSystemInfo() {
  console.log('💻 系统信息');
  console.log('-' .repeat(30));
  
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  console.log(`Node.js版本: ${process.version}`);
  console.log(`平台: ${process.platform} ${process.arch}`);
  console.log(`内存使用:`);
  console.log(`   RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
  console.log(`   堆使用: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  console.log(`   堆总量: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
  console.log(`   外部: ${Math.round(memUsage.external / 1024 / 1024)}MB`);
  console.log(`CPU使用: ${cpuUsage.user}μs (用户), ${cpuUsage.system}μs (系统)\n`);
  
  return {
    node: process.version,
    platform: `${process.platform} ${process.arch}`,
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    },
    cpu: cpuUsage
  };
}

/**
 * 生成性能报告
 */
function generateReport(apiResults, loadResults, frontendResults, systemInfo) {
  console.log('📋 性能测试报告');
  console.log('=' .repeat(50));
  
  // API性能总结
  const avgApiTime = Math.round(
    apiResults
      .filter(r => r.avgTime !== null)
      .reduce((sum, r) => sum + r.avgTime, 0) / 
    apiResults.filter(r => r.avgTime !== null).length
  );
  
  const avgSuccessRate = Math.round(
    apiResults.reduce((sum, r) => sum + r.successRate, 0) / apiResults.length
  );
  
  console.log(`🎯 API性能评估:`);
  console.log(`   平均响应时间: ${avgApiTime}ms`);
  console.log(`   平均成功率: ${avgSuccessRate}%`);
  console.log(`   性能评级: ${getPerformanceGrade(avgApiTime, avgSuccessRate)}`);
  
  // 负载能力评估
  const maxConcurrent = Math.max(...loadResults.map(r => r.concurrent));
  const bestThroughput = Math.max(...loadResults.map(r => r.throughput));
  
  console.log(`\n🚀 负载能力评估:`);
  console.log(`   最大并发支持: ${maxConcurrent}`);
  console.log(`   最佳吞吐量: ${bestThroughput} req/s`);
  console.log(`   负载评级: ${getLoadGrade(bestThroughput)}`);
  
  // 前端性能评估  
  const frontendAvgTime = Math.round(
    frontendResults
      .filter(r => r.loadTime)
      .reduce((sum, r) => sum + r.loadTime, 0) / 
    frontendResults.filter(r => r.loadTime).length
  );
  
  console.log(`\n🌐 前端性能评估:`);
  console.log(`   平均加载时间: ${frontendAvgTime}ms`);
  console.log(`   加载评级: ${getLoadTimeGrade(frontendAvgTime)}`);
  
  // 系统资源评估
  console.log(`\n💻 资源使用评估:`);
  console.log(`   内存使用: ${systemInfo.memory.heapUsed}MB`);
  console.log(`   资源评级: ${getResourceGrade(systemInfo.memory.heapUsed)}`);
  
  // 总体评估
  const overallScore = calculateOverallScore(avgApiTime, avgSuccessRate, bestThroughput, frontendAvgTime);
  console.log(`\n⭐ 总体性能评分:`);
  console.log(`   综合评分: ${overallScore}/100`);
  console.log(`   总体评级: ${getOverallGrade(overallScore)}`);
  
  // 性能建议
  console.log(`\n💡 性能优化建议:`);
  generateOptimizationTips(avgApiTime, avgSuccessRate, bestThroughput, frontendAvgTime);
  
  return {
    api: { avgTime: avgApiTime, successRate: avgSuccessRate },
    load: { maxConcurrent, bestThroughput },
    frontend: { avgLoadTime: frontendAvgTime },
    system: systemInfo,
    score: overallScore
  };
}

function getPerformanceGrade(avgTime, successRate) {
  if (successRate < 80) return '❌ 差 (成功率过低)';
  if (avgTime < 100) return '🟢 优秀';
  if (avgTime < 300) return '🟡 良好';
  if (avgTime < 1000) return '🟠 一般';
  return '🔴 差';
}

function getLoadGrade(throughput) {
  if (throughput > 1000) return '🟢 优秀';
  if (throughput > 500) return '🟡 良好';
  if (throughput > 100) return '🟠 一般';
  return '🔴 差';
}

function getLoadTimeGrade(loadTime) {
  if (loadTime < 100) return '🟢 优秀';
  if (loadTime < 300) return '🟡 良好';
  if (loadTime < 1000) return '🟠 一般';
  return '🔴 差';
}

function getResourceGrade(memoryMB) {
  if (memoryMB < 50) return '🟢 优秀';
  if (memoryMB < 100) return '🟡 良好';
  if (memoryMB < 200) return '🟠 一般';
  return '🔴 差';
}

function calculateOverallScore(avgTime, successRate, throughput, loadTime) {
  let score = 0;
  
  // API响应时间评分 (30分)
  if (avgTime < 100) score += 30;
  else if (avgTime < 300) score += 25;
  else if (avgTime < 1000) score += 15;
  else score += 5;
  
  // 成功率评分 (25分)
  score += Math.round((successRate / 100) * 25);
  
  // 吞吐量评分 (25分)
  if (throughput > 1000) score += 25;
  else if (throughput > 500) score += 20;
  else if (throughput > 100) score += 15;
  else score += 5;
  
  // 前端加载时间评分 (20分)
  if (loadTime < 100) score += 20;
  else if (loadTime < 300) score += 15;
  else if (loadTime < 1000) score += 10;
  else score += 5;
  
  return Math.min(100, score);
}

function getOverallGrade(score) {
  if (score >= 90) return '🟢 优秀 (A+)';
  if (score >= 80) return '🟡 良好 (A)';
  if (score >= 70) return '🟠 一般 (B)';
  if (score >= 60) return '🟤 较差 (C)';
  return '🔴 差 (D)';
}

function generateOptimizationTips(avgTime, successRate, throughput, loadTime) {
  const tips = [];
  
  if (avgTime > 500) {
    tips.push('   • API响应时间较长，考虑优化数据库查询和缓存机制');
  }
  
  if (successRate < 95) {
    tips.push('   • API成功率需要提升，检查错误处理和服务稳定性');
  }
  
  if (throughput < 500) {
    tips.push('   • 服务吞吐量较低，考虑增加服务器资源或优化并发处理');
  }
  
  if (loadTime > 300) {
    tips.push('   • 前端加载时间较长，考虑代码分割、资源压缩和CDN优化');
  }
  
  if (tips.length === 0) {
    tips.push('   • 性能表现良好，继续保持当前的优化策略');
  }
  
  tips.forEach(tip => console.log(tip));
}

/**
 * 主测试函数
 */
async function runPerformanceTests() {
  const startTime = performance.now();
  
  console.log(`开始时间: ${new Date().toLocaleString()}\n`);
  
  try {
    // 获取系统信息
    const systemInfo = getSystemInfo();
    
    // API性能测试
    const apiResults = await testAPIPerformance();
    
    // 负载测试
    const loadResults = await testLoadCapacity();
    
    // 前端性能测试
    const frontendResults = await testFrontendPerformance();
    
    // 生成报告
    const report = generateReport(apiResults, loadResults, frontendResults, systemInfo);
    
    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);
    
    console.log(`\n⏱️  测试总耗时: ${totalTime}ms`);
    console.log(`结束时间: ${new Date().toLocaleString()}`);
    
    return report;
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message);
    return null;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

module.exports = { runPerformanceTests, testAPIPerformance, testLoadCapacity, testFrontendPerformance };