/**
 * 优化融合生成器
 * 重绘原图像并融合动物特征，保持精髓的同时更加流畅
 */

const Canvas = require('canvas');
const { createCanvas, loadImage } = Canvas;

/**
 * 主函数：生成优化的人身动物融合图像
 */
async function generateOptimizedFusion(userDrawingBase64, animalType, analysisResult = {}) {
    console.log('🎨 开始优化融合生成...');
    console.log('动物类型:', animalType);
    console.log('Base64数据长度:', userDrawingBase64 ? userDrawingBase64.length : 'null');
    
    // 创建画布
    const canvas = createCanvas(1024, 1024);
    const ctx = canvas.getContext('2d');
    
    // 设置背景
    const gradient = ctx.createLinearGradient(0, 0, 0, 1024);
    gradient.addColorStop(0, '#f0f4f8');
    gradient.addColorStop(1, '#e2e8f0');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1024, 1024);
    
    try {
        // 1. 加载并分析用户原画像
        console.log('步骤1: 分析用户画像...');
        const userImage = await loadUserDrawing(userDrawingBase64);
        const bodyStructure = analyzeBodyStructure(userImage);
        
        // 2. 重绘优化的身体轮廓
        console.log('步骤2: 重绘优化身体...');
        const bodyInfo = await drawOptimizedBody(ctx, bodyStructure, animalType);
        
        // 3. 融合动物头部特征
        console.log('步骤3: 融合动物头部...');
        await drawFusedAnimalHead(ctx, animalType, bodyInfo, analysisResult);
        
        // 4. 添加细节和装饰
        console.log('步骤4: 添加细节装饰...');
        addDetailsAndAccessories(ctx, animalType, bodyInfo);
        
        // 5. 应用艺术风格
        console.log('步骤5: 应用艺术风格...');
        applyArtStyle(ctx, analysisResult.style || 'cartoon');
        
        // 生成结果
        const resultBase64 = canvas.toDataURL('image/png');
        console.log('📷 生成图像格式:', resultBase64.substring(0, 50) + '...');
        console.log('📷 图像数据长度:', resultBase64.length);
        
        return {
            success: true,
            imageUrl: resultBase64,
            fusionDetails: {
                method: 'Optimized Fusion',
                animalType,
                style: 'Smooth Hybrid',
                features: {
                    bodyRedraw: true,
                    headFusion: true,
                    smoothTransition: true
                }
            }
        };
        
    } catch (error) {
        console.error('优化融合失败:', error.message);
        return generateFallback(canvas, ctx, animalType);
    }
}

/**
 * 加载用户绘画
 */
async function loadUserDrawing(base64Data) {
    try {
        if (!base64Data || typeof base64Data !== 'string') {
            throw new Error('Invalid base64 data');
        }
        
        const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
        const buffer = Buffer.from(imageData, 'base64');
        const image = await Canvas.loadImage(buffer);
        
        return image;
    } catch (error) {
        // 创建默认图像
        const canvas = createCanvas(300, 400);
        const ctx = canvas.getContext('2d');
        drawDefaultFigure(ctx);
        return canvas;
    }
}

/**
 * 分析身体结构
 */
function analyzeBodyStructure(image) {
    // 分析原图像的身体结构特征
    return {
        width: image.width,
        height: image.height,
        proportions: {
            headRatio: 0.15,
            shoulderRatio: 0.25,
            waistRatio: 0.5,
            legRatio: 0.7
        },
        pose: detectPose(image),
        style: detectDrawingStyle(image)
    };
}

/**
 * 检测姿势
 */
function detectPose(image) {
    // 简化的姿势检测
    return {
        type: 'standing',
        direction: 'front',
        armPosition: 'sides'
    };
}

/**
 * 检测绘画风格
 */
function detectDrawingStyle(image) {
    // 简化的风格检测
    return {
        lineWeight: 'medium',
        detail: 'moderate',
        shading: 'light'
    };
}

/**
 * 绘制优化的身体
 */
async function drawOptimizedBody(ctx, structure, animalType) {
    const centerX = 512;
    const centerY = 512;
    const scale = Math.min(600 / structure.width, 700 / structure.height);
    
    const bodyWidth = 180 * scale;
    const bodyHeight = 400 * scale;
    
    ctx.save();
    
    // 设置绘画风格
    ctx.strokeStyle = '#2d3748';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 绘制身体轮廓（流畅曲线）
    ctx.beginPath();
    
    // 肩部
    const shoulderY = centerY - bodyHeight * 0.3;
    const shoulderWidth = bodyWidth * 0.8;
    
    ctx.moveTo(centerX - shoulderWidth/2, shoulderY);
    ctx.bezierCurveTo(
        centerX - shoulderWidth/2, shoulderY - 20,
        centerX + shoulderWidth/2, shoulderY - 20,
        centerX + shoulderWidth/2, shoulderY
    );
    
    // 躯干（自然曲线）
    const waistY = centerY;
    const waistWidth = bodyWidth * 0.6;
    
    ctx.bezierCurveTo(
        centerX + shoulderWidth/2, shoulderY + 40,
        centerX + waistWidth/2, waistY - 20,
        centerX + waistWidth/2, waistY
    );
    
    // 臀部和腿部连接
    const hipY = centerY + bodyHeight * 0.1;
    const hipWidth = bodyWidth * 0.7;
    
    ctx.bezierCurveTo(
        centerX + waistWidth/2, waistY + 20,
        centerX + hipWidth/2, hipY - 10,
        centerX + hipWidth/2, hipY
    );
    
    // 腿部轮廓
    const legEndY = centerY + bodyHeight * 0.4;
    
    // 右腿
    ctx.lineTo(centerX + bodyWidth * 0.2, legEndY);
    ctx.lineTo(centerX + bodyWidth * 0.15, legEndY + 20);
    
    // 裆部曲线
    ctx.bezierCurveTo(
        centerX + bodyWidth * 0.1, legEndY,
        centerX - bodyWidth * 0.1, legEndY,
        centerX - bodyWidth * 0.15, legEndY + 20
    );
    
    // 左腿
    ctx.lineTo(centerX - bodyWidth * 0.2, legEndY);
    ctx.lineTo(centerX - hipWidth/2, hipY);
    
    // 回到腰部
    ctx.bezierCurveTo(
        centerX - hipWidth/2, hipY - 10,
        centerX - waistWidth/2, waistY + 20,
        centerX - waistWidth/2, waistY
    );
    
    // 回到肩部
    ctx.bezierCurveTo(
        centerX - waistWidth/2, waistY - 20,
        centerX - shoulderWidth/2, shoulderY + 40,
        centerX - shoulderWidth/2, shoulderY
    );
    
    ctx.stroke();
    
    // 填充身体
    ctx.fillStyle = getBodyColor(animalType);
    ctx.fill();
    
    // 绘制手臂
    drawArms(ctx, centerX, shoulderY, shoulderWidth, bodyHeight);
    
    // 添加衣物细节
    drawClothing(ctx, centerX, centerY, bodyWidth, bodyHeight, animalType);
    
    ctx.restore();
    
    return {
        centerX,
        centerY,
        shoulderY,
        shoulderWidth,
        bodyWidth,
        bodyHeight,
        neckY: shoulderY - 30
    };
}

/**
 * 获取身体颜色
 */
function getBodyColor(animalType) {
    const colors = {
        'OXHORSE': '#f4e4d4',
        'PET': '#ffeedd',
        'DIVINE': '#f0e6ff'
    };
    return colors[animalType] || '#f5f5f5';
}

/**
 * 绘制手臂
 */
function drawArms(ctx, centerX, shoulderY, shoulderWidth, bodyHeight) {
    ctx.strokeStyle = '#2d3748';
    ctx.lineWidth = 3;
    
    // 左臂
    ctx.beginPath();
    ctx.moveTo(centerX - shoulderWidth/2, shoulderY + 10);
    ctx.bezierCurveTo(
        centerX - shoulderWidth/2 - 30, shoulderY + 60,
        centerX - shoulderWidth/2 - 40, shoulderY + 120,
        centerX - shoulderWidth/2 - 20, shoulderY + 180
    );
    ctx.stroke();
    
    // 右臂
    ctx.beginPath();
    ctx.moveTo(centerX + shoulderWidth/2, shoulderY + 10);
    ctx.bezierCurveTo(
        centerX + shoulderWidth/2 + 30, shoulderY + 60,
        centerX + shoulderWidth/2 + 40, shoulderY + 120,
        centerX + shoulderWidth/2 + 20, shoulderY + 180
    );
    ctx.stroke();
    
    // 手
    ctx.fillStyle = '#fdbcb4';
    ctx.beginPath();
    ctx.arc(centerX - shoulderWidth/2 - 20, shoulderY + 180, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    ctx.beginPath();
    ctx.arc(centerX + shoulderWidth/2 + 20, shoulderY + 180, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
}

/**
 * 绘制衣物
 */
function drawClothing(ctx, centerX, centerY, bodyWidth, bodyHeight, animalType) {
    ctx.save();
    
    if (animalType === 'OXHORSE') {
        // 工装
        ctx.fillStyle = '#4a5568';
        ctx.fillRect(centerX - bodyWidth * 0.35, centerY - bodyHeight * 0.2, bodyWidth * 0.7, bodyHeight * 0.35);
        
        // 口袋
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 2;
        ctx.strokeRect(centerX - bodyWidth * 0.25, centerY - bodyHeight * 0.1, bodyWidth * 0.15, bodyHeight * 0.08);
        ctx.strokeRect(centerX + bodyWidth * 0.1, centerY - bodyHeight * 0.1, bodyWidth * 0.15, bodyHeight * 0.08);
        
    } else if (animalType === 'PET') {
        // 可爱T恤
        ctx.fillStyle = '#ffc0cb';
        ctx.beginPath();
        ctx.moveTo(centerX - bodyWidth * 0.35, centerY - bodyHeight * 0.25);
        ctx.lineTo(centerX + bodyWidth * 0.35, centerY - bodyHeight * 0.25);
        ctx.lineTo(centerX + bodyWidth * 0.3, centerY);
        ctx.lineTo(centerX - bodyWidth * 0.3, centerY);
        ctx.closePath();
        ctx.fill();
        
        // 图案
        ctx.fillStyle = '#ff69b4';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('♥', centerX, centerY - bodyHeight * 0.1);
        
    } else if (animalType === 'DIVINE') {
        // 长袍
        const gradient = ctx.createLinearGradient(centerX - bodyWidth/2, 0, centerX + bodyWidth/2, 0);
        gradient.addColorStop(0, '#9370db');
        gradient.addColorStop(0.5, '#8a2be2');
        gradient.addColorStop(1, '#9370db');
        ctx.fillStyle = gradient;
        
        ctx.beginPath();
        ctx.moveTo(centerX - bodyWidth * 0.4, centerY - bodyHeight * 0.25);
        ctx.lineTo(centerX + bodyWidth * 0.4, centerY - bodyHeight * 0.25);
        ctx.lineTo(centerX + bodyWidth * 0.45, centerY + bodyHeight * 0.3);
        ctx.lineTo(centerX - bodyWidth * 0.45, centerY + bodyHeight * 0.3);
        ctx.closePath();
        ctx.fill();
        
        // 金边
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        ctx.stroke();
    }
    
    ctx.restore();
}

/**
 * 绘制融合的动物头部
 */
async function drawFusedAnimalHead(ctx, animalType, bodyInfo, analysisResult) {
    const { centerX, neckY, shoulderWidth } = bodyInfo;
    const headSize = shoulderWidth * 0.7;
    const headY = neckY - headSize * 0.3;
    
    ctx.save();
    
    // 绘制脖子连接
    ctx.strokeStyle = '#2d3748';
    ctx.lineWidth = 3;
    ctx.fillStyle = getBodyColor(animalType);
    
    ctx.beginPath();
    ctx.moveTo(centerX - shoulderWidth * 0.2, neckY);
    ctx.bezierCurveTo(
        centerX - shoulderWidth * 0.15, neckY - 10,
        centerX - headSize * 0.3, headY + headSize * 0.8,
        centerX - headSize * 0.3, headY + headSize * 0.6
    );
    ctx.lineTo(centerX + headSize * 0.3, headY + headSize * 0.6);
    ctx.bezierCurveTo(
        centerX + headSize * 0.3, headY + headSize * 0.8,
        centerX + shoulderWidth * 0.15, neckY - 10,
        centerX + shoulderWidth * 0.2, neckY
    );
    ctx.fill();
    ctx.stroke();
    
    // 根据动物类型绘制头部
    switch(animalType) {
        case 'OXHORSE':
            drawOxHorseHead(ctx, centerX, headY, headSize);
            break;
        case 'PET':
            drawPetHead(ctx, centerX, headY, headSize);
            break;
        case 'DIVINE':
            drawDivineHead(ctx, centerX, headY, headSize);
            break;
        default:
            drawOxHorseHead(ctx, centerX, headY, headSize);
    }
    
    ctx.restore();
}

/**
 * 绘制牛马头部
 */
function drawOxHorseHead(ctx, x, y, size) {
    // 头部基础形状（马脸）
    ctx.fillStyle = '#d2691e';
    ctx.strokeStyle = '#8b4513';
    ctx.lineWidth = 3;
    
    ctx.beginPath();
    ctx.ellipse(x, y + size * 0.4, size * 0.5, size * 0.6, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 鼻部（延长）
    ctx.beginPath();
    ctx.ellipse(x, y + size * 0.7, size * 0.35, size * 0.4, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 耳朵
    ctx.fillStyle = '#a0522d';
    
    // 左耳
    ctx.beginPath();
    ctx.moveTo(x - size * 0.3, y + size * 0.2);
    ctx.lineTo(x - size * 0.4, y - size * 0.1);
    ctx.lineTo(x - size * 0.2, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 右耳
    ctx.beginPath();
    ctx.moveTo(x + size * 0.3, y + size * 0.2);
    ctx.lineTo(x + size * 0.4, y - size * 0.1);
    ctx.lineTo(x + size * 0.2, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 眼睛
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.2, y + size * 0.3, size * 0.08, size * 0.1, 0, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.ellipse(x + size * 0.2, y + size * 0.3, size * 0.08, size * 0.1, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻孔
    ctx.fillStyle = '#654321';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.08, y + size * 0.85, size * 0.05, size * 0.08, 0, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.ellipse(x + size * 0.08, y + size * 0.85, size * 0.05, size * 0.08, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 鬃毛
    ctx.strokeStyle = '#654321';
    ctx.lineWidth = 2;
    for (let i = 0; i < 5; i++) {
        ctx.beginPath();
        ctx.moveTo(x - size * 0.1 + i * size * 0.05, y);
        ctx.lineTo(x - size * 0.1 + i * size * 0.05, y - size * 0.2);
        ctx.stroke();
    }
}

/**
 * 绘制宠物头部
 */
function drawPetHead(ctx, x, y, size) {
    // 猫头（圆润）
    ctx.fillStyle = '#ffa500';
    ctx.strokeStyle = '#ff8c00';
    ctx.lineWidth = 3;
    
    ctx.beginPath();
    ctx.arc(x, y + size * 0.4, size * 0.5, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 猫耳
    ctx.fillStyle = '#ff8c00';
    
    // 左耳
    ctx.beginPath();
    ctx.moveTo(x - size * 0.35, y + size * 0.1);
    ctx.lineTo(x - size * 0.45, y - size * 0.2);
    ctx.lineTo(x - size * 0.2, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 右耳
    ctx.beginPath();
    ctx.moveTo(x + size * 0.35, y + size * 0.1);
    ctx.lineTo(x + size * 0.45, y - size * 0.2);
    ctx.lineTo(x + size * 0.2, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 耳朵内部
    ctx.fillStyle = '#ffb6c1';
    ctx.beginPath();
    ctx.moveTo(x - size * 0.3, y);
    ctx.lineTo(x - size * 0.35, y - size * 0.1);
    ctx.lineTo(x - size * 0.25, y - size * 0.05);
    ctx.closePath();
    ctx.fill();
    
    ctx.beginPath();
    ctx.moveTo(x + size * 0.3, y);
    ctx.lineTo(x + size * 0.35, y - size * 0.1);
    ctx.lineTo(x + size * 0.25, y - size * 0.05);
    ctx.closePath();
    ctx.fill();
    
    // 大眼睛
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.35, size * 0.1, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(x + size * 0.15, y + size * 0.35, size * 0.1, 0, Math.PI * 2);
    ctx.fill();
    
    // 眼睛高光
    ctx.fillStyle = '#fff';
    ctx.beginPath();
    ctx.arc(x - size * 0.13, y + size * 0.33, size * 0.03, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(x + size * 0.17, y + size * 0.33, size * 0.03, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻子
    ctx.fillStyle = '#ff1493';
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.5);
    ctx.lineTo(x - size * 0.05, y + size * 0.55);
    ctx.lineTo(x + size * 0.05, y + size * 0.55);
    ctx.closePath();
    ctx.fill();
    
    // 嘴巴
    ctx.strokeStyle = '#ff1493';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.55);
    ctx.quadraticCurveTo(x - size * 0.1, y + size * 0.6, x - size * 0.15, y + size * 0.55);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.55);
    ctx.quadraticCurveTo(x + size * 0.1, y + size * 0.6, x + size * 0.15, y + size * 0.55);
    ctx.stroke();
    
    // 胡须
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1.5;
    
    for (let i = 0; i < 3; i++) {
        // 左侧
        ctx.beginPath();
        ctx.moveTo(x - size * 0.25, y + size * 0.4 + i * size * 0.05);
        ctx.lineTo(x - size * 0.5, y + size * 0.4 + i * size * 0.03);
        ctx.stroke();
        
        // 右侧
        ctx.beginPath();
        ctx.moveTo(x + size * 0.25, y + size * 0.4 + i * size * 0.05);
        ctx.lineTo(x + size * 0.5, y + size * 0.4 + i * size * 0.03);
        ctx.stroke();
    }
}

/**
 * 绘制神兽头部
 */
function drawDivineHead(ctx, x, y, size) {
    // 龙头基础
    ctx.fillStyle = '#4169e1';
    ctx.strokeStyle = '#191970';
    ctx.lineWidth = 3;
    
    // 头部主体（威严的形状）
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.bezierCurveTo(
        x - size * 0.5, y,
        x - size * 0.5, y + size * 0.8,
        x, y + size * 0.9
    );
    ctx.bezierCurveTo(
        x + size * 0.5, y + size * 0.8,
        x + size * 0.5, y,
        x, y
    );
    ctx.fill();
    ctx.stroke();
    
    // 龙角
    ctx.strokeStyle = '#ffd700';
    ctx.lineWidth = 4;
    
    // 左角
    ctx.beginPath();
    ctx.moveTo(x - size * 0.2, y + size * 0.1);
    ctx.quadraticCurveTo(x - size * 0.3, y - size * 0.2, x - size * 0.25, y - size * 0.3);
    ctx.stroke();
    
    // 右角
    ctx.beginPath();
    ctx.moveTo(x + size * 0.2, y + size * 0.1);
    ctx.quadraticCurveTo(x + size * 0.3, y - size * 0.2, x + size * 0.25, y - size * 0.3);
    ctx.stroke();
    
    // 龙须
    ctx.strokeStyle = '#4169e1';
    ctx.lineWidth = 2;
    
    // 左须
    ctx.beginPath();
    ctx.moveTo(x - size * 0.3, y + size * 0.6);
    ctx.quadraticCurveTo(x - size * 0.5, y + size * 0.65, x - size * 0.6, y + size * 0.6);
    ctx.stroke();
    
    // 右须
    ctx.beginPath();
    ctx.moveTo(x + size * 0.3, y + size * 0.6);
    ctx.quadraticCurveTo(x + size * 0.5, y + size * 0.65, x + size * 0.6, y + size * 0.6);
    ctx.stroke();
    
    // 眼睛（威严）
    ctx.fillStyle = '#ff0000';
    ctx.shadowColor = '#ff0000';
    ctx.shadowBlur = 10;
    
    ctx.beginPath();
    ctx.ellipse(x - size * 0.2, y + size * 0.35, size * 0.08, size * 0.12, -Math.PI/8, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.ellipse(x + size * 0.2, y + size * 0.35, size * 0.08, size * 0.12, Math.PI/8, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.shadowBlur = 0;
    
    // 鼻子
    ctx.fillStyle = '#191970';
    ctx.beginPath();
    ctx.ellipse(x, y + size * 0.7, size * 0.15, size * 0.1, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 鳞片纹理
    ctx.strokeStyle = '#6495ed';
    ctx.lineWidth = 1;
    ctx.setLineDash([3, 3]);
    
    for (let i = 0; i < 4; i++) {
        ctx.beginPath();
        ctx.arc(x - size * 0.2 + i * size * 0.1, y + size * 0.2, size * 0.05, 0, Math.PI);
        ctx.stroke();
    }
    
    ctx.setLineDash([]);
}

/**
 * 添加细节和装饰
 */
function addDetailsAndAccessories(ctx, animalType, bodyInfo) {
    const { centerX, centerY, bodyWidth, bodyHeight } = bodyInfo;
    
    // 工牌
    drawBadge(ctx, centerX + bodyWidth * 0.3, centerY - bodyHeight * 0.15, animalType);
    
    // 根据类型添加特殊装饰
    if (animalType === 'OXHORSE') {
        // 工具腰带
        ctx.strokeStyle = '#8b4513';
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(centerX - bodyWidth * 0.35, centerY);
        ctx.lineTo(centerX + bodyWidth * 0.35, centerY);
        ctx.stroke();
        
        // 工具
        ctx.fillStyle = '#696969';
        ctx.fillRect(centerX - bodyWidth * 0.25, centerY - 5, 20, 30);
        ctx.fillRect(centerX + bodyWidth * 0.15, centerY - 5, 20, 30);
        
    } else if (animalType === 'PET') {
        // 铃铛项圈
        ctx.strokeStyle = '#ff1493';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY - bodyHeight * 0.35, bodyWidth * 0.25, bodyHeight * 0.03, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 铃铛
        ctx.fillStyle = '#ffd700';
        ctx.beginPath();
        ctx.arc(centerX, centerY - bodyHeight * 0.32, 10, 0, Math.PI * 2);
        ctx.fill();
        
    } else if (animalType === 'DIVINE') {
        // 光环
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        ctx.shadowColor = '#ffd700';
        ctx.shadowBlur = 15;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY - bodyHeight * 0.5, bodyWidth * 0.3, bodyHeight * 0.05, 0, 0, Math.PI * 2);
        ctx.stroke();
        ctx.shadowBlur = 0;
    }
}

/**
 * 绘制工牌
 */
function drawBadge(ctx, x, y, animalType) {
    // 工牌背景
    ctx.fillStyle = '#fff';
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.fillRect(x, y, 60, 80);
    ctx.strokeRect(x, y, 60, 80);
    
    // 挂绳
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(x + 30, y);
    ctx.lineTo(x + 30, y - 20);
    ctx.stroke();
    
    // 照片区域
    ctx.fillStyle = '#e0e0e0';
    ctx.fillRect(x + 10, y + 10, 40, 40);
    
    // 简单头像
    ctx.fillStyle = '#666';
    ctx.beginPath();
    ctx.arc(x + 30, y + 25, 8, 0, Math.PI * 2);
    ctx.fill();
    
    // 文字
    ctx.fillStyle = '#333';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    
    const titles = {
        'OXHORSE': '勤劳员工',
        'PET': '人气明星',
        'DIVINE': '精英领袖'
    };
    
    ctx.fillText(titles[animalType] || '打工人', x + 30, y + 65);
    ctx.font = '8px Arial';
    ctx.fillText('ID: ' + Math.floor(Math.random() * 9999), x + 30, y + 75);
}

/**
 * 应用艺术风格
 */
function applyArtStyle(ctx, style) {
    if (style === 'sketch') {
        // 素描效果
        applySketchEffect(ctx);
    } else if (style === 'cartoon') {
        // 卡通效果
        applyCartoonEffect(ctx);
    } else {
        // 默认柔和效果
        applySoftEffect(ctx);
    }
}

/**
 * 素描效果
 */
function applySketchEffect(ctx) {
    const imageData = ctx.getImageData(0, 0, 1024, 1024);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
        const sketch = gray + (Math.random() - 0.5) * 20;
        
        data[i] = Math.min(255, Math.max(0, sketch));
        data[i + 1] = Math.min(255, Math.max(0, sketch));
        data[i + 2] = Math.min(255, Math.max(0, sketch));
    }
    
    ctx.putImageData(imageData, 0, 0);
}

/**
 * 卡通效果
 */
function applyCartoonEffect(ctx) {
    const imageData = ctx.getImageData(0, 0, 1024, 1024);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
        // 量化颜色值
        data[i] = Math.round(data[i] / 32) * 32;
        data[i + 1] = Math.round(data[i + 1] / 32) * 32;
        data[i + 2] = Math.round(data[i + 2] / 32) * 32;
    }
    
    ctx.putImageData(imageData, 0, 0);
}

/**
 * 柔和效果
 */
function applySoftEffect(ctx) {
    ctx.globalAlpha = 0.1;
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, 1024, 1024);
    ctx.globalAlpha = 1.0;
}

/**
 * 绘制默认图形
 */
function drawDefaultFigure(ctx) {
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 300, 400);
    
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 3;
    ctx.beginPath();
    // 头
    ctx.arc(150, 60, 30, 0, Math.PI * 2);
    // 身体
    ctx.moveTo(150, 90);
    ctx.lineTo(150, 250);
    // 手臂
    ctx.moveTo(120, 130);
    ctx.lineTo(180, 130);
    // 腿
    ctx.moveTo(150, 250);
    ctx.lineTo(130, 320);
    ctx.moveTo(150, 250);
    ctx.lineTo(170, 320);
    ctx.stroke();
}

/**
 * 生成降级图像
 */
function generateFallback(canvas, ctx, animalType) {
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, 1024, 1024);
    
    ctx.fillStyle = '#333333';
    ctx.font = '32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${animalType} 打工人`, 512, 400);
    ctx.fillText('生成中...', 512, 450);
    
    return {
        success: false,
        imageUrl: canvas.toDataURL('image/png'),
        fallbackUsed: true
    };
}

module.exports = {
    generateOptimizedFusion
};