#!/bin/bash

# 启动后端服务脚本
echo "🚀 正在启动牛马动物园后端服务..."

# 检查环境变量
if [ -f .env ]; then
    echo "📄 加载环境变量文件 .env"
    source .env
else
    echo "⚠️  未找到 .env 文件，使用默认配置"
fi

# 检查Node.js版本
echo "🔍 检查 Node.js 版本..."
node_version=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js 版本: $node_version"
else
    echo "❌ 未安装 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 检查数据库连接（如果配置了数据库）
if [ ! -z "$DATABASE_URL" ] || [ ! -z "$DB_HOST" ]; then
    echo "🔗 数据库已配置，请确保数据库服务正在运行"
fi

# 显示配置信息
echo "📋 服务配置信息:"
echo "   - 端口: ${PORT:-3000}"
echo "   - 环境: ${NODE_ENV:-development}"
echo "   - API 密钥状态:"
echo "     * Gemini API: ${GEMINI_API_KEY:+已配置}${GEMINI_API_KEY:-未配置}"
echo "     * OpenAI API: ${OPENAI_API_KEY:+已配置}${OPENAI_API_KEY:-未配置}"
echo "     * Replicate API: ${REPLICATE_API_TOKEN:+已配置}${REPLICATE_API_TOKEN:-未配置}"

# 启动服务
echo ""
echo "🎯 启动后端服务..."
if [ "$NODE_ENV" = "development" ]; then
    echo "🔧 开发模式：启动热重载服务"
    npm run start:dev
else
    echo "🏗️  构建项目..."
    npm run build
    echo "🚀 生产模式：启动服务"
    npm run start:prod
fi