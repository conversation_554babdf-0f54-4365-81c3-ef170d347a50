// 测试增强后的三种动物类型效果
const axios = require('axios');
const fs = require('fs');
const { createCanvas } = require('canvas');

// 创建一个更复杂的测试画像
function createEnhancedTestImage() {
    const canvas = createCanvas(350, 400);
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 350, 400);
    
    // 画一个更详细的人像
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 3;
    
    // 头发
    ctx.beginPath();
    ctx.arc(175, 180, 90, Math.PI, 2 * Math.PI);
    ctx.stroke();
    
    // 脸部轮廓 (椭圆脸)
    ctx.beginPath();
    ctx.ellipse(175, 200, 70, 85, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 眼睛
    ctx.beginPath();
    ctx.ellipse(155, 180, 12, 8, 0, 0, Math.PI * 2);
    ctx.ellipse(195, 180, 12, 8, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 眼珠
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(155, 180, 5, 0, Math.PI * 2);
    ctx.arc(195, 180, 5, 0, Math.PI * 2);
    ctx.fill();
    
    // 眉毛
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(145, 165);
    ctx.lineTo(165, 160);
    ctx.moveTo(185, 160);
    ctx.lineTo(205, 165);
    ctx.stroke();
    
    // 鼻子
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(175, 190);
    ctx.lineTo(170, 205);
    ctx.lineTo(175, 210);
    ctx.lineTo(180, 205);
    ctx.closePath();
    ctx.stroke();
    
    // 嘴巴 (微笑)
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(175, 220, 18, 0.2 * Math.PI, 0.8 * Math.PI);
    ctx.stroke();
    
    // 添加一些个性细节
    ctx.lineWidth = 1;
    ctx.strokeStyle = '#666';
    
    // 脸颊
    ctx.beginPath();
    ctx.arc(130, 210, 8, 0, Math.PI * 2);
    ctx.arc(220, 210, 8, 0, Math.PI * 2);
    ctx.stroke();
    
    // 一些纹理线条
    for (let i = 0; i < 20; i++) {
        const x1 = 120 + Math.random() * 110;
        const y1 = 120 + Math.random() * 160;
        const x2 = x1 + (Math.random() - 0.5) * 15;
        const y2 = y1 + (Math.random() - 0.5) * 15;
        
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
    }
    
    return canvas.toDataURL();
}

async function testEnhancedAnimals() {
    console.log('🦄 测试增强版动物融合效果...\n');
    
    const testImage = createEnhancedTestImage();
    console.log(`✅ 创建增强测试画像: ${(testImage.length / 1024).toFixed(1)}KB`);
    
    const animalTests = [
        {
            name: '🐲 神兽龙头 (DIVINE)',
            type: 'DIVINE',
            expectFeatures: ['龙角', '金色装饰', '神秘符号', '威严']
        },
        {
            name: '🐱 宠物猫头 (PET)', 
            type: 'PET',
            expectFeatures: ['猫耳', '三角鼻', '胡须', '可爱']
        },
        {
            name: '🐂 牛马头 (OXHORSE)',
            type: 'OXHORSE', 
            expectFeatures: ['牛耳', '长鼻吻', '牛鼻孔', '勤劳']
        }
    ];
    
    for (let i = 0; i < animalTests.length; i++) {
        const test = animalTests[i];
        console.log(`\n${i + 1}. 🎨 ${test.name}`);
        
        try {
            const startTime = Date.now();
            
            const response = await axios.post('http://localhost:3005/api/v1/avatar/real-generate', {
                imageData: testImage,
                animalType: test.type
            }, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 60000
            });
            
            const duration = Date.now() - startTime;
            console.log(`   ⚡ 生成速度: ${duration}ms`);
            
            // 保存图像文件
            const base64Data = response.data.avatar.imageUrl.replace(/^data:image\/png;base64,/, '');
            const filename = `/Users/<USER>/WorkSpace/niuma/enhanced_${test.type.toLowerCase()}.png`;
            fs.writeFileSync(filename, Buffer.from(base64Data, 'base64'));
            console.log(`   📁 已保存: enhanced_${test.type.toLowerCase()}.png`);
            
            // 分析特征
            const analysis = response.data.avatar.geminiAnalysis;
            const description = response.data.avatar.characterDescription;
            
            console.log(`   🔍 用户特征识别:`);
            if (analysis.includes('椭圆') || analysis.includes('长脸')) console.log(`      - 面部形状: ✅ 已识别`);
            if (analysis.includes('微笑') || analysis.includes('友善')) console.log(`      - 表情特征: ✅ 已识别`);
            
            console.log(`   🦁 动物特征验证:`);
            test.expectFeatures.forEach(feature => {
                const hasFeature = description.toLowerCase().includes(feature.toLowerCase()) || 
                                 description.includes(test.type.toLowerCase()) ||
                                 (test.type === 'DIVINE' && description.includes('dragon')) ||
                                 (test.type === 'PET' && description.includes('feline')) ||
                                 (test.type === 'OXHORSE' && description.includes('bovine'));
                console.log(`      - ${feature}: ${hasFeature ? '✅' : '❌'}`);
            });
            
            console.log(`   📊 图像质量:`);
            const imageSize = (Buffer.from(base64Data, 'base64').length / 1024).toFixed(1);
            console.log(`      - 文件大小: ${imageSize}KB`);
            console.log(`      - 素描风格: ${description.includes('pencil sketch') ? '✅' : '❌'}`);
            console.log(`      - 人身动物头: ${description.includes('office worker') && description.includes('head') ? '✅' : '❌'}`);
            
        } catch (error) {
            console.log(`   ❌ 测试失败: ${error.message}`);
        }
    }
    
    console.log('\n🎯 增强效果总结:');
    console.log('📈 改进项目:');
    console.log('  ✅ 动物耳朵/角 - 增加粗细度和装饰细节');
    console.log('  ✅ 鼻吻部特征 - 牛鼻孔、猫胡须、龙嘴威严');
    console.log('  ✅ 用户画像融合 - 剪切遮罩 + 颜色混合');
    console.log('  ✅ 对比度提升 - 更粗线条、填充色彩');
    console.log('  ✅ 三种风格差异化 - 神秘/可爱/勤劳特征明显');
}

testEnhancedAnimals();