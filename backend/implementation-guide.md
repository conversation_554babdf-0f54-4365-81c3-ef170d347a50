# 牛马动物园 - Gemini 2.0 Flash 集成实施指南

## 问题分析

### 问题1：图像生成质量
当前问题：
- ❌ 没有真正调用 Gemini API，只是返回模拟数据
- ❌ 没有使用用户的自画像数据
- ❌ 没有生成真实的动物头像
- ❌ 不是素描风格
- ❌ 融合效果差（实际上没有融合）

### 问题2：动物园集成
当前问题：
- ❌ 生成的角色在动物园中显示不正确
- ❌ 缺少行走动画
- ❌ 角色渲染过于简单
- ❌ 没有体现人身动物头的特征

## 解决方案

### 1. 真实 Gemini API 集成

#### 步骤1：获取 Gemini API 密钥
```bash
# 1. 访问 Google AI Studio
https://makersuite.google.com/app/apikey

# 2. 创建 API 密钥

# 3. 设置环境变量
export GEMINI_API_KEY="your-actual-api-key-here"
```

#### 步骤2：安装必要依赖
```bash
cd backend
npm install @google/generative-ai axios sharp canvas
```

#### 步骤3：使用真实的 Gemini API
```javascript
// 使用 Google 官方 SDK
const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ 
    model: "gemini-2.0-flash-preview" 
});

// 真实的图像生成调用
async function generateFusionAvatar(userDrawing, animalType) {
    // 准备用户自画像
    const userImage = {
        inlineData: {
            data: userDrawing.split(',')[1],
            mimeType: "image/png"
        }
    };
    
    // 准备动物参考图
    const animalReference = await loadAnimalReference(animalType);
    
    // 构建融合提示词
    const prompt = `
    请将这幅自画像和${animalReference.name}的头像结合，
    形成一个人身动物头的打工人形象。
    
    要求：
    1. 深度融合：保留人物的面部特征（眼睛位置、表情），
       但头部形状和特征要变成${animalReference.name}
    2. 素描风格：使用铅笔素描技法，线条清晰，有明暗对比
    3. 打工人身体：穿着职业装，体现办公室工作者形象
    4. 全身像：完整的站立或行走姿态
    5. 自然融合：不是简单拼接，要自然过渡
    `;
    
    // 调用 Gemini
    const result = await model.generateContent([prompt, userImage, animalReference]);
    const response = await result.response;
    
    return response;
}
```

### 2. 改进动物园集成

#### 步骤1：创建精灵图生成器
```javascript
// 使用 Canvas 生成行走动画精灵图
const { createCanvas, loadImage } = require('canvas');

async function generateWalkingSprite(fusionImage) {
    const canvas = createCanvas(256, 256);
    const ctx = canvas.getContext('2d');
    
    // 加载融合后的图像
    const img = await loadImage(fusionImage);
    
    // 生成4个方向的行走帧
    const directions = ['down', 'left', 'right', 'up'];
    const frames = 4;
    
    for (let d = 0; d < directions.length; d++) {
        for (let f = 0; f < frames; f++) {
            // 绘制不同角度和姿态的角色
            ctx.save();
            ctx.translate(f * 64, d * 64);
            
            // 根据方向旋转
            if (directions[d] === 'left') ctx.scale(-1, 1);
            if (directions[d] === 'up') ctx.rotate(Math.PI);
            
            // 绘制角色
            ctx.drawImage(img, 0, 0, 64, 64);
            
            // 添加行走动画效果
            if (f % 2 === 0) {
                ctx.translate(0, -2); // 模拟步伐
            }
            
            ctx.restore();
        }
    }
    
    return canvas.toDataURL();
}
```

#### 步骤2：改进动物园渲染
```javascript
// 在 index.html 中改进渲染函数
function renderGeminiAvatar(animal, size) {
    // 如果有精灵图，使用精灵动画
    if (animal.spriteSheet) {
        renderAnimatedSprite(animal);
    } else if (animal.geminiAvatar && animal.geminiAvatar.imageUrl) {
        // 加载并渲染 Gemini 生成的图像
        const img = new Image();
        img.onload = function() {
            zooCtx.save();
            
            // 根据移动方向调整角色朝向
            const direction = getMovementDirection(animal);
            
            // 绘制角色
            zooCtx.translate(animal.x, animal.y);
            
            // 根据方向旋转
            if (direction === 'left') {
                zooCtx.scale(-1, 1);
            }
            
            // 绘制人身动物头形象
            zooCtx.drawImage(img, -size/2, -size, size, size * 1.5);
            
            // 添加阴影效果
            zooCtx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            zooCtx.shadowBlur = 10;
            zooCtx.shadowOffsetY = 5;
            
            zooCtx.restore();
        };
        img.src = animal.geminiAvatar.imageUrl;
    }
}

// 渲染动画精灵
function renderAnimatedSprite(animal) {
    const sprite = animal.spriteSheet;
    const frame = Math.floor(Date.now() / sprite.animationSpeed) % sprite.frames;
    const direction = getMovementDirection(animal);
    
    // 计算精灵图中的位置
    const sx = frame * sprite.frameWidth;
    const sy = directions.indexOf(direction) * sprite.frameHeight;
    
    // 绘制当前帧
    zooCtx.drawImage(
        sprite.image,
        sx, sy, sprite.frameWidth, sprite.frameHeight,
        animal.x - sprite.frameWidth/2, 
        animal.y - sprite.frameHeight,
        sprite.frameWidth, 
        sprite.frameHeight
    );
}
```

### 3. 完整的工作流程

```mermaid
graph TD
    A[用户绘制自画像] --> B[AI分析动物类型]
    B --> C[调用 Gemini API]
    C --> D[深度融合生成]
    D --> E[生成精灵动画]
    E --> F[加入动物园]
    F --> G[角色在动物园中活动]
```

### 4. 环境配置

创建 `.env` 文件：
```env
# Gemini API 配置
GEMINI_API_KEY=your-actual-api-key-here
GEMINI_MODEL=gemini-2.0-flash-preview

# 服务配置
PORT=3001
NODE_ENV=development

# 图像处理配置
MAX_IMAGE_SIZE=5242880  # 5MB
IMAGE_FORMAT=png
SKETCH_STYLE_STRENGTH=0.8
```

### 5. 启动真实服务

```bash
# 1. 设置 API 密钥
export GEMINI_API_KEY="your-actual-key"

# 2. 安装依赖
cd backend
npm install

# 3. 启动 Gemini 集成服务
node gemini-integration.js

# 4. 启动前端
cd ..
python3 -m http.server 8000
```

## 测试步骤

1. **测试 Gemini API 连接**
```bash
curl http://localhost:3002/api/v1/health
```

2. **测试图像生成**
- 打开 http://localhost:8000
- 绘制自画像
- 点击"Gemini深度融合生成人身动物头打工人"
- 查看生成结果

3. **测试动物园集成**
- 生成头像后点击"进入动物园"
- 观察角色是否正确显示
- 检查行走动画是否流畅

## 注意事项

1. **API 限制**
   - Gemini 2.0 Flash Preview 有调用限制
   - 建议实现缓存机制避免重复生成

2. **图像质量**
   - 用户绘画质量会影响融合效果
   - 建议提供绘画指导

3. **性能优化**
   - 使用 WebWorker 处理图像
   - 实现图像懒加载
   - 限制同时显示的动物数量

## 备用方案

如果 Gemini API 不可用：
1. 使用 Stable Diffusion API
2. 使用 DALL-E 3 API
3. 使用预生成的素材库
4. 使用简化的 SVG 组合方案

## 联系支持

如需帮助，请提供：
- API 调用日志
- 浏览器控制台错误
- 网络请求截图
- 生成的图像示例