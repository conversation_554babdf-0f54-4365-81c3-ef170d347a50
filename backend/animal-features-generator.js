/**
 * 动物特征生成器
 * 在保持用户原画像的基础上，添加动物特征装饰
 */

const Canvas = require('canvas');
const { createCanvas, loadImage } = Canvas;

/**
 * 主函数：生成带动物特征的打工人头像
 */
async function generateAnimalFeatures(userDrawingBase64, animalType, analysisResult = {}) {
    console.log('🎭 开始生成动物特征装饰...');
    console.log('动物类型:', animalType);
    
    // 创建画布
    const canvas = createCanvas(1024, 1024);
    const ctx = canvas.getContext('2d');
    
    // 设置背景
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, 1024, 1024);
    
    try {
        // 1. 加载并绘制用户原画像（完整保留）
        console.log('步骤1: 加载用户原画像...');
        const userImage = await loadUserDrawing(userDrawingBase64);
        
        // 居中绘制原画像
        const scale = Math.min(800 / userImage.width, 800 / userImage.height);
        const drawWidth = userImage.width * scale;
        const drawHeight = userImage.height * scale;
        const x = (1024 - drawWidth) / 2;
        const y = (1024 - drawHeight) / 2;
        
        ctx.drawImage(userImage, x, y, drawWidth, drawHeight);
        console.log('原画像绘制完成');
        
        // 2. 分析画像特征，找到头部位置
        const headInfo = analyzeHeadPosition(ctx, x, y, drawWidth, drawHeight);
        
        // 3. 根据动物类型添加特征装饰
        console.log('步骤2: 添加动物特征...');
        await addAnimalFeatures(ctx, animalType, headInfo, analysisResult);
        
        // 4. 添加打工人元素
        console.log('步骤3: 添加打工人元素...');
        addWorkerElements(ctx, animalType, { x, y, width: drawWidth, height: drawHeight });
        
        // 5. 应用艺术滤镜
        console.log('步骤4: 应用艺术效果...');
        applyArtisticEffect(ctx);
        
        // 生成结果
        const resultBase64 = canvas.toDataURL('image/png');
        
        return {
            success: true,
            imageUrl: resultBase64,
            fusionDetails: {
                method: 'Animal Features Overlay',
                animalType,
                bodySource: 'User Original Drawing',
                features: 'Animal Accessories Added',
                style: 'Hybrid'
            }
        };
        
    } catch (error) {
        console.error('特征生成失败:', error.message);
        return generateFallbackImage(canvas, ctx, animalType);
    }
}

/**
 * 加载用户绘画
 */
async function loadUserDrawing(base64Data) {
    try {
        if (!base64Data || typeof base64Data !== 'string') {
            throw new Error('Invalid base64 data');
        }
        
        const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
        const buffer = Buffer.from(imageData, 'base64');
        const image = await Canvas.loadImage(buffer);
        
        return image;
    } catch (error) {
        console.error('加载用户绘画失败:', error.message);
        // 返回默认人形
        const canvas = createCanvas(300, 400);
        const ctx = canvas.getContext('2d');
        drawDefaultHuman(ctx);
        return canvas;
    }
}

/**
 * 分析头部位置
 */
function analyzeHeadPosition(ctx, x, y, width, height) {
    // 简单估算：头部通常在上方1/4区域
    return {
        x: x + width / 2,
        y: y + height * 0.15,
        width: width * 0.3,
        height: height * 0.25,
        imageX: x,
        imageY: y,
        imageWidth: width,
        imageHeight: height
    };
}

/**
 * 添加动物特征装饰
 */
async function addAnimalFeatures(ctx, animalType, headInfo, analysisResult) {
    ctx.save();
    
    switch(animalType) {
        case 'OXHORSE':
            addOxHorseFeatures(ctx, headInfo);
            break;
        case 'PET':
            addPetFeatures(ctx, headInfo);
            break;
        case 'DIVINE':
            addDivineFeatures(ctx, headInfo);
            break;
        default:
            addOxHorseFeatures(ctx, headInfo);
    }
    
    ctx.restore();
}

/**
 * 添加牛马特征
 */
function addOxHorseFeatures(ctx, headInfo) {
    const { x, y, width, height } = headInfo;
    
    // 牛角
    ctx.save();
    ctx.strokeStyle = '#8B4513';
    ctx.lineWidth = 8;
    ctx.lineCap = 'round';
    
    // 左角
    ctx.beginPath();
    ctx.moveTo(x - width * 0.3, y);
    ctx.quadraticCurveTo(x - width * 0.5, y - height * 0.3, x - width * 0.4, y - height * 0.5);
    ctx.stroke();
    
    // 右角
    ctx.beginPath();
    ctx.moveTo(x + width * 0.3, y);
    ctx.quadraticCurveTo(x + width * 0.5, y - height * 0.3, x + width * 0.4, y - height * 0.5);
    ctx.stroke();
    
    // 耳朵
    ctx.fillStyle = '#D2691E';
    ctx.strokeStyle = '#8B4513';
    ctx.lineWidth = 3;
    
    // 左耳
    ctx.beginPath();
    ctx.ellipse(x - width * 0.4, y + height * 0.1, width * 0.15, height * 0.2, -Math.PI/6, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 右耳
    ctx.beginPath();
    ctx.ellipse(x + width * 0.4, y + height * 0.1, width * 0.15, height * 0.2, Math.PI/6, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 鼻环（牛的特征）
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.arc(x, y + height * 0.6, width * 0.08, Math.PI * 0.2, Math.PI * 0.8);
    ctx.stroke();
    
    ctx.restore();
}

/**
 * 添加宠物特征
 */
function addPetFeatures(ctx, headInfo) {
    const { x, y, width, height } = headInfo;
    
    ctx.save();
    
    // 猫耳朵
    ctx.fillStyle = '#FFB6C1';
    ctx.strokeStyle = '#FF69B4';
    ctx.lineWidth = 3;
    
    // 左耳
    ctx.beginPath();
    ctx.moveTo(x - width * 0.35, y);
    ctx.lineTo(x - width * 0.5, y - height * 0.4);
    ctx.lineTo(x - width * 0.2, y - height * 0.2);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 右耳
    ctx.beginPath();
    ctx.moveTo(x + width * 0.35, y);
    ctx.lineTo(x + width * 0.5, y - height * 0.4);
    ctx.lineTo(x + width * 0.2, y - height * 0.2);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 胡须
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;
    
    // 左侧胡须
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.moveTo(x - width * 0.2, y + height * 0.5 + i * 10);
        ctx.lineTo(x - width * 0.6, y + height * 0.5 + i * 10 - 5);
        ctx.stroke();
    }
    
    // 右侧胡须
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.moveTo(x + width * 0.2, y + height * 0.5 + i * 10);
        ctx.lineTo(x + width * 0.6, y + height * 0.5 + i * 10 - 5);
        ctx.stroke();
    }
    
    // 可爱的鼻子
    ctx.fillStyle = '#FF69B4';
    ctx.beginPath();
    ctx.moveTo(x, y + height * 0.55);
    ctx.lineTo(x - width * 0.05, y + height * 0.6);
    ctx.lineTo(x + width * 0.05, y + height * 0.6);
    ctx.closePath();
    ctx.fill();
    
    // 铃铛项圈
    ctx.strokeStyle = '#FF0000';
    ctx.lineWidth = 6;
    ctx.beginPath();
    ctx.ellipse(x, y + height * 1.2, width * 0.5, height * 0.1, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 铃铛
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(x, y + height * 1.3, width * 0.08, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.restore();
}

/**
 * 添加神兽特征
 */
function addDivineFeatures(ctx, headInfo) {
    const { x, y, width, height } = headInfo;
    
    ctx.save();
    
    // 龙角
    ctx.strokeStyle = '#4B0082';
    ctx.lineWidth = 6;
    ctx.lineCap = 'round';
    
    // 左角（分叉）
    ctx.beginPath();
    ctx.moveTo(x - width * 0.25, y);
    ctx.lineTo(x - width * 0.35, y - height * 0.4);
    ctx.moveTo(x - width * 0.35, y - height * 0.3);
    ctx.lineTo(x - width * 0.45, y - height * 0.35);
    ctx.stroke();
    
    // 右角（分叉）
    ctx.beginPath();
    ctx.moveTo(x + width * 0.25, y);
    ctx.lineTo(x + width * 0.35, y - height * 0.4);
    ctx.moveTo(x + width * 0.35, y - height * 0.3);
    ctx.lineTo(x + width * 0.45, y - height * 0.35);
    ctx.stroke();
    
    // 光环
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 4;
    ctx.shadowColor = '#FFD700';
    ctx.shadowBlur = 20;
    ctx.beginPath();
    ctx.ellipse(x, y - height * 0.6, width * 0.6, height * 0.15, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 神光效果
    const gradient = ctx.createRadialGradient(x, y, 0, x, y, width);
    gradient.addColorStop(0, 'rgba(255, 215, 0, 0.3)');
    gradient.addColorStop(1, 'rgba(255, 215, 0, 0)');
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(x, y, width * 1.5, 0, Math.PI * 2);
    ctx.fill();
    
    // 云纹装饰
    ctx.strokeStyle = '#9370DB';
    ctx.lineWidth = 3;
    ctx.setLineDash([5, 5]);
    
    // 左侧云纹
    ctx.beginPath();
    ctx.arc(x - width * 0.6, y + height * 0.3, width * 0.15, 0, Math.PI);
    ctx.arc(x - width * 0.4, y + height * 0.3, width * 0.1, 0, Math.PI);
    ctx.stroke();
    
    // 右侧云纹
    ctx.beginPath();
    ctx.arc(x + width * 0.6, y + height * 0.3, width * 0.15, Math.PI, 0);
    ctx.arc(x + width * 0.4, y + height * 0.3, width * 0.1, Math.PI, 0);
    ctx.stroke();
    
    ctx.restore();
}

/**
 * 添加打工人元素
 */
function addWorkerElements(ctx, animalType, bounds) {
    ctx.save();
    
    const { x, y, width, height } = bounds;
    
    // 工牌
    const badgeX = x + width * 0.7;
    const badgeY = y + height * 0.3;
    
    ctx.fillStyle = '#FFF';
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.fillRect(badgeX, badgeY, width * 0.15, height * 0.1);
    ctx.strokeRect(badgeX, badgeY, width * 0.15, height * 0.1);
    
    ctx.fillStyle = '#333';
    ctx.font = `${width * 0.03}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText('WORKER', badgeX + width * 0.075, badgeY + height * 0.06);
    
    // 根据动物类型添加不同的配饰
    if (animalType === 'OXHORSE') {
        // 安全帽
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(x + width / 2, y - height * 0.05, width * 0.2, Math.PI, 0);
        ctx.fill();
    } else if (animalType === 'PET') {
        // 蝴蝶结
        ctx.fillStyle = '#FF69B4';
        ctx.beginPath();
        ctx.moveTo(x + width / 2, y - height * 0.1);
        ctx.lineTo(x + width / 2 - width * 0.1, y - height * 0.15);
        ctx.lineTo(x + width / 2, y - height * 0.05);
        ctx.lineTo(x + width / 2 + width * 0.1, y - height * 0.15);
        ctx.closePath();
        ctx.fill();
    } else if (animalType === 'DIVINE') {
        // 皇冠
        ctx.fillStyle = '#FFD700';
        ctx.strokeStyle = '#FF6347';
        ctx.lineWidth = 2;
        
        const crownY = y - height * 0.15;
        ctx.beginPath();
        ctx.moveTo(x + width * 0.3, crownY);
        ctx.lineTo(x + width * 0.35, crownY - height * 0.08);
        ctx.lineTo(x + width * 0.45, crownY - height * 0.05);
        ctx.lineTo(x + width * 0.5, crownY - height * 0.1);
        ctx.lineTo(x + width * 0.55, crownY - height * 0.05);
        ctx.lineTo(x + width * 0.65, crownY - height * 0.08);
        ctx.lineTo(x + width * 0.7, crownY);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }
    
    ctx.restore();
}

/**
 * 应用艺术效果
 */
function applyArtisticEffect(ctx) {
    // 添加轻微的素描效果
    const imageData = ctx.getImageData(0, 0, 1024, 1024);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
        // 轻微降低饱和度，增加艺术感
        const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
        data[i] = data[i] * 0.7 + gray * 0.3;
        data[i + 1] = data[i + 1] * 0.7 + gray * 0.3;
        data[i + 2] = data[i + 2] * 0.7 + gray * 0.3;
    }
    
    ctx.putImageData(imageData, 0, 0);
}

/**
 * 绘制默认人形
 */
function drawDefaultHuman(ctx) {
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 300, 400);
    
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 3;
    ctx.beginPath();
    // 头
    ctx.arc(150, 60, 30, 0, Math.PI * 2);
    // 身体
    ctx.moveTo(150, 90);
    ctx.lineTo(150, 250);
    // 手臂
    ctx.moveTo(120, 130);
    ctx.lineTo(180, 130);
    // 腿
    ctx.moveTo(150, 250);
    ctx.lineTo(130, 320);
    ctx.moveTo(150, 250);
    ctx.lineTo(170, 320);
    ctx.stroke();
}

/**
 * 生成降级图像
 */
function generateFallbackImage(canvas, ctx, animalType) {
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, 1024, 1024);
    
    ctx.fillStyle = '#333333';
    ctx.font = '32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${animalType} 打工人`, 512, 400);
    ctx.fillText('特征生成中...', 512, 450);
    
    const resultBase64 = canvas.toDataURL('image/png');
    
    return {
        success: false,
        imageUrl: resultBase64,
        fallbackUsed: true,
        fusionDetails: {
            method: 'Fallback',
            animalType,
            error: '特征生成失败'
        }
    };
}

module.exports = {
    generateAnimalFeatures
};