"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const compression = require("compression");
const helmet = require("helmet");
const app_module_1 = require("./app.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
const transform_interceptor_1 = require("./common/interceptors/transform.interceptor");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const port = configService.get('PORT', 3000);
    const nodeEnv = configService.get('NODE_ENV', 'development');
    app.enableVersioning({
        type: common_1.VersioningType.URI,
    });
    app.use(helmet());
    app.use(compression());
    app.enableCors({
        origin: nodeEnv === 'production'
            ? ['https://your-domain.com', 'https://www.your-domain.com']
            : true,
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization'],
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter());
    app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor(), new transform_interceptor_1.TransformInterceptor());
    if (nodeEnv !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('牛马动物园 API')
            .setDescription('打工人自嘲互动娱乐平台后端API文档')
            .setVersion('1.0')
            .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
        }, 'JWT-auth')
            .addTag('认证', '用户认证相关接口')
            .addTag('用户', '用户管理相关接口')
            .addTag('测试', '打工人分类测试相关接口')
            .addTag('绘画分析', '用户绘画分析和头像生成相关接口')
            .addTag('动物园', '3D动物园场景相关接口')
            .addTag('社交', '社交互动相关接口')
            .addTag('排行榜', '排行榜相关接口')
            .addTag('内容', '内容管理相关接口')
            .addTag('文件', '文件上传相关接口')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/docs', app, document, {
            swaggerOptions: {
                persistAuthorization: true,
            },
        });
        console.log(`📚 API文档地址: http://localhost:${port}/api/docs`);
    }
    await app.listen(port);
    console.log(`🚀 牛马动物园后端服务已启动`);
    console.log(`🌍 服务地址: http://localhost:${port}`);
    console.log(`🔧 运行环境: ${nodeEnv}`);
    console.log(`📦 应用版本: ${configService.get('APP_VERSION', '1.0.0')}`);
}
bootstrap();
//# sourceMappingURL=main.js.map