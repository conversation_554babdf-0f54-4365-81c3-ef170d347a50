"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ANIMAL_CONFIG = exports.AnimalSpecies = exports.AnimalCategory = void 0;
exports.calculateAnimalType = calculateAnimalType;
var AnimalCategory;
(function (AnimalCategory) {
    AnimalCategory["DIVINE_BEAST"] = "divine_beast";
    AnimalCategory["PET"] = "pet";
    AnimalCategory["WORKING_ANIMAL"] = "working_animal";
})(AnimalCategory || (exports.AnimalCategory = AnimalCategory = {}));
var AnimalSpecies;
(function (AnimalSpecies) {
    AnimalSpecies["QILIN_DEER"] = "qilin_deer";
    AnimalSpecies["DRAGON"] = "dragon";
    AnimalSpecies["PHOENIX"] = "phoenix";
    AnimalSpecies["UNICORN"] = "unicorn";
    AnimalSpecies["GOLDEN_RETRIEVER"] = "golden_retriever";
    AnimalSpecies["PERSIAN_CAT"] = "persian_cat";
    AnimalSpecies["RABBIT"] = "rabbit";
    AnimalSpecies["HAMSTER"] = "hamster";
    AnimalSpecies["PARROT"] = "parrot";
    AnimalSpecies["OX"] = "ox";
    AnimalSpecies["HORSE"] = "horse";
    AnimalSpecies["DONKEY"] = "donkey";
    AnimalSpecies["MULE"] = "mule";
    AnimalSpecies["BUFFALO"] = "buffalo";
    AnimalSpecies["SHEEP"] = "sheep";
    AnimalSpecies["GOAT"] = "goat";
})(AnimalSpecies || (exports.AnimalSpecies = AnimalSpecies = {}));
exports.ANIMAL_CONFIG = {
    [AnimalSpecies.QILIN_DEER]: {
        category: AnimalCategory.DIVINE_BEAST,
        name: '麒麟鹿',
        rarity: 0.02,
        attributes: {
            wisdom: 95,
            calmness: 90,
            leadership: 85,
            endurance: 70,
        },
        description: '温文尔雅的智者，在职场中如鱼得水，是同事眼中的大神',
        color: '#FFD700',
    },
    [AnimalSpecies.DRAGON]: {
        category: AnimalCategory.DIVINE_BEAST,
        name: '祥龙',
        rarity: 0.02,
        attributes: {
            wisdom: 90,
            leadership: 95,
            calmness: 80,
            endurance: 85,
        },
        description: '威严而温和的领导者，能力出众但乐于助人',
        color: '#FF69B4',
    },
    [AnimalSpecies.GOLDEN_RETRIEVER]: {
        category: AnimalCategory.PET,
        name: '金毛狗',
        rarity: 0.08,
        attributes: {
            loyalty: 95,
            optimism: 90,
            sociability: 85,
            endurance: 60,
        },
        description: '忠诚乐观的好同事，人见人爱，总是给团队带来正能量',
        color: '#FF69B4',
    },
    [AnimalSpecies.PERSIAN_CAT]: {
        category: AnimalCategory.PET,
        name: '波斯猫',
        rarity: 0.05,
        attributes: {
            independence: 90,
            elegance: 95,
            selectivity: 85,
            endurance: 50,
        },
        description: '优雅独立的精致主义者，有品味但不易接近',
        color: '#FF69B4',
    },
    [AnimalSpecies.OX]: {
        category: AnimalCategory.WORKING_ANIMAL,
        name: '黄牛',
        rarity: 0.35,
        attributes: {
            endurance: 95,
            reliability: 90,
            patience: 85,
            speed: 40,
        },
        description: '任劳任怨的老实人，承担着最多的工作量，是团队的中坚力量',
        color: '#8B4513',
    },
    [AnimalSpecies.HORSE]: {
        category: AnimalCategory.WORKING_ANIMAL,
        name: '棕马',
        rarity: 0.25,
        attributes: {
            endurance: 85,
            speed: 90,
            reliability: 80,
            agility: 75,
        },
        description: '勤劳快速的执行者，效率很高但压力也大',
        color: '#8B4513',
    },
    [AnimalSpecies.DONKEY]: {
        category: AnimalCategory.WORKING_ANIMAL,
        name: '毛驴',
        rarity: 0.15,
        attributes: {
            endurance: 80,
            stubbornness: 90,
            reliability: 75,
            speed: 35,
        },
        description: '固执但可靠的员工，虽然速度慢但从不掉链子',
        color: '#8B4513',
    },
};
function calculateAnimalType(scores) {
    const { workload, stress, creativity, leadership, socialSkill, patience, } = scores;
    const totalScore = workload + creativity + leadership + socialSkill - stress;
    const leadershipScore = leadership + creativity;
    const socialScore = socialSkill + (100 - stress);
    const workScore = workload + patience;
    if (totalScore >= 350 &&
        leadershipScore >= 140 &&
        socialScore >= 140) {
        if (creativity >= 80 && socialSkill >= 85) {
            return AnimalSpecies.DRAGON;
        }
        return AnimalSpecies.QILIN_DEER;
    }
    if (socialScore >= 130 &&
        stress <= 60 &&
        workload <= 70) {
        if (socialSkill >= 80) {
            return AnimalSpecies.GOLDEN_RETRIEVER;
        }
        if (creativity >= 70) {
            return AnimalSpecies.PERSIAN_CAT;
        }
        return AnimalSpecies.RABBIT;
    }
    if (workload >= 80 && patience >= 70) {
        if (workload >= 90) {
            return AnimalSpecies.OX;
        }
        if (workload >= 75 && socialSkill >= 60) {
            return AnimalSpecies.HORSE;
        }
        return AnimalSpecies.DONKEY;
    }
    return AnimalSpecies.OX;
}
//# sourceMappingURL=animal-types.js.map