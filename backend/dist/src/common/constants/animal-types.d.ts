export declare enum AnimalCategory {
    DIVINE_BEAST = "divine_beast",
    PET = "pet",
    WORKING_ANIMAL = "working_animal"
}
export declare enum AnimalSpecies {
    QILIN_DEER = "qilin_deer",
    DRAGON = "dragon",
    PHOENIX = "phoenix",
    UNICORN = "unicorn",
    GOLDEN_RETRIEVER = "golden_retriever",
    PERSIAN_CAT = "persian_cat",
    RABBIT = "rabbit",
    HAMSTER = "hamster",
    PARROT = "parrot",
    OX = "ox",
    HORSE = "horse",
    DONKEY = "donkey",
    MULE = "mule",
    BUFFALO = "buffalo",
    SHEEP = "sheep",
    GOAT = "goat"
}
export declare const ANIMAL_CONFIG: {
    qilin_deer: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            wisdom: number;
            calmness: number;
            leadership: number;
            endurance: number;
        };
        description: string;
        color: string;
    };
    dragon: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            wisdom: number;
            leadership: number;
            calmness: number;
            endurance: number;
        };
        description: string;
        color: string;
    };
    golden_retriever: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            loyalty: number;
            optimism: number;
            sociability: number;
            endurance: number;
        };
        description: string;
        color: string;
    };
    persian_cat: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            independence: number;
            elegance: number;
            selectivity: number;
            endurance: number;
        };
        description: string;
        color: string;
    };
    ox: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            endurance: number;
            reliability: number;
            patience: number;
            speed: number;
        };
        description: string;
        color: string;
    };
    horse: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            endurance: number;
            speed: number;
            reliability: number;
            agility: number;
        };
        description: string;
        color: string;
    };
    donkey: {
        category: AnimalCategory;
        name: string;
        rarity: number;
        attributes: {
            endurance: number;
            stubbornness: number;
            reliability: number;
            speed: number;
        };
        description: string;
        color: string;
    };
};
export declare function calculateAnimalType(scores: {
    workload: number;
    stress: number;
    creativity: number;
    leadership: number;
    socialSkill: number;
    patience: number;
}): AnimalSpecies;
