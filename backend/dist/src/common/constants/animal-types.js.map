{"version": 3, "file": "animal-types.js", "sourceRoot": "", "sources": ["../../../../src/common/constants/animal-types.ts"], "names": [], "mappings": ";;;AAsIA,kDA+DC;AApMD,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,+CAA6B,CAAA;IAC7B,6BAAW,CAAA;IACX,mDAAiC,CAAA;AACnC,CAAC,EAJW,cAAc,8BAAd,cAAc,QAIzB;AAGD,IAAY,aAsBX;AAtBD,WAAY,aAAa;IAEvB,0CAAyB,CAAA;IACzB,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IAGnB,sDAAqC,CAAA;IACrC,4CAA2B,CAAA;IAC3B,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;IACnB,kCAAiB,CAAA;IAGjB,0BAAS,CAAA;IACT,gCAAe,CAAA;IACf,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,oCAAmB,CAAA;IACnB,gCAAe,CAAA;IACf,8BAAa,CAAA;AACf,CAAC,EAtBW,aAAa,6BAAb,aAAa,QAsBxB;AAGY,QAAA,aAAa,GAAG;IAC3B,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;QAC1B,QAAQ,EAAE,cAAc,CAAC,YAAY;QACrC,IAAI,EAAE,KAAK;QACX,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;SACd;QACD,WAAW,EAAE,2BAA2B;QACxC,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QACtB,QAAQ,EAAE,cAAc,CAAC,YAAY;QACrC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;SACd;QACD,WAAW,EAAE,qBAAqB;QAClC,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;QAChC,QAAQ,EAAE,cAAc,CAAC,GAAG;QAC5B,IAAI,EAAE,KAAK;QACX,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;SACd;QACD,WAAW,EAAE,0BAA0B;QACvC,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE;QAC3B,QAAQ,EAAE,cAAc,CAAC,GAAG;QAC5B,IAAI,EAAE,KAAK;QACX,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;SACd;QACD,WAAW,EAAE,qBAAqB;QAClC,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE;QAClB,QAAQ,EAAE,cAAc,CAAC,cAAc;QACvC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,EAAE;SACV;QACD,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QACrB,QAAQ,EAAE,cAAc,CAAC,cAAc;QACvC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,OAAO,EAAE,EAAE;SACZ;QACD,WAAW,EAAE,oBAAoB;QACjC,KAAK,EAAE,SAAS;KACjB;IAED,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QACtB,QAAQ,EAAE,cAAc,CAAC,cAAc;QACvC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE;YACV,SAAS,EAAE,EAAE;YACb,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,EAAE;YACf,KAAK,EAAE,EAAE;SACV;QACD,WAAW,EAAE,sBAAsB;QACnC,KAAK,EAAE,SAAS;KACjB;CACF,CAAC;AAGF,SAAgB,mBAAmB,CAAC,MAOnC;IACC,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,UAAU,EACV,UAAU,EACV,WAAW,EACX,QAAQ,GACT,GAAG,MAAM,CAAC;IAGX,MAAM,UAAU,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,WAAW,GAAG,MAAM,CAAC;IAC7E,MAAM,eAAe,GAAG,UAAU,GAAG,UAAU,CAAC;IAChD,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;IACjD,MAAM,SAAS,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAGtC,IACE,UAAU,IAAI,GAAG;QACjB,eAAe,IAAI,GAAG;QACtB,WAAW,IAAI,GAAG,EAClB,CAAC;QACD,IAAI,UAAU,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YAC1C,OAAO,aAAa,CAAC,MAAM,CAAC;QAC9B,CAAC;QACD,OAAO,aAAa,CAAC,UAAU,CAAC;IAClC,CAAC;IAGD,IACE,WAAW,IAAI,GAAG;QAClB,MAAM,IAAI,EAAE;QACZ,QAAQ,IAAI,EAAE,EACd,CAAC;QACD,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YACtB,OAAO,aAAa,CAAC,gBAAgB,CAAC;QACxC,CAAC;QACD,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;YACrB,OAAO,aAAa,CAAC,WAAW,CAAC;QACnC,CAAC;QACD,OAAO,aAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAGD,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;QACrC,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;YACnB,OAAO,aAAa,CAAC,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,QAAQ,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YACxC,OAAO,aAAa,CAAC,KAAK,CAAC;QAC7B,CAAC;QACD,OAAO,aAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAGD,OAAO,aAAa,CAAC,EAAE,CAAC;AAC1B,CAAC"}