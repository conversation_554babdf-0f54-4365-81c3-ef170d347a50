"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let HttpExceptionFilter = class HttpExceptionFilter {
    constructor(logger) {
        this.logger = logger;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let error = 'Internal Server Error';
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            message = exceptionResponse.message || exception.message;
            error = exceptionResponse.error || exception.name;
            if (Array.isArray(exceptionResponse.message)) {
                message = exceptionResponse.message.join('; ');
            }
        }
        const errorInfo = {
            timestamp: new Date().toISOString(),
            url: request.url,
            method: request.method,
            status,
            error,
            message,
            userAgent: request.get('User-Agent'),
            ip: request.ip,
            userId: request.user?.id,
        };
        if (status >= 500) {
            this.logger.error('Server Error', { ...errorInfo, stack: exception });
        }
        else {
            this.logger.warn('Client Error', errorInfo);
        }
        const errorResponse = {
            success: false,
            error: {
                code: status,
                name: error,
                message,
                timestamp: new Date().toISOString(),
                path: request.url,
                method: request.method,
            },
            data: null,
        };
        if (process.env.NODE_ENV === 'production' && status >= 500) {
            errorResponse.error.message = 'Internal server error';
        }
        response.status(status).json(errorResponse);
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], HttpExceptionFilter);
//# sourceMappingURL=http-exception.filter.js.map