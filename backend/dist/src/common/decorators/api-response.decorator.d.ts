import { Type } from '@nestjs/common';
export declare const ApiResponseWrapper: <TModel extends Type<any>>(model: TModel, status?: number, description?: string, isArray?: boolean) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ApiErrorResponse: (status: number, description: string) => MethodDecorator & ClassDecorator;
