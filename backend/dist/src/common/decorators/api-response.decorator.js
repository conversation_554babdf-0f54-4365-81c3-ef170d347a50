"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiErrorResponse = exports.ApiResponseWrapper = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ApiResponseWrapper = (model, status = 200, description, isArray = false) => {
    return (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
        status,
        description: description || 'Success',
        schema: {
            allOf: [
                {
                    properties: {
                        success: {
                            type: 'boolean',
                            example: true,
                        },
                        message: {
                            type: 'string',
                            example: 'Success',
                        },
                        timestamp: {
                            type: 'string',
                            example: '2025-01-15T10:30:00Z',
                        },
                        path: {
                            type: 'string',
                            example: '/api/v1/users',
                        },
                        method: {
                            type: 'string',
                            example: 'GET',
                        },
                        data: isArray
                            ? {
                                type: 'array',
                                items: { $ref: (0, swagger_1.getSchemaPath)(model) },
                            }
                            : { $ref: (0, swagger_1.getSchemaPath)(model) },
                    },
                },
            ],
        },
    }));
};
exports.ApiResponseWrapper = ApiResponseWrapper;
const ApiErrorResponse = (status, description) => {
    return (0, swagger_1.ApiResponse)({
        status,
        description,
        schema: {
            properties: {
                success: {
                    type: 'boolean',
                    example: false,
                },
                error: {
                    type: 'object',
                    properties: {
                        code: {
                            type: 'number',
                            example: status,
                        },
                        name: {
                            type: 'string',
                            example: 'BadRequestException',
                        },
                        message: {
                            type: 'string',
                            example: description,
                        },
                        timestamp: {
                            type: 'string',
                            example: '2025-01-15T10:30:00Z',
                        },
                        path: {
                            type: 'string',
                            example: '/api/v1/users',
                        },
                        method: {
                            type: 'string',
                            example: 'POST',
                        },
                    },
                },
                data: {
                    type: 'null',
                    example: null,
                },
            },
        },
    });
};
exports.ApiErrorResponse = ApiErrorResponse;
//# sourceMappingURL=api-response.decorator.js.map