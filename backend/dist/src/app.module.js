"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const mongoose_1 = require("@nestjs/mongoose");
const cache_manager_1 = require("@nestjs/cache-manager");
const throttler_1 = require("@nestjs/throttler");
const bull_1 = require("@nestjs/bull");
const nest_winston_1 = require("nest-winston");
const configuration_1 = require("./config/configuration");
const database_config_1 = require("./config/database.config");
const redis_config_1 = require("./config/redis.config");
const logger_config_1 = require("./config/logger.config");
const auth_module_1 = require("./modules/auth/auth.module");
const user_module_1 = require("./modules/user/user.module");
const zoo_module_1 = require("./modules/zoo/zoo.module");
const drawing_module_1 = require("./modules/drawing/drawing.module");
const ai_module_1 = require("./modules/ai/ai.module");
const health_module_1 = require("./modules/health/health.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [configuration_1.default],
                envFilePath: ['.env.local', '.env'],
            }),
            nest_winston_1.WinstonModule.forRootAsync({
                useFactory: () => logger_config_1.loggerConfig,
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                useFactory: database_config_1.typeOrmConfig,
                inject: [config_1.ConfigModule],
            }),
            mongoose_1.MongooseModule.forRootAsync({
                useFactory: async () => ({
                    uri: process.env.MONGODB_URI,
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                }),
            }),
            cache_manager_1.CacheModule.registerAsync({
                useFactory: redis_config_1.redisConfig,
                isGlobal: true,
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                useFactory: () => ({
                    ttl: parseInt(process.env.THROTTLE_TTL) || 60,
                    limit: parseInt(process.env.THROTTLE_LIMIT) || 10,
                }),
            }),
            bull_1.BullModule.forRootAsync({
                useFactory: () => ({
                    redis: {
                        host: process.env.REDIS_HOST || 'localhost',
                        port: parseInt(process.env.REDIS_PORT) || 6379,
                        password: process.env.REDIS_PASSWORD || undefined,
                    },
                }),
            }),
            health_module_1.HealthModule,
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            zoo_module_1.ZooModule,
            drawing_module_1.DrawingModule,
            ai_module_1.AIModule,
        ],
        controllers: [],
        providers: [],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map