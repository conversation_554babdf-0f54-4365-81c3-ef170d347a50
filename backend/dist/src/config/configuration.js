"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = () => ({
    app: {
        nodeEnv: process.env.NODE_ENV || 'development',
        port: parseInt(process.env.PORT, 10) || 3000,
        version: process.env.APP_VERSION || '1.0.0',
    },
    database: {
        host: process.env.DATABASE_HOST || 'localhost',
        port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
        name: process.env.DATABASE_NAME || 'niuma_zoo',
        username: process.env.DATABASE_USERNAME || 'niuma',
        password: process.env.DATABASE_PASSWORD || 'niuma123',
        sync: process.env.DATABASE_SYNC === 'true',
    },
    mongodb: {
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/niuma_zoo',
    },
    redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT, 10) || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB, 10) || 0,
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'dev-secret',
        expiresIn: process.env.JWT_EXPIRES_IN || '1h',
        refreshSecret: process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    },
    oauth: {
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        },
        wechat: {
            appId: process.env.WECHAT_APP_ID,
            appSecret: process.env.WECHAT_APP_SECRET,
        },
    },
    aliyun: {
        accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
        accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET,
        oss: {
            bucket: process.env.ALIYUN_OSS_BUCKET || 'niuma-zoo-dev',
            region: process.env.ALIYUN_OSS_REGION || 'oss-cn-hangzhou',
        },
    },
    contentSecurity: {
        enabled: process.env.CONTENT_SECURITY_ENABLED === 'true',
        endpoint: process.env.CONTENT_SECURITY_ENDPOINT,
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        dir: process.env.LOG_DIR || './logs',
    },
    throttle: {
        ttl: parseInt(process.env.THROTTLE_TTL, 10) || 60,
        limit: parseInt(process.env.THROTTLE_LIMIT, 10) || 10,
    },
    upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10485760,
        uploadDir: process.env.UPLOAD_DIR || './uploads',
    },
    email: {
        smtp: {
            host: process.env.SMTP_HOST,
            port: parseInt(process.env.SMTP_PORT, 10) || 587,
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
        },
    },
    ai: {
        gemini: {
            apiKey: process.env.GEMINI_API_KEY,
            model: process.env.GEMINI_MODEL || 'gemini-1.5-flash',
            imageModel: process.env.GEMINI_IMAGE_MODEL || 'gemini-2.0-flash-exp',
            maxTokens: parseInt(process.env.GEMINI_MAX_TOKENS, 10) || 8192,
            temperature: parseFloat(process.env.GEMINI_TEMPERATURE) || 1.0,
            topP: parseFloat(process.env.GEMINI_TOP_P) || 0.95,
            topK: parseInt(process.env.GEMINI_TOP_K, 10) || 40,
        },
        openai: {
            apiKey: process.env.OPENAI_API_KEY,
            model: process.env.OPENAI_MODEL || 'dall-e-3',
            maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS, 10) || 4000,
            temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 1.0,
            imageSize: process.env.OPENAI_IMAGE_SIZE || '1024x1024',
            imageQuality: process.env.OPENAI_IMAGE_QUALITY || 'standard',
            imageStyle: process.env.OPENAI_IMAGE_STYLE || 'natural',
        },
        replicate: {
            apiToken: process.env.REPLICATE_API_TOKEN,
            model: process.env.REPLICATE_MODEL || 'stability-ai/stable-diffusion',
            maxWaitTime: parseInt(process.env.REPLICATE_MAX_WAIT_TIME, 10) || 300000,
        },
        general: {
            fallbackToCanvas: process.env.AI_FALLBACK_TO_CANVAS !== 'false',
            enablePixelAnalysis: process.env.AI_ENABLE_PIXEL_ANALYSIS !== 'false',
            defaultTimeout: parseInt(process.env.AI_DEFAULT_TIMEOUT, 10) || 30000,
            maxRetries: parseInt(process.env.AI_MAX_RETRIES, 10) || 3,
        },
    },
    zoo: {
        maxAnimalsPerUser: parseInt(process.env.ZOO_MAX_ANIMALS_PER_USER, 10) || 10,
        maxAnimalsPerScene: parseInt(process.env.ZOO_MAX_ANIMALS_PER_SCENE, 10) || 50,
        interactionCooldownMs: parseInt(process.env.ZOO_INTERACTION_COOLDOWN_MS, 10) || 30000,
        worldWidth: parseInt(process.env.ZOO_WORLD_WIDTH, 10) || 3200,
        worldHeight: parseInt(process.env.ZOO_WORLD_HEIGHT, 10) || 2400,
        enableAIBehavior: process.env.ZOO_ENABLE_AI_BEHAVIOR !== 'false',
        behaviorUpdateIntervalMs: parseInt(process.env.ZOO_BEHAVIOR_UPDATE_INTERVAL_MS, 10) || 5000,
    },
});
//# sourceMappingURL=configuration.js.map