declare const _default: () => {
    app: {
        nodeEnv: string;
        port: number;
        version: string;
    };
    database: {
        host: string;
        port: number;
        name: string;
        username: string;
        password: string;
        sync: boolean;
    };
    mongodb: {
        uri: string;
    };
    redis: {
        host: string;
        port: number;
        password: string;
        db: number;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    oauth: {
        google: {
            clientId: string;
            clientSecret: string;
        };
        wechat: {
            appId: string;
            appSecret: string;
        };
    };
    aliyun: {
        accessKeyId: string;
        accessKeySecret: string;
        oss: {
            bucket: string;
            region: string;
        };
    };
    contentSecurity: {
        enabled: boolean;
        endpoint: string;
    };
    logging: {
        level: string;
        dir: string;
    };
    throttle: {
        ttl: number;
        limit: number;
    };
    upload: {
        maxFileSize: number;
        uploadDir: string;
    };
    email: {
        smtp: {
            host: string;
            port: number;
            user: string;
            pass: string;
        };
    };
    ai: {
        gemini: {
            apiKey: string;
            model: string;
            imageModel: string;
            maxTokens: number;
            temperature: number;
            topP: number;
            topK: number;
        };
        openai: {
            apiKey: string;
            model: string;
            maxTokens: number;
            temperature: number;
            imageSize: string;
            imageQuality: string;
            imageStyle: string;
        };
        replicate: {
            apiToken: string;
            model: string;
            maxWaitTime: number;
        };
        general: {
            fallbackToCanvas: boolean;
            enablePixelAnalysis: boolean;
            defaultTimeout: number;
            maxRetries: number;
        };
    };
    zoo: {
        maxAnimalsPerUser: number;
        maxAnimalsPerScene: number;
        interactionCooldownMs: number;
        worldWidth: number;
        worldHeight: number;
        enableAIBehavior: boolean;
        behaviorUpdateIntervalMs: number;
    };
};
export default _default;
