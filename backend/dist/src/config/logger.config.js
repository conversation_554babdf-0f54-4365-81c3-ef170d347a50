"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggerConfig = void 0;
const winston = require("winston");
const path = require("path");
const logDir = process.env.LOG_DIR || './logs';
const nodeEnv = process.env.NODE_ENV || 'development';
const logFormat = winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.errors({ stack: true }), winston.format.printf(({ timestamp, level, message, stack, context }) => {
    let log = `${timestamp} [${level.toUpperCase()}]`;
    if (context)
        log += ` [${context}]`;
    log += ` ${message}`;
    if (stack)
        log += `\n${stack}`;
    return log;
}));
const consoleFormat = winston.format.combine(winston.format.colorize({ all: true }), winston.format.timestamp({ format: 'HH:mm:ss' }), winston.format.printf(({ timestamp, level, message, context }) => {
    let log = `${timestamp} [${level}]`;
    if (context)
        log += ` [${context}]`;
    return `${log} ${message}`;
}));
exports.loggerConfig = {
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    transports: [
        new winston.transports.Console({
            format: nodeEnv === 'development' ? consoleFormat : logFormat,
        }),
        new winston.transports.File({
            filename: path.join(logDir, 'error.log'),
            level: 'error',
            maxsize: 10 * 1024 * 1024,
            maxFiles: 5,
        }),
        new winston.transports.File({
            filename: path.join(logDir, 'combined.log'),
            maxsize: 10 * 1024 * 1024,
            maxFiles: 10,
        }),
    ],
    exceptionHandlers: [
        new winston.transports.File({
            filename: path.join(logDir, 'exceptions.log'),
        }),
    ],
    rejectionHandlers: [
        new winston.transports.File({
            filename: path.join(logDir, 'rejections.log'),
        }),
    ],
};
//# sourceMappingURL=logger.config.js.map