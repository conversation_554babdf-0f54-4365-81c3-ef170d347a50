"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisConfig = void 0;
const redisStore = require("cache-manager-redis-store");
const redisConfig = () => ({
    store: redisStore,
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB) || 0,
    ttl: 300,
    max: 1000,
    isGlobal: true,
});
exports.redisConfig = redisConfig;
//# sourceMappingURL=redis.config.js.map