"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIGS = exports.SystemConfigSchema = exports.SystemConfig = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let SystemConfig = class SystemConfig {
};
exports.SystemConfig = SystemConfig;
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "key", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], SystemConfig.prototype, "value", void 0);
__decorate([
    (0, mongoose_1.Prop)({ maxlength: 500 }),
    __metadata("design:type", String)
], SystemConfig.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], SystemConfig.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], SystemConfig.prototype, "createdAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], SystemConfig.prototype, "updatedAt", void 0);
exports.SystemConfig = SystemConfig = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'system_configs',
        timestamps: true,
    })
], SystemConfig);
exports.SystemConfigSchema = mongoose_1.SchemaFactory.createForClass(SystemConfig);
exports.DEFAULT_CONFIGS = {
    TEST_QUESTIONS: {
        key: 'test_questions',
        value: {
            version: 'v1.0',
            questions: [
                {
                    id: 'q1',
                    type: 'single',
                    category: 'workload',
                    question: '你通常每周的工作时间是多少？',
                    options: [
                        { id: 'a', text: '35小时以内', score: 20 },
                        { id: 'b', text: '35-45小时', score: 40 },
                        { id: 'c', text: '45-55小时', score: 60 },
                        { id: 'd', text: '55-65小时', score: 80 },
                        { id: 'e', text: '65小时以上', score: 100 },
                    ],
                },
                {
                    id: 'q2',
                    type: 'single',
                    category: 'stress',
                    question: '面对紧急任务时，你的反应通常是？',
                    options: [
                        { id: 'a', text: '淡定处理，按部就班', score: 20 },
                        { id: 'b', text: '稍微紧张，但能控制', score: 40 },
                        { id: 'c', text: '比较焦虑，会影响发挥', score: 60 },
                        { id: 'd', text: '非常紧张，容易出错', score: 80 },
                        { id: 'e', text: '压力山大，经常失眠', score: 100 },
                    ],
                },
                {
                    id: 'q3',
                    type: 'single',
                    category: 'creativity',
                    question: '在工作中，你更倾向于？',
                    options: [
                        { id: 'a', text: '严格按照流程执行', score: 20 },
                        { id: 'b', text: '偶尔会有小创新', score: 40 },
                        { id: 'c', text: '经常提出改进建议', score: 60 },
                        { id: 'd', text: '喜欢尝试新方法', score: 80 },
                        { id: 'e', text: '总是在寻找突破', score: 100 },
                    ],
                },
                {
                    id: 'q4',
                    type: 'single',
                    category: 'leadership',
                    question: '在团队项目中，你通常？',
                    options: [
                        { id: 'a', text: '默默执行分配的任务', score: 20 },
                        { id: 'b', text: '偶尔提供建议', score: 40 },
                        { id: 'c', text: '积极参与讨论', score: 60 },
                        { id: 'd', text: '经常主导项目方向', score: 80 },
                        { id: 'e', text: '天然的团队领袖', score: 100 },
                    ],
                },
                {
                    id: 'q5',
                    type: 'single',
                    category: 'social',
                    question: '公司聚餐时，你会？',
                    options: [
                        { id: 'a', text: '能避免就避免', score: 20 },
                        { id: 'b', text: '去了也不怎么说话', score: 40 },
                        { id: 'c', text: '和熟悉的同事聊天', score: 60 },
                        { id: 'd', text: '主动和大家交流', score: 80 },
                        { id: 'e', text: '是聚餐的活跃分子', score: 100 },
                    ],
                },
                {
                    id: 'q6',
                    type: 'single',
                    category: 'patience',
                    question: '遇到重复性工作时，你会？',
                    options: [
                        { id: 'a', text: '很快就感到厌烦', score: 20 },
                        { id: 'b', text: '坚持一段时间就想逃避', score: 40 },
                        { id: 'c', text: '能够忍受，但不喜欢', score: 60 },
                        { id: 'd', text: '认真完成，寻找乐趣', score: 80 },
                        { id: 'e', text: '享受这种稳定感', score: 100 },
                    ],
                },
            ],
        },
        description: '打工人分类测试题库',
    },
    ANIMAL_DESCRIPTIONS: {
        key: 'animal_descriptions',
        value: {
            [AnimalSpecies.QILIN_DEER]: {
                title: '麒麟鹿 - 职场智者',
                description: '你是职场中的智者，拥有超凡的智慧和淡定的心境。同事们都把你当作大神，遇到难题总是第一个想到你。你不仅能力出众，还乐于分享经验，是团队中不可或缺的精神领袖。',
                traits: ['智慧超群', '心境淡定', '乐于助人', '受人敬仰'],
                advice: '继续保持你的智慧和淡定，但也要注意不要过于完美主义，适当放松自己。',
                career: ['技术专家', '项目经理', '咨询顾问', '团队leader'],
            },
            [AnimalSpecies.OX]: {
                title: '黄牛 - 职场中坚',
                description: '你是团队中最可靠的存在，任劳任怨，承担着最多的工作量。虽然有时会感到疲惫，但你的坚持和努力是团队成功的重要保障。记住，你的付出大家都看在眼里。',
                traits: ['任劳任怨', '责任心强', '执行力好', '默默奉献'],
                advice: '学会适当说不，合理分配工作量，注意劳逸结合，你的健康同样重要。',
                career: ['项目执行', '运营专员', '客户服务', '质量控制'],
            },
        },
        description: '动物类型详细描述配置',
    },
    RANKING_RULES: {
        key: 'ranking_rules',
        value: {
            rules: [
                {
                    type: 'overtime_king',
                    name: '加班王',
                    description: '工作最拼命的打工人',
                    formula: 'workload_score * 0.6 + stress_score * 0.4',
                    updateFrequency: 'daily',
                },
                {
                    type: 'social_star',
                    name: '社交达人',
                    description: '最受欢迎的动物',
                    formula: 'like_count * 2 + comment_count * 1 + feed_count * 3',
                    updateFrequency: 'hourly',
                },
                {
                    type: 'content_creator',
                    name: '整活达人',
                    description: '最会发段子的打工人',
                    formula: 'post_count * 5 + total_likes * 2 + share_count * 3',
                    updateFrequency: 'daily',
                },
            ],
        },
        description: '排行榜计算规则配置',
    },
};
//# sourceMappingURL=system-config.schema.js.map