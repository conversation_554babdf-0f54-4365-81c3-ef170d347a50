import { Document, Types } from 'mongoose';
export type PostDocument = Post & Document;
export declare enum PostStatus {
    PUBLISHED = 1,
    DRAFT = 2,
    REVIEWING = 3,
    REJECTED = 4,
    DELETED = 5
}
export declare enum PostType {
    TEXT = "text",
    IMAGE = "image",
    VIDEO = "video",
    MIXED = "mixed"
}
export declare class Post {
    userId: string;
    content: string;
    type: PostType;
    images?: string[];
    videos?: string[];
    tags?: string[];
    isAnonymous: boolean;
    likeCount: number;
    commentCount: number;
    shareCount: number;
    viewCount: number;
    status: PostStatus;
    aiModeration?: {
        score: number;
        labels: string[];
        suggestion: 'pass' | 'review' | 'block';
        checkedAt: Date;
    };
    location?: {
        name: string;
        coordinates: [number, number];
    };
    metadata?: {
        device?: string;
        platform?: string;
        version?: string;
        ip?: string;
    };
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date;
}
export declare const PostSchema: import("mongoose").Schema<Post, import("mongoose").Model<Post, any, any, any, Document<unknown, any, Post> & Post & {
    _id: Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Post, Document<unknown, {}, import("mongoose").FlatRecord<Post>> & import("mongoose").FlatRecord<Post> & {
    _id: Types.ObjectId;
}>;
