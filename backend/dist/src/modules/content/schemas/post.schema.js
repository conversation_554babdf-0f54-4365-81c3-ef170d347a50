"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostSchema = exports.Post = exports.PostType = exports.PostStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
var PostStatus;
(function (PostStatus) {
    PostStatus[PostStatus["PUBLISHED"] = 1] = "PUBLISHED";
    PostStatus[PostStatus["DRAFT"] = 2] = "DRAFT";
    PostStatus[PostStatus["REVIEWING"] = 3] = "REVIEWING";
    PostStatus[PostStatus["REJECTED"] = 4] = "REJECTED";
    PostStatus[PostStatus["DELETED"] = 5] = "DELETED";
})(PostStatus || (exports.PostStatus = PostStatus = {}));
var PostType;
(function (PostType) {
    PostType["TEXT"] = "text";
    PostType["IMAGE"] = "image";
    PostType["VIDEO"] = "video";
    PostType["MIXED"] = "mixed";
})(PostType || (exports.PostType = PostType = {}));
let Post = class Post {
};
exports.Post = Post;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], Post.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, maxlength: 2000 }),
    __metadata("design:type", String)
], Post.prototype, "content", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        enum: PostType,
        default: PostType.TEXT,
    }),
    __metadata("design:type", String)
], Post.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], Post.prototype, "images", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], Post.prototype, "videos", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], Post.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Post.prototype, "isAnonymous", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0, index: true }),
    __metadata("design:type", Number)
], Post.prototype, "likeCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Post.prototype, "commentCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Post.prototype, "shareCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Post.prototype, "viewCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: Number,
        enum: PostStatus,
        default: PostStatus.PUBLISHED,
        index: true,
    }),
    __metadata("design:type", Number)
], Post.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: Object,
        default: null,
    }),
    __metadata("design:type", Object)
], Post.prototype, "aiModeration", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: Object,
        default: null,
    }),
    __metadata("design:type", Object)
], Post.prototype, "location", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: Object,
        default: {},
    }),
    __metadata("design:type", Object)
], Post.prototype, "metadata", void 0);
__decorate([
    (0, mongoose_1.Prop)({ index: true }),
    __metadata("design:type", Date)
], Post.prototype, "createdAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Post.prototype, "updatedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ index: true, sparse: true }),
    __metadata("design:type", Date)
], Post.prototype, "deletedAt", void 0);
exports.Post = Post = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'posts',
        timestamps: true,
        toJSON: {
            transform: (doc, ret) => {
                ret.id = ret._id;
                delete ret._id;
                delete ret.__v;
                return ret;
            },
        },
    })
], Post);
exports.PostSchema = mongoose_1.SchemaFactory.createForClass(Post);
exports.PostSchema.index({ userId: 1, createdAt: -1 });
exports.PostSchema.index({ tags: 1, status: 1 });
exports.PostSchema.index({ createdAt: -1, likeCount: -1 });
exports.PostSchema.index({ status: 1, createdAt: -1 });
exports.PostSchema.index({ 'location.coordinates': '2dsphere' });
exports.PostSchema.virtual('id').get(function () {
    return this._id.toHexString();
});
exports.PostSchema.pre(['find', 'findOne', 'findOneAndUpdate'], function () {
    this.where({ deletedAt: { $exists: false } });
});
exports.PostSchema.pre('countDocuments', function () {
    this.where({ deletedAt: { $exists: false } });
});
//# sourceMappingURL=post.schema.js.map