import { Document } from 'mongoose';
export type SystemConfigDocument = SystemConfig & Document;
export declare class SystemConfig {
    key: string;
    value: any;
    description?: string;
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const SystemConfigSchema: import("mongoose").Schema<SystemConfig, import("mongoose").Model<SystemConfig, any, any, any, Document<unknown, any, SystemConfig> & SystemConfig & {
    _id: import("mongoose").Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, SystemConfig, Document<unknown, {}, import("mongoose").FlatRecord<SystemConfig>> & import("mongoose").FlatRecord<SystemConfig> & {
    _id: import("mongoose").Types.ObjectId;
}>;
export declare const DEFAULT_CONFIGS: {
    TEST_QUESTIONS: {
        key: string;
        value: {
            version: string;
            questions: {
                id: string;
                type: string;
                category: string;
                question: string;
                options: {
                    id: string;
                    text: string;
                    score: number;
                }[];
            }[];
        };
        description: string;
    };
    ANIMAL_DESCRIPTIONS: {
        key: string;
        value: {
            qilin_deer: {
                title: string;
                description: string;
                traits: string[];
                advice: string;
                career: string[];
            };
            ox: {
                title: string;
                description: string;
                traits: string[];
                advice: string;
                career: string[];
            };
        };
        description: string;
    };
    RANKING_RULES: {
        key: string;
        value: {
            rules: {
                type: string;
                name: string;
                description: string;
                formula: string;
                updateFrequency: string;
            }[];
        };
        description: string;
    };
};
