import { User } from '../../user/entities/user.entity';
export declare enum InteractionType {
    LIKE = 1,
    FEED = 2,
    FOLLOW = 3
}
export declare class Interaction {
    id: string;
    userId: string;
    targetUserId: string;
    interactionType: InteractionType;
    createdDate: Date;
    metadata?: {
        message?: string;
        amount?: number;
        location?: {
            x: number;
            y: number;
            z: number;
        };
    };
    createdAt: Date;
    user: User;
    targetUser: User;
}
