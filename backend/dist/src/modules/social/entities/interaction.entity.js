"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Interaction = exports.InteractionType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
var InteractionType;
(function (InteractionType) {
    InteractionType[InteractionType["LIKE"] = 1] = "LIKE";
    InteractionType[InteractionType["FEED"] = 2] = "FEED";
    InteractionType[InteractionType["FOLLOW"] = 3] = "FOLLOW";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
let Interaction = class Interaction {
};
exports.Interaction = Interaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Interaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], Interaction.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], Interaction.prototype, "targetUserId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: InteractionType,
    }),
    __metadata("design:type", Number)
], Interaction.prototype, "interactionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', default: () => 'CURRENT_DATE' }),
    __metadata("design:type", Date)
], Interaction.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Interaction.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Interaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.interactions, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], Interaction.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.receivedInteractions, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'targetUserId' }),
    __metadata("design:type", user_entity_1.User)
], Interaction.prototype, "targetUser", void 0);
exports.Interaction = Interaction = __decorate([
    (0, typeorm_1.Entity)('interactions'),
    (0, typeorm_1.Index)(['targetUserId', 'interactionType', 'createdAt']),
    (0, typeorm_1.Index)(['userId', 'interactionType', 'createdAt']),
    (0, typeorm_1.Unique)(['userId', 'targetUserId', 'interactionType', 'createdDate'])
], Interaction);
//# sourceMappingURL=interaction.entity.js.map