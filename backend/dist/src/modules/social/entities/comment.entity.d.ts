import { User } from '../../user/entities/user.entity';
export declare enum ContentType {
    USER = "user",
    POST = "post",
    COMMENT = "comment"
}
export declare enum CommentStatus {
    ACTIVE = 1,
    HIDDEN = 2,
    DELETED = 3
}
export declare class Comment {
    id: string;
    userId: string;
    contentId: string;
    contentType: ContentType;
    content: string;
    likeCount: number;
    replyCount: number;
    status: CommentStatus;
    metadata?: {
        mentions?: string[];
        hashtags?: string[];
        emotions?: string[];
        images?: string[];
    };
    createdAt: Date;
    updatedAt: Date;
    user: User;
    parent?: Comment;
    children?: Comment[];
}
