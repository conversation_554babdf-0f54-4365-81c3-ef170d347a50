import { Request } from 'express';
import { AuthService } from './auth.service';
import { CurrentUserInfo } from '@/common/decorators/current-user.decorator';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<AuthResponseDto>;
    login(loginDto: LoginDto, req: Request): Promise<AuthResponseDto>;
    refreshTokens(refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto>;
    logout(user: CurrentUserInfo): Promise<{
        message: string;
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    getProfile(user: CurrentUserInfo): Promise<CurrentUserInfo>;
    googleAuth(): Promise<void>;
    googleAuthCallback(req: Request): Promise<void>;
    wechatAuth(): Promise<void>;
    wechatAuthCallback(req: Request): Promise<void>;
}
