"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt = require("bcryptjs");
const auth_service_1 = require("./auth.service");
const user_entity_1 = require("../user/entities/user.entity");
const user_profile_entity_1 = require("../user/entities/user-profile.entity");
const test_data_factory_1 = require("../../../test/utils/test-data-factory");
const common_1 = require("@nestjs/common");
jest.mock('bcryptjs');
const mockedBcrypt = bcrypt;
describe('AuthService', () => {
    let service;
    let userRepository;
    let userProfileRepository;
    let jwtService;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                auth_service_1.AuthService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(user_entity_1.User),
                    useValue: {
                        findOne: jest.fn(),
                        create: jest.fn(),
                        save: jest.fn(),
                        update: jest.fn(),
                    },
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(user_profile_entity_1.UserProfile),
                    useValue: {
                        create: jest.fn(),
                        save: jest.fn(),
                    },
                },
                {
                    provide: jwt_1.JwtService,
                    useValue: {
                        sign: jest.fn(),
                        verify: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(auth_service_1.AuthService);
        userRepository = module.get((0, typeorm_1.getRepositoryToken)(user_entity_1.User));
        userProfileRepository = module.get((0, typeorm_1.getRepositoryToken)(user_profile_entity_1.UserProfile));
        jwtService = module.get(jwt_1.JwtService);
    });
    describe('register', () => {
        it('应该成功注册新用户', async () => {
            const registerDto = test_data_factory_1.TestDataFactory.createRegisterDto();
            const hashedPassword = 'hashed_password';
            const user = test_data_factory_1.TestDataFactory.createUser({
                username: registerDto.username,
                email: registerDto.email,
                passwordHash: hashedPassword,
            });
            const profile = test_data_factory_1.TestDataFactory.createUserProfile(user.id);
            userRepository.findOne.mockResolvedValue(null);
            mockedBcrypt.hash.mockResolvedValue(hashedPassword);
            userRepository.create.mockReturnValue(user);
            userRepository.save.mockResolvedValue(user);
            userProfileRepository.create.mockReturnValue(profile);
            userProfileRepository.save.mockResolvedValue(profile);
            jwtService.sign.mockReturnValue('jwt_token');
            const result = await service.register(registerDto);
            expect(userRepository.findOne).toHaveBeenCalledWith({
                where: [
                    { email: registerDto.email },
                    { username: registerDto.username },
                ],
            });
            expect(mockedBcrypt.hash).toHaveBeenCalledWith(registerDto.password, 12);
            expect(userRepository.save).toHaveBeenCalledWith(user);
            expect(userProfileRepository.save).toHaveBeenCalledWith(profile);
            expect(result).toEqual({
                user: expect.objectContaining({
                    id: user.id,
                    username: user.username,
                    email: user.email,
                }),
                accessToken: 'jwt_token',
            });
        });
        it('应该抛出冲突异常当用户已存在', async () => {
            const registerDto = test_data_factory_1.TestDataFactory.createRegisterDto();
            const existingUser = test_data_factory_1.TestDataFactory.createUser();
            userRepository.findOne.mockResolvedValue(existingUser);
            await expect(service.register(registerDto)).rejects.toThrow(common_1.ConflictException);
        });
        it('应该抛出错误当密码不匹配', async () => {
            const registerDto = test_data_factory_1.TestDataFactory.createRegisterDto({
                password: 'password1',
                confirmPassword: 'password2',
            });
            await expect(service.register(registerDto)).rejects.toThrow('密码确认不匹配');
        });
    });
    describe('login', () => {
        it('应该成功登录用户', async () => {
            const loginDto = test_data_factory_1.TestDataFactory.createLoginDto();
            const user = test_data_factory_1.TestDataFactory.createUser({
                email: loginDto.email,
                passwordHash: 'hashed_password',
            });
            userRepository.findOne.mockResolvedValue(user);
            mockedBcrypt.compare.mockResolvedValue(true);
            jwtService.sign.mockReturnValue('jwt_token');
            const result = await service.login(loginDto);
            expect(userRepository.findOne).toHaveBeenCalledWith({
                where: { email: loginDto.email },
                relations: ['profile'],
            });
            expect(mockedBcrypt.compare).toHaveBeenCalledWith(loginDto.password, user.passwordHash);
            expect(result).toEqual({
                user: expect.objectContaining({
                    id: user.id,
                    email: user.email,
                }),
                accessToken: 'jwt_token',
            });
        });
        it('应该抛出未授权异常当用户不存在', async () => {
            const loginDto = test_data_factory_1.TestDataFactory.createLoginDto();
            userRepository.findOne.mockResolvedValue(null);
            await expect(service.login(loginDto)).rejects.toThrow(common_1.UnauthorizedException);
        });
        it('应该抛出未授权异常当密码错误', async () => {
            const loginDto = test_data_factory_1.TestDataFactory.createLoginDto();
            const user = test_data_factory_1.TestDataFactory.createUser({
                email: loginDto.email,
                passwordHash: 'hashed_password',
            });
            userRepository.findOne.mockResolvedValue(user);
            mockedBcrypt.compare.mockResolvedValue(false);
            await expect(service.login(loginDto)).rejects.toThrow(common_1.UnauthorizedException);
        });
    });
    describe('validateUser', () => {
        it('应该返回用户当凭据有效', async () => {
            const email = '<EMAIL>';
            const password = 'password';
            const user = test_data_factory_1.TestDataFactory.createUser({
                email,
                passwordHash: 'hashed_password',
            });
            userRepository.findOne.mockResolvedValue(user);
            mockedBcrypt.compare.mockResolvedValue(true);
            const result = await service.validateUser(email, password);
            expect(result).toEqual(expect.objectContaining({
                id: user.id,
                email: user.email,
            }));
            expect(result.passwordHash).toBeUndefined();
        });
        it('应该返回null当凭据无效', async () => {
            const email = '<EMAIL>';
            const password = 'password';
            const user = test_data_factory_1.TestDataFactory.createUser({
                email,
                passwordHash: 'hashed_password',
            });
            userRepository.findOne.mockResolvedValue(user);
            mockedBcrypt.compare.mockResolvedValue(false);
            const result = await service.validateUser(email, password);
            expect(result).toBeNull();
        });
    });
    describe('refreshToken', () => {
        it('应该返回新的访问令牌', async () => {
            const refreshToken = 'refresh_token';
            const payload = { sub: 'user_id', email: '<EMAIL>' };
            const user = test_data_factory_1.TestDataFactory.createUser({
                id: payload.sub,
                email: payload.email,
            });
            jwtService.verify.mockReturnValue(payload);
            userRepository.findOne.mockResolvedValue(user);
            jwtService.sign.mockReturnValue('new_access_token');
            const result = await service.refreshToken(refreshToken);
            expect(jwtService.verify).toHaveBeenCalledWith(refreshToken);
            expect(userRepository.findOne).toHaveBeenCalledWith({
                where: { id: payload.sub },
            });
            expect(result).toEqual({
                accessToken: 'new_access_token',
            });
        });
        it('应该抛出未授权异常当令牌无效', async () => {
            const refreshToken = 'invalid_token';
            jwtService.verify.mockImplementation(() => {
                throw new Error('Invalid token');
            });
            await expect(service.refreshToken(refreshToken)).rejects.toThrow(common_1.UnauthorizedException);
        });
    });
});
//# sourceMappingURL=auth.service.spec.js.map