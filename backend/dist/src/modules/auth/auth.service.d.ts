import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { Cache } from 'cache-manager';
import { User } from '../user/entities/user.entity';
import { UserProfile } from '../user/entities/user-profile.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { CurrentUserInfo } from '@/common/decorators/current-user.decorator';
export declare class AuthService {
    private userRepository;
    private userProfileRepository;
    private jwtService;
    private configService;
    private cacheManager;
    constructor(userRepository: Repository<User>, userProfileRepository: Repository<UserProfile>, jwtService: JwtService, configService: ConfigService, cacheManager: Cache);
    register(registerDto: RegisterDto): Promise<AuthResponseDto>;
    login(loginDto: LoginDto, context?: {
        userAgent: string;
        ip: string;
    }): Promise<AuthResponseDto>;
    refreshTokens(refreshToken: string): Promise<AuthResponseDto>;
    logout(userId: string): Promise<void>;
    forgotPassword(email: string): Promise<void>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void>;
    getProfile(userId: string): Promise<CurrentUserInfo>;
    private generateTokens;
    private recordLoginActivity;
}
