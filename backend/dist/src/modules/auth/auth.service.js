"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cache_manager_1 = require("@nestjs/cache-manager");
const bcrypt = require("bcryptjs");
const uuid_1 = require("uuid");
const user_entity_1 = require("../user/entities/user.entity");
const user_profile_entity_1 = require("../user/entities/user-profile.entity");
let AuthService = class AuthService {
    constructor(userRepository, userProfileRepository, jwtService, configService, cacheManager) {
        this.userRepository = userRepository;
        this.userProfileRepository = userProfileRepository;
        this.jwtService = jwtService;
        this.configService = configService;
        this.cacheManager = cacheManager;
    }
    async register(registerDto) {
        const { username, email, password, phone } = registerDto;
        const existingUser = await this.userRepository.findOne({
            where: [{ username }, { email }, ...(phone ? [{ phone }] : [])],
        });
        if (existingUser) {
            throw new common_1.ConflictException('用户名、邮箱或手机号已存在');
        }
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        const user = this.userRepository.create({
            username,
            email,
            phone,
            passwordHash,
            authProvider: user_entity_1.AuthProvider.LOCAL,
            status: user_entity_1.UserStatus.ACTIVE,
        });
        const savedUser = await this.userRepository.save(user);
        const profile = this.userProfileRepository.create({
            user: savedUser,
            points: 100,
            level: 1,
            settings: {
                privacy: {
                    profileVisibility: 'public',
                    allowMessages: true,
                    allowFriendRequests: true,
                },
                notifications: {
                    email: true,
                    push: true,
                    likes: true,
                    comments: true,
                    feeds: true,
                    system: true,
                },
            },
        });
        await this.userProfileRepository.save(profile);
        const tokens = await this.generateTokens(savedUser);
        return {
            user: {
                id: savedUser.id,
                username: savedUser.username,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatarUrl: savedUser.avatarUrl,
                roles: ['user'],
            },
            ...tokens,
        };
    }
    async login(loginDto, context) {
        const { identifier, password } = loginDto;
        const user = await this.userRepository
            .createQueryBuilder('user')
            .addSelect('user.passwordHash')
            .where('user.username = :identifier', { identifier })
            .orWhere('user.email = :identifier', { identifier })
            .getOne();
        if (!user) {
            throw new common_1.UnauthorizedException('登录凭证无效');
        }
        if (user.status !== user_entity_1.UserStatus.ACTIVE) {
            throw new common_1.UnauthorizedException('账号已被禁用');
        }
        const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('登录凭证无效');
        }
        if (context) {
            await this.recordLoginActivity(user.id, context);
        }
        const tokens = await this.generateTokens(user);
        return {
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                nickname: user.nickname,
                avatarUrl: user.avatarUrl,
                roles: ['user'],
            },
            ...tokens,
        };
    }
    async refreshTokens(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
            });
            const isBlacklisted = await this.cacheManager.get(`blacklist:${refreshToken}`);
            if (isBlacklisted) {
                throw new common_1.UnauthorizedException('刷新令牌已失效');
            }
            const user = await this.userRepository.findOne({
                where: { id: payload.sub },
            });
            if (!user || user.status !== user_entity_1.UserStatus.ACTIVE) {
                throw new common_1.UnauthorizedException('用户不存在或已被禁用');
            }
            const refreshTTL = 7 * 24 * 60 * 60;
            await this.cacheManager.set(`blacklist:${refreshToken}`, true, refreshTTL);
            const tokens = await this.generateTokens(user);
            return {
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    nickname: user.nickname,
                    avatarUrl: user.avatarUrl,
                    roles: ['user'],
                },
                ...tokens,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('刷新令牌无效');
        }
    }
    async logout(userId) {
        await this.cacheManager.del(`user_session:${userId}`);
    }
    async forgotPassword(email) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const resetToken = (0, uuid_1.v4)();
        const expiry = new Date();
        expiry.setHours(expiry.getHours() + 1);
        await this.cacheManager.set(`reset_token:${resetToken}`, { userId: user.id, email }, 3600);
        console.log(`Reset token for ${email}: ${resetToken}`);
    }
    async resetPassword(resetPasswordDto) {
        const { token, newPassword } = resetPasswordDto;
        const resetData = await this.cacheManager.get(`reset_token:${token}`);
        if (!resetData) {
            throw new common_1.BadRequestException('重置令牌无效或已过期');
        }
        const { userId } = resetData;
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(newPassword, saltRounds);
        await this.userRepository.update(userId, { passwordHash });
        await this.cacheManager.del(`reset_token:${token}`);
        await this.cacheManager.del(`user_session:${userId}`);
    }
    async getProfile(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['profile'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            roles: ['user'],
        };
    }
    async generateTokens(user) {
        const payload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            roles: ['user'],
        };
        const accessToken = this.jwtService.sign(payload, {
            secret: this.configService.get('JWT_SECRET'),
            expiresIn: this.configService.get('JWT_EXPIRES_IN', '1h'),
        });
        const refreshToken = this.jwtService.sign({ sub: user.id }, {
            secret: this.configService.get('JWT_REFRESH_SECRET'),
            expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
        });
        return {
            accessToken,
            refreshToken,
            expiresIn: 3600,
        };
    }
    async recordLoginActivity(userId, context) {
        const loginRecord = {
            userId,
            timestamp: new Date(),
            userAgent: context.userAgent,
            ip: context.ip,
        };
        await this.cacheManager.set(`last_login:${userId}`, loginRecord, 24 * 60 * 60);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_profile_entity_1.UserProfile)),
    __param(4, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        config_1.ConfigService, Object])
], AuthService);
//# sourceMappingURL=auth.service.js.map