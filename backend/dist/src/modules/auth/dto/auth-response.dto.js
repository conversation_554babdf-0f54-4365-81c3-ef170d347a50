"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthResponseDto = exports.UserInfo = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserInfo {
}
exports.UserInfo = UserInfo;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户ID',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], UserInfo.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户名',
        example: 'john_doe',
    }),
    __metadata("design:type", String)
], UserInfo.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '邮箱',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], UserInfo.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '昵称',
        example: '打工人小王',
    }),
    __metadata("design:type", String)
], UserInfo.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '头像URL',
        example: 'https://example.com/avatar.jpg',
    }),
    __metadata("design:type", String)
], UserInfo.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户角色',
        example: ['user'],
        isArray: true,
    }),
    __metadata("design:type", Array)
], UserInfo.prototype, "roles", void 0);
class AuthResponseDto {
}
exports.AuthResponseDto = AuthResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户信息',
        type: UserInfo,
    }),
    __metadata("design:type", UserInfo)
], AuthResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '访问令牌',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], AuthResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '刷新令牌',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], AuthResponseDto.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '令牌过期时间（秒）',
        example: 3600,
    }),
    __metadata("design:type", Number)
], AuthResponseDto.prototype, "expiresIn", void 0);
//# sourceMappingURL=auth-response.dto.js.map