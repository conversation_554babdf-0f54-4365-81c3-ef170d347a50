"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const mongoose_1 = require("@nestjs/mongoose");
const typeorm_2 = require("typeorm");
const mongoose_2 = require("mongoose");
const config_1 = require("@nestjs/config");
const health_check_dto_1 = require("./dto/health-check.dto");
const Redis = require("redis");
let HealthService = class HealthService {
    constructor(dataSource, mongoConnection, configService) {
        this.dataSource = dataSource;
        this.mongoConnection = mongoConnection;
        this.configService = configService;
        this.redis = Redis.createClient({
            socket: {
                host: this.configService.get('REDIS_HOST'),
                port: this.configService.get('REDIS_PORT'),
            },
            password: this.configService.get('REDIS_PASSWORD'),
        });
    }
    async check() {
        const checks = {
            status: health_check_dto_1.ServiceStatus.HEALTHY,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: this.configService.get('APP_VERSION', '1.0.0'),
            environment: this.configService.get('NODE_ENV', 'development'),
            memory: process.memoryUsage(),
            services: {
                database: health_check_dto_1.ServiceStatus.UNKNOWN,
                mongodb: health_check_dto_1.ServiceStatus.UNKNOWN,
                redis: health_check_dto_1.ServiceStatus.UNKNOWN,
            },
        };
        try {
            await this.dataSource.query('SELECT 1');
            checks.services.database = health_check_dto_1.ServiceStatus.HEALTHY;
        }
        catch (error) {
            checks.services.database = health_check_dto_1.ServiceStatus.UNHEALTHY;
            checks.status = health_check_dto_1.ServiceStatus.UNHEALTHY;
        }
        try {
            if (this.mongoConnection.readyState === 1) {
                checks.services.mongodb = health_check_dto_1.ServiceStatus.HEALTHY;
            }
            else {
                checks.services.mongodb = health_check_dto_1.ServiceStatus.UNHEALTHY;
                checks.status = health_check_dto_1.ServiceStatus.UNHEALTHY;
            }
        }
        catch (error) {
            checks.services.mongodb = health_check_dto_1.ServiceStatus.UNHEALTHY;
            checks.status = health_check_dto_1.ServiceStatus.UNHEALTHY;
        }
        try {
            if (!this.redis.isOpen) {
                await this.redis.connect();
            }
            await this.redis.ping();
            checks.services.redis = health_check_dto_1.ServiceStatus.HEALTHY;
        }
        catch (error) {
            checks.services.redis = health_check_dto_1.ServiceStatus.UNHEALTHY;
            checks.status = health_check_dto_1.ServiceStatus.UNHEALTHY;
        }
        if (checks.status === health_check_dto_1.ServiceStatus.UNHEALTHY) {
            throw new common_1.ServiceUnavailableException('Service is unhealthy');
        }
        return checks;
    }
    async ready() {
        const health = await this.check();
        return {
            status: 'ready',
            timestamp: new Date().toISOString(),
            checks: health.services,
        };
    }
    async live() {
        return {
            status: 'alive',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            pid: process.pid,
        };
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __param(1, (0, mongoose_1.InjectConnection)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        mongoose_2.Connection,
        config_1.ConfigService])
], HealthService);
//# sourceMappingURL=health.service.js.map