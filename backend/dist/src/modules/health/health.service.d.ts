import { DataSource } from 'typeorm';
import { Connection } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { HealthCheckDto, ServiceStatus } from './dto/health-check.dto';
export declare class HealthService {
    private dataSource;
    private mongoConnection;
    private configService;
    private redis;
    constructor(dataSource: DataSource, mongoConnection: Connection, configService: ConfigService);
    check(): Promise<HealthCheckDto>;
    ready(): Promise<{
        status: string;
        timestamp: string;
        checks: {
            database: ServiceStatus;
            mongodb: ServiceStatus;
            redis: ServiceStatus;
        };
    }>;
    live(): Promise<{
        status: string;
        timestamp: string;
        uptime: number;
        pid: number;
    }>;
}
