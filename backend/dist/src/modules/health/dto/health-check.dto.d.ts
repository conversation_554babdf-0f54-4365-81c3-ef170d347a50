export declare enum ServiceStatus {
    HEALTHY = "healthy",
    UNHEALTHY = "unhealthy",
    UNKNOWN = "unknown"
}
export declare class HealthCheckDto {
    status: ServiceStatus;
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    memory: NodeJS.MemoryUsage;
    services: {
        database: ServiceStatus;
        mongodb: ServiceStatus;
        redis: ServiceStatus;
    };
}
