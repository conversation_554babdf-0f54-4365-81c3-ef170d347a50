{"version": 3, "file": "health-check.dto.js", "sourceRoot": "", "sources": ["../../../../../src/modules/health/dto/health-check.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAE9C,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,oCAAmB,CAAA;AACrB,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAED,MAAa,cAAc;CAwD1B;AAxDD,wCAwDC;AAnDC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,IAAI,EAAE,aAAa;KACpB,CAAC;;8CACoB;AAMtB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,OAAO;QACpB,OAAO,EAAE,sBAAsB;KAChC,CAAC;;iDACgB;AAMlB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,IAAI;KACd,CAAC;;8CACa;AAMf;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,OAAO;KACjB,CAAC;;+CACc;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,YAAY;KACtB,CAAC;;mDACkB;AAYpB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE;YACP,GAAG,EAAE,QAAQ;YACb,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,OAAO;YACjB,YAAY,EAAE,KAAK;SACpB;KACF,CAAC;;8CACyB;AAU3B;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE;YACP,QAAQ,EAAE,SAAS;YACnB,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,SAAS;SACjB;KACF,CAAC;;gDAKA"}