"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCheckDto = exports.ServiceStatus = void 0;
const swagger_1 = require("@nestjs/swagger");
var ServiceStatus;
(function (ServiceStatus) {
    ServiceStatus["HEALTHY"] = "healthy";
    ServiceStatus["UNHEALTHY"] = "unhealthy";
    ServiceStatus["UNKNOWN"] = "unknown";
})(ServiceStatus || (exports.ServiceStatus = ServiceStatus = {}));
class HealthCheckDto {
}
exports.HealthCheckDto = HealthCheckDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '服务整体健康状态',
        enum: ServiceStatus,
    }),
    __metadata("design:type", String)
], HealthCheckDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '检查时间戳',
        example: '2025-01-15T10:30:00Z',
    }),
    __metadata("design:type", String)
], HealthCheckDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '服务运行时间（秒）',
        example: 3600,
    }),
    __metadata("design:type", Number)
], HealthCheckDto.prototype, "uptime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '应用版本',
        example: '1.0.0',
    }),
    __metadata("design:type", String)
], HealthCheckDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '运行环境',
        example: 'production',
    }),
    __metadata("design:type", String)
], HealthCheckDto.prototype, "environment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '内存使用情况',
        example: {
            rss: 41943040,
            heapTotal: 7684096,
            heapUsed: 4194304,
            external: 1089536,
            arrayBuffers: 17632,
        },
    }),
    __metadata("design:type", Object)
], HealthCheckDto.prototype, "memory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '各服务健康状态',
        example: {
            database: 'healthy',
            mongodb: 'healthy',
            redis: 'healthy',
        },
    }),
    __metadata("design:type", Object)
], HealthCheckDto.prototype, "services", void 0);
//# sourceMappingURL=health-check.dto.js.map