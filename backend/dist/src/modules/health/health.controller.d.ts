import { HealthService } from './health.service';
import { HealthCheckDto } from './dto/health-check.dto';
export declare class HealthController {
    private readonly healthService;
    constructor(healthService: HealthService);
    check(): Promise<HealthCheckDto>;
    ready(): Promise<{
        status: string;
        timestamp: string;
        checks: {
            database: import("./dto/health-check.dto").ServiceStatus;
            mongodb: import("./dto/health-check.dto").ServiceStatus;
            redis: import("./dto/health-check.dto").ServiceStatus;
        };
    }>;
    live(): Promise<{
        status: string;
        timestamp: string;
        uptime: number;
        pid: number;
    }>;
}
