"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AIService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const generative_ai_1 = require("@google/generative-ai");
const openai_1 = require("openai");
const canvas_1 = require("canvas");
const sharp = require("sharp");
const axios_1 = require("axios");
const animal_types_1 = require("../../common/constants/animal-types");
let AIService = AIService_1 = class AIService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AIService_1.name);
        const aiConfig = this.configService.get('ai');
        const geminiApiKey = aiConfig?.gemini?.apiKey || this.configService.get('GEMINI_API_KEY');
        const openaiApiKey = aiConfig?.openai?.apiKey || this.configService.get('OPENAI_API_KEY');
        if (geminiApiKey) {
            this.genAI = new generative_ai_1.GoogleGenerativeAI(geminiApiKey);
            this.logger.log('Gemini AI service initialized');
        }
        else {
            this.logger.warn('Gemini API key not found');
        }
        if (openaiApiKey) {
            this.openai = new openai_1.OpenAI({
                apiKey: openaiApiKey,
                timeout: aiConfig?.general?.defaultTimeout || 30000,
                maxRetries: aiConfig?.general?.maxRetries || 3,
            });
            this.logger.log('OpenAI service initialized');
        }
        else {
            this.logger.warn('OpenAI API key not found');
        }
        this.logger.log(`AI Service initialized with fallback: ${aiConfig?.general?.fallbackToCanvas !== false}`);
    }
    async analyzeDrawing(imageData) {
        const startTime = Date.now();
        try {
            if (this.genAI) {
                this.logger.log('Using Gemini API for drawing analysis');
                const result = await this.analyzeWithGemini(imageData);
                const processingTime = Date.now() - startTime;
                this.logger.log(`Gemini analysis completed in ${processingTime}ms`);
                return result;
            }
        }
        catch (error) {
            this.logger.warn('Gemini API analysis failed, falling back to pixel analysis:', error.message);
        }
        this.logger.log('Using fallback pixel analysis');
        const result = await this.analyzeWithPixelData(imageData);
        const processingTime = Date.now() - startTime;
        this.logger.log(`Pixel analysis completed in ${processingTime}ms`);
        return result;
    }
    async generateAvatar(imageData, animalType, animalSpecies, analysisResult) {
        const startTime = Date.now();
        try {
            if (this.openai) {
                this.logger.log('Attempting avatar generation with DALL-E 3');
                const result = await this.generateWithDallE(imageData, animalType, animalSpecies, analysisResult);
                const processingTime = Date.now() - startTime;
                this.logger.log(`DALL-E generation completed in ${processingTime}ms`);
                return { ...result, processingTime };
            }
        }
        catch (error) {
            this.logger.warn('DALL-E generation failed, falling back to Canvas:', error.message);
        }
        this.logger.log('Using Canvas fallback generation');
        const result = await this.generateWithCanvas(imageData, animalType, animalSpecies, analysisResult);
        const processingTime = Date.now() - startTime;
        this.logger.log(`Canvas generation completed in ${processingTime}ms`);
        return { ...result, processingTime };
    }
    async generateWorkerAnimalAvatar(userImageData, animalType, animalSpecies, analysisResult, options) {
        const startTime = Date.now();
        const fusionOptions = {
            fusionStrength: 0.7,
            keepUserFeatures: true,
            workEnvironment: 'office',
            clothingStyle: 'formal',
            ...options,
        };
        this.logger.log(`Generating worker animal avatar: ${animalType} with fusion strength ${fusionOptions.fusionStrength}`);
        try {
            let userAnalysis = analysisResult;
            if (!userAnalysis) {
                userAnalysis = await this.analyzeDrawing(userImageData);
            }
            if (this.genAI) {
                try {
                    const geminiResult = await this.generateWithGeminiFlash(userImageData, animalType, animalSpecies, userAnalysis, fusionOptions);
                    if (geminiResult) {
                        const processingTime = Date.now() - startTime;
                        this.logger.log(`Gemini Flash generation completed in ${processingTime}ms`);
                        return { ...geminiResult, processingTime };
                    }
                }
                catch (error) {
                    this.logger.warn('Gemini Flash generation failed:', error.message);
                }
            }
            if (this.openai) {
                try {
                    const dalleResult = await this.generateWorkerAvatarWithDallE(userImageData, animalType, animalSpecies, userAnalysis, fusionOptions);
                    const processingTime = Date.now() - startTime;
                    this.logger.log(`DALL-E worker avatar generation completed in ${processingTime}ms`);
                    return { ...dalleResult, processingTime };
                }
                catch (error) {
                    this.logger.warn('DALL-E worker avatar generation failed:', error.message);
                }
            }
            const canvasResult = await this.generateWorkerAvatarWithCanvas(userImageData, animalType, animalSpecies, userAnalysis, fusionOptions);
            const processingTime = Date.now() - startTime;
            this.logger.log(`Canvas worker avatar generation completed in ${processingTime}ms`);
            return { ...canvasResult, processingTime };
        }
        catch (error) {
            this.logger.error('Worker animal avatar generation failed:', error);
            throw new Error(`动物版头像生成失败: ${error.message}`);
        }
    }
    async analyzeWithGemini(imageData) {
        const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        const imagePart = {
            inlineData: {
                data: userDrawingBase64,
                mimeType: 'image/png'
            }
        };
        const prompt = `请仔细分析这幅用户自画像，描述以下特征：
1. 面部形状（圆脸、长脸、方脸等）
2. 表情特征（微笑、严肃、友善等）
3. 绘画风格（简约、细腻、素描等）
4. 个性特征（通过画风推测性格）
5. 线条特点和绘画复杂度

请用中文回答，格式简洁清晰。

基于以上特征，推荐最适合的动物类型：
- 如果体现出高雅、智慧、领导力特质，推荐神兽类（DIVINE_BEAST）
- 如果体现出可爱、友善、活泼特质，推荐宠物类（PET）
- 如果体现出勤劳、坚韧、务实特质，推荐牛马类（WORKING_ANIMAL）`;
        const result = await model.generateContent([prompt, imagePart]);
        const response = await result.response;
        const analysisText = response.text();
        const faceFeatures = this.extractFaceFeatures(analysisText);
        const detectedAnimalType = this.detectAnimalType(analysisText);
        const recommendedSpecies = this.getRecommendedSpecies(detectedAnimalType, faceFeatures);
        return {
            description: analysisText,
            faceFeatures,
            confidence: 85,
            detectedAnimalType,
            recommendedSpecies,
        };
    }
    async analyzeWithPixelData(imageData) {
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        const imageBuffer = Buffer.from(userDrawingBase64, 'base64');
        const img = await (0, canvas_1.loadImage)(imageBuffer);
        const canvas = (0, canvas_1.createCanvas)(img.width, img.height);
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        const imageDataObj = ctx.getImageData(0, 0, img.width, img.height);
        const pixelAnalysis = this.analyzePixelFeatures(imageDataObj.data, img.width, img.height);
        const faceFeatures = this.extractFeaturesFromPixels(pixelAnalysis);
        const detectedAnimalType = this.detectAnimalTypeFromFeatures(faceFeatures);
        const recommendedSpecies = this.getRecommendedSpecies(detectedAnimalType, faceFeatures);
        return {
            description: this.generateAnalysisDescription(faceFeatures, pixelAnalysis),
            faceFeatures,
            pixelAnalysis,
            confidence: 70,
            detectedAnimalType,
            recommendedSpecies,
        };
    }
    analyzePixelFeatures(pixels, width, height) {
        const totalPixels = width * height;
        let darkPixels = 0;
        let edgePixels = 0;
        const colorCount = {};
        for (let i = 0; i < pixels.length; i += 4) {
            const r = pixels[i];
            const g = pixels[i + 1];
            const b = pixels[i + 2];
            const brightness = (r + g + b) / 3;
            if (brightness < 128) {
                darkPixels++;
            }
            const colorKey = `${Math.floor(r / 32)}_${Math.floor(g / 32)}_${Math.floor(b / 32)}`;
            colorCount[colorKey] = (colorCount[colorKey] || 0) + 1;
        }
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = (y * width + x) * 4;
                const current = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / 3;
                const neighbors = [
                    (pixels[((y - 1) * width + x) * 4] + pixels[((y - 1) * width + x) * 4 + 1] + pixels[((y - 1) * width + x) * 4 + 2]) / 3,
                    (pixels[(y * width + (x - 1)) * 4] + pixels[(y * width + (x - 1)) * 4 + 1] + pixels[(y * width + (x - 1)) * 4 + 2]) / 3,
                    (pixels[(y * width + (x + 1)) * 4] + pixels[(y * width + (x + 1)) * 4 + 1] + pixels[(y * width + (x + 1)) * 4 + 2]) / 3,
                    (pixels[((y + 1) * width + x) * 4] + pixels[((y + 1) * width + x) * 4 + 1] + pixels[((y + 1) * width + x) * 4 + 2]) / 3
                ];
                const maxDiff = Math.max(...neighbors.map(n => Math.abs(current - n)));
                if (maxDiff > 50) {
                    edgePixels++;
                }
            }
        }
        const darkRatio = darkPixels / totalPixels;
        const aspectRatio = width / height;
        const lineComplexity = edgePixels / totalPixels;
        const uniqueColors = Object.keys(colorCount).length;
        const dominantColors = Object.entries(colorCount)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([color]) => color);
        return {
            width,
            height,
            darkRatio,
            aspectRatio,
            lineComplexity,
            uniqueColors,
            dominantColors,
            edgePixels,
        };
    }
    extractFaceFeatures(analysisText) {
        const features = {
            faceShape: 'oval',
            expression: 'neutral',
            style: 'sketch',
            complexity: 'medium',
        };
        if (analysisText.includes('圆脸'))
            features.faceShape = 'round';
        else if (analysisText.includes('方脸'))
            features.faceShape = 'square';
        else if (analysisText.includes('长脸'))
            features.faceShape = 'long';
        else if (analysisText.includes('瓜子脸'))
            features.faceShape = 'heart';
        if (analysisText.includes('疲惫'))
            features.expression = 'tired';
        else if (analysisText.includes('活力') || analysisText.includes('精神'))
            features.expression = 'energetic';
        else if (analysisText.includes('专注') || analysisText.includes('认真'))
            features.expression = 'focused';
        else if (analysisText.includes('友善') || analysisText.includes('温和'))
            features.expression = 'gentle';
        else if (analysisText.includes('微笑') || analysisText.includes('开心'))
            features.expression = 'happy';
        if (analysisText.includes('简约'))
            features.style = 'minimalist';
        else if (analysisText.includes('写实'))
            features.style = 'realistic';
        else if (analysisText.includes('卡通'))
            features.style = 'cartoon';
        if (analysisText.includes('复杂') || analysisText.includes('详细'))
            features.complexity = 'high';
        else if (analysisText.includes('简单') || analysisText.includes('简洁'))
            features.complexity = 'low';
        return features;
    }
    extractFeaturesFromPixels(pixelAnalysis) {
        const features = {
            faceShape: 'oval',
            expression: 'neutral',
            style: 'sketch',
            complexity: 'medium',
            lineCount: pixelAnalysis.edgePixels,
            darkRatio: pixelAnalysis.darkRatio,
        };
        if (pixelAnalysis.aspectRatio > 1.3) {
            features.faceShape = 'long';
        }
        else if (pixelAnalysis.aspectRatio < 0.8) {
            features.faceShape = 'wide';
        }
        else if (pixelAnalysis.lineComplexity < 0.1) {
            features.faceShape = 'round';
        }
        if (pixelAnalysis.lineComplexity > 0.4) {
            features.expression = 'focused';
            features.complexity = 'high';
        }
        else if (pixelAnalysis.lineComplexity > 0.2) {
            features.complexity = 'medium';
        }
        else {
            features.expression = 'gentle';
            features.complexity = 'low';
        }
        if (pixelAnalysis.darkRatio > 0.5) {
            features.expression = 'tired';
        }
        else if (pixelAnalysis.darkRatio < 0.2) {
            features.expression = 'energetic';
        }
        if (pixelAnalysis.uniqueColors > 20) {
            features.style = 'realistic';
        }
        else if (pixelAnalysis.uniqueColors < 10) {
            features.style = 'minimalist';
        }
        return features;
    }
    detectAnimalType(analysisText) {
        if (analysisText.includes('神兽') || analysisText.includes('DIVINE_BEAST') ||
            analysisText.includes('高雅') || analysisText.includes('智慧') ||
            analysisText.includes('领导')) {
            return animal_types_1.AnimalCategory.DIVINE_BEAST;
        }
        if (analysisText.includes('宠物') || analysisText.includes('PET') ||
            analysisText.includes('可爱') || analysisText.includes('友善') ||
            analysisText.includes('活泼')) {
            return animal_types_1.AnimalCategory.PET;
        }
        return animal_types_1.AnimalCategory.WORKING_ANIMAL;
    }
    detectAnimalTypeFromFeatures(faceFeatures) {
        if (faceFeatures.expression === 'energetic' && faceFeatures.style === 'realistic' && faceFeatures.complexity === 'high') {
            return animal_types_1.AnimalCategory.DIVINE_BEAST;
        }
        if (faceFeatures.expression === 'happy' || faceFeatures.expression === 'gentle') {
            return animal_types_1.AnimalCategory.PET;
        }
        return animal_types_1.AnimalCategory.WORKING_ANIMAL;
    }
    getRecommendedSpecies(animalType, features) {
        switch (animalType) {
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                return features.expression === 'energetic' ? animal_types_1.AnimalSpecies.DRAGON : animal_types_1.AnimalSpecies.QILIN_DEER;
            case animal_types_1.AnimalCategory.PET:
                return features.faceShape === 'round' ? animal_types_1.AnimalSpecies.GOLDEN_RETRIEVER : animal_types_1.AnimalSpecies.PERSIAN_CAT;
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
            default:
                if (features.expression === 'focused')
                    return animal_types_1.AnimalSpecies.HORSE;
                if (features.complexity === 'high')
                    return animal_types_1.AnimalSpecies.OX;
                return animal_types_1.AnimalSpecies.DONKEY;
        }
    }
    generateAnalysisDescription(faceFeatures, pixelAnalysis) {
        const complexityDesc = faceFeatures.complexity === 'high' ? '线条丰富，细节详细' :
            faceFeatures.complexity === 'low' ? '线条简洁，风格简约' : '线条适中，层次分明';
        return `[智能像素分析] 面部形状：${faceFeatures.faceShape}；表情神态：${faceFeatures.expression}；` +
            `绘画风格：${faceFeatures.style}；${complexityDesc}。` +
            `图像尺寸：${pixelAnalysis.width}x${pixelAnalysis.height}，暗像素比例：${(pixelAnalysis.darkRatio * 100).toFixed(1)}%，` +
            `线条复杂度：${(pixelAnalysis.lineComplexity * 100).toFixed(1)}%。` +
            `这幅自画像很好地展现了用户的个人特色，非常适合与动物头像进行深度融合创作。`;
    }
    async generateWithDallE(imageData, animalType, animalSpecies, analysisResult) {
        const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
        const animalConfig = animal_types_1.ANIMAL_CONFIG[species];
        const prompt = this.buildDallEPrompt(animalType, species, animalConfig, analysisResult);
        this.logger.log(`Generating with DALL-E 3, prompt: ${prompt.substring(0, 100)}...`);
        const response = await this.openai.images.generate({
            model: 'dall-e-3',
            prompt,
            n: 1,
            size: '1024x1024',
            quality: 'standard',
            style: 'natural'
        });
        const imageUrl = response.data[0].url;
        const imageBase64 = await this.convertUrlToBase64(imageUrl);
        const sketchBase64 = await this.convertToSketch(imageBase64);
        return {
            imageBase64: sketchBase64,
            imageUrl,
            generationMethod: 'OpenAI DALL-E 3',
            stats: this.generateWorkerStats(),
            prompt,
        };
    }
    async generateWithCanvas(imageData, animalType, animalSpecies, analysisResult) {
        const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
        const animalConfig = animal_types_1.ANIMAL_CONFIG[species];
        this.logger.log(`Generating with Canvas for ${species}`);
        const canvas = (0, canvas_1.createCanvas)(512, 768);
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 512, 768);
        this.drawProfessionalBody(ctx);
        this.drawAnimalHead(ctx, animalType, species, analysisResult?.faceFeatures);
        this.applySketchTexture(ctx);
        const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
        return {
            imageBase64,
            generationMethod: 'Canvas Local Generation',
            stats: this.generateWorkerStats(),
        };
    }
    buildDallEPrompt(animalType, species, animalConfig, analysisResult) {
        const animalName = animalConfig.name;
        const basePrompt = `Professional pencil sketch portrait of a single office worker with ${animalName} head and human body, ` +
            `standing upright in formal business suit, ` +
            `black and white line art style, clean sketch lines, ` +
            `single character full body view, no duplicates, only one person`;
        if (analysisResult?.faceFeatures) {
            const features = analysisResult.faceFeatures;
            return `${basePrompt}, ${features.faceShape} face shape, ${features.expression} expression, ${features.style} artistic approach`;
        }
        return basePrompt;
    }
    getDefaultSpecies(animalType) {
        switch (animalType) {
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                return animal_types_1.AnimalSpecies.DRAGON;
            case animal_types_1.AnimalCategory.PET:
                return animal_types_1.AnimalSpecies.GOLDEN_RETRIEVER;
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
            default:
                return animal_types_1.AnimalSpecies.OX;
        }
    }
    generateWorkerStats() {
        return {
            workEfficiency: Math.floor(Math.random() * 40) + 60,
            happiness: Math.floor(Math.random() * 40) + 40,
            energy: Math.floor(Math.random() * 40) + 50,
            creativity: Math.floor(Math.random() * 40) + 45
        };
    }
    async convertUrlToBase64(url) {
        const response = await axios_1.default.get(url, { responseType: 'arraybuffer' });
        return Buffer.from(response.data).toString('base64');
    }
    async convertToSketch(imageBase64) {
        try {
            const imageBuffer = Buffer.from(imageBase64, 'base64');
            const processedBuffer = await sharp(imageBuffer)
                .resize(512, 768, { fit: 'contain', background: { r: 255, g: 255, b: 255 } })
                .greyscale()
                .normalize()
                .linear(1.2, -(128 * 1.2) + 128)
                .blur(0.3)
                .sharpen({ sigma: 1.5 })
                .toBuffer();
            return processedBuffer.toString('base64');
        }
        catch (error) {
            this.logger.error('Sketch conversion failed:', error);
            return imageBase64;
        }
    }
    drawProfessionalBody(ctx) {
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.beginPath();
        ctx.moveTo(180, 320);
        ctx.lineTo(160, 450);
        ctx.lineTo(170, 550);
        ctx.lineTo(342, 550);
        ctx.lineTo(352, 450);
        ctx.lineTo(332, 320);
        ctx.lineTo(300, 300);
        ctx.lineTo(256, 280);
        ctx.lineTo(212, 300);
        ctx.closePath();
        ctx.stroke();
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(256, 300);
        ctx.lineTo(250, 420);
        ctx.lineTo(256, 430);
        ctx.lineTo(262, 420);
        ctx.closePath();
        ctx.fillStyle = '#444';
        ctx.fill();
        ctx.stroke();
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(180, 350);
        ctx.lineTo(130, 480);
        ctx.moveTo(332, 350);
        ctx.lineTo(382, 480);
        ctx.moveTo(200, 550);
        ctx.lineTo(195, 720);
        ctx.moveTo(312, 550);
        ctx.lineTo(317, 720);
        ctx.stroke();
    }
    drawAnimalHead(ctx, animalType, species, faceFeatures) {
        const centerX = 256;
        const centerY = 200;
        const headSize = 80;
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
        ctx.stroke();
        switch (animalType) {
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                this.drawDivineBeastFeatures(ctx, centerX, centerY, headSize);
                break;
            case animal_types_1.AnimalCategory.PET:
                this.drawPetFeatures(ctx, centerX, centerY, headSize, species);
                break;
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
            default:
                this.drawWorkingAnimalFeatures(ctx, centerX, centerY, headSize, species);
                break;
        }
        this.drawBasicFacialFeatures(ctx, centerX, centerY);
    }
    drawDivineBeastFeatures(ctx, centerX, centerY, headSize) {
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(centerX - 40, centerY - headSize);
        ctx.lineTo(centerX - 50, centerY - headSize - 40);
        ctx.moveTo(centerX + 40, centerY - headSize);
        ctx.lineTo(centerX + 50, centerY - headSize - 40);
        ctx.stroke();
    }
    drawPetFeatures(ctx, centerX, centerY, headSize, species) {
        if (species === animal_types_1.AnimalSpecies.GOLDEN_RETRIEVER) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.ellipse(centerX - 50, centerY - 20, 20, 30, -0.3, 0, Math.PI * 2);
            ctx.ellipse(centerX + 50, centerY - 20, 20, 30, 0.3, 0, Math.PI * 2);
            ctx.stroke();
        }
        else {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 35, centerY - headSize);
            ctx.lineTo(centerX - 50, centerY - headSize - 30);
            ctx.lineTo(centerX - 20, centerY - headSize + 10);
            ctx.closePath();
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(centerX + 35, centerY - headSize);
            ctx.lineTo(centerX + 50, centerY - headSize - 30);
            ctx.lineTo(centerX + 20, centerY - headSize + 10);
            ctx.closePath();
            ctx.stroke();
        }
    }
    drawWorkingAnimalFeatures(ctx, centerX, centerY, headSize, species) {
        if (species === animal_types_1.AnimalSpecies.OX || species === animal_types_1.AnimalSpecies.BUFFALO) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(centerX - 30, centerY - headSize);
            ctx.quadraticCurveTo(centerX - 40, centerY - headSize - 20, centerX - 35, centerY - headSize - 40);
            ctx.moveTo(centerX + 30, centerY - headSize);
            ctx.quadraticCurveTo(centerX + 40, centerY - headSize - 20, centerX + 35, centerY - headSize - 40);
            ctx.stroke();
        }
        else {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.ellipse(centerX - 45, centerY - headSize + 20, 15, 25, -0.2, 0, Math.PI * 2);
            ctx.ellipse(centerX + 45, centerY - headSize + 20, 15, 25, 0.2, 0, Math.PI * 2);
            ctx.stroke();
        }
    }
    drawBasicFacialFeatures(ctx, centerX, centerY) {
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(centerX - 25, centerY - 15, 6, 0, Math.PI * 2);
        ctx.arc(centerX + 25, centerY - 15, 6, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY + 5, 8, 6, 0, 0, Math.PI * 2);
        ctx.stroke();
        ctx.beginPath();
        ctx.arc(centerX, centerY + 25, 12, 0.2 * Math.PI, 0.8 * Math.PI);
        ctx.stroke();
    }
    applySketchTexture(ctx) {
        ctx.strokeStyle = 'rgba(51, 51, 51, 0.3)';
        ctx.lineWidth = 0.5;
        for (let i = 0; i < 50; i++) {
            const x1 = Math.random() * 512;
            const y1 = Math.random() * 768;
            const x2 = x1 + (Math.random() - 0.5) * 20;
            const y2 = y1 + (Math.random() - 0.5) * 20;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }
    async generateWithGeminiFlash(userImageData, animalType, animalSpecies, analysisResult, options) {
        const aiConfig = this.configService.get('ai');
        const model = this.genAI.getGenerativeModel({
            model: aiConfig?.gemini?.imageModel || 'gemini-2.0-flash-exp',
            generationConfig: {
                temperature: aiConfig?.gemini?.temperature || 1.0,
                topP: aiConfig?.gemini?.topP || 0.95,
                topK: aiConfig?.gemini?.topK || 40,
                maxOutputTokens: aiConfig?.gemini?.maxTokens || 8192,
                responseMimeType: 'image/png',
            }
        });
        const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
        const animalConfig = animal_types_1.ANIMAL_CONFIG[species];
        const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
        const prompt = this.buildGeminiWorkerPrompt(animalType, species, animalConfig, analysisResult, options);
        const parts = [
            { text: prompt },
            {
                inlineData: {
                    mimeType: 'image/png',
                    data: userDrawingBase64
                }
            }
        ];
        try {
            this.logger.log('Calling Gemini 2.0 Flash for image generation...');
            const result = await model.generateContent(parts);
            const response = await result.response;
            let generatedImageBase64 = null;
            if (response.candidates && response.candidates[0]) {
                const candidate = response.candidates[0];
                if (candidate.content && candidate.content.parts) {
                    for (const part of candidate.content.parts) {
                        if (part.inlineData && part.inlineData.mimeType.includes('image')) {
                            generatedImageBase64 = part.inlineData.data;
                            break;
                        }
                    }
                }
            }
            if (!generatedImageBase64) {
                this.logger.warn('Gemini 2.0 Flash did not return image data');
                return null;
            }
            const sketchImageBase64 = await this.convertToSketch(generatedImageBase64);
            return {
                imageBase64: sketchImageBase64,
                generationMethod: 'Google Gemini 2.0 Flash',
                stats: this.generateWorkerStats(),
                prompt,
            };
        }
        catch (error) {
            this.logger.warn('Gemini Flash generation failed:', error.message);
            return null;
        }
    }
    async generateWorkerAvatarWithDallE(userImageData, animalType, animalSpecies, analysisResult, options) {
        const aiConfig = this.configService.get('ai');
        const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
        const animalConfig = animal_types_1.ANIMAL_CONFIG[species];
        const prompt = this.buildDallEWorkerPrompt(animalType, species, animalConfig, analysisResult, options);
        this.logger.log(`Generating worker avatar with DALL-E 3, prompt: ${prompt.substring(0, 100)}...`);
        const response = await this.openai.images.generate({
            model: aiConfig?.openai?.model || 'dall-e-3',
            prompt,
            n: 1,
            size: (aiConfig?.openai?.imageSize || '1024x1024'),
            quality: (aiConfig?.openai?.imageQuality || 'standard'),
            style: (aiConfig?.openai?.imageStyle || 'natural'),
        });
        const imageUrl = response.data[0].url;
        const imageBase64 = await this.convertUrlToBase64(imageUrl);
        const fusedImageBase64 = await this.fuseUserFeaturesWithAI(imageBase64, userImageData, animalType, options);
        const sketchBase64 = await this.convertToSketch(fusedImageBase64);
        return {
            imageBase64: sketchBase64,
            imageUrl,
            generationMethod: 'OpenAI DALL-E 3 + User Feature Fusion',
            stats: this.generateWorkerStats(),
            prompt,
        };
    }
    async generateWorkerAvatarWithCanvas(userImageData, animalType, animalSpecies, analysisResult, options) {
        const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
        const animalConfig = animal_types_1.ANIMAL_CONFIG[species];
        this.logger.log(`Generating worker avatar with enhanced Canvas for ${species}`);
        const canvas = (0, canvas_1.createCanvas)(512, 768);
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 512, 768);
        await this.blendUserImageToCanvas(ctx, userImageData, analysisResult?.pixelAnalysis);
        this.drawProfessionalWorkerBody(ctx, options?.workEnvironment, options?.clothingStyle);
        this.drawEnhancedAnimalHead(ctx, animalType, species, analysisResult?.faceFeatures, options);
        this.addWorkEnvironmentDetails(ctx, options?.workEnvironment);
        this.applyUserBasedSketchTexture(ctx, analysisResult?.pixelAnalysis);
        const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
        return {
            imageBase64,
            generationMethod: 'Enhanced Canvas + User Feature Fusion',
            stats: this.generateWorkerStats(),
        };
    }
    buildGeminiWorkerPrompt(animalType, species, animalConfig, analysisResult, options) {
        const animalName = animalConfig.name;
        const environment = options?.workEnvironment || 'office';
        const clothing = options?.clothingStyle || 'formal';
        const fusionStrength = options?.fusionStrength || 0.7;
        let basePrompt = `创建一个打工人形象的素描画，要求：
1. 头部是${animalName}的头部特征，保持动物的标志性特征
2. 身体是人类的身体，穿着${clothing === 'formal' ? '正式的商务装' : clothing === 'casual' ? '休闲工作装' : clothing === 'uniform' ? '工作制服' : '创意工作服装'}
3. 整体风格为黑白素描，线条清晰
4. 融合程度：${fusionStrength > 0.8 ? '深度融合，动物特征明显' : fusionStrength > 0.5 ? '平衡融合，人类和动物特征并重' : '轻度融合，保留更多人类特征'}
5. 工作环境：${environment === 'office' ? '办公室背景' : environment === 'factory' ? '工厂车间背景' : environment === 'field' ? '田野工作背景' : '创意工作空间背景'}`;
        if (analysisResult?.faceFeatures) {
            const features = analysisResult.faceFeatures;
            basePrompt += `\n6. 融合用户面部特征：${features.faceShape}脸型，${features.expression}表情，${features.style}风格`;
        }
        basePrompt += '\n7. 确保输出是单一角色，全身立像，专业的素描风格';
        return basePrompt;
    }
    buildDallEWorkerPrompt(animalType, species, animalConfig, analysisResult, options) {
        const animalName = animalConfig.name;
        const environment = options?.workEnvironment || 'office';
        const clothing = options?.clothingStyle || 'formal';
        let prompt = `Professional pencil sketch portrait of a single office worker with ${animalName} head and human body, ` +
            `standing upright in ${clothing === 'formal' ? 'formal business suit' : clothing === 'casual' ? 'casual work attire' : clothing === 'uniform' ? 'work uniform' : 'creative work clothes'}, ` +
            `${environment === 'office' ? 'office environment' : environment === 'factory' ? 'factory setting' : environment === 'field' ? 'field work environment' : 'creative workspace'} background, ` +
            `black and white line art style, clean sketch lines, ` +
            `single character full body view, no duplicates, only one person`;
        if (analysisResult?.faceFeatures) {
            const features = analysisResult.faceFeatures;
            prompt += `, incorporating ${features.faceShape} face shape, ${features.expression} expression, ${features.style} artistic approach`;
        }
        return prompt;
    }
    async fuseUserFeaturesWithAI(aiImageBase64, userImageData, animalType, options) {
        try {
            const fusionStrength = options?.fusionStrength || 0.7;
            const canvas = (0, canvas_1.createCanvas)(512, 768);
            const ctx = canvas.getContext('2d');
            const aiImageBuffer = Buffer.from(aiImageBase64, 'base64');
            const aiImage = await (0, canvas_1.loadImage)(aiImageBuffer);
            const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
            const userImageBuffer = Buffer.from(userDrawingBase64, 'base64');
            const userImage = await (0, canvas_1.loadImage)(userImageBuffer);
            ctx.drawImage(aiImage, 0, 0, 512, 768);
            ctx.save();
            ctx.globalAlpha = fusionStrength * 0.3;
            ctx.globalCompositeOperation = 'multiply';
            const headX = 256 - 80;
            const headY = 150 - 100;
            const headWidth = 160;
            const headHeight = 200;
            ctx.drawImage(userImage, headX, headY, headWidth, headHeight);
            ctx.restore();
            return canvas.toDataURL('image/png').split(',')[1];
        }
        catch (error) {
            this.logger.warn('User feature fusion failed:', error.message);
            return aiImageBase64;
        }
    }
    async blendUserImageToCanvas(ctx, userImageData, pixelAnalysis) {
        try {
            const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
            const imageBuffer = Buffer.from(userDrawingBase64, 'base64');
            const userImage = await (0, canvas_1.loadImage)(imageBuffer);
            ctx.save();
            ctx.globalAlpha = 0.4;
            ctx.globalCompositeOperation = 'multiply';
            ctx.drawImage(userImage, 100, 50, 312, 400);
            ctx.restore();
        }
        catch (error) {
            this.logger.warn('User image blending failed:', error.message);
        }
    }
    drawProfessionalWorkerBody(ctx, environment, clothingStyle) {
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        if (clothingStyle === 'uniform') {
            this.drawWorkUniform(ctx);
        }
        else if (clothingStyle === 'casual') {
            this.drawCasualWear(ctx);
        }
        else if (clothingStyle === 'creative') {
            this.drawCreativeWear(ctx);
        }
        else {
            this.drawProfessionalSuit(ctx);
        }
    }
    drawProfessionalSuit(ctx) {
        ctx.beginPath();
        ctx.moveTo(180, 320);
        ctx.lineTo(160, 450);
        ctx.lineTo(170, 550);
        ctx.lineTo(342, 550);
        ctx.lineTo(352, 450);
        ctx.lineTo(332, 320);
        ctx.lineTo(300, 300);
        ctx.lineTo(256, 280);
        ctx.lineTo(212, 300);
        ctx.closePath();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(200, 350);
        ctx.lineTo(190, 420);
        ctx.moveTo(312, 350);
        ctx.lineTo(322, 420);
        ctx.stroke();
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(256, 300);
        ctx.lineTo(250, 420);
        ctx.lineTo(256, 430);
        ctx.lineTo(262, 420);
        ctx.closePath();
        ctx.fillStyle = '#444';
        ctx.fill();
        ctx.stroke();
        ctx.fillStyle = '#888';
        ctx.beginPath();
        ctx.arc(140, 470, 4, 0, Math.PI * 2);
        ctx.arc(372, 470, 4, 0, Math.PI * 2);
        ctx.fill();
    }
    drawWorkUniform(ctx) {
        ctx.beginPath();
        ctx.moveTo(170, 320);
        ctx.lineTo(150, 450);
        ctx.lineTo(160, 550);
        ctx.lineTo(352, 550);
        ctx.lineTo(362, 450);
        ctx.lineTo(342, 320);
        ctx.lineTo(256, 280);
        ctx.closePath();
        ctx.stroke();
        ctx.strokeRect(200, 380, 30, 25);
        ctx.strokeRect(282, 380, 30, 25);
    }
    drawCasualWear(ctx) {
        ctx.beginPath();
        ctx.moveTo(170, 320);
        ctx.lineTo(150, 500);
        ctx.lineTo(362, 500);
        ctx.lineTo(342, 320);
        ctx.lineTo(300, 290);
        ctx.lineTo(212, 290);
        ctx.closePath();
        ctx.stroke();
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 2;
        ctx.strokeRect(230, 350, 52, 30);
    }
    drawCreativeWear(ctx) {
        ctx.beginPath();
        ctx.moveTo(175, 320);
        ctx.lineTo(155, 480);
        ctx.lineTo(357, 480);
        ctx.lineTo(337, 320);
        ctx.lineTo(300, 295);
        ctx.lineTo(212, 295);
        ctx.closePath();
        ctx.stroke();
        ctx.strokeStyle = '#888';
        ctx.lineWidth = 1;
        for (let i = 0; i < 5; i++) {
            ctx.beginPath();
            ctx.arc(200 + i * 20, 400, 3, 0, Math.PI * 2);
            ctx.stroke();
        }
    }
    drawEnhancedAnimalHead(ctx, animalType, species, faceFeatures, options) {
        const centerX = 256;
        const centerY = 200;
        const headSize = 90;
        switch (animalType) {
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                this.drawEnhancedDivineHead(ctx, centerX, centerY, headSize, species);
                break;
            case animal_types_1.AnimalCategory.PET:
                this.drawEnhancedPetHead(ctx, centerX, centerY, headSize, species);
                break;
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
            default:
                this.drawEnhancedWorkingAnimalHead(ctx, centerX, centerY, headSize, species);
                break;
        }
    }
    drawEnhancedDivineHead(ctx, centerX, centerY, headSize, species) {
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
        ctx.stroke();
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(centerX - 50, centerY - headSize);
        ctx.bezierCurveTo(centerX - 60, centerY - headSize - 30, centerX - 45, centerY - headSize - 50, centerX - 40, centerY - headSize - 60);
        ctx.moveTo(centerX + 50, centerY - headSize);
        ctx.bezierCurveTo(centerX + 60, centerY - headSize - 30, centerX + 45, centerY - headSize - 50, centerX + 40, centerY - headSize - 60);
        ctx.stroke();
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 3;
        ctx.fillStyle = '#FFF8DC';
        ctx.beginPath();
        ctx.ellipse(centerX - 30, centerY - 15, 12, 8, 0, 0, Math.PI * 2);
        ctx.ellipse(centerX + 30, centerY - 15, 12, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(centerX - 30, centerY - 15, 4, 0, Math.PI * 2);
        ctx.arc(centerX + 30, centerY - 15, 4, 0, Math.PI * 2);
        ctx.fill();
    }
    drawEnhancedPetHead(ctx, centerX, centerY, headSize, species) {
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headSize * 0.9, headSize, 0, 0, Math.PI * 2);
        ctx.stroke();
        if (species === animal_types_1.AnimalSpecies.PERSIAN_CAT) {
            ctx.fillStyle = '#FFC0CB';
            ctx.beginPath();
            ctx.moveTo(centerX - 45, centerY - headSize);
            ctx.lineTo(centerX - 60, centerY - headSize - 40);
            ctx.lineTo(centerX - 25, centerY - headSize + 15);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(centerX + 45, centerY - headSize);
            ctx.lineTo(centerX + 60, centerY - headSize - 40);
            ctx.lineTo(centerX + 25, centerY - headSize + 15);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            for (let i = 0; i < 3; i++) {
                const y = centerY + i * 8;
                ctx.beginPath();
                ctx.moveTo(centerX - 30, y);
                ctx.lineTo(centerX - 70, y - 5);
                ctx.moveTo(centerX + 30, y);
                ctx.lineTo(centerX + 70, y - 5);
                ctx.stroke();
            }
        }
        else {
            ctx.fillStyle = '#DEB887';
            ctx.beginPath();
            ctx.ellipse(centerX - 55, centerY - 10, 25, 40, -0.3, 0, Math.PI * 2);
            ctx.ellipse(centerX + 55, centerY - 10, 25, 40, 0.3, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
        }
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(centerX - 25, centerY - 15, 8, 0, Math.PI * 2);
        ctx.arc(centerX + 25, centerY - 15, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#FFF';
        ctx.beginPath();
        ctx.arc(centerX - 22, centerY - 18, 3, 0, Math.PI * 2);
        ctx.arc(centerX + 28, centerY - 18, 3, 0, Math.PI * 2);
        ctx.fill();
    }
    drawEnhancedWorkingAnimalHead(ctx, centerX, centerY, headSize, species) {
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
        ctx.stroke();
        if (species === animal_types_1.AnimalSpecies.OX || species === animal_types_1.AnimalSpecies.BUFFALO) {
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(centerX - 40, centerY - headSize);
            ctx.quadraticCurveTo(centerX - 55, centerY - headSize - 25, centerX - 45, centerY - headSize - 50);
            ctx.moveTo(centerX + 40, centerY - headSize);
            ctx.quadraticCurveTo(centerX + 55, centerY - headSize - 25, centerX + 45, centerY - headSize - 50);
            ctx.stroke();
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerX, centerY + 25, 12, Math.PI * 0.2, Math.PI * 0.8);
            ctx.stroke();
        }
        else {
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.ellipse(centerX - 50, centerY - headSize + 30, 18, 35, -0.2, 0, Math.PI * 2);
            ctx.ellipse(centerX + 50, centerY - headSize + 30, 18, 35, 0.2, 0, Math.PI * 2);
            ctx.stroke();
        }
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(centerX - 25, centerY - 15, 10, 6, 0, 0, Math.PI * 2);
        ctx.ellipse(centerX + 25, centerY - 15, 10, 6, 0, 0, Math.PI * 2);
        ctx.stroke();
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(centerX - 25, centerY - 15, 4, 0, Math.PI * 2);
        ctx.arc(centerX + 25, centerY - 15, 4, 0, Math.PI * 2);
        ctx.fill();
    }
    addWorkEnvironmentDetails(ctx, environment) {
        ctx.strokeStyle = 'rgba(102, 102, 102, 0.5)';
        ctx.lineWidth = 1;
        switch (environment) {
            case 'office':
                ctx.strokeRect(450, 100, 50, 30);
                ctx.strokeRect(460, 110, 10, 8);
                break;
            case 'factory':
                ctx.beginPath();
                ctx.moveTo(450, 150);
                ctx.lineTo(500, 120);
                ctx.lineTo(500, 180);
                ctx.closePath();
                ctx.stroke();
                break;
            case 'field':
                for (let i = 0; i < 5; i++) {
                    ctx.beginPath();
                    ctx.moveTo(420 + i * 15, 200);
                    ctx.lineTo(425 + i * 15, 180);
                    ctx.stroke();
                }
                break;
            case 'creative':
                ctx.strokeStyle = '#888';
                ctx.beginPath();
                ctx.arc(470, 150, 15, 0, Math.PI * 2);
                ctx.moveTo(455, 135);
                ctx.lineTo(485, 165);
                ctx.moveTo(485, 135);
                ctx.lineTo(455, 165);
                ctx.stroke();
                break;
        }
    }
    applyUserBasedSketchTexture(ctx, pixelAnalysis) {
        ctx.strokeStyle = 'rgba(51, 51, 51, 0.3)';
        if (pixelAnalysis) {
            const textureCount = Math.floor(pixelAnalysis.lineComplexity * 100) + 30;
            const lineWidth = pixelAnalysis.darkRatio > 0.4 ? 0.8 : 0.4;
            ctx.lineWidth = lineWidth;
            for (let i = 0; i < textureCount; i++) {
                const x1 = Math.random() * 512;
                const y1 = Math.random() * 768;
                const lineLength = pixelAnalysis.lineComplexity * 30 + 5;
                const x2 = x1 + (Math.random() - 0.5) * lineLength;
                const y2 = y1 + (Math.random() - 0.5) * lineLength;
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
        }
        else {
            ctx.lineWidth = 0.5;
            for (let i = 0; i < 60; i++) {
                const x1 = Math.random() * 512;
                const y1 = Math.random() * 768;
                const x2 = x1 + (Math.random() - 0.5) * 20;
                const y2 = y1 + (Math.random() - 0.5) * 20;
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
        }
    }
};
exports.AIService = AIService;
exports.AIService = AIService = AIService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AIService);
//# sourceMappingURL=ai.service.js.map