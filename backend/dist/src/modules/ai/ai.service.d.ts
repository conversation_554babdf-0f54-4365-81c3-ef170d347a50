import { ConfigService } from '@nestjs/config';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export interface AnalysisResult {
    description: string;
    faceFeatures: {
        faceShape: string;
        expression: string;
        style: string;
        complexity: string;
        lineCount?: number;
        darkRatio?: number;
    };
    pixelAnalysis?: {
        width: number;
        height: number;
        aspectRatio: number;
        lineComplexity: number;
        dominantColors: string[];
    };
    confidence: number;
    detectedAnimalType: AnimalCategory;
    recommendedSpecies: AnimalSpecies;
}
export interface AvatarGenerationResult {
    imageBase64: string;
    imageUrl?: string;
    generationMethod: string;
    processingTime: number;
    stats: {
        workEfficiency: number;
        happiness: number;
        energy: number;
        creativity: number;
    };
    prompt?: string;
}
export declare class AIService {
    private configService;
    private readonly logger;
    private genAI;
    private openai;
    constructor(configService: ConfigService);
    analyzeDrawing(imageData: string): Promise<AnalysisResult>;
    generateAvatar(imageData: string, animalType: AnimalCategory, animalSpecies?: AnimalSpecies, analysisResult?: AnalysisResult): Promise<AvatarGenerationResult>;
    generateWorkerAnimalAvatar(userImageData: string, animalType: AnimalCategory, animalSpecies?: AnimalSpecies, analysisResult?: AnalysisResult, options?: {
        fusionStrength?: number;
        keepUserFeatures?: boolean;
        workEnvironment?: 'office' | 'factory' | 'field' | 'creative';
        clothingStyle?: 'formal' | 'casual' | 'uniform' | 'creative';
    }): Promise<AvatarGenerationResult>;
    private analyzeWithGemini;
    private analyzeWithPixelData;
    private analyzePixelFeatures;
    private extractFaceFeatures;
    private extractFeaturesFromPixels;
    private detectAnimalType;
    private detectAnimalTypeFromFeatures;
    private getRecommendedSpecies;
    private generateAnalysisDescription;
    private generateWithDallE;
    private generateWithCanvas;
    private buildDallEPrompt;
    private getDefaultSpecies;
    private generateWorkerStats;
    private convertUrlToBase64;
    private convertToSketch;
    private drawProfessionalBody;
    private drawAnimalHead;
    private drawDivineBeastFeatures;
    private drawPetFeatures;
    private drawWorkingAnimalFeatures;
    private drawBasicFacialFeatures;
    private applySketchTexture;
    private generateWithGeminiFlash;
    private generateWorkerAvatarWithDallE;
    private generateWorkerAvatarWithCanvas;
    private buildGeminiWorkerPrompt;
    private buildDallEWorkerPrompt;
    private fuseUserFeaturesWithAI;
    private blendUserImageToCanvas;
    private drawProfessionalWorkerBody;
    private drawProfessionalSuit;
    private drawWorkUniform;
    private drawCasualWear;
    private drawCreativeWear;
    private drawEnhancedAnimalHead;
    private drawEnhancedDivineHead;
    private drawEnhancedPetHead;
    private drawEnhancedWorkingAnimalHead;
    private addWorkEnvironmentDetails;
    private applyUserBasedSketchTexture;
}
