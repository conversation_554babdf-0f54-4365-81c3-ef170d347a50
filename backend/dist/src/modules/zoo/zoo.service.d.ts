import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { ZooAnimal } from './entities/zoo-animal.entity';
import { AnimalInteractionRecord } from './entities/animal-interaction-record.entity';
import { GeneratedAvatar } from '@/modules/drawing/entities/generated-avatar.entity';
import { ScoringService } from './services/scoring.service';
import { ZooSceneQueryDto, ZooSceneResponseDto, ZooAnimalsQueryDto, ZooAnimalsResponseDto, AnimalInteractionDto, AnimalInteractionResponseDto } from './dto';
export declare class ZooService {
    private zooAnimalRepository;
    private interactionRecordRepository;
    private generatedAvatarRepository;
    private configService;
    private scoringService;
    private readonly logger;
    constructor(zooAnimalRepository: Repository<ZooAnimal>, interactionRecordRepository: Repository<AnimalInteractionRecord>, generatedAvatarRepository: Repository<GeneratedAvatar>, configService: ConfigService, scoringService: ScoringService);
    getZooScene(userId: string, query: ZooSceneQueryDto): Promise<ZooSceneResponseDto>;
    getZooAnimals(userId: string, query: ZooAnimalsQueryDto): Promise<ZooAnimalsResponseDto>;
    interactWithAnimal(userId: string, animalId: string, interactionDto: AnimalInteractionDto): Promise<AnimalInteractionResponseDto>;
    createZooAnimalFromAvatar(userId: string, avatarId: string, customName?: string): Promise<ZooAnimal>;
    private convertToSceneAnimal;
    private convertToAnimalDto;
    private generateEnvironment;
    private generateFacilities;
    private generateEvents;
    private calculateSceneStatistics;
    private generateAnimalStatistics;
    private getSceneName;
    private getSceneDescription;
    private getInitialMood;
    private getRandomPosition;
    private getInitialPreferences;
    private calculateInteractionEffectiveness;
    private applyInteractionEffects;
    private generateAnimalReaction;
    private calculateRewards;
    private generateInteractionSuggestions;
    private getInteractionCooldown;
    private getInteractionTips;
    private getInteractionSuccessMessage;
    private updateAnimalPreferences;
    private getCurrentWeather;
    private getCurrentSeason;
    private getEnvironmentBackground;
    private getEnvironmentSounds;
}
