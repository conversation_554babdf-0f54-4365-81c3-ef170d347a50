"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ZooAISchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooAISchedulerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const zoo_animal_entity_1 = require("../entities/zoo-animal.entity");
const animal_behavior_service_1 = require("./animal-behavior.service");
const animal_types_1 = require("../../../common/constants/animal-types");
let ZooAISchedulerService = ZooAISchedulerService_1 = class ZooAISchedulerService {
    constructor(zooAnimalRepository, animalBehaviorService, configService) {
        this.zooAnimalRepository = zooAnimalRepository;
        this.animalBehaviorService = animalBehaviorService;
        this.configService = configService;
        this.logger = new common_1.Logger(ZooAISchedulerService_1.name);
        this.isRunning = false;
        this.animalBehaviorQueue = new Map();
        this.globalEvents = [];
        this.initializeEnvironment();
        this.startAIScheduler();
    }
    initializeEnvironment() {
        this.environmentState = {
            timeOfDay: new Date().getHours(),
            weather: this.generateWeather(),
            crowdLevel: this.calculateCrowdLevel(),
            events: []
        };
        this.logger.log('Zoo AI environment initialized');
    }
    startAIScheduler() {
        const zooConfig = this.configService.get('zoo');
        const isEnabled = zooConfig?.enableAIBehavior !== false;
        if (!isEnabled) {
            this.logger.warn('Zoo AI behavior is disabled in configuration');
            return;
        }
        this.isRunning = true;
        this.logger.log('Zoo AI Scheduler started');
    }
    async updateAnimalBehaviors() {
        if (!this.isRunning)
            return;
        try {
            await this.processAnimalBehaviorCycle();
        }
        catch (error) {
            this.logger.error('Failed to update animal behaviors:', error);
        }
    }
    async updateEnvironment() {
        if (!this.isRunning)
            return;
        try {
            await this.updateEnvironmentState();
        }
        catch (error) {
            this.logger.error('Failed to update environment:', error);
        }
    }
    async triggerHourlyEvents() {
        if (!this.isRunning)
            return;
        try {
            await this.processHourlyEvents();
        }
        catch (error) {
            this.logger.error('Failed to process hourly events:', error);
        }
    }
    async processAnimalBehaviorCycle() {
        const animals = await this.zooAnimalRepository.find({
            where: { isActive: true },
            relations: ['user']
        });
        if (animals.length === 0)
            return;
        this.logger.debug(`Processing behavior cycle for ${animals.length} animals`);
        const behaviorPromises = animals.map(animal => this.processAnimalBehavior(animal, animals));
        await Promise.all(behaviorPromises);
    }
    async processAnimalBehavior(animal, allAnimals) {
        try {
            const behaviorState = await this.animalBehaviorService.getAnimalBehaviorState(animal.id);
            if (behaviorState.isActive) {
                return;
            }
            const nearbyAnimals = this.getNearbyAnimals(animal, allAnimals);
            const context = {
                timeOfDay: this.environmentState.timeOfDay,
                nearbyAnimals,
                weather: this.environmentState.weather,
                crowdLevel: this.environmentState.crowdLevel,
                globalEvents: this.globalEvents
            };
            const nextBehavior = await this.animalBehaviorService.generateNextBehavior(animal, context);
            const modifiedBehavior = this.applyEnvironmentalEffects(nextBehavior, animal, context);
            await this.animalBehaviorService.executeBehavior(animal.id, modifiedBehavior);
        }
        catch (error) {
            this.logger.error(`Failed to process behavior for animal ${animal.id}:`, error);
        }
    }
    getNearbyAnimals(animal, allAnimals, maxDistance = 300) {
        return allAnimals.filter(other => {
            if (other.id === animal.id)
                return false;
            const distance = Math.sqrt((other.position.x - animal.position.x) ** 2 +
                (other.position.y - animal.position.y) ** 2);
            return distance <= maxDistance;
        });
    }
    applyEnvironmentalEffects(behavior, animal, context) {
        const modified = { ...behavior };
        switch (context.weather.condition) {
            case 'rainy':
                if (behavior.type === 'move') {
                    modified.intensity *= 0.7;
                }
                if (behavior.type === 'play') {
                    modified.intensity *= 0.5;
                }
                break;
            case 'sunny':
                if (behavior.type === 'play') {
                    modified.intensity *= 1.2;
                }
                break;
            case 'stormy':
                if (behavior.type !== 'rest') {
                    modified.type = 'rest';
                    modified.description = '因暴风雨寻求庇护';
                }
                break;
        }
        const temp = context.weather.temperature;
        if (temp > 30) {
            modified.intensity *= 0.8;
            if (behavior.type === 'work') {
                modified.duration *= 0.9;
            }
        }
        else if (temp < 5) {
            if (behavior.type === 'play') {
                modified.type = 'rest';
                modified.description = '因寒冷而休息';
            }
        }
        const hour = context.timeOfDay;
        if (hour < 6 || hour > 22) {
            modified.intensity *= 0.6;
            if (behavior.type === 'work') {
                modified.type = 'rest';
                modified.description = '夜间休息';
            }
        }
        else if (hour >= 8 && hour <= 18) {
            if (animal.category === animal_types_1.AnimalCategory.WORKING_ANIMAL && behavior.type === 'idle') {
                modified.type = 'work';
                modified.description = '工作时间到了';
            }
        }
        if (context.crowdLevel === 'high') {
            if (animal.category === animal_types_1.AnimalCategory.PET) {
                modified.intensity *= 1.3;
            }
            else if (animal.category === animal_types_1.AnimalCategory.WORKING_ANIMAL) {
                modified.intensity *= 0.9;
            }
        }
        return modified;
    }
    async updateEnvironmentState() {
        this.environmentState.timeOfDay = new Date().getHours();
        if (Math.random() < 0.1) {
            this.environmentState.weather = this.generateWeather();
        }
        this.environmentState.crowdLevel = this.calculateCrowdLevel();
        this.environmentState.events = this.environmentState.events.filter(event => new Date().getTime() - event.startTime.getTime() < event.duration);
        this.logger.debug(`Environment updated: ${this.environmentState.timeOfDay}:00, ${this.environmentState.weather.condition}, crowd: ${this.environmentState.crowdLevel}`);
    }
    generateWeather() {
        const conditions = ['sunny', 'cloudy', 'rainy', 'stormy'];
        const weights = [0.5, 0.3, 0.15, 0.05];
        let random = Math.random();
        for (let i = 0; i < conditions.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                return {
                    condition: conditions[i],
                    temperature: 15 + Math.random() * 20,
                    humidity: 40 + Math.random() * 40
                };
            }
        }
        return {
            condition: 'sunny',
            temperature: 25,
            humidity: 60
        };
    }
    calculateCrowdLevel() {
        const hour = new Date().getHours();
        if (hour >= 10 && hour <= 16) {
            return 'high';
        }
        else if (hour >= 8 && hour <= 18) {
            return 'medium';
        }
        else {
            return 'low';
        }
    }
    async processHourlyEvents() {
        const hour = this.environmentState.timeOfDay;
        if (hour === 8) {
            await this.triggerGlobalEvent('morning_exercise', '晨练时光', 30 * 60 * 1000);
        }
        else if (hour === 12) {
            await this.triggerGlobalEvent('lunch_time', '午餐时间', 60 * 60 * 1000);
        }
        else if (hour === 18) {
            await this.triggerGlobalEvent('evening_rest', '傍晚休息', 45 * 60 * 1000);
        }
        if (Math.random() < 0.3) {
            await this.triggerRandomEvent();
        }
    }
    async triggerGlobalEvent(id, name, duration) {
        const event = {
            id,
            name,
            type: 'show',
            startTime: new Date(),
            duration,
            affectedAreas: ['all']
        };
        this.environmentState.events.push(event);
        this.globalEvents.push(event);
        this.logger.log(`Global event triggered: ${name}`);
        await this.notifyAnimalsOfEvent(event);
        setTimeout(() => {
            this.globalEvents = this.globalEvents.filter(e => e.id !== event.id);
            this.logger.log(`Global event ended: ${name}`);
        }, duration);
    }
    async triggerRandomEvent() {
        const randomEvents = [
            { name: '突然雨季', effect: () => { this.environmentState.weather.condition = 'rainy'; } },
            { name: '阳光普照', effect: () => { this.environmentState.weather.condition = 'sunny'; } },
            { name: '游客激增', effect: () => { this.environmentState.crowdLevel = 'high'; } },
            { name: '安静时光', effect: () => { this.environmentState.crowdLevel = 'low'; } }
        ];
        const event = randomEvents[Math.floor(Math.random() * randomEvents.length)];
        event.effect();
        this.logger.log(`Random event: ${event.name}`);
    }
    async notifyAnimalsOfEvent(event) {
        const animals = await this.zooAnimalRepository.find({
            where: { isActive: true }
        });
        const eventBehavior = {
            type: 'socialize',
            duration: Math.min(event.duration, 15000),
            intensity: 0.8,
            animation: `event_${event.type}`,
            description: `参与${event.name}`
        };
        const participants = animals.filter(() => Math.random() < 0.7);
        await Promise.all(participants.map(animal => this.animalBehaviorService.executeBehavior(animal.id, eventBehavior)));
        this.logger.debug(`${participants.length} animals participating in ${event.name}`);
    }
    getEnvironmentState() {
        return { ...this.environmentState };
    }
    async getZooStatistics() {
        const animals = await this.zooAnimalRepository.find({
            where: { isActive: true }
        });
        const totalAnimals = animals.length;
        const activeAnimals = animals.filter(a => a.currentState.activity !== 'resting' && a.currentState.activity !== 'idle').length;
        const averageHappiness = totalAnimals > 0 ?
            animals.reduce((sum, a) => sum + a.currentState.happiness, 0) / totalAnimals : 0;
        const averageEnergy = totalAnimals > 0 ?
            animals.reduce((sum, a) => sum + a.currentState.energy, 0) / totalAnimals : 0;
        return {
            totalAnimals,
            activeAnimals,
            averageHappiness: Math.round(averageHappiness),
            averageEnergy: Math.round(averageEnergy),
            environment: this.environmentState,
            activeEvents: this.environmentState.events.length,
            behaviorQueueSize: Array.from(this.animalBehaviorQueue.values()).reduce((sum, queue) => sum + queue.length, 0)
        };
    }
    async triggerManualEvent(eventType, duration = 60000) {
        try {
            await this.triggerGlobalEvent(`manual_${Date.now()}`, `手动事件：${eventType}`, duration);
            return true;
        }
        catch (error) {
            this.logger.error('Failed to trigger manual event:', error);
            return false;
        }
    }
    setSchedulerState(running) {
        this.isRunning = running;
        this.logger.log(`Zoo AI Scheduler ${running ? 'resumed' : 'paused'}`);
    }
    onModuleDestroy() {
        this.isRunning = false;
        this.animalBehaviorQueue.clear();
        this.logger.log('Zoo AI Scheduler destroyed');
    }
};
exports.ZooAISchedulerService = ZooAISchedulerService;
__decorate([
    (0, schedule_1.Cron)('*/5 * * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooAISchedulerService.prototype, "updateAnimalBehaviors", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooAISchedulerService.prototype, "updateEnvironment", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooAISchedulerService.prototype, "triggerHourlyEvents", null);
exports.ZooAISchedulerService = ZooAISchedulerService = ZooAISchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(zoo_animal_entity_1.ZooAnimal)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        animal_behavior_service_1.AnimalBehaviorService,
        config_1.ConfigService])
], ZooAISchedulerService);
//# sourceMappingURL=zoo-ai-scheduler.service.js.map