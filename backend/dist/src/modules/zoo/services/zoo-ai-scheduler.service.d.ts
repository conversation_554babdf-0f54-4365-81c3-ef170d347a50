import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { AnimalBehaviorService } from './animal-behavior.service';
export interface ZooEnvironmentState {
    timeOfDay: number;
    weather: {
        condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy';
        temperature: number;
        humidity: number;
    };
    crowdLevel: 'low' | 'medium' | 'high';
    events: Array<{
        id: string;
        name: string;
        type: 'feeding' | 'show' | 'cleaning' | 'maintenance';
        startTime: Date;
        duration: number;
        affectedAreas: string[];
    }>;
}
export declare class ZooAISchedulerService {
    private zooAnimalRepository;
    private animalBehaviorService;
    private configService;
    private readonly logger;
    private isRunning;
    private environmentState;
    private animalBehaviorQueue;
    private globalEvents;
    constructor(zooAnimalRepository: Repository<ZooAnimal>, animalBehaviorService: AnimalBehaviorService, configService: ConfigService);
    private initializeEnvironment;
    private startAIScheduler;
    updateAnimalBehaviors(): Promise<void>;
    updateEnvironment(): Promise<void>;
    triggerHourlyEvents(): Promise<void>;
    private processAnimalBehaviorCycle;
    private processAnimalBehavior;
    private getNearbyAnimals;
    private applyEnvironmentalEffects;
    private updateEnvironmentState;
    private generateWeather;
    private calculateCrowdLevel;
    private processHourlyEvents;
    private triggerGlobalEvent;
    private triggerRandomEvent;
    private notifyAnimalsOfEvent;
    getEnvironmentState(): ZooEnvironmentState;
    getZooStatistics(): Promise<{
        totalAnimals: number;
        activeAnimals: number;
        averageHappiness: number;
        averageEnergy: number;
        environment: ZooEnvironmentState;
        activeEvents: number;
        behaviorQueueSize: number;
    }>;
    triggerManualEvent(eventType: string, duration?: number): Promise<boolean>;
    setSchedulerState(running: boolean): void;
    onModuleDestroy(): void;
}
