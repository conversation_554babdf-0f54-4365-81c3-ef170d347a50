import { Repository } from 'typeorm';
import { UserScore } from '../entities/user-score.entity';
import { AnimalInteractionRecord } from '../entities/animal-interaction-record.entity';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { InteractionType } from '../dto/animal-interaction.dto';
export interface ScoreCalculationResult {
    baseScore: number;
    bonusScore: number;
    totalScore: number;
    experience: number;
    reason: string;
}
export interface LeaderboardEntry {
    userId: string;
    username: string;
    avatarUrl?: string;
    totalScore: number;
    level: number;
    title: string;
    rank: number;
    badges: Array<{
        name: string;
        icon: string;
        rarity: string;
    }>;
    stats: {
        interactionScore: number;
        workPerformanceScore: number;
        socialScore: number;
        achievementScore: number;
        creativityScore: number;
    };
}
export declare class ScoringService {
    private userScoreRepository;
    private interactionRepository;
    private animalRepository;
    private readonly INTERACTION_BASE_SCORES;
    private readonly LEVEL_EXP_TABLE;
    private readonly TITLES;
    constructor(userScoreRepository: Repository<UserScore>, interactionRepository: Repository<AnimalInteractionRecord>, animalRepository: Repository<ZooAnimal>);
    calculateInteractionScore(userId: string, animalId: string, interactionType: InteractionType, effectiveness: number, duration: number): Promise<ScoreCalculationResult>;
    updateUserScore(userId: string, scoreType: 'interaction' | 'work' | 'social' | 'achievement' | 'creativity', points: number, experience?: number): Promise<UserScore>;
    private calculateLevel;
    private getNextLevelExp;
    private calculateTitle;
    private getWeekPeriod;
    getLeaderboard(period?: 'daily' | 'weekly' | 'monthly' | 'total', limit?: number, offset?: number): Promise<{
        leaderboard: LeaderboardEntry[];
        total: number;
    }>;
    getUserRanking(userId: string): Promise<{
        globalRank: number;
        dailyRank: number;
        weeklyRank: number;
        monthlyRank: number;
        totalScore: number;
        level: number;
        title: string;
    }>;
    private calculateRank;
    processInteractionScore(interactionRecord: AnimalInteractionRecord): Promise<void>;
}
