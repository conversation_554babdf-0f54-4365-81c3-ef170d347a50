import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { AnimalInteractionRecord } from '../entities/animal-interaction-record.entity';
export interface AnimalBehavior {
    type: 'move' | 'idle' | 'interact' | 'work' | 'rest' | 'play' | 'socialize';
    duration: number;
    intensity: number;
    targetPosition?: {
        x: number;
        y: number;
        z: number;
    };
    targetAnimalId?: string;
    animation?: string;
    soundEffect?: string;
    description?: string;
}
export interface BehaviorPattern {
    weight: number;
    conditions?: {
        timeOfDay?: number[];
        energyRange?: [number, number];
        happinessRange?: [number, number];
        nearbyAnimals?: number;
        weatherCondition?: string;
    };
    behavior: AnimalBehavior;
}
export declare class AnimalBehaviorService {
    private zooAnimalRepository;
    private interactionRecordRepository;
    private configService;
    private readonly logger;
    private behaviorPatterns;
    private activeAnimations;
    constructor(zooAnimalRepository: Repository<ZooAnimal>, interactionRecordRepository: Repository<AnimalInteractionRecord>, configService: ConfigService);
    private initializeBehaviorPatterns;
    generateNextBehavior(animal: ZooAnimal, context: {
        timeOfDay: number;
        nearbyAnimals: ZooAnimal[];
        weather: any;
    }): Promise<AnimalBehavior>;
    private evaluateBehaviorPattern;
    private getPersonalityMultiplier;
    private enhanceBehaviorWithContext;
    private generateTargetPosition;
    private selectSocialTarget;
    private getDefaultBehavior;
    executeBehavior(animalId: string, behavior: AnimalBehavior): Promise<void>;
    private updateAnimalStateForBehavior;
    private getLocationFromPosition;
    private onBehaviorComplete;
    getAnimalBehaviorState(animalId: string): Promise<{
        currentBehavior?: AnimalBehavior;
        isActive: boolean;
        timeRemaining?: number;
    }>;
    stopAnimalBehavior(animalId: string): Promise<void>;
    onModuleDestroy(): void;
}
