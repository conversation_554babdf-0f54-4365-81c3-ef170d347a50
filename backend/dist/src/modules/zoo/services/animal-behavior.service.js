"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnimalBehaviorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimalBehaviorService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const zoo_animal_entity_1 = require("../entities/zoo-animal.entity");
const animal_interaction_record_entity_1 = require("../entities/animal-interaction-record.entity");
const animal_types_1 = require("../../../common/constants/animal-types");
let AnimalBehaviorService = AnimalBehaviorService_1 = class AnimalBehaviorService {
    constructor(zooAnimalRepository, interactionRecordRepository, configService) {
        this.zooAnimalRepository = zooAnimalRepository;
        this.interactionRecordRepository = interactionRecordRepository;
        this.configService = configService;
        this.logger = new common_1.Logger(AnimalBehaviorService_1.name);
        this.behaviorPatterns = new Map();
        this.activeAnimations = new Map();
        this.initializeBehaviorPatterns();
    }
    initializeBehaviorPatterns() {
        this.behaviorPatterns.set(animal_types_1.AnimalSpecies.DRAGON, [
            {
                weight: 0.3,
                conditions: { energyRange: [70, 100] },
                behavior: {
                    type: 'move',
                    duration: 10000,
                    intensity: 0.8,
                    animation: 'majestic_flight',
                    soundEffect: 'dragon_roar',
                    description: '威严地巡视领地'
                }
            },
            {
                weight: 0.2,
                conditions: { timeOfDay: [6, 18] },
                behavior: {
                    type: 'work',
                    duration: 15000,
                    intensity: 0.9,
                    animation: 'divine_guidance',
                    description: '指导其他动物工作'
                }
            },
            {
                weight: 0.3,
                conditions: { happinessRange: [60, 100] },
                behavior: {
                    type: 'idle',
                    duration: 8000,
                    intensity: 0.5,
                    animation: 'meditative_pose',
                    description: '冥想修炼'
                }
            },
            {
                weight: 0.2,
                conditions: { nearbyAnimals: 3 },
                behavior: {
                    type: 'socialize',
                    duration: 12000,
                    intensity: 0.7,
                    animation: 'blessing_gesture',
                    description: '为其他动物祝福'
                }
            }
        ]);
        this.behaviorPatterns.set(animal_types_1.AnimalSpecies.PERSIAN_CAT, [
            {
                weight: 0.4,
                conditions: { energyRange: [40, 80] },
                behavior: {
                    type: 'play',
                    duration: 8000,
                    intensity: 0.6,
                    animation: 'playful_pounce',
                    soundEffect: 'cat_meow',
                    description: '愉快地玩耍'
                }
            },
            {
                weight: 0.3,
                conditions: { energyRange: [0, 30] },
                behavior: {
                    type: 'rest',
                    duration: 12000,
                    intensity: 0.2,
                    animation: 'cat_nap',
                    description: '舒适地小憩'
                }
            },
            {
                weight: 0.2,
                conditions: { nearbyAnimals: 1 },
                behavior: {
                    type: 'socialize',
                    duration: 6000,
                    intensity: 0.5,
                    animation: 'friendly_approach',
                    description: '友好地接近同伴'
                }
            },
            {
                weight: 0.1,
                behavior: {
                    type: 'move',
                    duration: 5000,
                    intensity: 0.4,
                    animation: 'graceful_walk',
                    description: '优雅地漫步'
                }
            }
        ]);
        this.behaviorPatterns.set(animal_types_1.AnimalSpecies.OX, [
            {
                weight: 0.5,
                conditions: { timeOfDay: [8, 18], energyRange: [30, 100] },
                behavior: {
                    type: 'work',
                    duration: 20000,
                    intensity: 0.8,
                    animation: 'diligent_work',
                    soundEffect: 'ox_grunt',
                    description: '勤奋地工作'
                }
            },
            {
                weight: 0.2,
                conditions: { energyRange: [0, 40] },
                behavior: {
                    type: 'rest',
                    duration: 15000,
                    intensity: 0.3,
                    animation: 'tired_rest',
                    description: '疲惫地休息'
                }
            },
            {
                weight: 0.2,
                conditions: { nearbyAnimals: 2 },
                behavior: {
                    type: 'socialize',
                    duration: 10000,
                    intensity: 0.6,
                    animation: 'work_collaboration',
                    description: '与同事协作'
                }
            },
            {
                weight: 0.1,
                behavior: {
                    type: 'move',
                    duration: 8000,
                    intensity: 0.5,
                    animation: 'steady_walk',
                    description: '稳重地移动'
                }
            }
        ]);
        this.logger.log(`Behavior patterns initialized for ${this.behaviorPatterns.size} species`);
    }
    async generateNextBehavior(animal, context) {
        const patterns = this.behaviorPatterns.get(animal.species) || [];
        if (patterns.length === 0) {
            return this.getDefaultBehavior(animal);
        }
        const evaluatedPatterns = patterns.map(pattern => ({
            ...pattern,
            score: this.evaluateBehaviorPattern(pattern, animal, context)
        }));
        evaluatedPatterns.sort((a, b) => b.score - a.score);
        const selectedPattern = evaluatedPatterns[0];
        if (selectedPattern.score > 0) {
            return this.enhanceBehaviorWithContext(selectedPattern.behavior, animal, context);
        }
        return this.getDefaultBehavior(animal);
    }
    evaluateBehaviorPattern(pattern, animal, context) {
        let score = pattern.weight;
        if (pattern.conditions) {
            const conditions = pattern.conditions;
            if (conditions.timeOfDay) {
                const inTimeRange = conditions.timeOfDay.some(hour => Math.abs(context.timeOfDay - hour) <= 1);
                score *= inTimeRange ? 1.5 : 0.5;
            }
            if (conditions.energyRange) {
                const [minEnergy, maxEnergy] = conditions.energyRange;
                const energyFits = animal.currentState.energy >= minEnergy &&
                    animal.currentState.energy <= maxEnergy;
                score *= energyFits ? 1.3 : 0.3;
            }
            if (conditions.happinessRange) {
                const [minHappiness, maxHappiness] = conditions.happinessRange;
                const happinessFits = animal.currentState.happiness >= minHappiness &&
                    animal.currentState.happiness <= maxHappiness;
                score *= happinessFits ? 1.2 : 0.4;
            }
            if (conditions.nearbyAnimals) {
                const nearbyCount = context.nearbyAnimals.length;
                const countMatches = nearbyCount >= conditions.nearbyAnimals;
                score *= countMatches ? 1.4 : 0.6;
            }
        }
        score *= this.getPersonalityMultiplier(animal, pattern.behavior.type);
        return Math.max(0, score);
    }
    getPersonalityMultiplier(animal, behaviorType) {
        const config = animal_types_1.ANIMAL_CONFIG[animal.species];
        switch (behaviorType) {
            case 'work':
                return animal.category === animal_types_1.AnimalCategory.WORKING_ANIMAL ? 1.3 : 0.8;
            case 'play':
                return animal.category === animal_types_1.AnimalCategory.PET ? 1.4 : 0.7;
            case 'socialize':
                return animal.currentState.happiness > 70 ? 1.2 : 0.8;
            case 'rest':
                return animal.currentState.energy < 50 ? 1.5 : 0.6;
            case 'move':
                return animal.currentState.energy > 60 ? 1.1 : 0.9;
            default:
                return 1.0;
        }
    }
    enhanceBehaviorWithContext(behavior, animal, context) {
        const enhanced = { ...behavior };
        if (behavior.type === 'move') {
            enhanced.targetPosition = this.generateTargetPosition(animal, context);
        }
        if (behavior.type === 'socialize' && context.nearbyAnimals.length > 0) {
            const targetAnimal = this.selectSocialTarget(animal, context.nearbyAnimals);
            if (targetAnimal) {
                enhanced.targetAnimalId = targetAnimal.id;
                enhanced.targetPosition = targetAnimal.position;
            }
        }
        if (context.timeOfDay < 6 || context.timeOfDay > 22) {
            enhanced.intensity *= 0.7;
        }
        return enhanced;
    }
    generateTargetPosition(animal, context) {
        const zooConfig = this.configService.get('zoo');
        const worldWidth = zooConfig?.worldWidth || 3200;
        const worldHeight = zooConfig?.worldHeight || 2400;
        let preferredZones = [];
        switch (animal.category) {
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
                preferredZones = [
                    { x: [0, worldWidth / 3], y: [0, worldHeight] }
                ];
                break;
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                preferredZones = [
                    { x: [worldWidth / 3, worldWidth * 2 / 3], y: [0, worldHeight] }
                ];
                break;
            case animal_types_1.AnimalCategory.PET:
                preferredZones = [
                    { x: [worldWidth * 2 / 3, worldWidth], y: [0, worldHeight] }
                ];
                break;
        }
        if (preferredZones.length > 0) {
            const zone = preferredZones[Math.floor(Math.random() * preferredZones.length)];
            return {
                x: zone.x[0] + Math.random() * (zone.x[1] - zone.x[0]),
                y: zone.y[0] + Math.random() * (zone.y[1] - zone.y[0]),
                z: 0,
            };
        }
        return {
            x: Math.random() * worldWidth,
            y: Math.random() * worldHeight,
            z: 0,
        };
    }
    selectSocialTarget(animal, nearbyAnimals) {
        const suitableTargets = nearbyAnimals.filter(target => {
            if (target.id === animal.id)
                return false;
            if (target.category === animal.category)
                return true;
            if (animal.category === animal_types_1.AnimalCategory.DIVINE_BEAST)
                return true;
            if (animal.category === animal_types_1.AnimalCategory.PET && target.currentState.happiness > 60)
                return true;
            return false;
        });
        if (suitableTargets.length === 0)
            return null;
        return suitableTargets.sort((a, b) => {
            const distA = Math.sqrt((a.position.x - animal.position.x) ** 2 + (a.position.y - animal.position.y) ** 2);
            const distB = Math.sqrt((b.position.x - animal.position.x) ** 2 + (b.position.y - animal.position.y) ** 2);
            return distA - distB;
        })[0];
    }
    getDefaultBehavior(animal) {
        return {
            type: 'idle',
            duration: 5000,
            intensity: 0.3,
            animation: 'default_idle',
            description: '静静地站立'
        };
    }
    async executeBehavior(animalId, behavior) {
        try {
            const animal = await this.zooAnimalRepository.findOne({
                where: { id: animalId, isActive: true }
            });
            if (!animal)
                return;
            this.logger.debug(`Executing behavior for ${animal.name}: ${behavior.type} (${behavior.duration}ms)`);
            await this.updateAnimalStateForBehavior(animal, behavior);
            if (this.activeAnimations.has(animalId)) {
                clearTimeout(this.activeAnimations.get(animalId));
            }
            const timeout = setTimeout(async () => {
                await this.onBehaviorComplete(animalId, behavior);
                this.activeAnimations.delete(animalId);
            }, behavior.duration);
            this.activeAnimations.set(animalId, timeout);
        }
        catch (error) {
            this.logger.error(`Failed to execute behavior for animal ${animalId}:`, error);
        }
    }
    async updateAnimalStateForBehavior(animal, behavior) {
        const stateChanges = {};
        switch (behavior.type) {
            case 'work':
                stateChanges.activity = 'working';
                stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 15);
                stateChanges.workEfficiency = Math.min(100, animal.currentState.workEfficiency + 5);
                break;
            case 'play':
                stateChanges.activity = 'playing';
                stateChanges.happiness = Math.min(100, animal.currentState.happiness + behavior.intensity * 20);
                stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 10);
                break;
            case 'rest':
                stateChanges.activity = 'resting';
                stateChanges.energy = Math.min(100, animal.currentState.energy + behavior.intensity * 25);
                stateChanges.health = Math.min(100, animal.currentState.health + 5);
                break;
            case 'move':
                stateChanges.activity = 'moving';
                stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 5);
                if (behavior.targetPosition) {
                    stateChanges.location = this.getLocationFromPosition(behavior.targetPosition);
                    animal.position = behavior.targetPosition;
                }
                break;
            case 'socialize':
                stateChanges.activity = 'socializing';
                stateChanges.happiness = Math.min(100, animal.currentState.happiness + behavior.intensity * 15);
                break;
            case 'idle':
            default:
                stateChanges.activity = 'idle';
                break;
        }
        Object.assign(animal.currentState, stateChanges);
        animal.lastActiveAt = new Date();
        await this.zooAnimalRepository.save(animal);
    }
    getLocationFromPosition(position) {
        const zooConfig = this.configService.get('zoo');
        const worldWidth = zooConfig?.worldWidth || 3200;
        if (position.x < worldWidth / 3)
            return 'work_area';
        if (position.x < worldWidth * 2 / 3)
            return 'divine_area';
        return 'leisure_area';
    }
    async onBehaviorComplete(animalId, behavior) {
        try {
            const animal = await this.zooAnimalRepository.findOne({
                where: { id: animalId, isActive: true }
            });
            if (!animal)
                return;
            if (behavior.type === 'work') {
                animal.workStats.totalWorkHours += behavior.duration / (1000 * 60 * 60);
            }
            animal.interactionStats.totalInteractions += 1;
            await this.zooAnimalRepository.save(animal);
            this.logger.debug(`Behavior completed for ${animal.name}: ${behavior.type}`);
        }
        catch (error) {
            this.logger.error(`Failed to handle behavior completion for animal ${animalId}:`, error);
        }
    }
    async getAnimalBehaviorState(animalId) {
        const timeout = this.activeAnimations.get(animalId);
        return {
            isActive: !!timeout,
            timeRemaining: timeout ? undefined : 0,
        };
    }
    async stopAnimalBehavior(animalId) {
        const timeout = this.activeAnimations.get(animalId);
        if (timeout) {
            clearTimeout(timeout);
            this.activeAnimations.delete(animalId);
            const animal = await this.zooAnimalRepository.findOne({
                where: { id: animalId, isActive: true }
            });
            if (animal) {
                animal.currentState.activity = 'idle';
                await this.zooAnimalRepository.save(animal);
            }
        }
    }
    onModuleDestroy() {
        this.activeAnimations.forEach(timeout => clearTimeout(timeout));
        this.activeAnimations.clear();
    }
};
exports.AnimalBehaviorService = AnimalBehaviorService;
exports.AnimalBehaviorService = AnimalBehaviorService = AnimalBehaviorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(zoo_animal_entity_1.ZooAnimal)),
    __param(1, (0, typeorm_1.InjectRepository)(animal_interaction_record_entity_1.AnimalInteractionRecord)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        config_1.ConfigService])
], AnimalBehaviorService);
//# sourceMappingURL=animal-behavior.service.js.map