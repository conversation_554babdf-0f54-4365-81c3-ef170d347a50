"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScoringService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_score_entity_1 = require("../entities/user-score.entity");
const animal_interaction_record_entity_1 = require("../entities/animal-interaction-record.entity");
const zoo_animal_entity_1 = require("../entities/zoo-animal.entity");
const animal_interaction_dto_1 = require("../dto/animal-interaction.dto");
const animal_types_1 = require("../../../common/constants/animal-types");
let ScoringService = class ScoringService {
    constructor(userScoreRepository, interactionRepository, animalRepository) {
        this.userScoreRepository = userScoreRepository;
        this.interactionRepository = interactionRepository;
        this.animalRepository = animalRepository;
        this.INTERACTION_BASE_SCORES = {
            FEED: 10,
            PET: 8,
            PLAY: 12,
            WORK_TOGETHER: 20,
            REST: 5,
            ENCOURAGE: 15,
        };
        this.LEVEL_EXP_TABLE = [
            0, 100, 250, 450, 700, 1000, 1400, 1850, 2350, 2900, 3500,
            4200, 5000, 5900, 6900, 8000, 9200, 10500, 11900, 13400, 15000
        ];
        this.TITLES = [
            { minScore: 0, title: '新手打工人' },
            { minScore: 500, title: '勤奋打工人' },
            { minScore: 1500, title: '资深打工人' },
            { minScore: 3000, title: '优秀打工人' },
            { minScore: 5000, title: '精英打工人' },
            { minScore: 8000, title: '模范打工人' },
            { minScore: 12000, title: '传奇打工人' },
            { minScore: 20000, title: '打工人导师' },
            { minScore: 35000, title: '打工人大师' },
            { minScore: 50000, title: '打工人传说' },
        ];
    }
    async calculateInteractionScore(userId, animalId, interactionType, effectiveness, duration) {
        const baseScore = this.INTERACTION_BASE_SCORES[interactionType] || 5;
        const effectivenessMultiplier = Math.max(0.5, Math.min(2.0, effectiveness / 5));
        const durationMultiplier = Math.min(1.5, 1 + (duration - 5) * 0.05);
        const animal = await this.animalRepository.findOne({ where: { id: animalId } });
        let animalTypeMultiplier = 1.0;
        if (animal) {
            switch (animal.category) {
                case animal_types_1.AnimalCategory.DIVINE_BEAST:
                    animalTypeMultiplier = 1.3;
                    break;
                case animal_types_1.AnimalCategory.WORKING_ANIMAL:
                    if (interactionType === animal_interaction_dto_1.InteractionType.WORK_TOGETHER) {
                        animalTypeMultiplier = 1.5;
                    }
                    break;
                case animal_types_1.AnimalCategory.PET:
                    if (interactionType === animal_interaction_dto_1.InteractionType.PET || interactionType === animal_interaction_dto_1.InteractionType.PLAY) {
                        animalTypeMultiplier = 1.2;
                    }
                    break;
            }
        }
        const recentInteractions = await this.interactionRepository.count({
            where: {
                userId,
                createdAt: (0, typeorm_2.MoreThan)(new Date(Date.now() - 10 * 60 * 1000)),
            },
        });
        const comboMultiplier = Math.min(2.0, 1 + recentInteractions * 0.1);
        const calculatedScore = Math.round(baseScore * effectivenessMultiplier * durationMultiplier * animalTypeMultiplier * comboMultiplier);
        const bonusScore = calculatedScore - baseScore;
        const experience = Math.round(calculatedScore * 0.8);
        let reason = `基础${baseScore}分`;
        if (effectivenessMultiplier !== 1) {
            reason += `, 效果${effectivenessMultiplier.toFixed(1)}倍`;
        }
        if (durationMultiplier > 1) {
            reason += `, 持续时间加成`;
        }
        if (animalTypeMultiplier > 1) {
            reason += `, ${animal?.category}类型加成`;
        }
        if (comboMultiplier > 1) {
            reason += `, 连击x${recentInteractions + 1}`;
        }
        return {
            baseScore,
            bonusScore,
            totalScore: calculatedScore,
            experience,
            reason,
        };
    }
    async updateUserScore(userId, scoreType, points, experience = 0) {
        const today = new Date().toISOString().split('T')[0];
        const currentWeek = this.getWeekPeriod(new Date());
        const currentMonth = new Date().toISOString().slice(0, 7);
        const periods = [
            { type: 'TOTAL', period: 'all-time' },
            { type: 'DAILY', period: today },
            { type: 'WEEKLY', period: currentWeek },
            { type: 'MONTHLY', period: currentMonth },
        ];
        const updatedScores = [];
        for (const { type, period } of periods) {
            let userScore = await this.userScoreRepository.findOne({
                where: { userId, scoreType: type, period },
                relations: ['user'],
            });
            if (!userScore) {
                userScore = this.userScoreRepository.create({
                    userId,
                    scoreType: type,
                    period,
                    totalScore: 0,
                    interactionScore: 0,
                    workPerformanceScore: 0,
                    socialScore: 0,
                    achievementScore: 0,
                    creativityScore: 0,
                    interactionStats: {
                        totalInteractions: 0,
                        feedCount: 0,
                        petCount: 0,
                        playCount: 0,
                        workTogetherCount: 0,
                        restCount: 0,
                        encourageCount: 0,
                        averageEffectiveness: 0,
                    },
                    workStats: {
                        totalWorkHours: 0,
                        averageEfficiency: 0,
                        completedTasks: 0,
                        promotions: 0,
                        teamworkScore: 0,
                        overtimeHours: 0,
                    },
                    socialStats: {
                        friendsMade: 0,
                        helpGiven: 0,
                        popularityScore: 0,
                        leadershipScore: 0,
                        mentorshipScore: 0,
                    },
                    achievementStats: {
                        totalAchievements: 0,
                        rareAchievements: 0,
                        firstTimeAchievements: 0,
                        challengesCompleted: 0,
                    },
                    creativityStats: {
                        avatarsCreated: 0,
                        uniqueInteractions: 0,
                        innovativeApproaches: 0,
                        artisticScore: 0,
                    },
                    level: 1,
                    experience: 0,
                    experienceToNextLevel: 100,
                    reputation: 100,
                    title: '新手打工人',
                    badges: [],
                    streaks: {
                        dailyLogin: 0,
                        maxDailyLogin: 0,
                        weeklyActive: 0,
                        maxWeeklyActive: 0,
                        consecutiveWork: 0,
                        maxConsecutiveWork: 0,
                    },
                    globalRank: 9999,
                    dailyRank: 9999,
                    weeklyRank: 9999,
                    monthlyRank: 9999,
                    lastUpdatedAt: new Date(),
                });
            }
            switch (scoreType) {
                case 'interaction':
                    userScore.interactionScore += points;
                    break;
                case 'work':
                    userScore.workPerformanceScore += points;
                    break;
                case 'social':
                    userScore.socialScore += points;
                    break;
                case 'achievement':
                    userScore.achievementScore += points;
                    break;
                case 'creativity':
                    userScore.creativityScore += points;
                    break;
            }
            userScore.totalScore += points;
            userScore.experience += experience;
            const newLevel = this.calculateLevel(userScore.experience);
            if (newLevel > userScore.level) {
                userScore.level = newLevel;
                userScore.totalScore += newLevel * 50;
            }
            userScore.title = this.calculateTitle(userScore.totalScore);
            userScore.experienceToNextLevel = this.getNextLevelExp(userScore.level) - userScore.experience;
            userScore.lastUpdatedAt = new Date();
            updatedScores.push(await this.userScoreRepository.save(userScore));
        }
        return updatedScores.find(s => s.scoreType === 'TOTAL');
    }
    calculateLevel(experience) {
        for (let i = this.LEVEL_EXP_TABLE.length - 1; i >= 0; i--) {
            if (experience >= this.LEVEL_EXP_TABLE[i]) {
                return i + 1;
            }
        }
        return 1;
    }
    getNextLevelExp(level) {
        if (level >= this.LEVEL_EXP_TABLE.length) {
            return this.LEVEL_EXP_TABLE[this.LEVEL_EXP_TABLE.length - 1] + (level - this.LEVEL_EXP_TABLE.length + 1) * 2000;
        }
        return this.LEVEL_EXP_TABLE[level] || 100;
    }
    calculateTitle(totalScore) {
        for (let i = this.TITLES.length - 1; i >= 0; i--) {
            if (totalScore >= this.TITLES[i].minScore) {
                return this.TITLES[i].title;
            }
        }
        return '新手打工人';
    }
    getWeekPeriod(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const weekDay = date.getDay();
        const monday = new Date(date);
        monday.setDate(date.getDate() - (weekDay === 0 ? 6 : weekDay - 1));
        return `${year}-W${String(Math.ceil(monday.getDate() / 7)).padStart(2, '0')}-${month}`;
    }
    async getLeaderboard(period = 'total', limit = 50, offset = 0) {
        const scoreType = period === 'daily' ? 'DAILY' :
            period === 'weekly' ? 'WEEKLY' :
                period === 'monthly' ? 'MONTHLY' : 'TOTAL';
        let whereClause = { scoreType };
        if (period !== 'total') {
            const today = new Date().toISOString().split('T')[0];
            const currentWeek = this.getWeekPeriod(new Date());
            const currentMonth = new Date().toISOString().slice(0, 7);
            const periodValue = period === 'daily' ? today :
                period === 'weekly' ? currentWeek : currentMonth;
            whereClause = { ...whereClause, period: periodValue };
        }
        const [userScores, total] = await this.userScoreRepository.findAndCount({
            where: whereClause,
            relations: ['user'],
            order: { totalScore: 'DESC' },
            take: limit,
            skip: offset,
        });
        const leaderboard = userScores.map((score, index) => ({
            userId: score.userId,
            username: score.user?.username || 'Unknown',
            avatarUrl: score.user?.profileImage,
            totalScore: score.totalScore,
            level: score.level,
            title: score.title,
            rank: offset + index + 1,
            badges: score.badges.slice(0, 3).map(badge => ({
                name: badge.name,
                icon: badge.icon,
                rarity: badge.rarity,
            })),
            stats: {
                interactionScore: score.interactionScore,
                workPerformanceScore: score.workPerformanceScore,
                socialScore: score.socialScore,
                achievementScore: score.achievementScore,
                creativityScore: score.creativityScore,
            },
        }));
        return {
            leaderboard,
            total,
        };
    }
    async getUserRanking(userId) {
        const totalScore = await this.userScoreRepository.findOne({
            where: { userId, scoreType: 'TOTAL' },
        });
        if (!totalScore) {
            return {
                globalRank: 9999,
                dailyRank: 9999,
                weeklyRank: 9999,
                monthlyRank: 9999,
                totalScore: 0,
                level: 1,
                title: '新手打工人',
            };
        }
        const globalRank = await this.calculateRank('TOTAL', 'all-time', totalScore.totalScore);
        const dailyRank = await this.calculateRank('DAILY', new Date().toISOString().split('T')[0], totalScore.totalScore);
        const weeklyRank = await this.calculateRank('WEEKLY', this.getWeekPeriod(new Date()), totalScore.totalScore);
        const monthlyRank = await this.calculateRank('MONTHLY', new Date().toISOString().slice(0, 7), totalScore.totalScore);
        return {
            globalRank,
            dailyRank,
            weeklyRank,
            monthlyRank,
            totalScore: totalScore.totalScore,
            level: totalScore.level,
            title: totalScore.title,
        };
    }
    async calculateRank(scoreType, period, userScore) {
        const count = await this.userScoreRepository.count({
            where: {
                scoreType: scoreType,
                period: period,
                totalScore: (0, typeorm_2.MoreThan)(userScore),
            },
        });
        return count + 1;
    }
    async processInteractionScore(interactionRecord) {
        const scoreResult = await this.calculateInteractionScore(interactionRecord.userId, interactionRecord.animalId, interactionRecord.interactionType, interactionRecord.effectiveness, interactionRecord.duration);
        await this.updateUserScore(interactionRecord.userId, 'interaction', scoreResult.totalScore, scoreResult.experience);
        interactionRecord.experienceGained = scoreResult.experience;
        await this.interactionRepository.save(interactionRecord);
    }
};
exports.ScoringService = ScoringService;
exports.ScoringService = ScoringService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_score_entity_1.UserScore)),
    __param(1, (0, typeorm_1.InjectRepository)(animal_interaction_record_entity_1.AnimalInteractionRecord)),
    __param(2, (0, typeorm_1.InjectRepository)(zoo_animal_entity_1.ZooAnimal)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ScoringService);
//# sourceMappingURL=scoring.service.js.map