"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const api_response_decorator_1 = require("../../common/decorators/api-response.decorator");
const zoo_service_1 = require("./zoo.service");
const animal_behavior_service_1 = require("./services/animal-behavior.service");
const zoo_ai_scheduler_service_1 = require("./services/zoo-ai-scheduler.service");
const dto_1 = require("./dto");
const leaderboard_dto_1 = require("./dto/leaderboard.dto");
const user_entity_1 = require("../user/entities/user.entity");
const scoring_service_1 = require("./services/scoring.service");
let ZooController = class ZooController {
    constructor(zooService, animalBehaviorService, zooAISchedulerService, scoringService) {
        this.zooService = zooService;
        this.animalBehaviorService = animalBehaviorService;
        this.zooAISchedulerService = zooAISchedulerService;
        this.scoringService = scoringService;
    }
    async getZooScene(user, query) {
        return this.zooService.getZooScene(user.id, query);
    }
    async getZooAnimals(user, query) {
        return this.zooService.getZooAnimals(user.id, query);
    }
    async interactWithAnimal(user, animalId, interactionDto) {
        return this.zooService.interactWithAnimal(user.id, animalId, interactionDto);
    }
    async getAnimalDetails(user, animalId) {
        return { message: '动物详情接口，待实现具体逻辑' };
    }
    async createAnimalFromAvatar(user, avatarId, createDto) {
        return this.zooService.createZooAnimalFromAvatar(user.id, avatarId, createDto?.name);
    }
    async getEnvironmentState() {
        return this.zooAISchedulerService.getEnvironmentState();
    }
    async getZooStatistics() {
        return this.zooAISchedulerService.getZooStatistics();
    }
    async getAnimalBehavior(animalId) {
        return this.animalBehaviorService.getAnimalBehaviorState(animalId);
    }
    async stopAnimalBehavior(animalId) {
        await this.animalBehaviorService.stopAnimalBehavior(animalId);
        return { message: '动物行为已停止' };
    }
    async triggerEvent(eventDto) {
        const success = await this.zooAISchedulerService.triggerManualEvent(eventDto.type, eventDto.duration);
        return {
            success,
            message: success ? '事件触发成功' : '事件触发失败'
        };
    }
    async controlScheduler(controlDto) {
        this.zooAISchedulerService.setSchedulerState(controlDto.enabled);
        return {
            message: `AI调度器已${controlDto.enabled ? '启动' : '暂停'}`
        };
    }
    async getBehaviorPatterns() {
        return {
            message: '行为模式调试信息',
            patterns: {
                dragon: '神兽行为模式：威严巡视、工作指导、冥想修炼、祝福仪式',
                cat: '宠物行为模式：愉快玩耍、舒适休息、友好社交、优雅漫步',
                ox: '工作动物行为模式：勤奋工作、疲惫休息、协作互动、稳重移动'
            }
        };
    }
    async getLeaderboard(query) {
        const { leaderboard, total } = await this.scoringService.getLeaderboard(query.period, query.limit, query.offset);
        const totalPages = Math.ceil(total / query.limit);
        const currentPage = Math.floor(query.offset / query.limit) + 1;
        return {
            leaderboard,
            total,
            page: currentPage,
            limit: query.limit,
            totalPages,
            period: query.period,
            updatedAt: new Date(),
        };
    }
    async getMyRanking(user) {
        return this.scoringService.getUserRanking(user.id);
    }
    async getMyScoreStats(user) {
        const totalScore = await this.scoringService['userScoreRepository'].findOne({
            where: { userId: user.id, scoreType: 'TOTAL' },
        });
        if (!totalScore) {
            const initialScore = await this.scoringService.updateUserScore(user.id, 'social', 0, 0);
            const rankings = await this.scoringService.getUserRanking(user.id);
            return {
                userId: user.id,
                totalScore: initialScore.totalScore,
                level: initialScore.level,
                experience: initialScore.experience,
                experienceToNextLevel: initialScore.experienceToNextLevel,
                title: initialScore.title,
                reputation: initialScore.reputation,
                categoryScores: {
                    interactionScore: initialScore.interactionScore,
                    workPerformanceScore: initialScore.workPerformanceScore,
                    socialScore: initialScore.socialScore,
                    achievementScore: initialScore.achievementScore,
                    creativityScore: initialScore.creativityScore,
                },
                rankings: {
                    globalRank: rankings.globalRank,
                    dailyRank: rankings.dailyRank,
                    weeklyRank: rankings.weeklyRank,
                    monthlyRank: rankings.monthlyRank,
                },
                badges: initialScore.badges,
                streaks: initialScore.streaks,
                lastUpdatedAt: initialScore.lastUpdatedAt,
            };
        }
        const rankings = await this.scoringService.getUserRanking(user.id);
        return {
            userId: user.id,
            totalScore: totalScore.totalScore,
            level: totalScore.level,
            experience: totalScore.experience,
            experienceToNextLevel: totalScore.experienceToNextLevel,
            title: totalScore.title,
            reputation: totalScore.reputation,
            categoryScores: {
                interactionScore: totalScore.interactionScore,
                workPerformanceScore: totalScore.workPerformanceScore,
                socialScore: totalScore.socialScore,
                achievementScore: totalScore.achievementScore,
                creativityScore: totalScore.creativityScore,
            },
            rankings: {
                globalRank: rankings.globalRank,
                dailyRank: rankings.dailyRank,
                weeklyRank: rankings.weeklyRank,
                monthlyRank: rankings.monthlyRank,
            },
            badges: totalScore.badges,
            streaks: totalScore.streaks,
            lastUpdatedAt: totalScore.lastUpdatedAt,
        };
    }
    async updateScore(user, scoreUpdateDto) {
        const updatedScore = await this.scoringService.updateUserScore(user.id, scoreUpdateDto.scoreType, scoreUpdateDto.points, scoreUpdateDto.experience);
        return {
            success: true,
            message: `积分更新成功，${scoreUpdateDto.reason || '获得'}${scoreUpdateDto.points}分`,
            data: {
                totalScore: updatedScore.totalScore,
                level: updatedScore.level,
                experience: updatedScore.experience,
                title: updatedScore.title,
            },
        };
    }
    async getScoreHistory(userId, period, limit = 30) {
        return {
            message: '积分历史接口，待实现具体逻辑',
            userId,
            period,
            limit,
        };
    }
    async getAchievements(user) {
        const achievements = [
            {
                id: 'first_interaction',
                name: '初次相遇',
                description: '完成第一次动物互动',
                category: '互动',
                rarity: 'COMMON',
                icon: '🤝',
                requirement: '完成1次互动',
                progress: 0,
                maxProgress: 1,
                reward: { score: 50, experience: 25 },
            },
            {
                id: 'social_butterfly',
                name: '社交达人',
                description: '与10只不同的动物互动',
                category: '社交',
                rarity: 'UNCOMMON',
                icon: '🦋',
                requirement: '与10只不同动物互动',
                progress: 0,
                maxProgress: 10,
                reward: { score: 200, experience: 100 },
            },
            {
                id: 'workaholic',
                name: '工作狂魔',
                description: '累计工作时间达到100小时',
                category: '工作',
                rarity: 'RARE',
                icon: '💼',
                requirement: '工作100小时',
                progress: 0,
                maxProgress: 100,
                reward: { score: 500, experience: 250 },
            },
            {
                id: 'legend_status',
                name: '传奇打工人',
                description: '达到20级并获得10000分',
                category: '成就',
                rarity: 'LEGENDARY',
                icon: '👑',
                requirement: '20级且10000分',
                progress: 0,
                maxProgress: 1,
                reward: { score: 2000, experience: 1000 },
            },
        ];
        return {
            achievements,
            message: '成就系统数据，实际进度需要从数据库计算',
        };
    }
    async claimAchievement(user, achievementId) {
        return {
            success: true,
            message: `成就"${achievementId}"奖励已领取`,
            reward: {
                score: 100,
                experience: 50,
                badge: {
                    name: '成就大师',
                    icon: '🏆',
                    rarity: 'RARE',
                },
            },
        };
    }
};
exports.ZooController = ZooController;
__decorate([
    (0, common_1.Get)('scene'),
    (0, swagger_1.ApiOperation)({
        summary: '获取动物园场景数据',
        description: '获取当前动物园的完整场景信息，包括动物、环境、设施和事件'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '场景数据获取成功',
        type: dto_1.ZooSceneResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.ZooSceneResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.ZooSceneQueryDto]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getZooScene", null);
__decorate([
    (0, common_1.Get)('animals'),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户动物列表',
        description: '分页获取动物园中的动物列表，支持按类型和种类筛选'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '动物列表获取成功',
        type: dto_1.ZooAnimalsResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.ZooAnimalsResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.ZooAnimalsQueryDto]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getZooAnimals", null);
__decorate([
    (0, common_1.Post)('animals/:id/interact'),
    (0, swagger_1.ApiOperation)({
        summary: '与动物互动',
        description: '与指定动物进行各种类型的互动，如喂食、抚摸、玩耍等'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '互动成功',
        type: dto_1.AnimalInteractionResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.AnimalInteractionResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String, dto_1.AnimalInteractionDto]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "interactWithAnimal", null);
__decorate([
    (0, common_1.Get)('animals/:id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取动物详情',
        description: '获取指定动物的详细信息'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '动物详情获取成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getAnimalDetails", null);
__decorate([
    (0, common_1.Post)('animals/create-from-avatar/:avatarId'),
    (0, swagger_1.ApiOperation)({
        summary: '从头像创建动物园动物',
        description: '基于生成的头像在动物园中创建对应的动物'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '动物创建成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('avatarId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String, Object]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "createAnimalFromAvatar", null);
__decorate([
    (0, common_1.Get)('environment'),
    (0, swagger_1.ApiOperation)({
        summary: '获取动物园环境状态',
        description: '获取当前动物园的环境信息，包括天气、时间、事件等'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '环境状态获取成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getEnvironmentState", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: '获取动物园统计信息',
        description: '获取动物园的综合统计信息，包括动物数量、活跃度等'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '统计信息获取成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getZooStatistics", null);
__decorate([
    (0, common_1.Get)('animals/:id/behavior'),
    (0, swagger_1.ApiOperation)({
        summary: '获取动物行为状态',
        description: '获取指定动物当前的行为状态'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '动物行为状态获取成功',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getAnimalBehavior", null);
__decorate([
    (0, common_1.Post)('animals/:id/behavior/stop'),
    (0, swagger_1.ApiOperation)({
        summary: '停止动物当前行为',
        description: '强制停止指定动物的当前行为'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '动物行为已停止',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "stopAnimalBehavior", null);
__decorate([
    (0, common_1.Post)('events/trigger'),
    (0, swagger_1.ApiOperation)({
        summary: '触发动物园事件',
        description: '手动触发一个动物园范围的事件'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '事件触发成功',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "triggerEvent", null);
__decorate([
    (0, common_1.Put)('ai/scheduler'),
    (0, swagger_1.ApiOperation)({
        summary: '控制AI调度器状态',
        description: '启动或暂停动物园AI行为调度器'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'AI调度器状态更新成功',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "controlScheduler", null);
__decorate([
    (0, common_1.Get)('debug/behavior-patterns'),
    (0, swagger_1.ApiOperation)({
        summary: '获取行为模式调试信息',
        description: '开发用：获取所有动物的行为模式定义'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '行为模式信息获取成功',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getBehaviorPatterns", null);
__decorate([
    (0, common_1.Get)('leaderboard'),
    (0, swagger_1.ApiOperation)({
        summary: '获取排行榜',
        description: '获取打工人积分排行榜，支持不同时间周期的排名'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '排行榜数据获取成功',
        type: leaderboard_dto_1.LeaderboardResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(leaderboard_dto_1.LeaderboardResponseDto),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leaderboard_dto_1.LeaderboardQueryDto]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getLeaderboard", null);
__decorate([
    (0, common_1.Get)('my-ranking'),
    (0, swagger_1.ApiOperation)({
        summary: '获取我的排名信息',
        description: '获取当前用户在各个时间周期的排名情况'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '排名信息获取成功',
        type: leaderboard_dto_1.UserRankingDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(leaderboard_dto_1.UserRankingDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getMyRanking", null);
__decorate([
    (0, common_1.Get)('my-score-stats'),
    (0, swagger_1.ApiOperation)({
        summary: '获取我的积分统计',
        description: '获取当前用户的详细积分统计信息，包括各类积分、等级、徽章等'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '积分统计获取成功',
        type: leaderboard_dto_1.UserScoreStatsDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(leaderboard_dto_1.UserScoreStatsDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getMyScoreStats", null);
__decorate([
    (0, common_1.Post)('update-score'),
    (0, swagger_1.ApiOperation)({
        summary: '手动更新积分',
        description: '管理员接口：手动为用户增加或减少积分'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '积分更新成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        leaderboard_dto_1.ScoreUpdateDto]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "updateScore", null);
__decorate([
    (0, common_1.Get)('score-history/:userId'),
    (0, swagger_1.ApiOperation)({
        summary: '获取积分历史',
        description: '获取指定用户的积分变化历史记录'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '积分历史获取成功',
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getScoreHistory", null);
__decorate([
    (0, common_1.Get)('achievements'),
    (0, swagger_1.ApiOperation)({
        summary: '获取成就系统',
        description: '获取所有可获得的成就和用户当前的成就进度'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成就信息获取成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "getAchievements", null);
__decorate([
    (0, common_1.Post)('claim-achievement/:achievementId'),
    (0, swagger_1.ApiOperation)({
        summary: '领取成就奖励',
        description: '领取已完成成就的奖励'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成就奖励领取成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('achievementId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], ZooController.prototype, "claimAchievement", null);
exports.ZooController = ZooController = __decorate([
    (0, swagger_1.ApiTags)('Zoo - 动物园管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/zoo'),
    __metadata("design:paramtypes", [zoo_service_1.ZooService,
        animal_behavior_service_1.AnimalBehaviorService,
        zoo_ai_scheduler_service_1.ZooAISchedulerService,
        scoring_service_1.ScoringService])
], ZooController);
//# sourceMappingURL=zoo.controller.js.map