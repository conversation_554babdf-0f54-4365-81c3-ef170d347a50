"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooSceneResponseDto = exports.ZooEnvironmentDto = exports.AnimalInSceneDto = exports.ZooSceneQueryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const animal_types_1 = require("../../../common/constants/animal-types");
class ZooSceneQueryDto {
}
exports.ZooSceneQueryDto = ZooSceneQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '场景主题筛选',
        enum: animal_types_1.AnimalCategory,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalCategory),
    __metadata("design:type", String)
], ZooSceneQueryDto.prototype, "theme", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间段（小时 0-23）',
        minimum: 0,
        maximum: 23,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(23),
    __metadata("design:type", Number)
], ZooSceneQueryDto.prototype, "timeOfDay", void 0);
class AnimalInSceneDto {
}
exports.AnimalInSceneDto = AnimalInSceneDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物ID' }),
    __metadata("design:type", String)
], AnimalInSceneDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物种类' }),
    __metadata("design:type", String)
], AnimalInSceneDto.prototype, "species", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物名称' }),
    __metadata("design:type", String)
], AnimalInSceneDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '在场景中的位置' }),
    __metadata("design:type", Object)
], AnimalInSceneDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物状态' }),
    __metadata("design:type", Object)
], AnimalInSceneDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像图片URL' }),
    __metadata("design:type", String)
], AnimalInSceneDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物类型' }),
    __metadata("design:type", String)
], AnimalInSceneDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为用户的动物' }),
    __metadata("design:type", Boolean)
], AnimalInSceneDto.prototype, "isUserAnimal", void 0);
class ZooEnvironmentDto {
}
exports.ZooEnvironmentDto = ZooEnvironmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '天气状况' }),
    __metadata("design:type", Object)
], ZooEnvironmentDto.prototype, "weather", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '时间信息' }),
    __metadata("design:type", Object)
], ZooEnvironmentDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '环境背景' }),
    __metadata("design:type", Object)
], ZooEnvironmentDto.prototype, "background", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '环境音效' }),
    __metadata("design:type", Array)
], ZooEnvironmentDto.prototype, "sounds", void 0);
class ZooSceneResponseDto {
}
exports.ZooSceneResponseDto = ZooSceneResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景ID' }),
    __metadata("design:type", String)
], ZooSceneResponseDto.prototype, "sceneId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景名称' }),
    __metadata("design:type", String)
], ZooSceneResponseDto.prototype, "sceneName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景描述' }),
    __metadata("design:type", String)
], ZooSceneResponseDto.prototype, "sceneDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '环境信息', type: ZooEnvironmentDto }),
    __metadata("design:type", ZooEnvironmentDto)
], ZooSceneResponseDto.prototype, "environment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景中的动物列表', type: [AnimalInSceneDto] }),
    __metadata("design:type", Array)
], ZooSceneResponseDto.prototype, "animals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景设施' }),
    __metadata("design:type", Array)
], ZooSceneResponseDto.prototype, "facilities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '特殊事件' }),
    __metadata("design:type", Array)
], ZooSceneResponseDto.prototype, "events", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景统计' }),
    __metadata("design:type", Object)
], ZooSceneResponseDto.prototype, "statistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间' }),
    __metadata("design:type", Date)
], ZooSceneResponseDto.prototype, "lastUpdated", void 0);
//# sourceMappingURL=zoo-scene.dto.js.map