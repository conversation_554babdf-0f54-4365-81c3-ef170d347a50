"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserScoreStatsDto = exports.ScoreUpdateDto = exports.UserRankingDto = exports.LeaderboardResponseDto = exports.LeaderboardEntryDto = exports.LeaderboardQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class LeaderboardQueryDto {
    constructor() {
        this.period = 'total';
        this.limit = 20;
        this.offset = 0;
    }
}
exports.LeaderboardQueryDto = LeaderboardQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '排行榜周期类型',
        enum: ['total', 'daily', 'weekly', 'monthly'],
        default: 'total'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['total', 'daily', 'weekly', 'monthly']),
    __metadata("design:type", String)
], LeaderboardQueryDto.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量', minimum: 1, maximum: 100, default: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], LeaderboardQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '偏移量', minimum: 0, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], LeaderboardQueryDto.prototype, "offset", void 0);
class LeaderboardEntryDto {
}
exports.LeaderboardEntryDto = LeaderboardEntryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], LeaderboardEntryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名' }),
    __metadata("design:type", String)
], LeaderboardEntryDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '头像URL' }),
    __metadata("design:type", String)
], LeaderboardEntryDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总积分' }),
    __metadata("design:type", Number)
], LeaderboardEntryDto.prototype, "totalScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级' }),
    __metadata("design:type", Number)
], LeaderboardEntryDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '称号' }),
    __metadata("design:type", String)
], LeaderboardEntryDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排名' }),
    __metadata("design:type", Number)
], LeaderboardEntryDto.prototype, "rank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '徽章列表',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                icon: { type: 'string' },
                rarity: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Array)
], LeaderboardEntryDto.prototype, "badges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类积分统计',
        type: 'object',
        properties: {
            interactionScore: { type: 'number' },
            workPerformanceScore: { type: 'number' },
            socialScore: { type: 'number' },
            achievementScore: { type: 'number' },
            creativityScore: { type: 'number' }
        }
    }),
    __metadata("design:type", Object)
], LeaderboardEntryDto.prototype, "stats", void 0);
class LeaderboardResponseDto {
}
exports.LeaderboardResponseDto = LeaderboardResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排行榜列表', type: [LeaderboardEntryDto] }),
    __metadata("design:type", Array)
], LeaderboardResponseDto.prototype, "leaderboard", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量' }),
    __metadata("design:type", Number)
], LeaderboardResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页' }),
    __metadata("design:type", Number)
], LeaderboardResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量' }),
    __metadata("design:type", Number)
], LeaderboardResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数' }),
    __metadata("design:type", Number)
], LeaderboardResponseDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排行榜周期' }),
    __metadata("design:type", String)
], LeaderboardResponseDto.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], LeaderboardResponseDto.prototype, "updatedAt", void 0);
class UserRankingDto {
}
exports.UserRankingDto = UserRankingDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '全球排名' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "globalRank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日排名' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "dailyRank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每周排名' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "weeklyRank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每月排名' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "monthlyRank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总积分' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "totalScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级' }),
    __metadata("design:type", Number)
], UserRankingDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '称号' }),
    __metadata("design:type", String)
], UserRankingDto.prototype, "title", void 0);
class ScoreUpdateDto {
    constructor() {
        this.experience = 0;
    }
}
exports.ScoreUpdateDto = ScoreUpdateDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分类型', enum: ['interaction', 'work', 'social', 'achievement', 'creativity'] }),
    (0, class_validator_1.IsEnum)(['interaction', 'work', 'social', 'achievement', 'creativity']),
    __metadata("design:type", String)
], ScoreUpdateDto.prototype, "scoreType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分数值', minimum: -1000, maximum: 10000 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-1000),
    (0, class_validator_1.Max)(10000),
    __metadata("design:type", Number)
], ScoreUpdateDto.prototype, "points", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '经验值', minimum: 0, maximum: 5000, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(5000),
    __metadata("design:type", Number)
], ScoreUpdateDto.prototype, "experience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '积分获得原因' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ScoreUpdateDto.prototype, "reason", void 0);
class UserScoreStatsDto {
}
exports.UserScoreStatsDto = UserScoreStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], UserScoreStatsDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总积分' }),
    __metadata("design:type", Number)
], UserScoreStatsDto.prototype, "totalScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级' }),
    __metadata("design:type", Number)
], UserScoreStatsDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前经验值' }),
    __metadata("design:type", Number)
], UserScoreStatsDto.prototype, "experience", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '升级所需经验值' }),
    __metadata("design:type", Number)
], UserScoreStatsDto.prototype, "experienceToNextLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '称号' }),
    __metadata("design:type", String)
], UserScoreStatsDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '声望值' }),
    __metadata("design:type", Number)
], UserScoreStatsDto.prototype, "reputation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类积分',
        type: 'object',
        properties: {
            interactionScore: { type: 'number' },
            workPerformanceScore: { type: 'number' },
            socialScore: { type: 'number' },
            achievementScore: { type: 'number' },
            creativityScore: { type: 'number' }
        }
    }),
    __metadata("design:type", Object)
], UserScoreStatsDto.prototype, "categoryScores", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排名信息',
        type: 'object',
        properties: {
            globalRank: { type: 'number' },
            dailyRank: { type: 'number' },
            weeklyRank: { type: 'number' },
            monthlyRank: { type: 'number' }
        }
    }),
    __metadata("design:type", Object)
], UserScoreStatsDto.prototype, "rankings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '徽章列表',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                category: { type: 'string' },
                rarity: { type: 'string' },
                earnedAt: { type: 'string', format: 'date-time' },
                icon: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Array)
], UserScoreStatsDto.prototype, "badges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '连续记录',
        type: 'object',
        properties: {
            dailyLogin: { type: 'number' },
            maxDailyLogin: { type: 'number' },
            weeklyActive: { type: 'number' },
            maxWeeklyActive: { type: 'number' },
            consecutiveWork: { type: 'number' },
            maxConsecutiveWork: { type: 'number' }
        }
    }),
    __metadata("design:type", Object)
], UserScoreStatsDto.prototype, "streaks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间' }),
    __metadata("design:type", Date)
], UserScoreStatsDto.prototype, "lastUpdatedAt", void 0);
//# sourceMappingURL=leaderboard.dto.js.map