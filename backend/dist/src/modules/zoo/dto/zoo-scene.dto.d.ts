import { AnimalCategory } from '@/common/constants/animal-types';
export declare class ZooSceneQueryDto {
    theme?: AnimalCategory;
    timeOfDay?: number;
}
export declare class AnimalInSceneDto {
    id: string;
    species: string;
    name: string;
    position: {
        x: number;
        y: number;
        z: number;
    };
    state: {
        mood: string;
        activity: string;
        energy: number;
        happiness: number;
    };
    avatarUrl: string;
    category: AnimalCategory;
    isUserAnimal: boolean;
}
export declare class ZooEnvironmentDto {
    weather: {
        condition: string;
        temperature: number;
        humidity: number;
        description: string;
    };
    time: {
        hour: number;
        period: string;
        season: string;
    };
    background: {
        skyColor: string;
        groundColor: string;
        lighting: string;
        ambience: string;
    };
    sounds: string[];
}
export declare class ZooSceneResponseDto {
    sceneId: string;
    sceneName: string;
    sceneDescription: string;
    environment: ZooEnvironmentDto;
    animals: AnimalInSceneDto[];
    facilities: Array<{
        id: string;
        name: string;
        type: string;
        position: {
            x: number;
            y: number;
            z: number;
        };
        description: string;
    }>;
    events: Array<{
        id: string;
        name: string;
        description: string;
        startTime: Date;
        duration: number;
        participants: string[];
    }>;
    statistics: {
        totalAnimals: number;
        userAnimals: number;
        averageHappiness: number;
        activeAnimals: number;
    };
    lastUpdated: Date;
}
