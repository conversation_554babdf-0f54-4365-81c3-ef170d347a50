import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class ZooAnimalsQueryDto {
    page?: number;
    limit?: number;
    category?: AnimalCategory;
    species?: AnimalSpecies;
    onlyUserAnimals?: boolean;
}
export declare class ZooAnimalDto {
    id: string;
    name: string;
    species: AnimalSpecies;
    category: AnimalCategory;
    avatarUrl: string;
    avatarBase64?: string;
    attributes: {
        [key: string]: number;
    };
    currentState: {
        mood: string;
        activity: string;
        location: string;
        energy: number;
        happiness: number;
        health: number;
    };
    interactions: {
        totalInteractions: number;
        lastInteractionTime?: Date;
        favoriteActivities: string[];
    };
    workStats: {
        workEfficiency: number;
        productivity: number;
        attendance: number;
        teamwork: number;
    };
    isUserAnimal: boolean;
    ownerNickname?: string;
    createdAt: Date;
    lastActiveAt: Date;
}
export declare class ZooAnimalsResponseDto {
    animals: ZooAnimalDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    statistics: {
        totalAnimals: number;
        userAnimals: number;
        categoryCounts: Record<AnimalCategory, number>;
        averageHappiness: number;
        mostActiveSpecies: AnimalSpecies;
    };
}
