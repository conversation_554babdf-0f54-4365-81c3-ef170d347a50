"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimalInteractionResponseDto = exports.AnimalInteractionDto = exports.InteractionType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
var InteractionType;
(function (InteractionType) {
    InteractionType["FEED"] = "feed";
    InteractionType["PET"] = "pet";
    InteractionType["PLAY"] = "play";
    InteractionType["WORK_TOGETHER"] = "work_together";
    InteractionType["ENCOURAGE"] = "encourage";
    InteractionType["REST"] = "rest";
    InteractionType["TRAIN"] = "train";
    InteractionType["CHAT"] = "chat";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
class AnimalInteractionDto {
    constructor() {
        this.intensity = 5;
        this.duration = 5;
    }
}
exports.AnimalInteractionDto = AnimalInteractionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '互动类型',
        enum: InteractionType,
    }),
    (0, class_validator_1.IsEnum)(InteractionType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AnimalInteractionDto.prototype, "interactionType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '互动强度 1-10',
        minimum: 1,
        maximum: 10,
        default: 5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], AnimalInteractionDto.prototype, "intensity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '互动消息或备注',
        required: false,
        maxLength: 200,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AnimalInteractionDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '互动持续时间（分钟）',
        minimum: 1,
        maximum: 60,
        default: 5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(60),
    __metadata("design:type", Number)
], AnimalInteractionDto.prototype, "duration", void 0);
class AnimalInteractionResponseDto {
}
exports.AnimalInteractionResponseDto = AnimalInteractionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '互动是否成功' }),
    __metadata("design:type", Boolean)
], AnimalInteractionResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '互动结果' }),
    __metadata("design:type", Object)
], AnimalInteractionResponseDto.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物状态变化' }),
    __metadata("design:type", Object)
], AnimalInteractionResponseDto.prototype, "stateChanges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物反应' }),
    __metadata("design:type", Object)
], AnimalInteractionResponseDto.prototype, "animalReaction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获得的奖励' }),
    __metadata("design:type", Object)
], AnimalInteractionResponseDto.prototype, "rewards", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '下次互动建议' }),
    __metadata("design:type", Object)
], AnimalInteractionResponseDto.prototype, "suggestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '互动时间' }),
    __metadata("design:type", Date)
], AnimalInteractionResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '消息' }),
    __metadata("design:type", String)
], AnimalInteractionResponseDto.prototype, "message", void 0);
//# sourceMappingURL=animal-interaction.dto.js.map