"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooAnimalsResponseDto = exports.ZooAnimalDto = exports.ZooAnimalsQueryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const animal_types_1 = require("../../../common/constants/animal-types");
class ZooAnimalsQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 20;
        this.onlyUserAnimals = false;
    }
}
exports.ZooAnimalsQueryDto = ZooAnimalsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        default: 1,
        minimum: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ZooAnimalsQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        default: 20,
        minimum: 1,
        maximum: 100,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ZooAnimalsQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '动物类型筛选',
        enum: animal_types_1.AnimalCategory,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalCategory),
    __metadata("design:type", String)
], ZooAnimalsQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '动物种类筛选',
        enum: animal_types_1.AnimalSpecies,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalSpecies),
    __metadata("design:type", String)
], ZooAnimalsQueryDto.prototype, "species", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否只显示用户的动物',
        default: false,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], ZooAnimalsQueryDto.prototype, "onlyUserAnimals", void 0);
class ZooAnimalDto {
}
exports.ZooAnimalDto = ZooAnimalDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物ID' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物名称（用户自定义或默认）' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物种类' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "species", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物类型' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像图片URL' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成头像的base64数据' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "avatarBase64", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物属性' }),
    __metadata("design:type", Object)
], ZooAnimalDto.prototype, "attributes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前状态' }),
    __metadata("design:type", Object)
], ZooAnimalDto.prototype, "currentState", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '互动统计' }),
    __metadata("design:type", Object)
], ZooAnimalDto.prototype, "interactions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工作统计' }),
    __metadata("design:type", Object)
], ZooAnimalDto.prototype, "workStats", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为用户的动物' }),
    __metadata("design:type", Boolean)
], ZooAnimalDto.prototype, "isUserAnimal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属用户昵称（如果不是隐私）' }),
    __metadata("design:type", String)
], ZooAnimalDto.prototype, "ownerNickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ZooAnimalDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后活动时间' }),
    __metadata("design:type", Date)
], ZooAnimalDto.prototype, "lastActiveAt", void 0);
class ZooAnimalsResponseDto {
}
exports.ZooAnimalsResponseDto = ZooAnimalsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物列表', type: [ZooAnimalDto] }),
    __metadata("design:type", Array)
], ZooAnimalsResponseDto.prototype, "animals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量' }),
    __metadata("design:type", Number)
], ZooAnimalsResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码' }),
    __metadata("design:type", Number)
], ZooAnimalsResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量' }),
    __metadata("design:type", Number)
], ZooAnimalsResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数' }),
    __metadata("design:type", Number)
], ZooAnimalsResponseDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有下一页' }),
    __metadata("design:type", Boolean)
], ZooAnimalsResponseDto.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有上一页' }),
    __metadata("design:type", Boolean)
], ZooAnimalsResponseDto.prototype, "hasPrev", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '统计信息' }),
    __metadata("design:type", Object)
], ZooAnimalsResponseDto.prototype, "statistics", void 0);
//# sourceMappingURL=zoo-animals.dto.js.map