export declare enum InteractionType {
    FEED = "feed",
    PET = "pet",
    PLAY = "play",
    WORK_TOGETHER = "work_together",
    ENCOURAGE = "encourage",
    REST = "rest",
    TRAIN = "train",
    CHAT = "chat"
}
export declare class AnimalInteractionDto {
    interactionType: InteractionType;
    intensity?: number;
    message?: string;
    duration?: number;
}
export declare class AnimalInteractionResponseDto {
    success: boolean;
    result: {
        interactionId: string;
        animalId: string;
        animalName: string;
        interactionType: InteractionType;
        effectiveness: number;
        experienceGained: number;
    };
    stateChanges: {
        before: {
            mood: string;
            energy: number;
            happiness: number;
            health: number;
        };
        after: {
            mood: string;
            energy: number;
            happiness: number;
            health: number;
        };
        changes: {
            mood?: string;
            energy?: number;
            happiness?: number;
            health?: number;
        };
    };
    animalReaction: {
        expression: string;
        sound: string;
        animation: string;
        responseText: string;
    };
    rewards: {
        coins?: number;
        experience?: number;
        items?: Array<{
            name: string;
            quantity: number;
            description: string;
        }>;
        achievements?: Array<{
            name: string;
            description: string;
            rarity: string;
        }>;
    };
    suggestions: {
        nextBestInteraction: InteractionType;
        cooldownTime?: number;
        tips: string[];
    };
    timestamp: Date;
    message: string;
}
