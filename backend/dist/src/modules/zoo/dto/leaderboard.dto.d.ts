export declare class LeaderboardQueryDto {
    period?: 'total' | 'daily' | 'weekly' | 'monthly';
    limit?: number;
    offset?: number;
}
export declare class LeaderboardEntryDto {
    userId: string;
    username: string;
    avatarUrl?: string;
    totalScore: number;
    level: number;
    title: string;
    rank: number;
    badges: Array<{
        name: string;
        icon: string;
        rarity: string;
    }>;
    stats: {
        interactionScore: number;
        workPerformanceScore: number;
        socialScore: number;
        achievementScore: number;
        creativityScore: number;
    };
}
export declare class LeaderboardResponseDto {
    leaderboard: LeaderboardEntryDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    period: string;
    updatedAt: Date;
}
export declare class UserRankingDto {
    globalRank: number;
    dailyRank: number;
    weeklyRank: number;
    monthlyRank: number;
    totalScore: number;
    level: number;
    title: string;
}
export declare class ScoreUpdateDto {
    scoreType: 'interaction' | 'work' | 'social' | 'achievement' | 'creativity';
    points: number;
    experience?: number;
    reason?: string;
}
export declare class UserScoreStatsDto {
    userId: string;
    totalScore: number;
    level: number;
    experience: number;
    experienceToNextLevel: number;
    title: string;
    reputation: number;
    categoryScores: {
        interactionScore: number;
        workPerformanceScore: number;
        socialScore: number;
        achievementScore: number;
        creativityScore: number;
    };
    rankings: {
        globalRank: number;
        dailyRank: number;
        weeklyRank: number;
        monthlyRank: number;
    };
    badges: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        rarity: string;
        earnedAt: Date;
        icon: string;
    }>;
    streaks: {
        dailyLogin: number;
        maxDailyLogin: number;
        weeklyActive: number;
        maxWeeklyActive: number;
        consecutiveWork: number;
        maxConsecutiveWork: number;
    };
    lastUpdatedAt: Date;
}
