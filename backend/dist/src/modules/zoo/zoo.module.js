"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
const zoo_controller_1 = require("./zoo.controller");
const zoo_service_1 = require("./zoo.service");
const animal_behavior_service_1 = require("./services/animal-behavior.service");
const zoo_ai_scheduler_service_1 = require("./services/zoo-ai-scheduler.service");
const scoring_service_1 = require("./services/scoring.service");
const zoo_animal_entity_1 = require("./entities/zoo-animal.entity");
const animal_interaction_record_entity_1 = require("./entities/animal-interaction-record.entity");
const user_score_entity_1 = require("./entities/user-score.entity");
const generated_avatar_entity_1 = require("../drawing/entities/generated-avatar.entity");
let ZooModule = class ZooModule {
};
exports.ZooModule = ZooModule;
exports.ZooModule = ZooModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                zoo_animal_entity_1.ZooAnimal,
                animal_interaction_record_entity_1.AnimalInteractionRecord,
                user_score_entity_1.UserScore,
                generated_avatar_entity_1.GeneratedAvatar,
            ]),
            config_1.ConfigModule,
            schedule_1.ScheduleModule.forRoot(),
        ],
        controllers: [zoo_controller_1.ZooController],
        providers: [
            zoo_service_1.ZooService,
            animal_behavior_service_1.AnimalBehaviorService,
            zoo_ai_scheduler_service_1.ZooAISchedulerService,
            scoring_service_1.ScoringService,
        ],
        exports: [
            zoo_service_1.ZooService,
            animal_behavior_service_1.AnimalBehaviorService,
            zoo_ai_scheduler_service_1.ZooAISchedulerService,
            scoring_service_1.ScoringService,
        ],
    })
], ZooModule);
//# sourceMappingURL=zoo.module.js.map