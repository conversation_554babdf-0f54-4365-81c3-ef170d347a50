"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimalInteractionRecord = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const zoo_animal_entity_1 = require("./zoo-animal.entity");
const animal_interaction_dto_1 = require("../dto/animal-interaction.dto");
let AnimalInteractionRecord = class AnimalInteractionRecord {
};
exports.AnimalInteractionRecord = AnimalInteractionRecord;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AnimalInteractionRecord.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], AnimalInteractionRecord.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], AnimalInteractionRecord.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'animal_id' }),
    __metadata("design:type", String)
], AnimalInteractionRecord.prototype, "animalId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => zoo_animal_entity_1.ZooAnimal, (animal) => animal.interactions, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'animal_id' }),
    __metadata("design:type", zoo_animal_entity_1.ZooAnimal)
], AnimalInteractionRecord.prototype, "animal", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_interaction_dto_1.InteractionType,
        comment: '互动类型',
    }),
    __metadata("design:type", String)
], AnimalInteractionRecord.prototype, "interactionType", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 5, comment: '互动强度 1-10' }),
    __metadata("design:type", Number)
], AnimalInteractionRecord.prototype, "intensity", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 5, comment: '互动持续时间（分钟）' }),
    __metadata("design:type", Number)
], AnimalInteractionRecord.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, nullable: true, comment: '互动消息或备注' }),
    __metadata("design:type", String)
], AnimalInteractionRecord.prototype, "message", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 5, scale: 2, default: 0, comment: '互动效果评分' }),
    __metadata("design:type", Number)
], AnimalInteractionRecord.prototype, "effectiveness", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '获得的经验值' }),
    __metadata("design:type", Number)
], AnimalInteractionRecord.prototype, "experienceGained", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '互动前动物状态' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "stateBefore", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '互动后动物状态' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "stateAfter", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '状态变化' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "stateChanges", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '动物反应' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "animalReaction", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '获得的奖励' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "rewards", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true, comment: '是否成功的互动' }),
    __metadata("design:type", Boolean)
], AnimalInteractionRecord.prototype, "isSuccessful", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '扩展数据' }),
    __metadata("design:type", Object)
], AnimalInteractionRecord.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], AnimalInteractionRecord.prototype, "createdAt", void 0);
exports.AnimalInteractionRecord = AnimalInteractionRecord = __decorate([
    (0, typeorm_1.Entity)('animal_interaction_records'),
    (0, typeorm_1.Index)(['userId', 'createdAt']),
    (0, typeorm_1.Index)(['animalId', 'createdAt']),
    (0, typeorm_1.Index)(['interactionType', 'createdAt'])
], AnimalInteractionRecord);
//# sourceMappingURL=animal-interaction-record.entity.js.map