"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZooAnimal = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const generated_avatar_entity_1 = require("../../drawing/entities/generated-avatar.entity");
const animal_interaction_record_entity_1 = require("./animal-interaction-record.entity");
const animal_types_1 = require("../../../common/constants/animal-types");
let ZooAnimal = class ZooAnimal {
};
exports.ZooAnimal = ZooAnimal;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ZooAnimal.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], ZooAnimal.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'avatar_id', nullable: true }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "avatarId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => generated_avatar_entity_1.GeneratedAvatar, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'avatar_id' }),
    __metadata("design:type", generated_avatar_entity_1.GeneratedAvatar)
], ZooAnimal.prototype, "avatar", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, comment: '动物名称（用户自定义或默认）' }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalSpecies,
        comment: '动物种类',
    }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "species", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalCategory,
        comment: '动物类型',
    }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true, comment: '头像图片URL' }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "avatarUrl", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true, comment: '头像base64数据' }),
    __metadata("design:type", String)
], ZooAnimal.prototype, "avatarBase64", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '动物基础属性' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "attributes", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '当前状态' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "currentState", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '在动物园中的位置' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '工作统计数据' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "workStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '互动统计' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "interactionStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '个性化设置' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "preferences", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true, comment: '是否激活状态' }),
    __metadata("design:type", Boolean)
], ZooAnimal.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false, comment: '是否为明星动物' }),
    __metadata("design:type", Boolean)
], ZooAnimal.prototype, "isStarAnimal", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '等级' }),
    __metadata("design:type", Number)
], ZooAnimal.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '经验值' }),
    __metadata("design:type", Number)
], ZooAnimal.prototype, "experience", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 100, comment: '声望值' }),
    __metadata("design:type", Number)
], ZooAnimal.prototype, "reputation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '最后活动时间' }),
    __metadata("design:type", Date)
], ZooAnimal.prototype, "lastActiveAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true, comment: '最后互动时间' }),
    __metadata("design:type", Date)
], ZooAnimal.prototype, "lastInteractionAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true, comment: '最后工作时间' }),
    __metadata("design:type", Date)
], ZooAnimal.prototype, "lastWorkAt", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '扩展元数据' }),
    __metadata("design:type", Object)
], ZooAnimal.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => animal_interaction_record_entity_1.AnimalInteractionRecord, (record) => record.animal),
    __metadata("design:type", Array)
], ZooAnimal.prototype, "interactions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], ZooAnimal.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], ZooAnimal.prototype, "updatedAt", void 0);
exports.ZooAnimal = ZooAnimal = __decorate([
    (0, typeorm_1.Entity)('zoo_animals'),
    (0, typeorm_1.Index)(['userId', 'createdAt']),
    (0, typeorm_1.Index)(['category', 'species']),
    (0, typeorm_1.Index)(['isActive', 'lastActiveAt'])
], ZooAnimal);
//# sourceMappingURL=zoo-animal.entity.js.map