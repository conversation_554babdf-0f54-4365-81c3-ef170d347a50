import { User } from '@/modules/user/entities/user.entity';
export declare class UserScore {
    id: string;
    userId: string;
    user: User;
    scoreType: 'TOTAL' | 'DAILY' | 'WEEKLY' | 'MONTHLY';
    period: string;
    totalScore: number;
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
    interactionStats: {
        totalInteractions: number;
        feedCount: number;
        petCount: number;
        playCount: number;
        workTogetherCount: number;
        restCount: number;
        encourageCount: number;
        averageEffectiveness: number;
    };
    workStats: {
        totalWorkHours: number;
        averageEfficiency: number;
        completedTasks: number;
        promotions: number;
        teamworkScore: number;
        overtimeHours: number;
    };
    socialStats: {
        friendsMade: number;
        helpGiven: number;
        popularityScore: number;
        leadershipScore: number;
        mentorshipScore: number;
    };
    achievementStats: {
        totalAchievements: number;
        rareAchievements: number;
        firstTimeAchievements: number;
        challengesCompleted: number;
    };
    creativityStats: {
        avatarsCreated: number;
        uniqueInteractions: number;
        innovativeApproaches: number;
        artisticScore: number;
    };
    level: number;
    experience: number;
    experienceToNextLevel: number;
    reputation: number;
    title: string;
    badges: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
        earnedAt: Date;
        icon: string;
    }>;
    streaks: {
        dailyLogin: number;
        maxDailyLogin: number;
        weeklyActive: number;
        maxWeeklyActive: number;
        consecutiveWork: number;
        maxConsecutiveWork: number;
    };
    globalRank: number;
    dailyRank: number;
    weeklyRank: number;
    monthlyRank: number;
    lastUpdatedAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
