import { User } from '@/modules/user/entities/user.entity';
import { ZooAnimal } from './zoo-animal.entity';
import { InteractionType } from '../dto/animal-interaction.dto';
export declare class AnimalInteractionRecord {
    id: string;
    userId: string;
    user: User;
    animalId: string;
    animal: ZooAnimal;
    interactionType: InteractionType;
    intensity: number;
    duration: number;
    message?: string;
    effectiveness: number;
    experienceGained: number;
    stateBefore: {
        mood: string;
        energy: number;
        happiness: number;
        health: number;
    };
    stateAfter: {
        mood: string;
        energy: number;
        happiness: number;
        health: number;
    };
    stateChanges: {
        mood?: string;
        energy?: number;
        happiness?: number;
        health?: number;
    };
    animalReaction: {
        expression: string;
        sound: string;
        animation: string;
        responseText: string;
    };
    rewards?: {
        coins?: number;
        experience?: number;
        items?: Array<{
            name: string;
            quantity: number;
            description: string;
        }>;
        achievements?: Array<{
            name: string;
            description: string;
            rarity: string;
        }>;
    };
    isSuccessful: boolean;
    metadata?: {
        weather?: string;
        timeOfDay?: number;
        otherParticipants?: string[];
        specialEvents?: string[];
    };
    createdAt: Date;
}
