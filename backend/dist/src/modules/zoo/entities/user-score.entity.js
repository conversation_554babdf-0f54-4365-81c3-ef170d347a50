"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserScore = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
let UserScore = class UserScore {
};
exports.UserScore = UserScore;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserScore.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], UserScore.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserScore.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['TOTAL', 'DAILY', 'WEEKLY', 'MONTHLY'],
        comment: '积分周期类型',
    }),
    __metadata("design:type", String)
], UserScore.prototype, "scoreType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', comment: '积分周期' }),
    __metadata("design:type", String)
], UserScore.prototype, "period", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '总积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "totalScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '互动积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "interactionScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '工作表现积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "workPerformanceScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '社交积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "socialScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '成就积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "achievementScore", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '创造力积分' }),
    __metadata("design:type", Number)
], UserScore.prototype, "creativityScore", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '互动统计详情' }),
    __metadata("design:type", Object)
], UserScore.prototype, "interactionStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '工作统计详情' }),
    __metadata("design:type", Object)
], UserScore.prototype, "workStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '社交统计详情' }),
    __metadata("design:type", Object)
], UserScore.prototype, "socialStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '成就统计详情' }),
    __metadata("design:type", Object)
], UserScore.prototype, "achievementStats", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '创造力统计详情' }),
    __metadata("design:type", Object)
], UserScore.prototype, "creativityStats", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '当前等级' }),
    __metadata("design:type", Number)
], UserScore.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '当前等级内经验值' }),
    __metadata("design:type", Number)
], UserScore.prototype, "experience", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0, comment: '升级所需总经验值' }),
    __metadata("design:type", Number)
], UserScore.prototype, "experienceToNextLevel", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 100, comment: '声望值' }),
    __metadata("design:type", Number)
], UserScore.prototype, "reputation", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, default: '新手打工人', comment: '打工人称号' }),
    __metadata("design:type", String)
], UserScore.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '徽章列表' }),
    __metadata("design:type", Array)
], UserScore.prototype, "badges", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '连续记录' }),
    __metadata("design:type", Object)
], UserScore.prototype, "streaks", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 1, comment: '全球排名' }),
    __metadata("design:type", Number)
], UserScore.prototype, "globalRank", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 1, comment: '每日排名' }),
    __metadata("design:type", Number)
], UserScore.prototype, "dailyRank", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 1, comment: '每周排名' }),
    __metadata("design:type", Number)
], UserScore.prototype, "weeklyRank", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 1, comment: '每月排名' }),
    __metadata("design:type", Number)
], UserScore.prototype, "monthlyRank", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '最后更新时间' }),
    __metadata("design:type", Date)
], UserScore.prototype, "lastUpdatedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], UserScore.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], UserScore.prototype, "updatedAt", void 0);
exports.UserScore = UserScore = __decorate([
    (0, typeorm_1.Entity)('user_scores'),
    (0, typeorm_1.Index)(['userId', 'scoreType', 'period']),
    (0, typeorm_1.Unique)(['userId', 'scoreType', 'period'])
], UserScore);
//# sourceMappingURL=user-score.entity.js.map