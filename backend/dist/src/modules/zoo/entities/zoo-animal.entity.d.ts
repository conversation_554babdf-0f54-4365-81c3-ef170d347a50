import { User } from '@/modules/user/entities/user.entity';
import { GeneratedAvatar } from '@/modules/drawing/entities/generated-avatar.entity';
import { AnimalInteractionRecord } from './animal-interaction-record.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class ZooAnimal {
    id: string;
    userId: string;
    user: User;
    avatarId?: string;
    avatar?: GeneratedAvatar;
    name: string;
    species: AnimalSpecies;
    category: AnimalCategory;
    avatarUrl?: string;
    avatarBase64?: string;
    attributes: {
        [key: string]: number;
    };
    currentState: {
        mood: string;
        activity: string;
        location: string;
        energy: number;
        happiness: number;
        health: number;
        workEfficiency: number;
    };
    position: {
        x: number;
        y: number;
        z: number;
        area: string;
    };
    workStats: {
        workEfficiency: number;
        productivity: number;
        attendance: number;
        teamwork: number;
        totalWorkHours: number;
        promotions: number;
    };
    interactionStats: {
        totalInteractions: number;
        dailyInteractions: number;
        weeklyInteractions: number;
        favoriteActivities: string[];
        bestFriends: string[];
    };
    preferences: {
        favoriteFood?: string[];
        preferredActivities?: string[];
        workPreferences?: string[];
        personality?: string[];
    };
    isActive: boolean;
    isStarAnimal: boolean;
    level: number;
    experience: number;
    reputation: number;
    lastActiveAt: Date;
    lastInteractionAt?: Date;
    lastWorkAt?: Date;
    metadata?: {
        originalPrompt?: string;
        generationMethod?: string;
        specialTraits?: string[];
        achievements?: Array<{
            name: string;
            earnedAt: Date;
            description: string;
        }>;
    };
    interactions: AnimalInteractionRecord[];
    createdAt: Date;
    updatedAt: Date;
}
