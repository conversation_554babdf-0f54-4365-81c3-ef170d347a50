import { ZooService } from './zoo.service';
import { AnimalBehaviorService } from './services/animal-behavior.service';
import { ZooAISchedulerService } from './services/zoo-ai-scheduler.service';
import { ZooSceneQueryDto, ZooSceneResponseDto, ZooAnimalsQueryDto, ZooAnimalsResponseDto, AnimalInteractionDto, AnimalInteractionResponseDto } from './dto';
import { LeaderboardQueryDto, LeaderboardResponseDto, UserRankingDto, ScoreUpdateDto, UserScoreStatsDto } from './dto/leaderboard.dto';
import { User } from '@/modules/user/entities/user.entity';
import { ScoringService } from './services/scoring.service';
export declare class ZooController {
    private readonly zooService;
    private readonly animalBehaviorService;
    private readonly zooAISchedulerService;
    private readonly scoringService;
    constructor(zooService: ZooService, animalBehaviorService: AnimalBehaviorService, zooAISchedulerService: ZooAISchedulerService, scoringService: ScoringService);
    getZooScene(user: User, query: ZooSceneQueryDto): Promise<ZooSceneResponseDto>;
    getZooAnimals(user: User, query: ZooAnimalsQueryDto): Promise<ZooAnimalsResponseDto>;
    interactWithAnimal(user: User, animalId: string, interactionDto: AnimalInteractionDto): Promise<AnimalInteractionResponseDto>;
    getAnimalDetails(user: User, animalId: string): Promise<{
        message: string;
    }>;
    createAnimalFromAvatar(user: User, avatarId: string, createDto?: {
        name?: string;
    }): Promise<import("./entities").ZooAnimal>;
    getEnvironmentState(): Promise<import("./services/zoo-ai-scheduler.service").ZooEnvironmentState>;
    getZooStatistics(): Promise<{
        totalAnimals: number;
        activeAnimals: number;
        averageHappiness: number;
        averageEnergy: number;
        environment: import("./services/zoo-ai-scheduler.service").ZooEnvironmentState;
        activeEvents: number;
        behaviorQueueSize: number;
    }>;
    getAnimalBehavior(animalId: string): Promise<{
        currentBehavior?: import("./services/animal-behavior.service").AnimalBehavior;
        isActive: boolean;
        timeRemaining?: number;
    }>;
    stopAnimalBehavior(animalId: string): Promise<{
        message: string;
    }>;
    triggerEvent(eventDto: {
        type: string;
        duration?: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    controlScheduler(controlDto: {
        enabled: boolean;
    }): Promise<{
        message: string;
    }>;
    getBehaviorPatterns(): Promise<{
        message: string;
        patterns: {
            dragon: string;
            cat: string;
            ox: string;
        };
    }>;
    getLeaderboard(query: LeaderboardQueryDto): Promise<LeaderboardResponseDto>;
    getMyRanking(user: User): Promise<UserRankingDto>;
    getMyScoreStats(user: User): Promise<UserScoreStatsDto>;
    updateScore(user: User, scoreUpdateDto: ScoreUpdateDto): Promise<{
        success: boolean;
        message: string;
        data: {
            totalScore: number;
            level: number;
            experience: number;
            title: string;
        };
    }>;
    getScoreHistory(userId: string, period?: 'daily' | 'weekly' | 'monthly', limit?: number): Promise<{
        message: string;
        userId: string;
        period: "daily" | "weekly" | "monthly";
        limit: number;
    }>;
    getAchievements(user: User): Promise<{
        achievements: {
            id: string;
            name: string;
            description: string;
            category: string;
            rarity: string;
            icon: string;
            requirement: string;
            progress: number;
            maxProgress: number;
            reward: {
                score: number;
                experience: number;
            };
        }[];
        message: string;
    }>;
    claimAchievement(user: User, achievementId: string): Promise<{
        success: boolean;
        message: string;
        reward: {
            score: number;
            experience: number;
            badge: {
                name: string;
                icon: string;
                rarity: string;
            };
        };
    }>;
}
