"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AIFusionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIFusionService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const generative_ai_1 = require("@google/generative-ai");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const undici_1 = require("undici");
let AIFusionService = AIFusionService_1 = class AIFusionService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AIFusionService_1.name);
        this.geminiAPI = null;
        this.initializeService();
    }
    initializeService() {
        const proxyUrl = process.env.HTTP_PROXY || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
        const undiciProxyAgent = new undici_1.ProxyAgent(proxyUrl);
        (0, undici_1.setGlobalDispatcher)(undiciProxyAgent);
        const geminiKey = this.configService.get('GEMINI_API_KEY');
        if (geminiKey) {
            this.geminiAPI = new generative_ai_1.GoogleGenerativeAI(geminiKey);
            this.logger.log(`✅ Gemini API 已配置，使用 undici 代理: ${proxyUrl}`);
        }
        else {
            this.logger.warn('❌ Gemini API Key 未配置');
            this.geminiAPI = null;
        }
        this.isEnabled = this.configService.get('AI_GENERATION_ENABLED') === 'true';
        this.logger.log(`AI服务初始化完成 - 启用状态: ${this.isEnabled}`);
    }
    async analyzeUserDrawing(imageBase64) {
        try {
            if (!this.isEnabled || !this.geminiAPI) {
                return this.getFallbackAnalysis();
            }
            const model = this.geminiAPI.getGenerativeModel({ model: "gemini-1.5-flash" });
            const prompt = `作为专业的艺术分析师，请详细分析这幅用户自画像，为后续的动物融合生成提供精确指导。

      请从以下维度进行分析并返回JSON格式：

      COMPOSITION_ANALYSIS: {
          "headPosition": "头部在画面中的位置和大小比例",
          "bodyPosture": "身体姿势描述（站立/坐着/倾斜等）",
          "frameComposition": "整体构图特点",
          "proportions": "头身比例关系"
      }

      STYLE_ANALYSIS: {
          "drawingStyle": "绘画技法（简笔画/写实/卡通/素描等）",
          "lineQuality": "线条特征（粗细/流畅度/风格）",
          "detailLevel": "细节丰富程度（简单/中等/复杂）",
          "colorUsage": "色彩运用情况"
      }

      PERSONALITY_TRAITS: {
          "expression": "面部表情特征",
          "mood": "整体情绪表达",
          "confidence": "自信程度体现",
          "personality": "性格特征推断"
      }

      DISTINCTIVE_FEATURES: {
          "uniqueElements": "独特的个人特征（眼镜/发型/服装等）",
          "artisticChoices": "特殊的艺术表现手法",
          "personalStyle": "个人风格标识"
      }

      FUSION_GUIDANCE: {
          "preserveElements": "需要在融合中保持的关键元素",
          "adaptationStrategy": "动物特征融合策略建议",
          "styleMatching": "如何匹配用户的绘画风格"
      }

      请确保分析结果能够指导AI准确理解用户的个人特色，而不是生成通用的动物头像。`;
            const result = await model.generateContent([
                {
                    inlineData: {
                        data: imageBase64 ? imageBase64.replace(/^data:image\/\w+;base64,/, '') : '',
                        mimeType: 'image/png'
                    }
                },
                prompt
            ]);
            const response = result.response;
            const analysisText = response.text();
            return this.parseAnalysisResult(analysisText);
        }
        catch (error) {
            this.logger.error('分析用户绘画失败:', error);
            return this.getFallbackAnalysis();
        }
    }
    async generateFusionAvatar(userDrawingBase64, animalType, analysisData) {
        try {
            if (!this.isEnabled) {
                throw new Error('AI生成服务未启用');
            }
            if (this.geminiAPI) {
                this.logger.log('🔍 开始分析用户自画像特征...');
                const detailedAnalysis = await this.analyzeUserDrawing(userDrawingBase64);
                this.logger.log('📊 用户绘画分析结果:', JSON.stringify(detailedAnalysis, null, 2));
                return await this.generateWithGemini(userDrawingBase64, animalType, detailedAnalysis);
            }
            throw new Error('没有可用的AI生成服务');
        }
        catch (error) {
            this.logger.error('融合头像生成失败:', error);
            return {
                success: false,
                avatar: {
                    avatarId: Date.now().toString(),
                    animalType,
                    generationMethod: 'Failed',
                    imageUrl: '',
                    fusionDetails: {
                        method: 'Failed',
                        animalType,
                        style: 'N/A'
                    }
                },
                message: `头像生成失败: ${error.message}`
            };
        }
    }
    async generateWithGemini(userDrawingBase64, animalType, analysisData) {
        const model = this.geminiAPI.getGenerativeModel({
            model: "gemini-2.0-flash-preview-image-generation",
            generationConfig: {
                temperature: 0.7,
            },
            requestOptions: {
                timeout: 30000,
            }
        });
        const animalDescriptions = {
            OXHORSE: 'a dedicated ox or horse head representing the hardworking "996" office worker spirit',
            PET: 'a cute cat or dog head representing a friendly and loyal office companion',
            DIVINE: 'a powerful dragon or phoenix head representing ambitious career goals and leadership'
        };
        const prompt = `CRITICAL TASK: Create a faithful transformation of the provided user self-portrait by adding animal features while preserving 90% of the original drawing.

    MANDATORY PRESERVATION RULES:
    1. BODY STRUCTURE: Keep the exact same body shape, size, and posture from the original drawing
    2. LEGS & FEET: Preserve the original leg positions, length, and foot details exactly as drawn
    3. ARMS & HANDS: Maintain the original arm positions and hand placement
    4. CLOTHING: Keep any clothing or garments exactly as shown in the original
    5. OVERALL COMPOSITION: Use the same framing, positioning, and layout
    6. ARTISTIC STYLE: Match the exact drawing technique, line quality, and color scheme

    COLOR & STYLE MATCHING:
    - If original is BLACK & WHITE sketch → Generate BLACK & WHITE sketch with same line style
    - If original is COLORED drawing → Generate COLORED version matching the original palette
    - If original uses simple lines → Keep lines simple and clean
    - If original is detailed → Maintain the same level of detail
    - NEVER change the artistic medium or complexity level

    MINIMAL ANIMAL MODIFICATIONS (Only 10% of the image):
    - Replace ONLY the head/face area with ${animalDescriptions[animalType]}
    - Keep the animal head the same size and position as the original human head
    - If the original has unique features (glasses, hat, etc.) → Add them to the animal head
    - Add minimal professional elements (small tie, collar) that fit the original style

    TECHNICAL EXECUTION:
    - Start with the user's exact drawing as the base template
    - Modify ONLY the head area, leaving everything else untouched
    - The result should look like "the user's drawing but with an animal head"
    - NOT a new creation, but a direct modification of their artwork

    USER ANALYSIS INSIGHTS: ${JSON.stringify(analysisData, null, 2)}

    EXECUTION CHECKLIST based on analysis:
    ✓ Preserve EXACT body structure from COMPOSITION_ANALYSIS  
    ✓ Match EXACT drawing style from STYLE_ANALYSIS (especially line quality and color usage)
    ✓ Keep the same emotional expression from PERSONALITY_TRAITS
    ✓ Maintain all DISTINCTIVE_FEATURES in animal form (glasses → animal glasses, etc.)
    ✓ Follow FUSION_GUIDANCE for style consistency

    FINAL OUTPUT REQUIREMENTS:
    - 90% identical to original drawing (body, legs, arms, clothing, style)
    - 10% animal head modification that respects original proportions
    - Same artistic medium (sketch/color/style) as original
    - Result should be recognizable as "their drawing + animal head"

    CRITICAL: This is NOT creating a new image. This is MODIFYING their existing self-portrait by replacing only the head with ${animalDescriptions[animalType]} while keeping everything else exactly the same.`;
        try {
            this.logger.log('正在使用 Gemini 2.0 Flash Preview Image Generation 生成图像...');
            const result = await model.generateContent([
                prompt,
                {
                    inlineData: {
                        data: userDrawingBase64 ? userDrawingBase64.replace(/^data:image\/\w+;base64,/, '') : '',
                        mimeType: 'image/png'
                    }
                }
            ]);
            const response = result.response;
            this.logger.log('📊 Gemini 2.0 Flash 响应结构:');
            this.logger.log('- 候选数量:', response.candidates?.length || 0);
            if (response.candidates && response.candidates[0]) {
                const candidate = response.candidates[0];
                if (candidate.content && candidate.content.parts) {
                    for (const part of candidate.content.parts) {
                        if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.includes('image')) {
                            this.logger.log('✅ Gemini 2.0 Flash 成功生成图像');
                            const avatarId = Date.now().toString();
                            const filename = `avatar-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
                            const filePath = path.join(process.cwd(), 'uploads', 'generated-avatars', filename);
                            const uploadDir = path.dirname(filePath);
                            if (!fs.existsSync(uploadDir)) {
                                fs.mkdirSync(uploadDir, { recursive: true });
                            }
                            const imageBuffer = Buffer.from(part.inlineData.data, 'base64');
                            fs.writeFileSync(filePath, imageBuffer);
                            this.logger.log('💾 图像已保存到:', filePath);
                            const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
                            return {
                                success: true,
                                avatar: {
                                    avatarId: avatarId,
                                    animalType: animalType,
                                    generationMethod: 'Gemini 2.0 Flash Image Generation',
                                    imageUrl: imageUrl,
                                    filePath: filePath,
                                    fusionDetails: {
                                        method: 'Gemini 2.0 Flash Direct Generation',
                                        animalType: animalType,
                                        style: analysisData.features.style || 'Professional',
                                        model: 'gemini-2.0-flash-preview-image-generation'
                                    }
                                }
                            };
                        }
                    }
                }
                this.logger.warn('🧠 Gemini图像生成未返回图像数据');
            }
            throw new Error('Gemini未返回有效的图像数据');
        }
        catch (error) {
            this.logger.error('🧠 Gemini生成失败:', error);
            throw error;
        }
    }
    parseAnalysisResult(analysisText) {
        try {
            const parsedResult = JSON.parse(analysisText);
            let animalType = 'OXHORSE';
            let confidence = 0.7;
            const text = analysisText.toLowerCase();
            if (text.includes('神兽') || text.includes('威严') || text.includes('领导') || text.includes('自信')) {
                animalType = 'DIVINE';
                confidence = 0.8;
            }
            else if (text.includes('可爱') || text.includes('温和') || text.includes('友善') || text.includes('宠物')) {
                animalType = 'PET';
                confidence = 0.75;
            }
            return {
                animalType,
                confidence,
                features: {
                    complexity: this.extractComplexity(analysisText),
                    emotionalTone: this.extractEmotionalTone(analysisText),
                    dominantColors: this.extractColors(analysisText),
                    style: this.extractStyle(analysisText)
                },
                recommendation: {
                    primaryChoice: animalType,
                    reasoning: `基于AI分析结果推荐${animalType}类型`,
                    alternatives: ['OXHORSE', 'PET', 'DIVINE'].filter(t => t !== animalType)
                }
            };
        }
        catch (error) {
            this.logger.warn('解析AI分析结果失败，使用默认分析:', error);
            return this.getFallbackAnalysis();
        }
    }
    getFallbackAnalysis() {
        return {
            animalType: 'OXHORSE',
            confidence: 0.6,
            features: {
                complexity: 50,
                emotionalTone: 'neutral',
                dominantColors: ['#000000', '#FFFFFF'],
                style: 'sketch'
            },
            recommendation: {
                primaryChoice: 'OXHORSE',
                reasoning: '默认推荐牛马类型，适合大部分打工人',
                alternatives: ['PET', 'DIVINE']
            }
        };
    }
    extractComplexity(text) {
        if (text.includes('复杂') || text.includes('详细'))
            return 80;
        if (text.includes('中等') || text.includes('适中'))
            return 50;
        if (text.includes('简单') || text.includes('简笔'))
            return 30;
        return 50;
    }
    extractEmotionalTone(text) {
        if (text.includes('快乐') || text.includes('开心'))
            return 'happy';
        if (text.includes('悲伤') || text.includes('忧郁'))
            return 'sad';
        if (text.includes('愤怒') || text.includes('生气'))
            return 'angry';
        if (text.includes('平静') || text.includes('中性'))
            return 'calm';
        return 'neutral';
    }
    extractColors(text) {
        const colors = [];
        if (text.includes('黑') || text.includes('black'))
            colors.push('#000000');
        if (text.includes('白') || text.includes('white'))
            colors.push('#FFFFFF');
        if (text.includes('红') || text.includes('red'))
            colors.push('#FF0000');
        if (text.includes('蓝') || text.includes('blue'))
            colors.push('#0000FF');
        if (text.includes('绿') || text.includes('green'))
            colors.push('#00FF00');
        return colors.length > 0 ? colors : ['#000000', '#FFFFFF'];
    }
    extractStyle(text) {
        if (text.includes('写实') || text.includes('realistic'))
            return 'realistic';
        if (text.includes('卡通') || text.includes('cartoon'))
            return 'cartoon';
        if (text.includes('素描') || text.includes('sketch'))
            return 'sketch';
        if (text.includes('简笔') || text.includes('simple'))
            return 'simple';
        return 'sketch';
    }
    getServiceStatus() {
        return {
            enabled: this.isEnabled,
            geminiAvailable: !!this.geminiAPI,
            status: this.isEnabled ? 'active' : 'disabled'
        };
    }
};
exports.AIFusionService = AIFusionService;
exports.AIFusionService = AIFusionService = AIFusionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AIFusionService);
//# sourceMappingURL=ai-fusion.service.js.map