import { ConfigService } from '@nestjs/config';
export interface OptimizedFusionResult {
    success: boolean;
    imageUrl: string;
    imageBase64?: string;
    filePath?: string;
    fusionDetails: {
        method: string;
        animalType: string;
        style: string;
        features: any;
    };
}
export interface BodyStructure {
    width: number;
    height: number;
    proportions: {
        headRatio: number;
        shoulderRatio: number;
        waistRatio: number;
        legRatio: number;
    };
    pose: {
        type: string;
        direction: string;
        armPosition: string;
    };
    style: {
        lineWeight: string;
        detail: string;
        shading: string;
    };
}
export declare class OptimizedFusionGeneratorService {
    private configService;
    private readonly logger;
    constructor(configService: ConfigService);
    generateOptimizedFusion(userDrawingBase64: string, animalType: 'OXHORSE' | 'PET' | 'DIVINE', analysisResult?: any): Promise<OptimizedFusionResult>;
    private loadUserDrawing;
    private drawDefaultFigure;
    private analyzeBodyStructure;
    private detectPose;
    private detectDrawingStyle;
    private drawOptimizedBody;
    private drawArms;
    private drawFusedAnimalHead;
    private drawOxHorseHead;
    private drawPetHead;
    private drawDivineHead;
    private addDetailsAndAccessories;
    private applyArtStyle;
    private generateFallback;
}
