{"version": 3, "file": "ai-fusion.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/drawing/services/ai-fusion.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,yDAA2D;AAC3D,yBAAyB;AACzB,6BAA6B;AAC7B,iCAAiC;AACjC,mCAAyD;AAuClD,IAAM,eAAe,uBAArB,MAAM,eAAe;IAK1B,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJ/B,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;QACnD,cAAS,GAA8B,IAAI,CAAC;QAIlD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,iBAAiB;QACvB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uBAAuB,CAAC;QAG9F,MAAM,gBAAgB,GAAG,IAAI,mBAAU,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAA,4BAAmB,EAAC,gBAAgB,CAAC,CAAC;QAGtC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,gBAAgB,CAAC,CAAC;QACnE,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,IAAI,kCAAkB,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,CAAC,KAAK,MAAM,CAAC;QACpF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAC1C,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;YAE/E,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAqCwB,CAAC;YAExC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC;gBACzC;oBACE,UAAU,EAAE;wBACV,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;wBAC5E,QAAQ,EAAE,WAAW;qBACtB;iBACF;gBACD,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAGrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,iBAAyB,EACzB,UAAwC,EACxC,YAAkB;QAElB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC;YAGD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAEnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;gBAC1E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAE3E,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAEtC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBAC/B,UAAU;oBACV,gBAAgB,EAAE,QAAQ;oBAC1B,QAAQ,EAAE,EAAE;oBACZ,aAAa,EAAE;wBACb,MAAM,EAAE,QAAQ;wBAChB,UAAU;wBACV,KAAK,EAAE,KAAK;qBACb;iBACF;gBACD,OAAO,EAAE,WAAW,KAAK,CAAC,OAAO,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAC9B,iBAAyB,EACzB,UAAwC,EACxC,YAA6B;QAG7B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAU,CAAC,kBAAkB,CAAC;YAC/C,KAAK,EAAE,2CAA2C;YAClD,gBAAgB,EAAE;gBAChB,WAAW,EAAE,GAAG;aAEjB;SACF,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG;YACzB,OAAO,EAAE,sFAAsF;YAC/F,GAAG,EAAE,2EAA2E;YAChF,MAAM,EAAE,sFAAsF;SAC/F,CAAC;QAEF,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;6CAkB0B,kBAAkB,CAAC,UAAU,CAAC;;;;;;;;;;;8BAW7C,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;iIAe8D,kBAAkB,CAAC,UAAU,CAAC,kDAAkD,CAAC;QAE9M,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC;gBACzC,MAAM;gBACN;oBACE,UAAU,EAAE;wBACV,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;wBACxF,QAAQ,EAAE,WAAW;qBACtB;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YAG7D,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAGzC,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;oBACjD,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;wBAC3C,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC9F,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;4BAG7C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;4BACvC,MAAM,QAAQ,GAAG,UAAU,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;4BACnF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC;4BAGpF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;4BACzC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gCAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;4BAC/C,CAAC;4BAGD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;4BAChE,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;4BAExC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;4BAGxC,MAAM,QAAQ,GAAG,mDAAmD,QAAQ,EAAE,CAAC;4BAE/E,OAAO;gCACL,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE;oCACN,QAAQ,EAAE,QAAQ;oCAClB,UAAU,EAAE,UAAU;oCACtB,gBAAgB,EAAE,mCAAmC;oCACrD,QAAQ,EAAE,QAAQ;oCAClB,QAAQ,EAAE,QAAQ;oCAClB,aAAa,EAAE;wCACb,MAAM,EAAE,oCAAoC;wCAC5C,UAAU,EAAE,UAAU;wCACtB,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,cAAc;wCACpD,KAAK,EAAE,2CAA2C;qCACnD;iCACF;6BACF,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAEtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,YAAoB;QAC9C,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAG9C,IAAI,UAAU,GAAiC,SAAS,CAAC;YACzD,IAAI,UAAU,GAAG,GAAG,CAAC;YAGrB,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7F,UAAU,GAAG,QAAQ,CAAC;gBACtB,UAAU,GAAG,GAAG,CAAC;YACnB,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpG,UAAU,GAAG,KAAK,CAAC;gBACnB,UAAU,GAAG,IAAI,CAAC;YACpB,CAAC;YAED,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;oBAChD,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC;oBACtD,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;oBAChD,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;iBACvC;gBACD,cAAc,EAAE;oBACd,aAAa,EAAE,UAAU;oBACzB,SAAS,EAAE,aAAa,UAAU,IAAI;oBACtC,YAAY,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC;iBACzE;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAKO,mBAAmB;QACzB,OAAO;YACL,UAAU,EAAE,SAAS;YACrB,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE;gBACR,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,SAAS;gBACxB,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;gBACtC,KAAK,EAAE,QAAQ;aAChB;YACD,cAAc,EAAE;gBACd,aAAa,EAAE,SAAS;gBACxB,SAAS,EAAE,mBAAmB;gBAC9B,YAAY,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;aAChC;SACF,CAAC;IACJ,CAAC;IAKO,iBAAiB,CAAC,IAAY;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QAC1D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QAC1D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QAC1D,OAAO,EAAE,CAAC;IACZ,CAAC;IAKO,oBAAoB,CAAC,IAAY;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,aAAa,CAAC,IAAY;QAChC,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEzE,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC7D,CAAC;IAKO,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,WAAW,CAAC;QAC1E,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACtE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QACpE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QACpE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,gBAAgB;QACd,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;YACjC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;SAC/C,CAAC;IACJ,CAAC;CACF,CAAA;AAjaY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAMwB,sBAAa;GALrC,eAAe,CAia3B"}