"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedFusionGeneratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedFusionGeneratorService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const Canvas = require("canvas");
const fs = require("fs/promises");
const path = require("path");
const crypto = require("crypto");
let EnhancedFusionGeneratorService = EnhancedFusionGeneratorService_1 = class EnhancedFusionGeneratorService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(EnhancedFusionGeneratorService_1.name);
    }
    async generateFusionAvatar(userDrawingBase64, animalType, analysisResult) {
        this.logger.log('🎨 开始生成人身动物头融合图像...');
        this.logger.log(`动物类型: ${animalType}`);
        const canvas = Canvas.createCanvas(1024, 1024);
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, 1024, 1024);
        try {
            this.logger.log('开始融合生成流程...');
            this.logger.log('步骤1: 加载用户绘画数据...');
            const userImage = await this.loadUserDrawing(userDrawingBase64);
            this.logger.log('用户绘画加载成功');
            const bodyFeatures = this.extractBodyFeatures(userImage, ctx);
            this.logger.log('身体特征提取完成:', bodyFeatures);
            this.logger.log('步骤2: 绘制用户身体...');
            await this.drawUserBody(ctx, userImage, bodyFeatures);
            this.logger.log('步骤3: 绘制动物头部...');
            const animalHead = await this.drawAnimalHead(ctx, animalType, analysisResult, bodyFeatures);
            this.logger.log('步骤4: 应用融合效果...');
            this.applyFusionTransition(ctx, bodyFeatures, animalHead);
            this.logger.log('步骤5: 添加打工人元素...');
            await this.addWorkerElements(ctx, animalType, bodyFeatures);
            this.logger.log('步骤6: 应用风格滤镜...');
            this.applySketchEffect(ctx);
            this.logger.log('融合生成完成！');
            const avatarId = Date.now().toString();
            const filename = `avatar-canvas-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
            const uploadDir = path.join(process.cwd(), 'uploads', 'generated-avatars');
            const filePath = path.join(uploadDir, filename);
            try {
                await fs.access(uploadDir);
            }
            catch {
                await fs.mkdir(uploadDir, { recursive: true });
            }
            const buffer = canvas.toBuffer('image/png');
            await fs.writeFile(filePath, buffer);
            this.logger.log(`💾 Canvas图像已保存到: ${filePath}`);
            const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
            const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
            return {
                success: true,
                imageUrl: imageUrl,
                imageBase64: imageBase64,
                filePath: filePath,
                fusionDetails: {
                    method: 'Enhanced Canvas Fusion',
                    animalType,
                    bodySource: 'User Drawing',
                    headSource: `${animalType} Animal Head`,
                    style: 'Sketch',
                    features: {
                        ...analysisResult,
                        bodyRetained: true,
                        headReplaced: true,
                        fusionQuality: 'Natural'
                    }
                }
            };
        }
        catch (error) {
            this.logger.error(`融合生成失败: ${error.message}`, error.stack);
            this.logger.log('启用降级方案，生成fallback图像...');
            try {
                return await this.generateFallbackFusion(canvas, ctx, animalType);
            }
            catch (fallbackError) {
                this.logger.error(`降级方案也失败: ${fallbackError.message}`);
                return this.generateErrorFallback(canvas, ctx, animalType, error.message);
            }
        }
    }
    async loadUserDrawing(base64Data) {
        try {
            if (!base64Data || typeof base64Data !== 'string') {
                throw new Error('Invalid base64 data format');
            }
            const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
            if (!imageData || imageData.length === 0) {
                throw new Error('Empty base64 image data');
            }
            const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
            if (!base64Regex.test(imageData)) {
                throw new Error('Invalid base64 format');
            }
            const buffer = Buffer.from(imageData, 'base64');
            if (buffer.length === 0) {
                throw new Error('Failed to decode base64 data');
            }
            const image = await Canvas.loadImage(buffer);
            if (!image || !image.width || !image.height) {
                throw new Error('Failed to load image: invalid dimensions');
            }
            this.logger.log(`Successfully loaded user drawing: ${image.width}x${image.height}`);
            return image;
        }
        catch (error) {
            this.logger.error(`Error loading user drawing: ${error.message}`);
            const canvas = Canvas.createCanvas(300, 400);
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 300, 400);
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用户绘画加载失败', 150, 200);
            ctx.fillText('使用默认模板', 150, 220);
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(150, 60, 30, 0, Math.PI * 2);
            ctx.moveTo(150, 90);
            ctx.lineTo(150, 250);
            ctx.moveTo(120, 130);
            ctx.lineTo(180, 130);
            ctx.moveTo(150, 250);
            ctx.lineTo(130, 320);
            ctx.moveTo(150, 250);
            ctx.lineTo(170, 320);
            ctx.stroke();
            return canvas;
        }
    }
    extractBodyFeatures(image, ctx) {
        const tempCanvas = Canvas.createCanvas(image.width, image.height);
        const tempCtx = tempCanvas.getContext('2d');
        tempCtx.drawImage(image, 0, 0);
        const imageData = tempCtx.getImageData(0, 0, image.width, image.height);
        const neckY = Math.floor(image.height * 0.28);
        const shoulderY = Math.floor(image.height * 0.35);
        return {
            neckPosition: { x: image.width / 2, y: neckY },
            shoulderPosition: { y: shoulderY },
            bodyWidth: image.width * 0.6,
            imageWidth: image.width,
            imageHeight: image.height
        };
    }
    async drawUserBody(ctx, userImage, bodyFeatures) {
        const x = (1024 - userImage.width) / 2;
        const y = (1024 - userImage.height) / 2;
        ctx.save();
        ctx.beginPath();
        ctx.rect(0, y + bodyFeatures.neckPosition.y, 1024, userImage.height);
        ctx.clip();
        ctx.drawImage(userImage, x, y);
        ctx.restore();
        return { x, y };
    }
    async drawAnimalHead(ctx, animalType, analysisResult, bodyFeatures) {
        const centerX = 512;
        const headY = (1024 - bodyFeatures.imageHeight) / 2;
        const headSize = bodyFeatures.bodyWidth * 0.8;
        ctx.save();
        switch (animalType) {
            case 'DIVINE':
                this.drawDivineHead(ctx, centerX, headY, headSize, analysisResult);
                break;
            case 'PET':
                this.drawPetHead(ctx, centerX, headY, headSize, analysisResult);
                break;
            case 'OXHORSE':
            default:
                this.drawOxHorseHead(ctx, centerX, headY, headSize, analysisResult);
                break;
        }
        ctx.restore();
        return {
            centerX,
            topY: headY,
            bottomY: headY + headSize,
            size: headSize
        };
    }
    drawOxHorseHead(ctx, x, y, size, features) {
        ctx.strokeStyle = '#4a4a4a';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(x - size * 0.3, y + size * 0.2);
        ctx.quadraticCurveTo(x - size * 0.35, y, x, y - size * 0.1);
        ctx.quadraticCurveTo(x + size * 0.35, y, x + size * 0.3, y + size * 0.2);
        ctx.quadraticCurveTo(x + size * 0.4, y + size * 0.5, x + size * 0.2, y + size * 0.8);
        ctx.lineTo(x - size * 0.2, y + size * 0.8);
        ctx.quadraticCurveTo(x - size * 0.4, y + size * 0.5, x - size * 0.3, y + size * 0.2);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x - size * 0.25, y);
        ctx.lineTo(x - size * 0.3, y - size * 0.2);
        ctx.lineTo(x - size * 0.15, y - size * 0.05);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x + size * 0.25, y);
        ctx.lineTo(x + size * 0.3, y - size * 0.2);
        ctx.lineTo(x + size * 0.15, y - size * 0.05);
        ctx.stroke();
        ctx.fillStyle = '#2c2c2c';
        ctx.beginPath();
        ctx.arc(x - size * 0.15, y + size * 0.25, size * 0.06, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(x + size * 0.15, y + size * 0.25, size * 0.06, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#1a1a1a';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.05, y + size * 0.6, size * 0.03, size * 0.05, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(x + size * 0.05, y + size * 0.6, size * 0.03, size * 0.05, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#4a4a4a';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x - size * 0.1, y + size * 0.7);
        ctx.quadraticCurveTo(x, y + size * 0.72, x + size * 0.1, y + size * 0.7);
        ctx.stroke();
    }
    drawPetHead(ctx, x, y, size, features) {
        ctx.strokeStyle = '#5a5a5a';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(x, y + size * 0.4, size * 0.45, 0, Math.PI * 2);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x - size * 0.35, y + size * 0.1);
        ctx.lineTo(x - size * 0.4, y - size * 0.15);
        ctx.lineTo(x - size * 0.2, y);
        ctx.closePath();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x + size * 0.35, y + size * 0.1);
        ctx.lineTo(x + size * 0.4, y - size * 0.15);
        ctx.lineTo(x + size * 0.2, y);
        ctx.closePath();
        ctx.stroke();
        ctx.fillStyle = '#2c2c2c';
        ctx.beginPath();
        ctx.arc(x - size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(x + size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#ff69b4';
        ctx.beginPath();
        ctx.moveTo(x, y + size * 0.5);
        ctx.lineTo(x - size * 0.04, y + size * 0.45);
        ctx.lineTo(x + size * 0.04, y + size * 0.45);
        ctx.closePath();
        ctx.fill();
        ctx.strokeStyle = '#5a5a5a';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y + size * 0.5);
        ctx.lineTo(x - size * 0.05, y + size * 0.55);
        ctx.moveTo(x, y + size * 0.5);
        ctx.lineTo(x + size * 0.05, y + size * 0.55);
        ctx.stroke();
    }
    drawDivineHead(ctx, x, y, size, features) {
        ctx.strokeStyle = '#8b4513';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(x - size * 0.3, y + size * 0.3);
        ctx.quadraticCurveTo(x - size * 0.4, y, x, y - size * 0.05);
        ctx.quadraticCurveTo(x + size * 0.4, y, x + size * 0.3, y + size * 0.3);
        ctx.quadraticCurveTo(x + size * 0.35, y + size * 0.6, x, y + size * 0.75);
        ctx.quadraticCurveTo(x - size * 0.35, y + size * 0.6, x - size * 0.3, y + size * 0.3);
        ctx.stroke();
        ctx.strokeStyle = '#daa520';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(x - size * 0.2, y - size * 0.05);
        ctx.quadraticCurveTo(x - size * 0.25, y - size * 0.25, x - size * 0.15, y - size * 0.3);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x + size * 0.2, y - size * 0.05);
        ctx.quadraticCurveTo(x + size * 0.25, y - size * 0.25, x + size * 0.15, y - size * 0.3);
        ctx.stroke();
        ctx.fillStyle = '#ff4500';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.15, y + size * 0.25, size * 0.08, size * 0.06, -0.2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(x + size * 0.15, y + size * 0.25, size * 0.08, size * 0.06, 0.2, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.15, y + size * 0.25, size * 0.03, size * 0.04, -0.2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(x + size * 0.15, y + size * 0.25, size * 0.03, size * 0.04, 0.2, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#2a2a2a';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.06, y + size * 0.5, size * 0.04, size * 0.03, -0.3, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(x + size * 0.06, y + size * 0.5, size * 0.04, size * 0.03, 0.3, 0, Math.PI * 2);
        ctx.fill();
    }
    applyFusionTransition(ctx, bodyFeatures, animalHead) {
        const gradient = ctx.createLinearGradient(animalHead.centerX, animalHead.bottomY - 20, animalHead.centerX, animalHead.bottomY + 40);
        gradient.addColorStop(0, 'rgba(248, 249, 250, 0)');
        gradient.addColorStop(0.5, 'rgba(248, 249, 250, 0.3)');
        gradient.addColorStop(1, 'rgba(248, 249, 250, 0)');
        ctx.fillStyle = gradient;
        ctx.fillRect(animalHead.centerX - bodyFeatures.bodyWidth / 2, animalHead.bottomY - 20, bodyFeatures.bodyWidth, 60);
    }
    async addWorkerElements(ctx, animalType, bodyFeatures) {
        const centerX = 512;
        const neckY = (1024 - bodyFeatures.imageHeight) / 2 + bodyFeatures.neckPosition.y;
        ctx.save();
        ctx.strokeStyle = '#1a237e';
        ctx.fillStyle = '#3f51b5';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX - 20, neckY);
        ctx.lineTo(centerX + 20, neckY);
        ctx.lineTo(centerX + 15, neckY + 20);
        ctx.lineTo(centerX - 15, neckY + 20);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(centerX - 15, neckY + 20);
        ctx.lineTo(centerX - 8, neckY + 150);
        ctx.lineTo(centerX, neckY + 160);
        ctx.lineTo(centerX + 8, neckY + 150);
        ctx.lineTo(centerX + 15, neckY + 20);
        ctx.fill();
        ctx.stroke();
        if (animalType === 'OXHORSE') {
            ctx.fillStyle = '#fff';
            ctx.fillRect(centerX + 80, neckY + 50, 80, 100);
            ctx.strokeStyle = '#333';
            ctx.strokeRect(centerX + 80, neckY + 50, 80, 100);
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('工号: 996', centerX + 90, neckY + 80);
            ctx.fillText('部门: 搬砖', centerX + 90, neckY + 100);
            ctx.fillText('级别: P3', centerX + 90, neckY + 120);
        }
        ctx.restore();
    }
    applySketchEffect(ctx) {
        const imageData = ctx.getImageData(0, 0, 1024, 1024);
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
            const contrast = 1.5;
            data[i] = Math.min(255, gray * contrast);
            data[i + 1] = Math.min(255, gray * contrast * 0.95);
            data[i + 2] = Math.min(255, gray * contrast * 0.9);
        }
        ctx.putImageData(imageData, 0, 0);
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.lineWidth = 0.5;
        for (let i = 0; i < 30; i++) {
            ctx.beginPath();
            ctx.moveTo(Math.random() * 1024, Math.random() * 1024);
            ctx.lineTo(Math.random() * 1024, Math.random() * 1024);
            ctx.stroke();
        }
    }
    async generateFallbackFusion(canvas, ctx, animalType) {
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, 1024, 1024);
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(412, 400);
        ctx.lineTo(412, 700);
        ctx.lineTo(462, 800);
        ctx.lineTo(562, 800);
        ctx.lineTo(612, 700);
        ctx.lineTo(612, 400);
        ctx.stroke();
        const headSize = 150;
        this.drawOxHorseHead(ctx, 512, 250, headSize, {});
        ctx.fillStyle = '#3f51b5';
        ctx.fillRect(492, 400, 40, 200);
        const avatarId = Date.now().toString();
        const filename = `avatar-fallback-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
        const uploadDir = path.join(process.cwd(), 'uploads', 'generated-avatars');
        const filePath = path.join(uploadDir, filename);
        try {
            await fs.access(uploadDir);
        }
        catch {
            await fs.mkdir(uploadDir, { recursive: true });
        }
        const buffer = canvas.toBuffer('image/png');
        await fs.writeFile(filePath, buffer);
        this.logger.log(`💾 Fallback图像已保存到: ${filePath}`);
        const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
        const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
        return {
            success: true,
            imageUrl: imageUrl,
            imageBase64: imageBase64,
            filePath: filePath,
            fusionDetails: {
                method: 'Fallback Simple Fusion',
                animalType,
                style: 'Simple Sketch'
            }
        };
    }
    generateErrorFallback(canvas, ctx, animalType, errorMessage) {
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 1024, 1024);
        ctx.fillStyle = '#333333';
        ctx.font = '32px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('图像生成暂时不可用', 512, 400);
        ctx.fillText('请稍后重试', 512, 450);
        ctx.fillText(`动物类型: ${animalType}`, 512, 500);
        const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
        return {
            success: false,
            imageUrl: `data:image/png;base64,${imageBase64}`,
            imageBase64: imageBase64,
            error: errorMessage,
            fallbackUsed: true,
            fusionDetails: {
                method: 'Error Fallback',
                animalType,
                error: '图像处理失败，返回占位符图像'
            }
        };
    }
};
exports.EnhancedFusionGeneratorService = EnhancedFusionGeneratorService;
exports.EnhancedFusionGeneratorService = EnhancedFusionGeneratorService = EnhancedFusionGeneratorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EnhancedFusionGeneratorService);
//# sourceMappingURL=enhanced-fusion-generator.service.js.map