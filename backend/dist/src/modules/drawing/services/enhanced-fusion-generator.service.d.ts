import { ConfigService } from '@nestjs/config';
export interface FusionAvatarResult {
    success: boolean;
    imageUrl: string;
    imageBase64?: string;
    filePath?: string;
    error?: string;
    fallbackUsed?: boolean;
    fusionDetails: {
        method: string;
        animalType: string;
        bodySource?: string;
        headSource?: string;
        style?: string;
        features?: any;
        error?: string;
    };
}
export interface BodyFeatures {
    neckPosition: {
        x: number;
        y: number;
    };
    shoulderPosition: {
        y: number;
    };
    bodyWidth: number;
    imageWidth: number;
    imageHeight: number;
}
export declare class EnhancedFusionGeneratorService {
    private configService;
    private readonly logger;
    constructor(configService: ConfigService);
    generateFusionAvatar(userDrawingBase64: string, animalType: 'OXHORSE' | 'PET' | 'DIVINE', analysisResult?: any): Promise<FusionAvatarResult>;
    private loadUserDrawing;
    private extractBodyFeatures;
    private drawUserBody;
    private drawAnimalHead;
    private drawOxHorseHead;
    private drawPetHead;
    private drawDivineHead;
    private applyFusionTransition;
    private addWorkerElements;
    private applySketchEffect;
    private generateFallbackFusion;
    private generateErrorFallback;
}
