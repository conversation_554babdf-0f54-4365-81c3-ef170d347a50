"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OptimizedFusionGeneratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizedFusionGeneratorService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const fs = require("fs/promises");
const path = require("path");
const crypto = require("crypto");
let OptimizedFusionGeneratorService = OptimizedFusionGeneratorService_1 = class OptimizedFusionGeneratorService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(OptimizedFusionGeneratorService_1.name);
    }
    async generateOptimizedFusion(userDrawingBase64, animalType, analysisResult = {}) {
        return {
            success: false,
            imageUrl: '',
            fusionDetails: {
                method: 'disabled',
                animalType: animalType,
                style: 'unavailable',
                features: {}
            }
        };
        this.logger.log('🎨 开始优化融合生成...');
        this.logger.log(`动物类型: ${animalType}`);
        this.logger.log(`Base64数据长度: ${userDrawingBase64 ? userDrawingBase64.length : 'null'}`);
        const canvas = Canvas.createCanvas(1024, 1024);
        const ctx = canvas.getContext('2d');
        const gradient = ctx.createLinearGradient(0, 0, 0, 1024);
        gradient.addColorStop(0, '#f0f4f8');
        gradient.addColorStop(1, '#e2e8f0');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 1024, 1024);
        try {
            this.logger.log('步骤1: 分析用户画像...');
            const userImage = await this.loadUserDrawing(userDrawingBase64);
            const bodyStructure = this.analyzeBodyStructure(userImage);
            this.logger.log('步骤2: 重绘优化身体...');
            const bodyInfo = await this.drawOptimizedBody(ctx, bodyStructure, animalType);
            this.logger.log('步骤3: 融合动物头部...');
            await this.drawFusedAnimalHead(ctx, animalType, bodyInfo, analysisResult);
            this.logger.log('步骤4: 添加细节装饰...');
            this.addDetailsAndAccessories(ctx, animalType, bodyInfo);
            this.logger.log('步骤5: 应用艺术风格...');
            this.applyArtStyle(ctx, analysisResult.style || 'cartoon');
            const avatarId = Date.now().toString();
            const filename = `avatar-optimized-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
            const uploadDir = path.join(process.cwd(), 'uploads', 'generated-avatars');
            const filePath = path.join(uploadDir, filename);
            try {
                await fs.access(uploadDir);
            }
            catch {
                await fs.mkdir(uploadDir, { recursive: true });
            }
            const buffer = canvas.toBuffer('image/png');
            await fs.writeFile(filePath, buffer);
            const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
            const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
            this.logger.log('📷 生成图像成功');
            this.logger.log(`📷 图像数据长度: ${imageBase64.length}`);
            this.logger.log(`💾 图像已保存到: ${filePath}`);
            return {
                success: true,
                imageUrl: imageUrl,
                imageBase64: imageBase64,
                filePath: filePath,
                fusionDetails: {
                    method: 'Optimized Fusion',
                    animalType,
                    style: 'Smooth Hybrid',
                    features: {
                        bodyRedraw: true,
                        headFusion: true,
                        smoothTransition: true
                    }
                }
            };
        }
        catch (error) {
            this.logger.error(`优化融合失败: ${error.message}`, error.stack);
            return this.generateFallback(canvas, ctx, animalType);
        }
    }
    async loadUserDrawing(base64Data) {
        try {
            if (!base64Data || typeof base64Data !== 'string') {
                throw new Error('Invalid base64 data');
            }
            const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
            const buffer = Buffer.from(imageData, 'base64');
            const image = await Canvas.loadImage(buffer);
            return image;
        }
        catch (error) {
            const canvas = Canvas.createCanvas(300, 400);
            const ctx = canvas.getContext('2d');
            this.drawDefaultFigure(ctx);
            return canvas;
        }
    }
    drawDefaultFigure(ctx) {
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 300, 400);
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(150, 60, 30, 0, Math.PI * 2);
        ctx.moveTo(150, 90);
        ctx.lineTo(150, 250);
        ctx.moveTo(120, 130);
        ctx.lineTo(180, 130);
        ctx.moveTo(150, 250);
        ctx.lineTo(130, 320);
        ctx.moveTo(150, 250);
        ctx.lineTo(170, 320);
        ctx.stroke();
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('默认人形模板', 150, 350);
    }
    analyzeBodyStructure(image) {
        return {
            width: image.width,
            height: image.height,
            proportions: {
                headRatio: 0.15,
                shoulderRatio: 0.25,
                waistRatio: 0.5,
                legRatio: 0.7
            },
            pose: this.detectPose(image),
            style: this.detectDrawingStyle(image)
        };
    }
    detectPose(image) {
        return {
            type: 'standing',
            direction: 'front',
            armPosition: 'sides'
        };
    }
    detectDrawingStyle(image) {
        return {
            lineWeight: 'medium',
            detail: 'moderate',
            shading: 'light'
        };
    }
    async drawOptimizedBody(ctx, structure, animalType) {
        const centerX = 512;
        const centerY = 512;
        const scale = Math.min(600 / structure.width, 700 / structure.height);
        const bodyWidth = 180 * scale;
        const bodyHeight = 400 * scale;
        ctx.save();
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.beginPath();
        const shoulderY = centerY - bodyHeight * 0.3;
        const shoulderWidth = bodyWidth * 0.8;
        ctx.moveTo(centerX - shoulderWidth / 2, shoulderY);
        ctx.bezierCurveTo(centerX - shoulderWidth / 2, shoulderY - 20, centerX + shoulderWidth / 2, shoulderY - 20, centerX + shoulderWidth / 2, shoulderY);
        const waistY = centerY;
        const waistWidth = bodyWidth * 0.6;
        ctx.bezierCurveTo(centerX + shoulderWidth / 2, shoulderY + 40, centerX + waistWidth / 2, waistY - 20, centerX + waistWidth / 2, waistY);
        const hipY = centerY + bodyHeight * 0.1;
        const hipWidth = bodyWidth * 0.7;
        ctx.bezierCurveTo(centerX + waistWidth / 2, waistY + 20, centerX + hipWidth / 2, hipY - 10, centerX + hipWidth / 2, hipY);
        const legEndY = centerY + bodyHeight * 0.4;
        ctx.lineTo(centerX + bodyWidth * 0.2, legEndY);
        ctx.lineTo(centerX + bodyWidth * 0.15, legEndY + 20);
        ctx.lineTo(centerX - bodyWidth * 0.15, legEndY + 20);
        ctx.lineTo(centerX - bodyWidth * 0.2, legEndY);
        ctx.lineTo(centerX - hipWidth / 2, hipY);
        ctx.bezierCurveTo(centerX - hipWidth / 2, hipY - 10, centerX - waistWidth / 2, waistY + 20, centerX - waistWidth / 2, waistY);
        ctx.bezierCurveTo(centerX - waistWidth / 2, waistY - 20, centerX - shoulderWidth / 2, shoulderY + 40, centerX - shoulderWidth / 2, shoulderY);
        ctx.closePath();
        ctx.stroke();
        this.drawArms(ctx, centerX, shoulderY, shoulderWidth, bodyHeight * 0.3);
        ctx.restore();
        return {
            centerX,
            centerY,
            shoulderY,
            bodyWidth,
            bodyHeight,
            scale
        };
    }
    drawArms(ctx, centerX, shoulderY, shoulderWidth, armLength) {
        ctx.beginPath();
        ctx.moveTo(centerX - shoulderWidth / 2, shoulderY);
        ctx.lineTo(centerX - shoulderWidth / 2 - 20, shoulderY + armLength * 0.6);
        ctx.lineTo(centerX - shoulderWidth / 2 - 15, shoulderY + armLength);
        ctx.moveTo(centerX + shoulderWidth / 2, shoulderY);
        ctx.lineTo(centerX + shoulderWidth / 2 + 20, shoulderY + armLength * 0.6);
        ctx.lineTo(centerX + shoulderWidth / 2 + 15, shoulderY + armLength);
        ctx.stroke();
    }
    async drawFusedAnimalHead(ctx, animalType, bodyInfo, analysisResult) {
        const headSize = bodyInfo.bodyWidth * 0.9;
        const headY = bodyInfo.shoulderY - headSize * 0.8;
        ctx.save();
        switch (animalType) {
            case 'DIVINE':
                this.drawDivineHead(ctx, bodyInfo.centerX, headY, headSize);
                break;
            case 'PET':
                this.drawPetHead(ctx, bodyInfo.centerX, headY, headSize);
                break;
            case 'OXHORSE':
            default:
                this.drawOxHorseHead(ctx, bodyInfo.centerX, headY, headSize);
                break;
        }
        ctx.restore();
    }
    drawOxHorseHead(ctx, x, y, size) {
        ctx.strokeStyle = '#4a4a4a';
        ctx.fillStyle = '#f5f5f5';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(x, y + size * 0.4, size * 0.35, size * 0.4, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.ellipse(x - size * 0.25, y + size * 0.1, size * 0.1, size * 0.15, -0.3, 0, Math.PI * 2);
        ctx.ellipse(x + size * 0.25, y + size * 0.1, size * 0.1, size * 0.15, 0.3, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.fillStyle = '#2c2c2c';
        ctx.beginPath();
        ctx.arc(x - size * 0.15, y + size * 0.3, size * 0.06, 0, Math.PI * 2);
        ctx.arc(x + size * 0.15, y + size * 0.3, size * 0.06, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#1a1a1a';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.05, y + size * 0.55, size * 0.02, size * 0.03, 0, 0, Math.PI * 2);
        ctx.ellipse(x + size * 0.05, y + size * 0.55, size * 0.02, size * 0.03, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(x, y + size * 0.65, size * 0.08, 0.2, Math.PI - 0.2);
        ctx.stroke();
    }
    drawPetHead(ctx, x, y, size) {
        ctx.strokeStyle = '#5a5a5a';
        ctx.fillStyle = '#f8f8f8';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(x, y + size * 0.4, size * 0.4, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x - size * 0.3, y + size * 0.1);
        ctx.lineTo(x - size * 0.35, y - size * 0.1);
        ctx.lineTo(x - size * 0.15, y + size * 0.05);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x + size * 0.3, y + size * 0.1);
        ctx.lineTo(x + size * 0.35, y - size * 0.1);
        ctx.lineTo(x + size * 0.15, y + size * 0.05);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        ctx.fillStyle = '#2c2c2c';
        ctx.beginPath();
        ctx.arc(x - size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
        ctx.arc(x + size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#ff69b4';
        ctx.beginPath();
        ctx.moveTo(x, y + size * 0.45);
        ctx.lineTo(x - size * 0.03, y + size * 0.41);
        ctx.lineTo(x + size * 0.03, y + size * 0.41);
        ctx.closePath();
        ctx.fill();
    }
    drawDivineHead(ctx, x, y, size) {
        ctx.strokeStyle = '#8b4513';
        ctx.fillStyle = '#daa520';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.ellipse(x, y + size * 0.4, size * 0.4, size * 0.35, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x - size * 0.2, y);
        ctx.quadraticCurveTo(x - size * 0.25, y - size * 0.25, x - size * 0.15, y - size * 0.3);
        ctx.moveTo(x + size * 0.2, y);
        ctx.quadraticCurveTo(x + size * 0.25, y - size * 0.25, x + size * 0.15, y - size * 0.3);
        ctx.stroke();
        ctx.fillStyle = '#ff4500';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.15, y + size * 0.3, size * 0.08, size * 0.06, 0, 0, Math.PI * 2);
        ctx.ellipse(x + size * 0.15, y + size * 0.3, size * 0.08, size * 0.06, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.15, y + size * 0.3, size * 0.03, size * 0.04, 0, 0, Math.PI * 2);
        ctx.ellipse(x + size * 0.15, y + size * 0.3, size * 0.03, size * 0.04, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = '#2a2a2a';
        ctx.beginPath();
        ctx.ellipse(x - size * 0.06, y + size * 0.5, size * 0.04, size * 0.02, 0, 0, Math.PI * 2);
        ctx.ellipse(x + size * 0.06, y + size * 0.5, size * 0.04, size * 0.02, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    addDetailsAndAccessories(ctx, animalType, bodyInfo) {
        const centerX = bodyInfo.centerX;
        const neckY = bodyInfo.shoulderY;
        ctx.save();
        ctx.fillStyle = '#2563eb';
        ctx.strokeStyle = '#1d4ed8';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX - 15, neckY);
        ctx.lineTo(centerX + 15, neckY);
        ctx.lineTo(centerX + 12, neckY + 15);
        ctx.lineTo(centerX - 12, neckY + 15);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(centerX - 12, neckY + 15);
        ctx.lineTo(centerX - 8, neckY + 120);
        ctx.lineTo(centerX, neckY + 130);
        ctx.lineTo(centerX + 8, neckY + 120);
        ctx.lineTo(centerX + 12, neckY + 15);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        if (animalType === 'OXHORSE') {
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(centerX + 60, neckY + 30, 70, 90);
            ctx.strokeStyle = '#333';
            ctx.strokeRect(centerX + 60, neckY + 30, 70, 90);
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('工号: 996', centerX + 65, neckY + 50);
            ctx.fillText('部门: 码农', centerX + 65, neckY + 70);
            ctx.fillText('等级: P0', centerX + 65, neckY + 90);
            ctx.fillText('状态: 搬砖', centerX + 65, neckY + 110);
        }
        ctx.restore();
    }
    applyArtStyle(ctx, style) {
        const imageData = ctx.getImageData(0, 0, 1024, 1024);
        const data = imageData.data;
        switch (style) {
            case 'cartoon':
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * 1.2);
                    data[i + 1] = Math.min(255, data[i + 1] * 1.2);
                    data[i + 2] = Math.min(255, data[i + 2] * 1.2);
                }
                break;
            case 'sketch':
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
                    data[i] = gray;
                    data[i + 1] = gray;
                    data[i + 2] = gray;
                }
                break;
            default:
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * 1.1);
                    data[i + 1] = Math.min(255, data[i + 1] * 1.1);
                    data[i + 2] = Math.min(255, data[i + 2] * 1.1);
                }
        }
        ctx.putImageData(imageData, 0, 0);
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.08)';
        ctx.lineWidth = 0.5;
        for (let i = 0; i < 20; i++) {
            ctx.beginPath();
            ctx.moveTo(Math.random() * 1024, Math.random() * 1024);
            ctx.lineTo(Math.random() * 1024, Math.random() * 1024);
            ctx.stroke();
        }
    }
    generateFallback(canvas, ctx, animalType) {
        ctx.fillStyle = '#f0f4f8';
        ctx.fillRect(0, 0, 1024, 1024);
        ctx.strokeStyle = '#4a5568';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(412, 400);
        ctx.lineTo(412, 700);
        ctx.lineTo(612, 700);
        ctx.lineTo(612, 400);
        ctx.closePath();
        ctx.stroke();
        ctx.beginPath();
        ctx.arc(512, 350, 50, 0, Math.PI * 2);
        ctx.stroke();
        ctx.fillStyle = '#4a5568';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('简化版融合图像', 512, 800);
        ctx.fillText(`动物类型: ${animalType}`, 512, 820);
        const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
        return {
            success: true,
            imageUrl: `data:image/png;base64,${imageBase64}`,
            imageBase64: imageBase64,
            fusionDetails: {
                method: 'Fallback Optimized Fusion',
                animalType,
                style: 'Simple',
                features: {
                    fallback: true
                }
            }
        };
    }
};
exports.OptimizedFusionGeneratorService = OptimizedFusionGeneratorService;
exports.OptimizedFusionGeneratorService = OptimizedFusionGeneratorService = OptimizedFusionGeneratorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], OptimizedFusionGeneratorService);
//# sourceMappingURL=optimized-fusion-generator.service.js.map