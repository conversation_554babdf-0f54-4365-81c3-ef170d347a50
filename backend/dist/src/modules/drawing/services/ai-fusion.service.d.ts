import { ConfigService } from '@nestjs/config';
export interface AIFusionResult {
    success: boolean;
    avatar: {
        avatarId: string;
        animalType: string;
        generationMethod: string;
        imageUrl: string;
        filePath?: string;
        prompt?: string;
        fusionDetails: {
            method: string;
            animalType: string;
            style: string;
            model?: string;
        };
    };
    message?: string;
    processingTime?: number;
}
export interface DrawingAnalysis {
    animalType: 'OXHORSE' | 'PET' | 'DIVINE';
    confidence: number;
    features: {
        complexity: number;
        emotionalTone: string;
        dominantColors: string[];
        style: string;
    };
    recommendation: {
        primaryChoice: string;
        reasoning: string;
        alternatives: string[];
    };
}
export declare class AIFusionService {
    private configService;
    private readonly logger;
    private geminiAPI;
    private isEnabled;
    constructor(configService: ConfigService);
    private initializeService;
    analyzeUserDrawing(imageBase64: string): Promise<DrawingAnalysis>;
    generateFusionAvatar(userDrawingBase64: string, animalType: 'OXHORSE' | 'PET' | 'DIVINE', analysisData?: any): Promise<AIFusionResult>;
    private generateWithGemini;
    private parseAnalysisResult;
    private getFallbackAnalysis;
    private extractComplexity;
    private extractEmotionalTone;
    private extractColors;
    private extractStyle;
    getServiceStatus(): {
        enabled: boolean;
        geminiAvailable: boolean;
        status: string;
    };
}
