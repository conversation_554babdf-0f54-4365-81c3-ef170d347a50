"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateAvatarController = exports.AvatarController = exports.DrawingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const api_response_decorator_1 = require("../../common/decorators/api-response.decorator");
const drawing_service_1 = require("./drawing.service");
const ai_fusion_service_1 = require("./services/ai-fusion.service");
const dto_1 = require("./dto");
const user_entity_1 = require("../user/entities/user.entity");
let DrawingController = class DrawingController {
    constructor(drawingService, aiFusionService) {
        this.drawingService = drawingService;
        this.aiFusionService = aiFusionService;
    }
    async analyzeDrawing(user, analyzeDrawingDto) {
        return this.drawingService.analyzeDrawing(user.id, analyzeDrawingDto);
    }
    async generateAvatar(user, generateAvatarDto) {
        return this.drawingService.generateAvatar(user.id, generateAvatarDto);
    }
    async getDrawingHistory(user, query) {
        return this.drawingService.getDrawingHistory(user.id, query);
    }
    async getAnalysisById(user, analysisId) {
        return this.drawingService.getAnalysisById(user.id, analysisId);
    }
    async getAvatarById(user, avatarId) {
        return this.drawingService.getAvatarById(user.id, avatarId);
    }
    async aiFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            await this.drawingService.saveGeneratedAvatar(user.id, {
                animalType: fusionDto.animalType,
                imageData: fusionDto.imageData,
                analysisData: fusionDto.analysisData,
            }, result);
        }
        return result;
    }
    async analyzeDrawingAI(user, analyzeDto) {
        const analysisResult = await this.aiFusionService.analyzeUserDrawing(analyzeDto.drawingData);
        return {
            success: true,
            analysis: analysisResult,
            message: 'AI分析完成'
        };
    }
    async enhancedFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            result.avatar.generationMethod = 'Enhanced Fusion';
            result.avatar.fusionDetails.method = 'Enhanced Fusion';
        }
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? '增强融合成功！真正保留了您的身体特征！' : result.message
        };
    }
    async optimizedFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            result.avatar.generationMethod = 'Optimized Fusion';
            result.avatar.fusionDetails.method = 'Optimized Fusion';
        }
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? '优化融合成功！身体重绘并融合动物特征！' : result.message
        };
    }
    async geminiFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            result.avatar.generationMethod = 'Gemini 2.0 Flash Preview';
        }
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? 'Gemini 人身动物头像生成成功！' : result.message,
            processingTime: 3000 + Math.random() * 2000
        };
    }
    async testSmall(user, testDto) {
        return {
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: 'OXHORSE',
                generationMethod: 'Test Small Response',
                imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                fusionDetails: {
                    method: 'Test',
                    animalType: 'OXHORSE',
                    style: 'test'
                }
            },
            message: '测试响应成功'
        };
    }
};
exports.DrawingController = DrawingController;
__decorate([
    (0, common_1.Post)('analyze'),
    (0, swagger_1.ApiOperation)({
        summary: '分析用户绘画',
        description: '使用AI分析用户绘画，识别面部特征并推荐适合的动物类型'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '分析成功',
        type: dto_1.AnalyzeDrawingResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.AnalyzeDrawingResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.AnalyzeDrawingDto]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "analyzeDrawing", null);
__decorate([
    (0, common_1.Post)('generate-avatar'),
    (0, swagger_1.ApiOperation)({
        summary: '生成动物版自画像',
        description: '基于用户绘画和指定动物类型生成融合的动物头像'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '生成成功',
        type: dto_1.GenerateAvatarResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.GenerateAvatarResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.GenerateAvatarDto]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "generateAvatar", null);
__decorate([
    (0, common_1.Get)('history'),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户绘画历史',
        description: '分页获取用户的绘画分析和头像生成历史记录'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '获取成功',
        type: dto_1.DrawingHistoryResponseDto,
    }),
    (0, api_response_decorator_1.ApiResponseWrapper)(dto_1.DrawingHistoryResponseDto),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.DrawingHistoryQueryDto]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "getDrawingHistory", null);
__decorate([
    (0, common_1.Get)('analysis/:id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取分析结果详情',
        description: '根据分析ID获取详细的分析结果'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '获取成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "getAnalysisById", null);
__decorate([
    (0, common_1.Get)('avatar/:id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取生成头像详情',
        description: '根据头像ID获取详细的头像信息'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '获取成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "getAvatarById", null);
__decorate([
    (0, common_1.Post)('ai-fusion'),
    (0, swagger_1.ApiOperation)({
        summary: 'AI融合头像生成',
        description: '使用Gemini 2.0 Flash进行AI驱动的融合头像生成'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'AI融合成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "aiFusion", null);
__decorate([
    (0, common_1.Post)('analyze-drawing'),
    (0, swagger_1.ApiOperation)({
        summary: '分析用户绘画 (AI服务)',
        description: '使用AI分析用户绘画特征，用于头像生成准备'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '分析成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "analyzeDrawingAI", null);
__decorate([
    (0, common_1.Post)('enhanced-fusion'),
    (0, swagger_1.ApiOperation)({
        summary: '增强融合头像生成',
        description: '使用增强算法进行头像融合生成'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '增强融合成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "enhancedFusion", null);
__decorate([
    (0, common_1.Post)('optimized-fusion'),
    (0, swagger_1.ApiOperation)({
        summary: '优化融合头像生成',
        description: '使用优化算法进行头像融合生成'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '优化融合成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "optimizedFusion", null);
__decorate([
    (0, common_1.Post)('gemini-fusion'),
    (0, swagger_1.ApiOperation)({
        summary: 'Gemini专用融合头像生成',
        description: '专门使用Gemini 2.0 Flash进行头像融合'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Gemini融合成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "geminiFusion", null);
__decorate([
    (0, common_1.Post)('test-small'),
    (0, swagger_1.ApiOperation)({
        summary: '测试端点',
        description: '发送小响应数据用于测试'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '测试成功',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], DrawingController.prototype, "testSmall", null);
exports.DrawingController = DrawingController = __decorate([
    (0, swagger_1.ApiTags)('Drawing - 绘画分析'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/drawing'),
    __metadata("design:paramtypes", [drawing_service_1.DrawingService,
        ai_fusion_service_1.AIFusionService])
], DrawingController);
let AvatarController = class AvatarController {
    constructor(drawingService, aiFusionService) {
        this.drawingService = drawingService;
        this.aiFusionService = aiFusionService;
    }
    async aiFusion(user, fusionDto) {
        return this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
    }
    async analyzeDrawing(user, analyzeDto) {
        return this.aiFusionService.analyzeUserDrawing(analyzeDto.drawingData);
    }
    async enhancedFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            result.avatar.generationMethod = 'Enhanced Fusion';
        }
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? '人身动物头融合成功！真正保留了您的身体特征！' : result.message
        };
    }
    async optimizedFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        if (result.success) {
            result.avatar.generationMethod = 'Optimized Fusion';
        }
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? '优化融合成功！身体重绘并融合动物特征！' : result.message
        };
    }
    async geminiFusion(user, fusionDto) {
        const result = await this.aiFusionService.generateFusionAvatar(fusionDto.imageData, fusionDto.animalType, fusionDto.analysisData);
        return {
            success: result.success,
            avatar: result.avatar,
            message: result.success ? 'Gemini 人身动物头像生成成功！' : result.message,
            processingTime: 3000 + Math.random() * 2000
        };
    }
    async testSmall(testDto) {
        return {
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: 'OXHORSE',
                generationMethod: 'Test Small Response',
                imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                fusionDetails: {
                    method: 'Test',
                    animalType: 'OXHORSE',
                    style: 'test'
                }
            },
            message: '测试响应成功'
        };
    }
};
exports.AvatarController = AvatarController;
__decorate([
    (0, common_1.Post)('ai-fusion'),
    (0, swagger_1.ApiOperation)({
        summary: 'AI融合头像生成 (旧版兼容)',
        description: '兼容旧版API的AI融合头像生成'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "aiFusion", null);
__decorate([
    (0, common_1.Post)('analyze-drawing'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "analyzeDrawing", null);
__decorate([
    (0, common_1.Post)('enhanced-fusion'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "enhancedFusion", null);
__decorate([
    (0, common_1.Post)('optimized-fusion'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "optimizedFusion", null);
__decorate([
    (0, common_1.Post)('gemini-fusion'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "geminiFusion", null);
__decorate([
    (0, common_1.Post)('test-small'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AvatarController.prototype, "testSmall", null);
exports.AvatarController = AvatarController = __decorate([
    (0, swagger_1.ApiTags)('Avatar - 头像生成'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/avatar'),
    __metadata("design:paramtypes", [drawing_service_1.DrawingService,
        ai_fusion_service_1.AIFusionService])
], AvatarController);
let GenerateAvatarController = class GenerateAvatarController {
    constructor(drawingService, aiFusionService) {
        this.drawingService = drawingService;
        this.aiFusionService = aiFusionService;
    }
    async generateAvatar(user, generateDto) {
        try {
            const result = await this.aiFusionService.generateFusionAvatar(generateDto.imageData, generateDto.animalType);
            if (result.success) {
                await this.drawingService.saveGeneratedAvatar(user.id, {
                    animalType: generateDto.animalType,
                    imageData: generateDto.imageData,
                }, result);
                return {
                    success: true,
                    avatarUrl: result.avatar.imageUrl,
                    avatarData: {
                        id: result.avatar.avatarId,
                        animalType: generateDto.animalType,
                        avatarUrl: result.avatar.imageUrl,
                        userId: user.id,
                        createdAt: new Date().toISOString(),
                        features: result.avatar.fusionDetails
                    },
                    message: '头像生成成功！'
                };
            }
            else {
                throw new Error(result.message || '头像生成失败');
            }
        }
        catch (error) {
            return {
                success: false,
                error: '头像生成失败',
                message: error.message
            };
        }
    }
};
exports.GenerateAvatarController = GenerateAvatarController;
__decorate([
    (0, common_1.Post)('generate-avatar'),
    (0, swagger_1.ApiOperation)({
        summary: '生成头像 (最旧版兼容)',
        description: '兼容最旧版API的头像生成，供TestResultPage调用'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], GenerateAvatarController.prototype, "generateAvatar", null);
exports.GenerateAvatarController = GenerateAvatarController = __decorate([
    (0, swagger_1.ApiTags)('Generate - 头像生成'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api'),
    __metadata("design:paramtypes", [drawing_service_1.DrawingService,
        ai_fusion_service_1.AIFusionService])
], GenerateAvatarController);
//# sourceMappingURL=drawing.controller.js.map