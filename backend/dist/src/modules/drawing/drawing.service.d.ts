import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AIService } from '@/modules/ai/ai.service';
import { AIFusionService, AIFusionResult } from './services/ai-fusion.service';
import { DrawingAnalysis } from './entities/drawing-analysis.entity';
import { GeneratedAvatar } from './entities/generated-avatar.entity';
import { AnalyzeDrawingDto, AnalyzeDrawingResponseDto, GenerateAvatarDto, GenerateAvatarResponseDto, DrawingHistoryQueryDto, DrawingHistoryResponseDto } from './dto';
export declare class DrawingService {
    private drawingAnalysisRepository;
    private generatedAvatarRepository;
    private aiService;
    private aiFusionService;
    private configService;
    private readonly logger;
    constructor(drawingAnalysisRepository: Repository<DrawingAnalysis>, generatedAvatarRepository: Repository<GeneratedAvatar>, aiService: AIService, aiFusionService: AIFusionService, configService: ConfigService);
    analyzeDrawing(userId: string, analyzeDrawingDto: AnalyzeDrawingDto): Promise<AnalyzeDrawingResponseDto>;
    generateAvatar(userId: string, generateAvatarDto: GenerateAvatarDto): Promise<GenerateAvatarResponseDto>;
    getDrawingHistory(userId: string, query: DrawingHistoryQueryDto): Promise<DrawingHistoryResponseDto>;
    getAnalysisById(userId: string, analysisId: string): Promise<DrawingAnalysis>;
    getAvatarById(userId: string, avatarId: string): Promise<GeneratedAvatar>;
    private validateImageData;
    private saveAnalysisResult;
    private saveGeneratedAvatarLegacy;
    private getDefaultSpecies;
    saveGeneratedAvatar(userId: string, fusionDto: {
        animalType: string;
        imageData: string;
        analysisData?: any;
    }, fusionResult: AIFusionResult): Promise<GeneratedAvatar>;
}
