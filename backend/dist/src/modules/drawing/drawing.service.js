"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DrawingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DrawingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const ai_service_1 = require("../ai/ai.service");
const ai_fusion_service_1 = require("./services/ai-fusion.service");
const drawing_analysis_entity_1 = require("./entities/drawing-analysis.entity");
const generated_avatar_entity_1 = require("./entities/generated-avatar.entity");
const animal_types_1 = require("../../common/constants/animal-types");
let DrawingService = DrawingService_1 = class DrawingService {
    constructor(drawingAnalysisRepository, generatedAvatarRepository, aiService, aiFusionService, configService) {
        this.drawingAnalysisRepository = drawingAnalysisRepository;
        this.generatedAvatarRepository = generatedAvatarRepository;
        this.aiService = aiService;
        this.aiFusionService = aiFusionService;
        this.configService = configService;
        this.logger = new common_1.Logger(DrawingService_1.name);
    }
    async analyzeDrawing(userId, analyzeDrawingDto) {
        const startTime = Date.now();
        try {
            this.logger.log(`Starting drawing analysis for user ${userId}`);
            this.validateImageData(analyzeDrawingDto.imageData);
            const analysisResult = await this.aiService.analyzeDrawing(analyzeDrawingDto.imageData);
            const processingTime = Date.now() - startTime;
            const drawingAnalysis = await this.saveAnalysisResult(userId, analyzeDrawingDto, analysisResult, processingTime);
            const recommendedAnimalConfig = animal_types_1.ANIMAL_CONFIG[analysisResult.recommendedSpecies];
            this.logger.log(`Drawing analysis completed for user ${userId} in ${processingTime}ms`);
            return {
                success: true,
                analysisId: drawingAnalysis.id,
                detectedAnimalType: analysisResult.detectedAnimalType,
                analysisDescription: analysisResult.description,
                confidence: analysisResult.confidence,
                faceFeatures: analysisResult.faceFeatures,
                recommendedAnimal: {
                    species: analysisResult.recommendedSpecies,
                    name: recommendedAnimalConfig.name,
                    description: recommendedAnimalConfig.description,
                    color: recommendedAnimalConfig.color,
                },
                timestamp: drawingAnalysis.createdAt,
            };
        }
        catch (error) {
            this.logger.error(`Drawing analysis failed for user ${userId}:`, error);
            throw new common_1.BadRequestException(`绘画分析失败: ${error.message}`);
        }
    }
    async generateAvatar(userId, generateAvatarDto) {
        const startTime = Date.now();
        try {
            this.logger.log(`Starting avatar generation for user ${userId}`);
            this.validateImageData(generateAvatarDto.imageData);
            let analysisResult;
            if (generateAvatarDto.analysisId) {
                const existingAnalysis = await this.drawingAnalysisRepository.findOne({
                    where: { id: generateAvatarDto.analysisId, userId },
                });
                if (existingAnalysis) {
                    analysisResult = {
                        description: existingAnalysis.analysisResult,
                        faceFeatures: existingAnalysis.faceFeatures,
                        pixelAnalysis: existingAnalysis.pixelAnalysis,
                        confidence: Number(existingAnalysis.confidence),
                        detectedAnimalType: existingAnalysis.animalType,
                        recommendedSpecies: existingAnalysis.animalSpecies || this.getDefaultSpecies(existingAnalysis.animalType),
                    };
                }
            }
            if (!analysisResult) {
                this.logger.log('No existing analysis found, performing quick analysis');
                analysisResult = await this.aiService.analyzeDrawing(generateAvatarDto.imageData);
            }
            const avatarResult = await this.aiService.generateAvatar(generateAvatarDto.imageData, generateAvatarDto.animalType, generateAvatarDto.animalSpecies, analysisResult);
            const totalProcessingTime = Date.now() - startTime;
            const finalSpecies = generateAvatarDto.animalSpecies ||
                analysisResult.recommendedSpecies ||
                this.getDefaultSpecies(generateAvatarDto.animalType);
            const generatedAvatar = await this.saveGeneratedAvatarLegacy(userId, generateAvatarDto, avatarResult, finalSpecies, totalProcessingTime, generateAvatarDto.analysisId);
            this.logger.log(`Avatar generation completed for user ${userId} in ${totalProcessingTime}ms`);
            return {
                success: true,
                avatar: {
                    avatarId: generatedAvatar.id,
                    animalType: generateAvatarDto.animalType,
                    animalSpecies: finalSpecies,
                    imageUrl: generatedAvatar.avatarImageUrl || `data:image/png;base64,${avatarResult.imageBase64}`,
                    imageBase64: avatarResult.imageBase64,
                    style: generatedAvatar.style,
                    features: generatedAvatar.features,
                    stats: generatedAvatar.stats,
                },
                generationMethod: avatarResult.generationMethod,
                processingTime: totalProcessingTime,
                message: '动物版自画像生成成功！',
                timestamp: generatedAvatar.createdAt,
            };
        }
        catch (error) {
            this.logger.error(`Avatar generation failed for user ${userId}:`, error);
            throw new common_1.BadRequestException(`头像生成失败: ${error.message}`);
        }
    }
    async getDrawingHistory(userId, query) {
        try {
            this.logger.log(`Fetching drawing history for user ${userId}`);
            const queryBuilder = this.generatedAvatarRepository
                .createQueryBuilder('avatar')
                .leftJoinAndSelect('avatar.analysis', 'analysis')
                .where('avatar.userId = :userId', { userId })
                .orderBy('avatar.createdAt', 'DESC');
            if (query.animalType) {
                queryBuilder.andWhere('avatar.animalType = :animalType', {
                    animalType: query.animalType,
                });
            }
            const limit = query.limit || 10;
            const page = query.page || 1;
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            const [avatars, total] = await queryBuilder.getManyAndCount();
            const items = avatars.map((avatar) => {
                const animalConfig = animal_types_1.ANIMAL_CONFIG[avatar.animalSpecies];
                return {
                    id: avatar.id,
                    originalImageUrl: avatar.analysis?.originalImageUrl ||
                        `data:image/png;base64,${avatar.analysis?.originalImageData || ''}`,
                    avatarImageUrl: avatar.avatarImageUrl ||
                        `data:image/png;base64,${avatar.avatarImageData}`,
                    animalType: avatar.animalType,
                    animalName: animalConfig?.name || avatar.animalSpecies,
                    analysisResult: avatar.analysis?.analysisResult || '未知分析结果',
                    generationMethod: avatar.generationMethod,
                    confidence: avatar.analysis ? Number(avatar.analysis.confidence) : 0,
                    createdAt: avatar.createdAt,
                    updatedAt: avatar.updatedAt,
                };
            });
            const totalPages = Math.ceil(total / limit);
            this.logger.log(`Found ${total} drawing history items for user ${userId}`);
            return {
                items,
                total,
                page,
                limit,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            };
        }
        catch (error) {
            this.logger.error(`Failed to fetch drawing history for user ${userId}:`, error);
            throw new common_1.BadRequestException(`获取绘画历史失败: ${error.message}`);
        }
    }
    async getAnalysisById(userId, analysisId) {
        const analysis = await this.drawingAnalysisRepository.findOne({
            where: { id: analysisId, userId },
        });
        if (!analysis) {
            throw new common_1.NotFoundException('分析结果不存在');
        }
        return analysis;
    }
    async getAvatarById(userId, avatarId) {
        const avatar = await this.generatedAvatarRepository.findOne({
            where: { id: avatarId, userId },
            relations: ['analysis'],
        });
        if (!avatar) {
            throw new common_1.NotFoundException('头像不存在');
        }
        return avatar;
    }
    validateImageData(imageData) {
        if (!imageData) {
            throw new common_1.BadRequestException('图像数据不能为空');
        }
        const base64Pattern = /^data:image\/(png|jpeg|jpg|gif|webp);base64,/;
        const isDataUrl = base64Pattern.test(imageData);
        if (!isDataUrl && !imageData.match(/^[A-Za-z0-9+/]*={0,2}$/)) {
            throw new common_1.BadRequestException('图像数据格式不正确，请提供有效的base64数据');
        }
        const sizeInBytes = (imageData.length * 3) / 4;
        const maxSizeInBytes = 10 * 1024 * 1024;
        if (sizeInBytes > maxSizeInBytes) {
            throw new common_1.BadRequestException('图像数据过大，请提供小于10MB的图像');
        }
    }
    async saveAnalysisResult(userId, dto, result, processingTime) {
        const drawingAnalysis = this.drawingAnalysisRepository.create({
            userId,
            originalImageData: dto.imageData,
            analysisResult: result.description,
            animalType: result.detectedAnimalType,
            animalSpecies: result.recommendedSpecies,
            faceFeatures: result.faceFeatures,
            pixelAnalysis: result.pixelAnalysis,
            confidence: result.confidence,
            analysisMethod: 'ai_service',
            processingTime,
        });
        return this.drawingAnalysisRepository.save(drawingAnalysis);
    }
    async saveGeneratedAvatarLegacy(userId, dto, result, animalSpecies, processingTime, analysisId) {
        const generatedAvatar = this.generatedAvatarRepository.create({
            userId,
            analysisId,
            animalType: dto.animalType,
            animalSpecies,
            avatarImageData: result.imageBase64,
            avatarImageUrl: result.imageUrl,
            style: 'sketch',
            features: {
                fusion: 'deep',
                quality: 'high',
                type: 'human_animal_hybrid',
            },
            stats: result.stats,
            generationMethod: result.generationMethod,
            generationPrompt: result.prompt,
            processingTime,
            metadata: {
                modelVersion: '1.0.0',
            },
        });
        return this.generatedAvatarRepository.save(generatedAvatar);
    }
    getDefaultSpecies(animalType) {
        switch (animalType) {
            case animal_types_1.AnimalCategory.DIVINE_BEAST:
                return animal_types_1.AnimalSpecies.DRAGON;
            case animal_types_1.AnimalCategory.PET:
                return animal_types_1.AnimalSpecies.GOLDEN_RETRIEVER;
            case animal_types_1.AnimalCategory.WORKING_ANIMAL:
            default:
                return animal_types_1.AnimalSpecies.OX;
        }
    }
    async saveGeneratedAvatar(userId, fusionDto, fusionResult) {
        try {
            let animalCategory;
            let animalSpecies;
            switch (fusionDto.animalType) {
                case 'OXHORSE':
                    animalCategory = animal_types_1.AnimalCategory.WORKING_ANIMAL;
                    animalSpecies = animal_types_1.AnimalSpecies.OX;
                    break;
                case 'PET':
                    animalCategory = animal_types_1.AnimalCategory.PET;
                    animalSpecies = animal_types_1.AnimalSpecies.GOLDEN_RETRIEVER;
                    break;
                case 'DIVINE':
                    animalCategory = animal_types_1.AnimalCategory.DIVINE_BEAST;
                    animalSpecies = animal_types_1.AnimalSpecies.DRAGON;
                    break;
                default:
                    animalCategory = animal_types_1.AnimalCategory.WORKING_ANIMAL;
                    animalSpecies = animal_types_1.AnimalSpecies.OX;
            }
            const generatedAvatar = this.generatedAvatarRepository.create({
                userId,
                analysisId: undefined,
                animalType: animalCategory,
                animalSpecies,
                avatarImageData: fusionResult.avatar?.imageUrl || '',
                avatarImageUrl: fusionResult.avatar?.imageUrl,
                style: 'ai_fusion',
                features: {
                    fusion: fusionResult.avatar?.fusionDetails?.method || 'ai_fusion',
                    quality: 'high',
                    type: 'ai_generated_fusion',
                },
                stats: {
                    workEfficiency: 80,
                    happiness: 75,
                    energy: 85,
                    creativity: 90,
                },
                generationMethod: fusionResult.avatar?.generationMethod || 'AI Fusion',
                generationPrompt: fusionResult.avatar?.prompt || '',
                processingTime: fusionResult.processingTime || 0,
                metadata: {
                    modelVersion: '1.0.0',
                },
            });
            const savedAvatar = await this.generatedAvatarRepository.save(generatedAvatar);
            this.logger.log(`AI融合头像已保存: ${savedAvatar.id}, 用户: ${userId}, 动物类型: ${fusionDto.animalType}`);
            return savedAvatar;
        }
        catch (error) {
            this.logger.error(`保存AI融合头像失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`保存头像失败: ${error.message}`);
        }
    }
};
exports.DrawingService = DrawingService;
exports.DrawingService = DrawingService = DrawingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(drawing_analysis_entity_1.DrawingAnalysis)),
    __param(1, (0, typeorm_1.InjectRepository)(generated_avatar_entity_1.GeneratedAvatar)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        ai_service_1.AIService,
        ai_fusion_service_1.AIFusionService,
        config_1.ConfigService])
], DrawingService);
//# sourceMappingURL=drawing.service.js.map