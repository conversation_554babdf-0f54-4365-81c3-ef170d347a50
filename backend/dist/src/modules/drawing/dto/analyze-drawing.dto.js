"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyzeDrawingResponseDto = exports.AnalyzeDrawingDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const animal_types_1 = require("../../../common/constants/animal-types");
class AnalyzeDrawingDto {
}
exports.AnalyzeDrawingDto = AnalyzeDrawingDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户绘画的base64图像数据',
        example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAK...',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AnalyzeDrawingDto.prototype, "imageData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '可选的动物类型偏好',
        enum: animal_types_1.AnimalCategory,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalCategory),
    __metadata("design:type", String)
], AnalyzeDrawingDto.prototype, "preferredAnimalType", void 0);
class AnalyzeDrawingResponseDto {
}
exports.AnalyzeDrawingResponseDto = AnalyzeDrawingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分析是否成功' }),
    __metadata("design:type", Boolean)
], AnalyzeDrawingResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分析结果ID' }),
    __metadata("design:type", String)
], AnalyzeDrawingResponseDto.prototype, "analysisId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '检测到的动物类型' }),
    __metadata("design:type", String)
], AnalyzeDrawingResponseDto.prototype, "detectedAnimalType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'AI分析的详细描述' }),
    __metadata("design:type", String)
], AnalyzeDrawingResponseDto.prototype, "analysisDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '置信度评分 (0-100)' }),
    __metadata("design:type", Number)
], AnalyzeDrawingResponseDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '面部特征分析' }),
    __metadata("design:type", Object)
], AnalyzeDrawingResponseDto.prototype, "faceFeatures", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推荐的动物特征' }),
    __metadata("design:type", Object)
], AnalyzeDrawingResponseDto.prototype, "recommendedAnimal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分析时间戳' }),
    __metadata("design:type", Date)
], AnalyzeDrawingResponseDto.prototype, "timestamp", void 0);
//# sourceMappingURL=analyze-drawing.dto.js.map