"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DrawingHistoryResponseDto = exports.DrawingHistoryItemDto = exports.DrawingHistoryQueryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const animal_types_1 = require("../../../common/constants/animal-types");
class DrawingHistoryQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.DrawingHistoryQueryDto = DrawingHistoryQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        default: 1,
        minimum: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], DrawingHistoryQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        default: 10,
        minimum: 1,
        maximum: 50,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], DrawingHistoryQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '筛选动物类型',
        enum: animal_types_1.AnimalCategory,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalCategory),
    __metadata("design:type", String)
], DrawingHistoryQueryDto.prototype, "animalType", void 0);
class DrawingHistoryItemDto {
}
exports.DrawingHistoryItemDto = DrawingHistoryItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘画记录ID' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始绘画图像URL' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "originalImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成的头像URL' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "avatarImageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物类型' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "animalType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '动物种类名称' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "animalName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'AI分析结果' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "analysisResult", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成方法' }),
    __metadata("design:type", String)
], DrawingHistoryItemDto.prototype, "generationMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '置信度' }),
    __metadata("design:type", Number)
], DrawingHistoryItemDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], DrawingHistoryItemDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间' }),
    __metadata("design:type", Date)
], DrawingHistoryItemDto.prototype, "updatedAt", void 0);
class DrawingHistoryResponseDto {
}
exports.DrawingHistoryResponseDto = DrawingHistoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘画历史列表', type: [DrawingHistoryItemDto] }),
    __metadata("design:type", Array)
], DrawingHistoryResponseDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量' }),
    __metadata("design:type", Number)
], DrawingHistoryResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码' }),
    __metadata("design:type", Number)
], DrawingHistoryResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量' }),
    __metadata("design:type", Number)
], DrawingHistoryResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数' }),
    __metadata("design:type", Number)
], DrawingHistoryResponseDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有下一页' }),
    __metadata("design:type", Boolean)
], DrawingHistoryResponseDto.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有上一页' }),
    __metadata("design:type", Boolean)
], DrawingHistoryResponseDto.prototype, "hasPrev", void 0);
//# sourceMappingURL=drawing-history.dto.js.map