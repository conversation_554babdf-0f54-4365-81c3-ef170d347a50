"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateAvatarResponseDto = exports.GenerateAvatarDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const animal_types_1 = require("../../../common/constants/animal-types");
class GenerateAvatarDto {
}
exports.GenerateAvatarDto = GenerateAvatarDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户绘画的base64图像数据',
        example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAK...',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GenerateAvatarDto.prototype, "imageData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '动物类型',
        enum: animal_types_1.AnimalCategory,
    }),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalCategory),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GenerateAvatarDto.prototype, "animalType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '具体动物种类（可选）',
        enum: animal_types_1.AnimalSpecies,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(animal_types_1.AnimalSpecies),
    __metadata("design:type", String)
], GenerateAvatarDto.prototype, "animalSpecies", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分析结果ID（如果已有分析结果）',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], GenerateAvatarDto.prototype, "analysisId", void 0);
class GenerateAvatarResponseDto {
}
exports.GenerateAvatarResponseDto = GenerateAvatarResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成是否成功' }),
    __metadata("design:type", Boolean)
], GenerateAvatarResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成的头像数据' }),
    __metadata("design:type", Object)
], GenerateAvatarResponseDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成方法' }),
    __metadata("design:type", String)
], GenerateAvatarResponseDto.prototype, "generationMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '处理时间（毫秒）' }),
    __metadata("design:type", Number)
], GenerateAvatarResponseDto.prototype, "processingTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '消息' }),
    __metadata("design:type", String)
], GenerateAvatarResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成时间戳' }),
    __metadata("design:type", Date)
], GenerateAvatarResponseDto.prototype, "timestamp", void 0);
//# sourceMappingURL=generate-avatar.dto.js.map