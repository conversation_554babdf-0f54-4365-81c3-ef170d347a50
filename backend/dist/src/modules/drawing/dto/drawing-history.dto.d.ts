import { AnimalCategory } from '@/common/constants/animal-types';
export declare class DrawingHistoryQueryDto {
    page?: number;
    limit?: number;
    animalType?: AnimalCategory;
}
export declare class DrawingHistoryItemDto {
    id: string;
    originalImageUrl: string;
    avatarImageUrl: string;
    animalType: AnimalCategory;
    animalName: string;
    analysisResult: string;
    generationMethod: string;
    confidence: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class DrawingHistoryResponseDto {
    items: DrawingHistoryItemDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
