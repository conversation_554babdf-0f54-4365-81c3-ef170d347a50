import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class GenerateAvatarDto {
    imageData: string;
    animalType: AnimalCategory;
    animalSpecies?: AnimalSpecies;
    analysisId?: string;
}
export declare class GenerateAvatarResponseDto {
    success: boolean;
    avatar: {
        avatarId: string;
        animalType: AnimalCategory;
        animalSpecies: AnimalSpecies;
        imageUrl: string;
        imageBase64: string;
        style: string;
        features: {
            fusion: string;
            quality: string;
            type: string;
        };
        stats: {
            workEfficiency: number;
            happiness: number;
            energy: number;
            creativity: number;
        };
    };
    generationMethod: string;
    processingTime: number;
    message: string;
    timestamp: Date;
}
