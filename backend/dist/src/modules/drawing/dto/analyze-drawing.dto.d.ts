import { AnimalCategory } from '@/common/constants/animal-types';
export declare class AnalyzeDrawingDto {
    imageData: string;
    preferredAnimalType?: AnimalCategory;
}
export declare class AnalyzeDrawingResponseDto {
    success: boolean;
    analysisId: string;
    detectedAnimalType: AnimalCategory;
    analysisDescription: string;
    confidence: number;
    faceFeatures: {
        faceShape: string;
        expression: string;
        style: string;
        complexity: string;
    };
    recommendedAnimal: {
        species: string;
        name: string;
        description: string;
        color: string;
    };
    timestamp: Date;
}
