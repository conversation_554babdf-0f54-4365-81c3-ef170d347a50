import { User } from '@/modules/user/entities/user.entity';
import { DrawingAnalysis } from './drawing-analysis.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class GeneratedAvatar {
    id: string;
    userId: string;
    user: User;
    analysisId?: string;
    analysis?: DrawingAnalysis;
    animalType: AnimalCategory;
    animalSpecies: AnimalSpecies;
    avatarImageData: string;
    avatarImageUrl?: string;
    style: string;
    features: {
        fusion: string;
        quality: string;
        type: string;
    };
    stats: {
        workEfficiency: number;
        happiness: number;
        energy: number;
        creativity: number;
    };
    generationMethod: string;
    generationPrompt?: string;
    processingTime?: number;
    metadata?: {
        modelVersion?: string;
        temperature?: number;
        guidanceScale?: number;
        steps?: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
