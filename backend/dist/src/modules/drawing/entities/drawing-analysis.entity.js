"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DrawingAnalysis = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const animal_types_1 = require("../../../common/constants/animal-types");
let DrawingAnalysis = class DrawingAnalysis {
};
exports.DrawingAnalysis = DrawingAnalysis;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], DrawingAnalysis.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { comment: '原始绘画图像base64数据' }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "originalImageData", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true, comment: '原始图像存储URL' }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "originalImageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { comment: 'AI分析结果详细描述' }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "analysisResult", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalCategory,
        comment: '检测到的动物类型',
    }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "animalType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalSpecies,
        nullable: true,
        comment: '具体动物种类',
    }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "animalSpecies", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '面部特征分析JSON数据' }),
    __metadata("design:type", Object)
], DrawingAnalysis.prototype, "faceFeatures", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 5, scale: 2, comment: '置信度评分 0-100' }),
    __metadata("design:type", Number)
], DrawingAnalysis.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '像素分析数据' }),
    __metadata("design:type", Object)
], DrawingAnalysis.prototype, "pixelAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, default: 'gemini_analysis', comment: '分析方法' }),
    __metadata("design:type", String)
], DrawingAnalysis.prototype, "analysisMethod", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { nullable: true, comment: '处理时间（毫秒）' }),
    __metadata("design:type", Number)
], DrawingAnalysis.prototype, "processingTime", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], DrawingAnalysis.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], DrawingAnalysis.prototype, "updatedAt", void 0);
exports.DrawingAnalysis = DrawingAnalysis = __decorate([
    (0, typeorm_1.Entity)('drawing_analyses'),
    (0, typeorm_1.Index)(['userId', 'createdAt']),
    (0, typeorm_1.Index)(['animalType', 'createdAt'])
], DrawingAnalysis);
//# sourceMappingURL=drawing-analysis.entity.js.map