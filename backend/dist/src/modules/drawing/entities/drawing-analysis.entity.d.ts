import { User } from '@/modules/user/entities/user.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class DrawingAnalysis {
    id: string;
    userId: string;
    user: User;
    originalImageData: string;
    originalImageUrl?: string;
    analysisResult: string;
    animalType: AnimalCategory;
    animalSpecies?: AnimalSpecies;
    faceFeatures: {
        faceShape: string;
        expression: string;
        style: string;
        complexity: string;
        lineCount?: number;
        darkRatio?: number;
    };
    confidence: number;
    pixelAnalysis?: {
        width: number;
        height: number;
        aspectRatio: number;
        lineComplexity: number;
        dominantColors: string[];
    };
    analysisMethod: string;
    processingTime?: number;
    createdAt: Date;
    updatedAt: Date;
}
