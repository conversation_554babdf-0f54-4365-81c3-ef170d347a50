"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneratedAvatar = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const drawing_analysis_entity_1 = require("./drawing-analysis.entity");
const animal_types_1 = require("../../../common/constants/animal-types");
let GeneratedAvatar = class GeneratedAvatar {
};
exports.GeneratedAvatar = GeneratedAvatar;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], GeneratedAvatar.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'analysis_id', nullable: true }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "analysisId", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => drawing_analysis_entity_1.DrawingAnalysis, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'analysis_id' }),
    __metadata("design:type", drawing_analysis_entity_1.DrawingAnalysis)
], GeneratedAvatar.prototype, "analysis", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalCategory,
        comment: '动物类型',
    }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "animalType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalSpecies,
        comment: '具体动物种类',
    }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "animalSpecies", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { comment: '生成的头像图像base64数据' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "avatarImageData", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, nullable: true, comment: '头像图像存储URL' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "avatarImageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, default: 'sketch', comment: '头像风格' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "style", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '头像特性配置' }),
    __metadata("design:type", Object)
], GeneratedAvatar.prototype, "features", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { comment: '生成的打工人属性' }),
    __metadata("design:type", Object)
], GeneratedAvatar.prototype, "stats", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, comment: '生成方法' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "generationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true, comment: '生成过程描述' }),
    __metadata("design:type", String)
], GeneratedAvatar.prototype, "generationPrompt", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { nullable: true, comment: '生成时间（毫秒）' }),
    __metadata("design:type", Number)
], GeneratedAvatar.prototype, "processingTime", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true, comment: '额外的生成参数' }),
    __metadata("design:type", Object)
], GeneratedAvatar.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], GeneratedAvatar.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], GeneratedAvatar.prototype, "updatedAt", void 0);
exports.GeneratedAvatar = GeneratedAvatar = __decorate([
    (0, typeorm_1.Entity)('generated_avatars'),
    (0, typeorm_1.Index)(['userId', 'createdAt']),
    (0, typeorm_1.Index)(['animalType', 'createdAt'])
], GeneratedAvatar);
//# sourceMappingURL=generated-avatar.entity.js.map