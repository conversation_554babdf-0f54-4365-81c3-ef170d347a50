"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DrawingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const drawing_controller_1 = require("./drawing.controller");
const drawing_service_1 = require("./drawing.service");
const ai_fusion_service_1 = require("./services/ai-fusion.service");
const enhanced_fusion_generator_service_1 = require("./services/enhanced-fusion-generator.service");
const optimized_fusion_generator_service_1 = require("./services/optimized-fusion-generator.service");
const drawing_analysis_entity_1 = require("./entities/drawing-analysis.entity");
const generated_avatar_entity_1 = require("./entities/generated-avatar.entity");
const ai_module_1 = require("../ai/ai.module");
let DrawingModule = class DrawingModule {
};
exports.DrawingModule = DrawingModule;
exports.DrawingModule = DrawingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([drawing_analysis_entity_1.DrawingAnalysis, generated_avatar_entity_1.GeneratedAvatar]),
            config_1.ConfigModule,
            ai_module_1.AIModule,
        ],
        controllers: [drawing_controller_1.DrawingController, drawing_controller_1.AvatarController, drawing_controller_1.GenerateAvatarController],
        providers: [
            drawing_service_1.DrawingService,
            ai_fusion_service_1.AIFusionService,
            enhanced_fusion_generator_service_1.EnhancedFusionGeneratorService,
            optimized_fusion_generator_service_1.OptimizedFusionGeneratorService
        ],
        exports: [
            drawing_service_1.DrawingService,
            ai_fusion_service_1.AIFusionService,
            enhanced_fusion_generator_service_1.EnhancedFusionGeneratorService,
            optimized_fusion_generator_service_1.OptimizedFusionGeneratorService
        ],
    })
], DrawingModule);
//# sourceMappingURL=drawing.module.js.map