import { DrawingService } from './drawing.service';
import { AIFusionService, AIFusionResult } from './services/ai-fusion.service';
import { AnalyzeDrawingDto, AnalyzeDrawingResponseDto, GenerateAvatarDto, GenerateAvatarResponseDto, DrawingHistoryQueryDto, DrawingHistoryResponseDto } from './dto';
import { User } from '@/modules/user/entities/user.entity';
export declare class DrawingController {
    private readonly drawingService;
    private readonly aiFusionService;
    constructor(drawingService: DrawingService, aiFusionService: AIFusionService);
    analyzeDrawing(user: User, analyzeDrawingDto: AnalyzeDrawingDto): Promise<AnalyzeDrawingResponseDto>;
    generateAvatar(user: User, generateAvatarDto: GenerateAvatarDto): Promise<GenerateAvatarResponseDto>;
    getDrawingHistory(user: User, query: DrawingHistoryQueryDto): Promise<DrawingHistoryResponseDto>;
    getAnalysisById(user: User, analysisId: string): Promise<import("./entities").DrawingAnalysis>;
    getAvatarById(user: User, avatarId: string): Promise<import("./entities").GeneratedAvatar>;
    aiFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<AIFusionResult>;
    analyzeDrawingAI(user: User, analyzeDto: {
        drawingData: string;
    }): Promise<{
        success: boolean;
        analysis: import("./services/ai-fusion.service").DrawingAnalysis;
        message: string;
    }>;
    enhancedFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
    }>;
    optimizedFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
    }>;
    geminiFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
        processingTime: number;
    }>;
    testSmall(user: User, testDto: any): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
            };
        };
        message: string;
    }>;
}
export declare class AvatarController {
    private readonly drawingService;
    private readonly aiFusionService;
    constructor(drawingService: DrawingService, aiFusionService: AIFusionService);
    aiFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<AIFusionResult>;
    analyzeDrawing(user: User, analyzeDto: {
        drawingData: string;
    }): Promise<import("./services/ai-fusion.service").DrawingAnalysis>;
    enhancedFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
    }>;
    optimizedFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
    }>;
    geminiFusion(user: User, fusionDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        analysisData?: any;
    }): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            filePath?: string;
            prompt?: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
        processingTime: number;
    }>;
    testSmall(testDto: any): Promise<{
        success: boolean;
        avatar: {
            avatarId: string;
            animalType: string;
            generationMethod: string;
            imageUrl: string;
            fusionDetails: {
                method: string;
                animalType: string;
                style: string;
            };
        };
        message: string;
    }>;
}
export declare class GenerateAvatarController {
    private readonly drawingService;
    private readonly aiFusionService;
    constructor(drawingService: DrawingService, aiFusionService: AIFusionService);
    generateAvatar(user: User, generateDto: {
        imageData: string;
        animalType: 'OXHORSE' | 'PET' | 'DIVINE';
        userId?: string;
    }): Promise<{
        success: boolean;
        avatarUrl: string;
        avatarData: {
            id: string;
            animalType: "PET" | "OXHORSE" | "DIVINE";
            avatarUrl: string;
            userId: string;
            createdAt: string;
            features: {
                method: string;
                animalType: string;
                style: string;
                model?: string;
            };
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        message: any;
        avatarUrl?: undefined;
        avatarData?: undefined;
    }>;
}
