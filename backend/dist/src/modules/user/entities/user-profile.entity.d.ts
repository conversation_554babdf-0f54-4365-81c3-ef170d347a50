import { User } from './user.entity';
export declare class UserProfile {
    id: string;
    points: number;
    level: number;
    totalLikesReceived: number;
    totalLikesGiven: number;
    totalCommentsReceived: number;
    totalCommentsGiven: number;
    totalFeedsReceived: number;
    totalFeedsGiven: number;
    totalPostsPublished: number;
    achievements?: {
        badgeId: string;
        badgeName: string;
        description: string;
        unlockedAt: Date;
    }[];
    settings?: {
        privacy: {
            profileVisibility: 'public' | 'friends' | 'private';
            allowMessages: boolean;
            allowFriendRequests: boolean;
        };
        notifications: {
            email: boolean;
            push: boolean;
            likes: boolean;
            comments: boolean;
            feeds: boolean;
            system: boolean;
        };
    };
    createdAt: Date;
    updatedAt: Date;
    user: User;
}
