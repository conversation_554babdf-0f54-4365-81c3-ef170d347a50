import { UserTestResult } from '../../test/entities/user-test-result.entity';
import { UserProfile } from './user-profile.entity';
import { Interaction } from '../../social/entities/interaction.entity';
import { Comment } from '../../social/entities/comment.entity';
export declare enum UserStatus {
    ACTIVE = 1,
    DISABLED = 2,
    DELETED = 3
}
export declare enum AuthProvider {
    LOCAL = "local",
    GOOGLE = "google",
    WECHAT = "wechat"
}
export declare class User {
    id: string;
    username: string;
    email?: string;
    phone?: string;
    passwordHash: string;
    avatarUrl?: string;
    nickname?: string;
    bio?: string;
    status: UserStatus;
    authProvider: AuthProvider;
    providerId?: string;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    profile?: UserProfile;
    testResults?: UserTestResult[];
    interactions?: Interaction[];
    receivedInteractions?: Interaction[];
    comments?: Comment[];
}
