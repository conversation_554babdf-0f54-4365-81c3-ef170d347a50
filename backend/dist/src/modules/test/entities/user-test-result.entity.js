"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTestResult = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../user/entities/user.entity");
const animal_types_1 = require("../../../common/constants/animal-types");
let UserTestResult = class UserTestResult {
};
exports.UserTestResult = UserTestResult;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserTestResult.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], UserTestResult.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalCategory,
    }),
    __metadata("design:type", String)
], UserTestResult.prototype, "animalType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: animal_types_1.AnimalSpecies,
    }),
    __metadata("design:type", String)
], UserTestResult.prototype, "animalSpecies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Array)
], UserTestResult.prototype, "testAnswers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], UserTestResult.prototype, "personalityScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], UserTestResult.prototype, "animalAttributes", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 10, default: 'v1.0' }),
    __metadata("design:type", String)
], UserTestResult.prototype, "testVersion", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], UserTestResult.prototype, "personalityDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], UserTestResult.prototype, "recommendations", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], UserTestResult.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.testResults, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], UserTestResult.prototype, "user", void 0);
exports.UserTestResult = UserTestResult = __decorate([
    (0, typeorm_1.Entity)('user_test_results'),
    (0, typeorm_1.Index)(['userId', 'createdAt'])
], UserTestResult);
//# sourceMappingURL=user-test-result.entity.js.map