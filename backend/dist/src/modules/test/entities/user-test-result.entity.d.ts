import { User } from '../../user/entities/user.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';
export declare class UserTestResult {
    id: string;
    userId: string;
    animalType: AnimalCategory;
    animalSpecies: AnimalSpecies;
    testAnswers: {
        questionId: string;
        answer: number | string;
        timeSpent?: number;
    }[];
    personalityScore: {
        workload: number;
        stress: number;
        creativity: number;
        leadership: number;
        socialSkill: number;
        patience: number;
    };
    animalAttributes?: {
        wisdom?: number;
        calmness?: number;
        leadership?: number;
        endurance?: number;
        loyalty?: number;
        optimism?: number;
        sociability?: number;
        independence?: number;
        elegance?: number;
        reliability?: number;
        speed?: number;
        agility?: number;
        stubbornness?: number;
    };
    testVersion: string;
    personalityDescription?: string;
    recommendations?: {
        career: string[];
        skills: string[];
        improvement: string[];
    };
    createdAt: Date;
    user: User;
}
