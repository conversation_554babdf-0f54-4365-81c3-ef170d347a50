import { User } from '../../src/modules/user/entities/user.entity';
import { UserProfile } from '../../src/modules/user/entities/user-profile.entity';
import { RegisterDto } from '../../src/modules/auth/dto/register.dto';
import { LoginDto } from '../../src/modules/auth/dto/login.dto';
export declare class TestDataFactory {
    static createUser(override?: Partial<User>): User;
    static createUserProfile(userId: string, override?: Partial<UserProfile>): UserProfile;
    static createRegisterDto(override?: Partial<RegisterDto>): RegisterDto;
    static createLoginDto(override?: Partial<LoginDto>): LoginDto;
    static createTestAnswers(): Array<{
        questionId: number;
        answerId: number;
    }>;
    static createComplaintContent(): {
        title: string;
        content: string;
        tags: ("加班" | "摸鱼" | "PUA" | "996" | "内卷")[];
        isAnonymous: boolean;
    };
    static createComment(): {
        content: string;
        parentId: any;
    };
    static createUsers(count: number): User[];
    static createJwtToken(payload?: any): string;
    static cleanup(): void;
}
