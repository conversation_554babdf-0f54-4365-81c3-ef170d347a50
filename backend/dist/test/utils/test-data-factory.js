"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestDataFactory = void 0;
const faker_1 = require("@faker-js/faker");
const user_entity_1 = require("../../src/modules/user/entities/user.entity");
const user_profile_entity_1 = require("../../src/modules/user/entities/user-profile.entity");
class TestDataFactory {
    static createUser(override) {
        const user = new user_entity_1.User();
        user.id = faker_1.faker.datatype.uuid();
        user.username = faker_1.faker.internet.userName();
        user.email = faker_1.faker.internet.email();
        user.passwordHash = faker_1.faker.internet.password();
        user.isActive = true;
        user.isVerified = false;
        user.createdAt = new Date();
        user.updatedAt = new Date();
        return Object.assign(user, override);
    }
    static createUserProfile(userId, override) {
        const profile = new user_profile_entity_1.UserProfile();
        profile.id = faker_1.faker.datatype.uuid();
        profile.userId = userId;
        profile.nickname = faker_1.faker.name.firstName();
        profile.avatar = faker_1.faker.image.avatar();
        profile.bio = faker_1.faker.lorem.sentence();
        profile.animalType = faker_1.faker.helpers.arrayElement(['神兽', '宠物', '牛马']);
        profile.animalSubtype = faker_1.faker.animal.type();
        profile.testScore = faker_1.faker.datatype.number({ min: 0, max: 100 });
        profile.createdAt = new Date();
        profile.updatedAt = new Date();
        return Object.assign(profile, override);
    }
    static createRegisterDto(override) {
        return {
            username: faker_1.faker.internet.userName(),
            email: faker_1.faker.internet.email(),
            password: 'Test123456!',
            confirmPassword: 'Test123456!',
            ...override,
        };
    }
    static createLoginDto(override) {
        return {
            email: faker_1.faker.internet.email(),
            password: 'Test123456!',
            ...override,
        };
    }
    static createTestAnswers() {
        const answers = [];
        for (let i = 1; i <= 20; i++) {
            answers.push({
                questionId: i,
                answerId: faker_1.faker.number.int({ min: 1, max: 4 }),
            });
        }
        return answers;
    }
    static createComplaintContent() {
        return {
            title: faker_1.faker.lorem.sentence(),
            content: faker_1.faker.lorem.paragraphs(2),
            tags: faker_1.faker.helpers.arrayElements(['加班', '摸鱼', 'PUA', '996', '内卷'], 2),
            isAnonymous: faker_1.faker.datatype.boolean(),
        };
    }
    static createComment() {
        return {
            content: faker_1.faker.lorem.sentence(),
            parentId: null,
        };
    }
    static createUsers(count) {
        return Array.from({ length: count }, () => this.createUser());
    }
    static createJwtToken(payload = {}) {
        const defaultPayload = {
            sub: faker_1.faker.string.uuid(),
            email: faker_1.faker.internet.email(),
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 3600,
        };
        return `mock.jwt.token.${Buffer.from(JSON.stringify({ ...defaultPayload, ...payload })).toString('base64')}`;
    }
    static cleanup() {
        faker_1.faker.seed(Date.now());
    }
}
exports.TestDataFactory = TestDataFactory;
//# sourceMappingURL=test-data-factory.js.map