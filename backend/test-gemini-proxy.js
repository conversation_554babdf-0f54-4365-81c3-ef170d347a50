/**
 * 使用代理测试 Gemini API
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');

// 设置 Node.js undici 的代理
const { setGlobalDispatcher, ProxyAgent } = require('undici');

async function testWithProxy() {
    console.log('🔧 配置 undici 代理...');
    
    // 设置 undici 全局代理
    const proxyAgent = new ProxyAgent('http://127.0.0.1:7890');
    setGlobalDispatcher(proxyAgent);
    
    console.log('✅ 代理已配置: http://127.0.0.1:7890');
    console.log('🔑 API Key:', process.env.GEMINI_API_KEY ? '已设置' : '未设置');
    
    if (!process.env.GEMINI_API_KEY) {
        console.error('❌ GEMINI_API_KEY 未设置');
        return;
    }
    
    try {
        console.log('📡 初始化 GoogleGenerativeAI...');
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        console.log('🤖 获取 gemini-1.5-flash 模型...');
        const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
        
        console.log('💭 发送测试提示...');
        const result = await model.generateContent("Hello! Please respond with 'Gemini is working!'");
        
        console.log('📤 获取响应...');
        const response = result.response;
        const text = response.text();
        
        console.log('✅ 成功! 响应:', text);
        
        // 测试图像模型
        console.log('\n🖼️ 测试 Gemini 2.0 Flash Preview Image Generation 模型...');
        try {
            const imageModel = genAI.getGenerativeModel({ 
                model: "gemini-2.0-flash-preview-image-generation",
                generationConfig: {
                    responseModalities: ["TEXT", "IMAGE"] // 指定需要文本和图像响应
                }
            });
            
            const imageResult = await imageModel.generateContent("Generate a professional business portrait of a person wearing a suit, looking confident and competent. Please provide both a description and the actual image.");
            const response = imageResult.response;
            
            console.log('📸 图像模型响应结构:');
            console.log('- 候选数量:', response.candidates?.length || 0);
            
            if (response.candidates && response.candidates[0]) {
                const candidate = response.candidates[0];
                
                if (candidate.content && candidate.content.parts) {
                    let foundImage = false;
                    candidate.content.parts.forEach((part, index) => {
                        console.log(`- 部分 ${index + 1}:`, {
                            hasText: !!part.text,
                            hasInlineData: !!part.inlineData,
                            mimeType: part.inlineData?.mimeType || 'none'
                        });
                        
                        if (part.inlineData && part.inlineData.mimeType?.includes('image')) {
                            foundImage = true;
                            console.log('✅ 找到图像数据!');
                        }
                    });
                    
                    if (!foundImage) {
                        console.log('⚠️ 未找到图像数据');
                        if (candidate.content.parts[0]?.text) {
                            console.log('📝 返回的文本:', candidate.content.parts[0].text.substring(0, 200) + '...');
                        }
                    }
                }
            }
            
        } catch (imageError) {
            console.error('❌ 图像模型测试失败:', imageError.message);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 错误:', error);
        console.error('错误消息:', error.message);
        
        return false;
    }
}

// 直接执行
testWithProxy();