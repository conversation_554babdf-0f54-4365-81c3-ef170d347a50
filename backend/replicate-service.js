// 使用 Replicate API 进行真实图像生成
const express = require('express');
const cors = require('cors');
const axios = require('axios');
const app = express();

app.use(cors());
app.use(express.json({ limit: '50mb' }));

// Replicate API 配置
const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const REPLICATE_API_URL = 'https://api.replicate.com/v1/predictions';

// 健康检查
app.get('/api/v1/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'Replicate Image Generation Service',
        apiConfigured: !!REPLICATE_API_TOKEN
    });
});

// 真实的图像生成接口
app.post('/api/v1/avatar/generate-real', async (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    if (!REPLICATE_API_TOKEN) {
        return res.status(500).json({
            success: false,
            error: 'Replicate API token not configured',
            message: '请设置 REPLICATE_API_TOKEN 环境变量'
        });
    }
    
    try {
        // 1. 根据动物类型构建提示词
        const animalHeads = {
            'DIVINE': 'dragon, phoenix, unicorn',
            'PET': 'cat, dog, rabbit',
            'OXHORSE': 'ox, horse, donkey'
        };
        
        const animal = animalHeads[animalType] || 'ox';
        
        // 2. 构建融合提示词（英文效果更好）
        const prompt = `
            pencil sketch drawing, black and white,
            office worker with ${animal} head,
            human body wearing business suit,
            standing pose, full body portrait,
            sketch art style, hand drawn illustration,
            clean lines, professional character design
        `.trim();
        
        const negativePrompt = `
            photo, realistic, color, 3d render,
            multiple heads, distorted, blurry,
            bad anatomy, extra limbs
        `.trim();
        
        // 3. 创建 Replicate 预测任务
        const createResponse = await axios.post(
            REPLICATE_API_URL,
            {
                version: "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b", // SDXL
                input: {
                    prompt: prompt,
                    negative_prompt: negativePrompt,
                    width: 512,
                    height: 768,
                    num_outputs: 1,
                    scheduler: "K_EULER",
                    num_inference_steps: 30,
                    guidance_scale: 7.5,
                    prompt_strength: 0.8,
                    refine: "expert_ensemble_refiner",
                    high_noise_frac: 0.8
                }
            },
            {
                headers: {
                    'Authorization': `Token ${REPLICATE_API_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const predictionId = createResponse.data.id;
        console.log('创建预测任务:', predictionId);
        
        // 4. 轮询等待结果
        let prediction = createResponse.data;
        while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const statusResponse = await axios.get(
                `${REPLICATE_API_URL}/${predictionId}`,
                {
                    headers: {
                        'Authorization': `Token ${REPLICATE_API_TOKEN}`
                    }
                }
            );
            
            prediction = statusResponse.data;
            console.log('预测状态:', prediction.status);
        }
        
        if (prediction.status === 'failed') {
            throw new Error('图像生成失败: ' + prediction.error);
        }
        
        // 5. 获取生成的图像
        const generatedImageUrl = prediction.output[0];
        
        // 6. 下载图像并转换为 base64
        const imageResponse = await axios.get(generatedImageUrl, {
            responseType: 'arraybuffer'
        });
        
        const base64Image = Buffer.from(imageResponse.data, 'binary').toString('base64');
        const dataUrl = `data:image/png;base64,${base64Image}`;
        
        // 7. 返回结果
        res.json({
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: animalType,
                animalHead: animal,
                imageUrl: dataUrl,
                originalUrl: generatedImageUrl,
                prompt: prompt,
                features: {
                    style: 'pencil_sketch',
                    type: 'human_animal_hybrid'
                },
                stats: generateWorkerStats()
            },
            message: '真实 AI 图像生成成功！'
        });
        
    } catch (error) {
        console.error('Replicate API 错误:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.response?.data
        });
    }
});

// 使用用户绘画作为参考的图像生成
app.post('/api/v1/avatar/generate-with-reference', async (req, res) => {
    const { imageData, animalType } = req.body;
    
    if (!REPLICATE_API_TOKEN) {
        return res.status(500).json({
            success: false,
            error: 'Replicate API token not configured'
        });
    }
    
    try {
        // 提取 base64 图像数据
        const base64Image = imageData.split(',')[1] || imageData;
        
        // 使用 img2img 模型进行图像到图像的转换
        const createResponse = await axios.post(
            REPLICATE_API_URL,
            {
                version: "15a3689ee13b0d2616e98820eca31d4c3abcd36672df6afce5cb6feb1d66087d", // stable-diffusion-img2img
                input: {
                    image: `data:image/png;base64,${base64Image}`,
                    prompt: `pencil sketch of office worker with animal head, ${animalType} style`,
                    negative_prompt: "photo, realistic, color",
                    strength: 0.7, // 保留原图特征的程度
                    guidance_scale: 7.5,
                    scheduler: "K_EULER",
                    num_inference_steps: 30
                }
            },
            {
                headers: {
                    'Authorization': `Token ${REPLICATE_API_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // 等待结果...
        // (同上面的轮询逻辑)
        
    } catch (error) {
        console.error('图像转换失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

function generateWorkerStats() {
    return {
        workEfficiency: Math.floor(Math.random() * 40) + 60,
        happiness: Math.floor(Math.random() * 40) + 40,
        energy: Math.floor(Math.random() * 40) + 50,
        creativity: Math.floor(Math.random() * 40) + 45
    };
}

const PORT = process.env.PORT || 3003;
app.listen(PORT, () => {
    console.log(`✅ Replicate 图像生成服务运行在 http://localhost:${PORT}`);
    if (!REPLICATE_API_TOKEN) {
        console.log(`⚠️  请设置 REPLICATE_API_TOKEN 环境变量:`);
        console.log(`   export REPLICATE_API_TOKEN=your_token_here`);
        console.log(`   获取 token: https://replicate.com/account/api-tokens`);
    }
});