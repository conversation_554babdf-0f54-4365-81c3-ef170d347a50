# 牛马动物园 - Frontend Implementation Guide

## 1. Project Structure

```
src/
├── components/
│   ├── Zoo/
│   │   ├── ZooCanvas.tsx          # Main zoo container
│   │   ├── ZoneRenderer.tsx       # Individual zone rendering
│   │   ├── AnimalSprite.tsx       # Animal sprite component
│   │   ├── EnvironmentLayer.tsx   # Background and decorations
│   │   └── PathNetwork.tsx        # Pathway system
│   ├── UI/
│   │   ├── ZooMiniMap.tsx         # Mini-map component
│   │   ├── AnimalInfoPanel.tsx    # Animal details sidebar
│   │   ├── ZooControls.tsx        # Zoom and camera controls
│   │   └── NotificationSystem.tsx # System messages
│   └── shared/
│       ├── SpriteAnimation.tsx    # Reusable animation system
│       └── CollisionDetector.tsx  # Collision and boundaries
├── assets/
│   ├── sprites/
│   │   ├── animals/               # Animal sprite sheets
│   │   ├── environments/          # Background textures
│   │   ├── decorations/           # Environmental objects
│   │   └── ui/                    # Interface elements
│   ├── sounds/                    # Audio assets
│   └── data/
│       ├── zones.json             # Zone configurations
│       ├── paths.json             # Pathway definitions
│       └── spawns.json            # Animal spawn points
├── hooks/
│   ├── useZooState.ts             # Zoo state management
│   ├── useAnimalMovement.ts       # Animal behavior logic
│   └── useViewport.ts             # Camera and zoom handling
├── utils/
│   ├── coordinates.ts             # Coordinate system utilities
│   ├── collision.ts               # Collision detection
│   ├── pathfinding.ts             # Animal movement paths
│   └── performance.ts             # Optimization utilities
└── types/
    ├── zoo.ts                     # Zoo-related type definitions
    ├── animals.ts                 # Animal type definitions
    └── ui.ts                      # UI component types
```

## 2. Core Component Implementation

### 2.1 ZooCanvas.tsx - Main Container
```tsx
import React, { useEffect, useRef, useState } from 'react';
import { useZooState } from '../hooks/useZooState';
import { useViewport } from '../hooks/useViewport';
import { ZoneRenderer } from './ZoneRenderer';
import { AnimalSprite } from './AnimalSprite';
import { ZONES, CANVAS_CONFIG } from '../utils/constants';

interface ZooCanvasProps {
  className?: string;
}

export const ZooCanvas: React.FC<ZooCanvasProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { animals, zones, loading } = useZooState();
  const { viewport, zoom, handlePan, handleZoom } = useViewport();
  
  const [hoveredAnimal, setHoveredAnimal] = useState<string | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = CANVAS_CONFIG.WIDTH;
    canvas.height = CANVAS_CONFIG.HEIGHT;

    // Render loop
    const render = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Apply viewport transform
      ctx.save();
      ctx.translate(-viewport.x, -viewport.y);
      ctx.scale(zoom, zoom);
      
      // Render zones (background first)
      zones.forEach(zone => {
        renderZone(ctx, zone);
      });
      
      // Render animals
      animals.forEach(animal => {
        renderAnimal(ctx, animal, hoveredAnimal === animal.id);
      });
      
      ctx.restore();
    };

    const animationId = requestAnimationFrame(render);
    return () => cancelAnimationFrame(animationId);
  }, [animals, zones, viewport, zoom, hoveredAnimal]);

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const canvasX = (e.clientX - rect.left + viewport.x) / zoom;
    const canvasY = (e.clientY - rect.top + viewport.y) / zoom;
    
    // Find hovered animal
    const hovered = animals.find(animal => 
      isPointInSprite(canvasX, canvasY, animal)
    );
    
    setHoveredAnimal(hovered?.id || null);
  };

  const handleClick = (e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const canvasX = (e.clientX - rect.left + viewport.x) / zoom;
    const canvasY = (e.clientY - rect.top + viewport.y) / zoom;
    
    const clickedAnimal = animals.find(animal => 
      isPointInSprite(canvasX, canvasY, animal)
    );
    
    if (clickedAnimal) {
      onAnimalClick(clickedAnimal);
    }
  };

  return (
    <div 
      ref={containerRef}
      className={`zoo-canvas-container ${className}`}
      style={{ position: 'relative', overflow: 'hidden' }}
    >
      <canvas
        ref={canvasRef}
        className="zoo-canvas"
        style={{ 
          cursor: hoveredAnimal ? 'pointer' : 'grab',
          width: '100%',
          height: '100%',
          imageRendering: 'pixelated' // For crisp pixel art
        }}
        onMouseMove={handleMouseMove}
        onMouseDown={handlePan}
        onWheel={handleZoom}
        onClick={handleClick}
      />
      
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner">Loading Zoo...</div>
        </div>
      )}
    </div>
  );
};
```

### 2.2 Animal Movement System
```tsx
// hooks/useAnimalMovement.ts
import { useCallback, useEffect, useState } from 'react';
import { Animal, Position, MovementState } from '../types/animals';
import { findPath, getRandomDestination } from '../utils/pathfinding';
import { ZONES, ANIMAL_CONFIG } from '../utils/constants';

export const useAnimalMovement = (animals: Animal[]) => {
  const [movementStates, setMovementStates] = useState<Map<string, MovementState>>(
    new Map()
  );

  const updateAnimalPosition = useCallback((animalId: string, position: Position) => {
    setMovementStates(prev => {
      const newMap = new Map(prev);
      const state = newMap.get(animalId);
      if (state) {
        newMap.set(animalId, { ...state, position });
      }
      return newMap;
    });
  }, []);

  const setAnimalDestination = useCallback((animalId: string, destination: Position) => {
    const animal = animals.find(a => a.id === animalId);
    if (!animal) return;

    const currentState = movementStates.get(animalId);
    if (!currentState) return;

    const path = findPath(currentState.position, destination, animal.species);
    
    setMovementStates(prev => {
      const newMap = new Map(prev);
      newMap.set(animalId, {
        ...currentState,
        destination,
        path,
        isMoving: true,
        pathIndex: 0
      });
      return newMap;
    });
  }, [animals, movementStates]);

  // AI behavior system
  const updateAnimalBehavior = useCallback((animal: Animal) => {
    const state = movementStates.get(animal.id);
    if (!state) return;

    const config = ANIMAL_CONFIG[animal.species];
    const currentZone = getCurrentZone(state.position);
    
    // Behavior decision making
    const shouldChangeDestination = 
      !state.isMoving || 
      state.stuckTimer > 300 || // 5 seconds stuck
      Math.random() < 0.01; // 1% chance per frame to change direction

    if (shouldChangeDestination) {
      let preferredZones = [config.preferredZone];
      
      // Add adjacent zones based on animal personality
      if (config.adventurous > 0.5) {
        preferredZones = [...preferredZones, ...getAdjacentZones(currentZone)];
      }
      
      const destination = getRandomDestination(preferredZones, animal.species);
      setAnimalDestination(animal.id, destination);
    }
  }, [movementStates, setAnimalDestination]);

  // Movement update loop
  useEffect(() => {
    const interval = setInterval(() => {
      animals.forEach(animal => {
        const state = movementStates.get(animal.id);
        if (!state || !state.isMoving) return;

        const config = ANIMAL_CONFIG[animal.species];
        const speed = config.baseSpeed * (1 + Math.random() * 0.2); // Speed variation

        // Move along path
        if (state.path && state.pathIndex < state.path.length) {
          const target = state.path[state.pathIndex];
          const current = state.position;
          
          const dx = target.x - current.x;
          const dy = target.y - current.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 2) {
            // Reached waypoint, move to next
            setMovementStates(prev => {
              const newMap = new Map(prev);
              const currentState = newMap.get(animal.id);
              if (currentState) {
                newMap.set(animal.id, {
                  ...currentState,
                  pathIndex: currentState.pathIndex + 1,
                  position: target,
                  stuckTimer: 0
                });
              }
              return newMap;
            });
          } else {
            // Move towards waypoint
            const moveX = (dx / distance) * speed;
            const moveY = (dy / distance) * speed;
            
            updateAnimalPosition(animal.id, {
              x: current.x + moveX,
              y: current.y + moveY
            });
          }
        } else {
          // Reached destination, stop moving
          setMovementStates(prev => {
            const newMap = new Map(prev);
            const currentState = newMap.get(animal.id);
            if (currentState) {
              newMap.set(animal.id, {
                ...currentState,
                isMoving: false,
                idleTimer: Date.now()
              });
            }
            return newMap;
          });
        }

        // Update AI behavior
        updateAnimalBehavior(animal);
      });
    }, 16); // 60 FPS

    return () => clearInterval(interval);
  }, [animals, movementStates, updateAnimalPosition, updateAnimalBehavior]);

  return {
    movementStates,
    setAnimalDestination,
    updateAnimalPosition
  };
};
```

### 2.3 Zone Configuration
```typescript
// utils/constants.ts
export const CANVAS_CONFIG = {
  WIDTH: 1920,
  HEIGHT: 1200,
  TILE_SIZE: 80
};

export const ZONES = {
  ENTRANCE: {
    id: 'entrance',
    name: '入口区',
    bounds: { x: 0, y: 0, width: 320, height: 240 },
    type: 'entrance',
    backgroundColor: '#8B4513',
    decorations: [
      { type: 'gate', x: 160, y: 120, sprite: 'entrance_gate' },
      { type: 'sign', x: 160, y: 60, sprite: 'zoo_sign' }
    ],
    spawnPoints: [
      { x: 160, y: 200 }
    ]
  },
  
  GRASSLAND: {
    id: 'grassland',
    name: '草地区',
    bounds: { x: 320, y: 0, width: 640, height: 480 },
    type: 'habitat',
    backgroundColor: '#90EE90',
    preferredAnimals: ['ox', 'horse', 'donkey', 'sheep', 'goat'],
    decorations: [
      { type: 'hay_bale', x: 400, y: 100, sprite: 'hay_bale_1' },
      { type: 'hay_bale', x: 600, y: 200, sprite: 'hay_bale_2' },
      { type: 'fence', x: 320, y: 240, sprite: 'wooden_fence_h' },
      { type: 'barn', x: 800, y: 50, sprite: 'small_barn' },
      { type: 'flowers', x: 500, y: 300, sprite: 'wildflowers_1' },
      { type: 'flowers', x: 700, y: 150, sprite: 'wildflowers_2' }
    ],
    spawnPoints: [
      { x: 400, y: 240 },
      { x: 600, y: 240 },
      { x: 800, y: 240 }
    ]
  },

  FOREST: {
    id: 'forest',
    name: '森林区',
    bounds: { x: 960, y: 0, width: 640, height: 480 },
    type: 'habitat',
    backgroundColor: '#228B22',
    preferredAnimals: ['golden_retriever', 'persian_cat', 'rabbit', 'hamster', 'parrot'],
    decorations: [
      { type: 'oak_tree', x: 1100, y: 100, sprite: 'oak_tree_large' },
      { type: 'pine_tree', x: 1300, y: 150, sprite: 'pine_tree_medium' },
      { type: 'tree_house', x: 1200, y: 80, sprite: 'cat_tree_house' },
      { type: 'log', x: 1400, y: 300, sprite: 'hollow_log' },
      { type: 'mushrooms', x: 1050, y: 350, sprite: 'mushroom_cluster' },
      { type: 'bird_perch', x: 1500, y: 100, sprite: 'bird_perch' }
    ],
    spawnPoints: [
      { x: 1100, y: 240 },
      { x: 1300, y: 240 },
      { x: 1500, y: 240 }
    ]
  },

  POND: {
    id: 'pond',
    name: '中央池塘',
    bounds: { x: 800, y: 480, width: 320, height: 320 },
    type: 'water',
    backgroundColor: '#87CEEB',
    decorations: [
      { type: 'water', x: 960, y: 640, sprite: 'pond_water' },
      { type: 'lily_pad', x: 920, y: 600, sprite: 'lily_pad_1' },
      { type: 'lily_pad', x: 980, y: 680, sprite: 'lily_pad_2' },
      { type: 'bridge', x: 960, y: 640, sprite: 'stone_bridge' },
      { type: 'bench', x: 850, y: 750, sprite: 'park_bench' }
    ],
    spawnPoints: [
      { x: 850, y: 640 },
      { x: 1070, y: 640 }
    ]
  },

  DESERT: {
    id: 'desert',
    name: '沙漠区',
    bounds: { x: 0, y: 800, width: 480, height: 400 },
    type: 'habitat',
    backgroundColor: '#F4A460',
    preferredAnimals: ['buffalo'], // Special desert-adapted variants
    decorations: [
      { type: 'cactus', x: 100, y: 900, sprite: 'large_cactus' },
      { type: 'cactus', x: 300, y: 950, sprite: 'small_cactus' },
      { type: 'oasis', x: 240, y: 1100, sprite: 'desert_oasis' },
      { type: 'rock', x: 400, y: 850, sprite: 'desert_rock' },
      { type: 'dune', x: 200, y: 850, sprite: 'sand_dune' }
    ],
    spawnPoints: [
      { x: 240, y: 1000 }
    ]
  },

  MOUNTAIN: {
    id: 'mountain',
    name: '山地区',
    bounds: { x: 1440, y: 800, width: 480, height: 400 },
    type: 'mystical',
    backgroundColor: '#9370DB',
    preferredAnimals: ['dragon', 'phoenix', 'qilin_deer', 'unicorn'],
    decorations: [
      { type: 'peak', x: 1680, y: 850, sprite: 'mountain_peak' },
      { type: 'crystal', x: 1500, y: 950, sprite: 'glowing_crystal' },
      { type: 'ruins', x: 1600, y: 1000, sprite: 'ancient_ruins' },
      { type: 'platform', x: 1750, y: 900, sprite: 'floating_platform' },
      { type: 'mist', x: 1680, y: 1100, sprite: 'mystical_mist' }
    ],
    spawnPoints: [
      { x: 1600, y: 1000 },
      { x: 1750, y: 900 }
    ],
    specialEffects: [
      { type: 'particles', x: 1680, y: 900, effect: 'golden_sparkles' },
      { type: 'aura', x: 1680, y: 900, effect: 'mystical_glow' }
    ]
  }
};

export const PATHWAYS = [
  // Main circular path
  { from: 'entrance', to: 'grassland', points: [{ x: 320, y: 120 }, { x: 400, y: 120 }] },
  { from: 'grassland', to: 'forest', points: [{ x: 960, y: 120 }, { x: 1100, y: 120 }] },
  { from: 'forest', to: 'pond', points: [{ x: 1120, y: 480 }, { x: 960, y: 500 }] },
  { from: 'pond', to: 'mountain', points: [{ x: 1120, y: 640 }, { x: 1440, y: 900 }] },
  { from: 'mountain', to: 'desert', points: [{ x: 1440, y: 1000 }, { x: 480, y: 1000 }] },
  { from: 'desert', to: 'entrance', points: [{ x: 240, y: 800 }, { x: 160, y: 240 }] }
];
```

## 3. Animation System

### 3.1 Sprite Animation Component
```tsx
// components/shared/SpriteAnimation.tsx
import React, { useEffect, useRef, useState } from 'react';

interface SpriteFrame {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface SpriteAnimationProps {
  spriteSheet: HTMLImageElement;
  frames: SpriteFrame[];
  frameRate: number;
  loop?: boolean;
  playing?: boolean;
  onComplete?: () => void;
  position: { x: number; y: number };
  scale?: number;
  flipX?: boolean;
}

export const SpriteAnimation: React.FC<SpriteAnimationProps> = ({
  spriteSheet,
  frames,
  frameRate,
  loop = true,
  playing = true,
  onComplete,
  position,
  scale = 1,
  flipX = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [lastFrameTime, setLastFrameTime] = useState(Date.now());

  useEffect(() => {
    if (!playing) return;

    const animate = () => {
      const now = Date.now();
      const elapsed = now - lastFrameTime;
      const frameInterval = 1000 / frameRate;

      if (elapsed >= frameInterval) {
        setCurrentFrame(prev => {
          const nextFrame = prev + 1;
          
          if (nextFrame >= frames.length) {
            if (loop) {
              return 0;
            } else {
              onComplete?.();
              return prev;
            }
          }
          
          return nextFrame;
        });
        setLastFrameTime(now);
      }

      requestAnimationFrame(animate);
    };

    const animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [playing, frameRate, frames.length, loop, onComplete, lastFrameTime]);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || !spriteSheet) return;

    const frame = frames[currentFrame];
    if (!frame) return;

    canvas.width = frame.width * scale;
    canvas.height = frame.height * scale;

    ctx.save();
    
    if (flipX) {
      ctx.scale(-1, 1);
      ctx.translate(-canvas.width, 0);
    }

    ctx.drawImage(
      spriteSheet,
      frame.x, frame.y, frame.width, frame.height,
      0, 0, canvas.width, canvas.height
    );
    
    ctx.restore();
  }, [spriteSheet, frames, currentFrame, scale, flipX]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        left: position.x,
        top: position.y,
        pointerEvents: 'none',
        imageRendering: 'pixelated'
      }}
    />
  );
};
```

### 3.2 Performance Optimization
```typescript
// utils/performance.ts
export class PerformanceManager {
  private static instance: PerformanceManager;
  private visibleAnimals: Set<string> = new Set();
  private frameCount = 0;
  private lastFPSUpdate = Date.now();
  private currentFPS = 60;

  static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  updateVisibleAnimals(viewport: Viewport, animals: Animal[]): string[] {
    this.visibleAnimals.clear();
    
    const buffer = 100; // Extra space around viewport
    const visibleBounds = {
      left: viewport.x - buffer,
      right: viewport.x + viewport.width + buffer,
      top: viewport.y - buffer,
      bottom: viewport.y + viewport.height + buffer
    };

    return animals
      .filter(animal => {
        const inBounds = animal.position.x >= visibleBounds.left &&
                        animal.position.x <= visibleBounds.right &&
                        animal.position.y >= visibleBounds.top &&
                        animal.position.y <= visibleBounds.bottom;
        
        if (inBounds) {
          this.visibleAnimals.add(animal.id);
        }
        
        return inBounds;
      })
      .map(animal => animal.id);
  }

  shouldUseDetailedAnimation(animalId: string, distanceFromCenter: number): boolean {
    // Use detailed animations for close animals, simple for distant ones
    const isVisible = this.visibleAnimals.has(animalId);
    const isClose = distanceFromCenter < 200;
    const hasGoodFPS = this.currentFPS > 45;

    return isVisible && (isClose || hasGoodFPS);
  }

  updateFPS(): void {
    this.frameCount++;
    const now = Date.now();
    
    if (now - this.lastFPSUpdate >= 1000) {
      this.currentFPS = this.frameCount;
      this.frameCount = 0;
      this.lastFPSUpdate = now;
    }
  }

  getOptimalAnimalCount(): number {
    if (this.currentFPS > 55) return 100;
    if (this.currentFPS > 45) return 75;
    if (this.currentFPS > 35) return 50;
    return 30;
  }
}
```

## 4. Asset Loading and Management

### 4.1 Asset Loader
```typescript
// utils/assetLoader.ts
export class AssetLoader {
  private static instance: AssetLoader;
  private loadedImages: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();

  static getInstance(): AssetLoader {
    if (!AssetLoader.instance) {
      AssetLoader.instance = new AssetLoader();
    }
    return AssetLoader.instance;
  }

  async loadImage(path: string): Promise<HTMLImageElement> {
    // Return cached image if available
    if (this.loadedImages.has(path)) {
      return this.loadedImages.get(path)!;
    }

    // Return existing loading promise if already loading
    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path)!;
    }

    // Create new loading promise
    const loadingPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.loadedImages.set(path, img);
        this.loadingPromises.delete(path);
        resolve(img);
      };
      img.onerror = () => {
        this.loadingPromises.delete(path);
        reject(new Error(`Failed to load image: ${path}`));
      };
      img.src = path;
    });

    this.loadingPromises.set(path, loadingPromise);
    return loadingPromise;
  }

  async preloadAssets(assetList: string[]): Promise<void> {
    const loadPromises = assetList.map(path => this.loadImage(path));
    await Promise.all(loadPromises);
  }

  getImage(path: string): HTMLImageElement | null {
    return this.loadedImages.get(path) || null;
  }
}

// Asset manifest
export const ASSET_MANIFEST = {
  backgrounds: [
    '/assets/sprites/environments/grassland_bg.png',
    '/assets/sprites/environments/forest_bg.png',
    '/assets/sprites/environments/desert_bg.png',
    '/assets/sprites/environments/mountain_bg.png',
    '/assets/sprites/environments/pond_bg.png'
  ],
  animals: [
    '/assets/sprites/animals/ox_spritesheet.png',
    '/assets/sprites/animals/horse_spritesheet.png',
    '/assets/sprites/animals/dragon_spritesheet.png',
    '/assets/sprites/animals/cat_spritesheet.png',
    '/assets/sprites/animals/dog_spritesheet.png'
  ],
  decorations: [
    '/assets/sprites/decorations/trees.png',
    '/assets/sprites/decorations/rocks.png',
    '/assets/sprites/decorations/buildings.png'
  ],
  effects: [
    '/assets/sprites/effects/sparkles.png',
    '/assets/sprites/effects/dust.png',
    '/assets/sprites/effects/water_ripples.png'
  ]
};
```

## 5. Responsive Design Implementation

### 5.1 Responsive Canvas Container
```tsx
// hooks/useResponsiveCanvas.ts
import { useEffect, useState } from 'react';

interface ResponsiveConfig {
  canvasWidth: number;
  canvasHeight: number;
  scale: number;
  uiScale: number;
}

export const useResponsiveCanvas = () => {
  const [config, setConfig] = useState<ResponsiveConfig>({
    canvasWidth: 1920,
    canvasHeight: 1200,
    scale: 1,
    uiScale: 1
  });

  useEffect(() => {
    const updateSize = () => {
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      
      // Calculate optimal scale to fit screen
      const scaleX = windowWidth / 1920;
      const scaleY = windowHeight / 1200;
      const optimalScale = Math.min(scaleX, scaleY);
      
      // Ensure minimum readable size
      const finalScale = Math.max(optimalScale, 0.5);
      
      // UI scaling for mobile
      const isMobile = windowWidth < 768;
      const uiScale = isMobile ? 1.2 : 1;

      setConfig({
        canvasWidth: Math.min(windowWidth / finalScale, 1920),
        canvasHeight: Math.min(windowHeight / finalScale, 1200),
        scale: finalScale,
        uiScale
      });
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return config;
};
```

This implementation guide provides a comprehensive framework for building the 牛马动物园 with proper performance optimization, responsive design, and maintainable code structure. The system is designed to handle 50+ simultaneous animals while maintaining smooth 60fps performance across different devices.