# 真实图像生成方案

## 重要说明

**Gemini 2.0 Flash 是一个多模态大语言模型，不是图像生成模型。** 它可以：
- ✅ 理解和分析图像
- ✅ 描述图像内容
- ✅ 提供图像相关的文字输出
- ❌ 不能生成新的图像
- ❌ 不能编辑或融合图像

## 真实的图像生成解决方案

### 方案1：使用 Stable Diffusion API

```javascript
// 使用 Replicate API 调用 Stable Diffusion
const Replicate = require("replicate");

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

async function generateFusionImage(userDrawing, animalType) {
  const prompt = `
    A pencil sketch of an office worker with ${animalType} head,
    human body in business suit, standing pose,
    sketch art style, black and white drawing,
    professional illustration
  `;
  
  const output = await replicate.run(
    "stability-ai/stable-diffusion:db21e45d",
    {
      input: {
        prompt: prompt,
        image: userDrawing, // 用户自画像作为参考
        strength: 0.5, // 融合强度
        num_outputs: 1,
        guidance_scale: 7.5
      }
    }
  );
  
  return output[0];
}
```

### 方案2：使用 OpenAI DALL-E 3

```javascript
const OpenAI = require("openai");

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function generateWithDallE(description, animalType) {
  const response = await openai.images.generate({
    model: "dall-e-3",
    prompt: `
      创建一幅素描风格的插画：
      一个办公室打工人，但是头部是${animalType}的头，
      身体是人类，穿着职业装，
      整体风格为铅笔素描，黑白线条，
      全身像，站立姿态
    `,
    n: 1,
    size: "1024x1024",
    quality: "standard",
    style: "natural"
  });
  
  return response.data[0].url;
}
```

### 方案3：使用 Midjourney API

```javascript
const { Midjourney } = require("midjourney");

const client = new Midjourney({
  ServerId: process.env.MJ_SERVER_ID,
  ChannelId: process.env.MJ_CHANNEL_ID,
  SalaiToken: process.env.MJ_SALAI_TOKEN,
});

async function generateWithMidjourney(userDrawing, animalType) {
  const prompt = `
    ${userDrawing} as reference,
    office worker with ${animalType} head,
    human body, business suit,
    pencil sketch style,
    --ar 4:5 --stylize 50
  `;
  
  const result = await client.Imagine(prompt);
  return result.uri;
}
```

### 方案4：使用本地 Canvas 合成（不需要 AI）

```javascript
const { createCanvas, loadImage } = require('canvas');

async function createHybridAvatar(userDrawing, animalType) {
  const canvas = createCanvas(512, 640);
  const ctx = canvas.getContext('2d');
  
  // 加载用户绘画
  const userImg = await loadImage(userDrawing);
  
  // 加载预制的动物头部素材
  const animalHeads = {
    'ox': '/assets/heads/ox_sketch.png',
    'cat': '/assets/heads/cat_sketch.png',
    'dragon': '/assets/heads/dragon_sketch.png'
  };
  
  const animalHead = await loadImage(animalHeads[animalType]);
  
  // 1. 绘制身体（从用户画作提取）
  ctx.drawImage(userImg, 0, 200, 512, 440, 0, 200, 512, 440);
  
  // 2. 应用素描滤镜
  applySketchFilter(ctx);
  
  // 3. 绘制动物头部
  ctx.globalCompositeOperation = 'source-over';
  ctx.drawImage(animalHead, 100, 0, 312, 250);
  
  // 4. 添加融合效果
  blendEdges(ctx, 200);
  
  return canvas.toDataURL();
}

function applySketchFilter(ctx) {
  const imageData = ctx.getImageData(0, 0, 512, 640);
  const data = imageData.data;
  
  // 转换为灰度
  for (let i = 0; i < data.length; i += 4) {
    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
    data[i] = gray;
    data[i + 1] = gray;
    data[i + 2] = gray;
  }
  
  // 边缘检测
  // ... Sobel 算子实现
  
  ctx.putImageData(imageData, 0, 0);
}
```

### 方案5：使用 ComfyUI API（推荐）

```javascript
const axios = require('axios');
const FormData = require('form-data');

class ComfyUIClient {
  constructor(serverUrl) {
    this.serverUrl = serverUrl || 'http://localhost:8188';
  }
  
  async generateFusionImage(userDrawing, animalType) {
    // 1. 上传用户绘画
    const uploadResponse = await this.uploadImage(userDrawing);
    
    // 2. 构建工作流
    const workflow = {
      "1": {
        "class_type": "LoadImage",
        "inputs": {
          "image": uploadResponse.name
        }
      },
      "2": {
        "class_type": "ControlNetPreprocessor",
        "inputs": {
          "image": ["1", 0],
          "preprocessor": "sketch"
        }
      },
      "3": {
        "class_type": "TextPrompt",
        "inputs": {
          "text": `office worker with ${animalType} head, pencil sketch style`
        }
      },
      "4": {
        "class_type": "StableDiffusionImg2Img",
        "inputs": {
          "image": ["2", 0],
          "prompt": ["3", 0],
          "strength": 0.6,
          "steps": 30
        }
      },
      "5": {
        "class_type": "SaveImage",
        "inputs": {
          "images": ["4", 0],
          "filename_prefix": "fusion_avatar"
        }
      }
    };
    
    // 3. 执行工作流
    const result = await this.executeWorkflow(workflow);
    return result.images[0];
  }
  
  async uploadImage(base64Image) {
    const formData = new FormData();
    const buffer = Buffer.from(base64Image.split(',')[1], 'base64');
    formData.append('image', buffer, 'drawing.png');
    
    const response = await axios.post(
      `${this.serverUrl}/upload/image`,
      formData,
      { headers: formData.getHeaders() }
    );
    
    return response.data;
  }
  
  async executeWorkflow(workflow) {
    const response = await axios.post(
      `${this.serverUrl}/prompt`,
      { prompt: workflow }
    );
    
    // 等待生成完成
    const promptId = response.data.prompt_id;
    return await this.waitForResult(promptId);
  }
  
  async waitForResult(promptId) {
    // 轮询等待结果
    while (true) {
      const response = await axios.get(
        `${this.serverUrl}/history/${promptId}`
      );
      
      if (response.data[promptId]?.outputs) {
        return response.data[promptId].outputs;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}
```

## 推荐实施方案

### 最佳选择：ComfyUI + Stable Diffusion

1. **安装 ComfyUI**
```bash
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI
pip install -r requirements.txt

# 下载 Stable Diffusion 模型
wget -P models/checkpoints/ https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned.safetensors
```

2. **启动 ComfyUI 服务**
```bash
python main.py --listen 0.0.0.0 --port 8188
```

3. **集成到项目**
```javascript
// backend/comfyui-service.js
const express = require('express');
const app = express();
const comfyUI = new ComfyUIClient('http://localhost:8188');

app.post('/api/v1/avatar/generate', async (req, res) => {
  const { userDrawing, animalType } = req.body;
  
  try {
    // 使用 ComfyUI 生成融合图像
    const fusionImage = await comfyUI.generateFusionImage(
      userDrawing,
      animalType
    );
    
    res.json({
      success: true,
      imageUrl: fusionImage,
      message: '人身动物头像生成成功！'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
```

### 次选方案：Replicate API

如果不想本地部署，可以使用 Replicate 的云端 API：

1. **注册获取 API Token**
   - 访问 https://replicate.com
   - 注册并获取 API token

2. **安装 SDK**
```bash
npm install replicate
```

3. **实现生成服务**
```javascript
const Replicate = require("replicate");

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

async function generateAvatar(userDrawing, animalType) {
  // 使用 SDXL 模型
  const output = await replicate.run(
    "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
    {
      input: {
        prompt: `pencil sketch of office worker with ${animalType} head, human body in suit`,
        negative_prompt: "photo, realistic, color",
        image: userDrawing,
        prompt_strength: 0.6,
        num_outputs: 1,
        guidance_scale: 7.5,
        scheduler: "K_EULER",
        num_inference_steps: 30
      }
    }
  );
  
  return output[0];
}
```

## 实施步骤

1. **选择方案**
   - 本地部署：ComfyUI（免费但需要 GPU）
   - 云端 API：Replicate 或 OpenAI（按次收费）
   - 简单合成：Canvas 本地处理（免费但效果有限）

2. **配置环境**
```bash
# 创建环境变量文件
cat > .env << EOF
# 选择一个
REPLICATE_API_TOKEN=your_token
OPENAI_API_KEY=your_key
COMFYUI_URL=http://localhost:8188
EOF
```

3. **更新前端调用**
```javascript
// 修改 index.html 中的 API 调用
async function generateFusionAvatar() {
  const response = await fetch('/api/v1/avatar/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      userDrawing: canvas.toDataURL(),
      animalType: currentAnimalData.animalType,
      prompt: '素描风格，人身动物头打工人'
    })
  });
  
  const result = await response.json();
  if (result.success) {
    displayGeneratedAvatar(result.imageUrl);
  }
}
```

## 总结

Gemini 2.0 Flash **不能生成图像**，它是语言模型。真正的图像生成需要使用：
1. **Stable Diffusion** - 开源，效果好
2. **DALL-E 3** - 质量高，但收费
3. **Midjourney** - 艺术效果好，但需要订阅
4. **ComfyUI** - 灵活强大，适合复杂工作流

推荐使用 ComfyUI + Stable Diffusion 的组合，可以实现真正的图像融合生成。