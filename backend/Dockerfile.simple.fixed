# 简单Express服务器的Dockerfile
FROM node:18-alpine AS base

# 安装Canvas依赖和系统工具
RUN apk add --no-cache \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    python3 \
    make \
    g++ \
    curl

WORKDIR /app

# 复制package文件并安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制源码
COPY . .

# 创建uploads目录
RUN mkdir -p uploads/generated-avatars

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S backend -u 1001 -G nodejs

# 设置文件权限
RUN chown -R backend:nodejs /app
USER backend

EXPOSE 3000

# 启动命令
CMD ["node", "simple-server.js"]