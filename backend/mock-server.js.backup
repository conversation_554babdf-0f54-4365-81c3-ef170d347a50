const express = require('express');
const cors = require('cors');
const { generateFusionAvatar } = require('./enhanced-fusion-generator');
const { generateOptimizedFusion } = require('./optimized-fusion-generator');
const { AIFusionService } = require('./ai-fusion-service');
const app = express();

// 初始化AI融合服务
const aiFusionService = new AIFusionService();

// 中间件
app.use(cors({
  origin: ['http://localhost:3001', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// 静态文件服务 - 为生成的头像提供访问路径
app.use('/uploads', express.static('./uploads'));

// 请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
  next();
});

// 健康检查
app.get('/api/v1/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        service: '牛马动物园 Mock API'
    });
});

// 用户注册 - 兼容前端API路径
app.post('/api/auth/register', (req, res) => {
    res.json({
        data: {
            user: {
                id: '1',
                email: req.body.email,
                username: req.body.username,
                animalType: 'OXHORSE',
                avatar: null
            },
            accessToken: 'mock-jwt-token',
            refreshToken: 'mock-refresh-token'
        },
        success: true,
        message: '注册成功'
    });
});

// 用户登录 - 兼容前端API路径
app.post('/api/auth/login', (req, res) => {
    console.log('用户登录请求:', req.body);
    // Mock环境下任何邮箱密码都可以登录
    res.json({
        data: {
            user: {
                id: '1',
                email: req.body.email || '<EMAIL>',
                username: req.body.username || 'testuser',
                animalType: 'OXHORSE',
                avatar: null
            },
            accessToken: 'mock-jwt-token',
            refreshToken: 'mock-refresh-token'
        },
        success: true,
        message: '登录成功'
    });
});

// 发送验证码 (Mock) - 兼容前端API路径
app.post('/api/auth/send-verification', (req, res) => {
    console.log('发送验证码请求:', req.body);
    // Mock环境下直接返回成功
    res.json({
        success: true,
        message: '验证码发送成功',
        mockCode: '123456' // 开发环境显示验证码
    });
});

// 验证验证码 (Mock) - 兼容前端API路径
app.post('/api/auth/verify-code', (req, res) => {
    console.log('验证码验证请求:', req.body);
    // Mock环境下任何验证码都通过
    res.json({
        success: true,
        message: '验证码验证成功'
    });
});

// 刷新Token (Mock) - 兼容前端API路径
app.post('/api/auth/refresh', (req, res) => {
    console.log('Token刷新请求:', req.body);
    res.json({
        data: {
            accessToken: 'mock-jwt-token-refreshed',
            refreshToken: 'mock-refresh-token-refreshed'
        }
    });
});

// 打工人测试问题
app.get('/api/v1/test/questions', (req, res) => {
    res.json({
        questions: [
            {
                id: '1',
                text: '你每周平均加班多少小时？',
                options: [
                    { value: 'A', text: '从不加班', score: 10 },
                    { value: 'B', text: '0-10小时', score: 7 },
                    { value: 'C', text: '10-20小时', score: 4 },
                    { value: 'D', text: '20小时以上', score: 1 }
                ]
            },
            {
                id: '2',
                text: '老板给你画饼的频率？',
                options: [
                    { value: 'A', text: '从不画饼', score: 10 },
                    { value: 'B', text: '偶尔画饼', score: 7 },
                    { value: 'C', text: '经常画饼', score: 4 },
                    { value: 'D', text: '天天画饼', score: 1 }
                ]
            }
        ]
    });
});

// 提交测试结果
app.post('/api/v1/test/submit', (req, res) => {
    const score = req.body.answers.reduce((sum, a) => sum + (a.score || 5), 0);
    let animalType = 'OXHORSE';
    if (score > 15) animalType = 'DIVINE';
    else if (score > 10) animalType = 'PET';
    
    res.json({
        animalType,
        score,
        description: '恭喜你成为了牛马动物园的一员！'
    });
});

// 获取动物园数据
app.get('/api/v1/zoo/animals', (req, res) => {
    res.json({
        animals: [
            { id: '1', type: 'OXHORSE', name: '打工牛马1号', x: 0, y: 0, z: 0 },
            { id: '2', type: 'PET', name: '老板宠物', x: 10, y: 0, z: 10 },
            { id: '3', type: 'DIVINE', name: '传说神兽', x: -10, y: 0, z: -10 }
        ]
    });
});

// AI分析绘画接口
app.post('/api/v1/drawing/analyze', (req, res) => {
    const { imageData, timestamp } = req.body;
    
    // 模拟AI分析延迟
    setTimeout(() => {
        // 基于随机性和一些简单规则分析
        const analysisResults = [
            {
                animalType: 'DIVINE',
                confidence: Math.random() * 0.2 + 0.8, // 80-100%
                analysis: '从你的画作中可以看出强烈的自信和创造力，线条流畅且富有表现力。你很可能是那种能够平衡工作与生活的神兽级打工人！画面构图均衡，色彩运用得当，体现了内心的平静与自信。',
                traits: ['创造力强', '心态平和', '工作效率高', '生活品质好', '艺术天赋']
            },
            {
                animalType: 'PET',
                confidence: Math.random() * 0.2 + 0.7, // 70-90%
                analysis: '你的画作显示出细心和耐心，注重细节但不失活力。这表明你在职场中既有实力又受欢迎，是老板眼中的宠物级员工！画面中的小细节体现了你的用心程度。',
                traits: ['细心负责', '人际关系好', '适应能力强', '工作积极', '善于沟通']
            },
            {
                animalType: 'OXHORSE',
                confidence: Math.random() * 0.25 + 0.6, // 60-85%
                analysis: '你的画作朴实而真诚，虽然简单但表达了真实的自我。这正是牛马级打工人的可贵品质 - 踏实、真诚、任劳任怨！简洁的线条背后是深刻的内心世界。',
                traits: ['踏实可靠', '任劳任怨', '真诚待人', '默默奉献', '内心丰富']
            }
        ];
        
        // 根据图像数据长度和复杂度进行简单判断
        let selectedResult;
        const imageComplexity = imageData.length;
        
        if (imageComplexity > 50000) {
            // 复杂画作更可能是神兽或宠物
            selectedResult = Math.random() > 0.3 ? analysisResults[0] : analysisResults[1];
        } else if (imageComplexity > 20000) {
            // 中等复杂度，随机选择
            selectedResult = analysisResults[Math.floor(Math.random() * 3)];
        } else {
            // 简单画作更可能是牛马，但也有例外
            selectedResult = Math.random() > 0.2 ? analysisResults[2] : analysisResults[1];
        }
        
        // 添加一些随机性增强真实感
        selectedResult.confidence = Math.round(selectedResult.confidence * 100) / 100;
        
        // 保存画作数据（模拟存储）
        console.log('收到新画作，分析结果:', selectedResult.animalType, '置信度:', Math.round(selectedResult.confidence * 100) + '%');
        
        res.json({
            ...selectedResult,
            drawingId: Date.now().toString(),
            timestamp: timestamp || new Date().toISOString(),
            message: '画作分析完成！'
        });
    }, 1500 + Math.random() * 1000); // 1.5-2.5秒分析时间
});

// 绘画分析接口 - 使用真实AI分析
app.post('/api/v1/avatar/analyze-drawing', async (req, res) => {
    const { drawingData } = req.body;
    
    try {
        console.log('🔍 开始AI绘画分析...');
        
        // 使用AI服务分析用户绘画
        const analysisResult = await aiFusionService.analyzeUserDrawing(drawingData);
        
        console.log('✅ AI分析完成:', analysisResult.animalType);
        
        res.json({
            success: true,
            analysis: analysisResult,
            aiService: aiFusionService.getServiceStatus(),
            message: 'AI绘画分析完成'
        });
        
    } catch (error) {
        console.error('❌ AI分析失败:', error);
        
        // 备选方案：基础分析算法
        const fallbackAnalysis = {
            animalType: Math.random() > 0.6 ? 'PET' : (Math.random() > 0.3 ? 'OXHORSE' : 'DIVINE'),
            confidence: 0.75,
            traits: ['focused', 'creative', 'professional'],
            suggestions: {
                bodyPosture: 'standing',
                expression: 'serious',
                style: 'professional'
            }
        };
        
        res.json({
            success: true,
            analysis: fallbackAnalysis,
            aiService: aiFusionService.getServiceStatus(),
            fallback: true,
            message: '使用基础分析算法完成分析'
        });
    }
});

// 增强版人身动物头融合生成接口 - 真正的身体+头部融合
app.post('/api/v1/avatar/enhanced-fusion', async (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    try {
        // 使用增强的融合生成器
        const fusionResult = await generateFusionAvatar(
            imageData,
            animalType,
            analysisData || { expression: 'neutral', style: 'sketch' }
        );
        
        if (fusionResult.success) {
            res.json({
                success: true,
                avatar: {
                    avatarId: Date.now().toString(),
                    animalType: animalType,
                    imageUrl: fusionResult.imageUrl,
                    generationMethod: 'Enhanced Fusion',
                    fusionDetails: fusionResult.fusionDetails,
                    features: fusionResult.fusionDetails.features,
                    timestamp: new Date().toISOString()
                },
                message: '人身动物头融合成功！真正保留了您的身体特征！'
            });
        } else {
            throw new Error('融合生成失败');
        }
    } catch (error) {
        console.error('增强融合生成失败:', error);
        console.error('请求参数:', { imageData: imageData?.length || 0, animalType, analysisData });
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.stack,
            message: '融合生成失败，请重试',
            timestamp: new Date().toISOString()
        });
    }
});

// 优化融合生成接口 - 重绘并融合
app.post('/api/v1/avatar/optimized-fusion', async (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    try {
        console.log('🎨 开始优化融合生成...');
        console.log('动物类型:', animalType);
        
        // 使用优化的融合生成器
        const fusionResult = await generateOptimizedFusion(
            imageData,
            animalType,
            analysisData || {}
        );
        
        if (fusionResult.success) {
            res.json({
                success: true,
                avatar: {
                    avatarId: Date.now().toString(),
                    animalType: animalType,
                    imageUrl: fusionResult.imageUrl,
                    generationMethod: 'Optimized Fusion',
                    fusionDetails: fusionResult.fusionDetails,
                    timestamp: new Date().toISOString()
                },
                message: '优化融合成功！身体重绘并融合动物特征！'
            });
        } else {
            throw new Error('优化融合失败');
        }
    } catch (error) {
        console.error('优化融合生成失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: '优化融合失败，请重试'
        });
    }
});

// AI驱动的融合生成接口 - 使用Gemini/DALL-E3
app.post('/api/v1/avatar/ai-fusion', async (req, res) => {
    handleAIFusion(req, res);
});

// 支持双重API路径的路由
app.post('/api/api/v1/avatar/ai-fusion', async (req, res) => {
    handleAIFusion(req, res);
});

// 测试端点：发送小的响应数据
app.post('/api/v1/avatar/test-small', (req, res) => {
    console.log('🧪 测试小响应端点被调用');
    res.json({
        success: true,
        avatar: {
            avatarId: Date.now().toString(),
            animalType: 'OXHORSE',
            generationMethod: 'Test Small Response',
            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // 1x1 透明像素
            fusionDetails: {
                method: 'Test',
                animalType: 'OXHORSE'
            }
        },
        message: '测试响应成功'
    });
});

// AI融合处理函数
async function handleAIFusion(req, res) {
    const { imageData, animalType, analysisData } = req.body;
    
    try {
        console.log('🎨 开始AI驱动的融合生成...', animalType);
        
        // 使用真实AI服务生成融合头像
        const aiResult = await aiFusionService.generateFusionAvatar(
            imageData,
            animalType,
            analysisData || { expression: 'professional', style: 'business' }
        );
        
        if (aiResult.success) {
            console.log('✅ AI融合生成成功:', aiResult.avatar.generationMethod);
            console.log('📷 图像数据长度:', aiResult.avatar.imageUrl?.length || 'undefined');
            console.log('📷 图像数据格式:', aiResult.avatar.imageUrl?.substring(0, 50) || 'undefined');
            
            const response = {
                success: true,
                avatar: aiResult.avatar,
                aiService: aiFusionService.getServiceStatus(),
                generatedAt: new Date().toISOString(),
                message: 'AI融合头像生成成功'
            };
            
            console.log('📤 发送响应给前端:', JSON.stringify(response, null, 2).substring(0, 500));
            console.log('📤 响应数据统计:', {
                responseSize: JSON.stringify(response).length,
                imageUrlLength: response.avatar?.imageUrl?.length || 0,
                timestamp: new Date().toISOString()
            });
            
            // 直接发送真实的图像URL
            console.log('📤 发送AI生成结果给前端');
            res.json(response);
        } else {
            throw new Error('AI生成返回失败状态');
        }
        
    } catch (error) {
        console.error('❌ AI融合生成失败，回退到Canvas融合:', error);
        
        try {
            // 回退到Canvas增强融合
            const canvasResult = await generateFusionAvatar(
                imageData,
                animalType,
                analysisData || { expression: 'professional', style: 'business' }
            );
            
            if (canvasResult.success) {
                console.log('✅ Canvas回退生成成功');
                console.log('📷 Canvas图像格式:', canvasResult.imageUrl?.substring(0, 50) + '...');
                console.log('📷 Canvas图像长度:', canvasResult.imageUrl?.length || 0);
                
                res.json({
                    success: true,
                    avatar: {
                        avatarId: Date.now().toString(),
                        animalType: animalType,
                        generationMethod: 'Canvas Fusion (AI Fallback)',
                        imageUrl: canvasResult.imageUrl,
                        fusionDetails: canvasResult.fusionDetails,
                        aiService: aiFusionService.getServiceStatus(),
                        fallback: true
                    },
                    message: '使用Canvas融合算法生成头像'
                });
            } else {
                throw new Error('Canvas融合也失败');
            }
            
        } catch (fallbackError) {
            console.error('❌ Canvas融合备选方案也失败:', fallbackError);
            res.status(500).json({
                success: false,
                error: 'AI和Canvas融合都失败',
                details: error.message,
                aiService: aiFusionService.getServiceStatus(),
                message: '头像生成失败，请稍后重试'
            });
        }
    }
}

// Gemini 2.0 Flash Preview 人身动物头像生成接口
app.post('/api/v1/avatar/gemini-fusion', (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    // 模拟 Gemini 2.0 Flash 图像生成处理
    setTimeout(() => {
        // 分析用户绘画特征
        const drawingFeatures = analyzeDrawingFeatures(imageData);
        
        // 选择随机动物头像进行融合
        const animalHeads = getRandomAnimalHead(animalType);
        
        // 生成 Gemini 提示词
        const geminiPrompt = generateGeminiPrompt(animalType, drawingFeatures, animalHeads);
        
        // 模拟 Gemini 生成的人身动物头像数据
        const generatedAvatar = {
            avatarId: Date.now().toString(),
            animalType: animalType,
            animalHead: animalHeads.name,
            bodyType: 'human_worker', // 打工人身体
            imageUrl: generateMockAvatarImage(animalType, animalHeads.type),
            prompt: geminiPrompt,
            features: {
                headShape: animalHeads.features.headShape,
                bodyPosture: 'professional_worker',
                clothing: getWorkerClothing(animalType),
                accessories: getWorkerAccessories(animalType),
                expression: drawingFeatures.emotionalTone,
                colors: extractColorsFromDrawing(imageData)
            },
            walkingSprite: generateWalkingSprite(animalType, animalHeads.type),
            personality: inferPersonalityFromDrawing(drawingFeatures),
            stats: {
                workEfficiency: Math.floor(Math.random() * 100) + 1,
                happiness: Math.floor(Math.random() * 100) + 1,
                energy: Math.floor(Math.random() * 100) + 1,
                creativity: Math.floor(Math.random() * 100) + 1
            }
        };
        
        console.log('Gemini 头像生成完成:', animalType, animalHeads.name);
        
        res.json({
            success: true,
            avatar: generatedAvatar,
            message: 'Gemini 人身动物头像生成成功！',
            processingTime: 3000 + Math.random() * 2000
        });
        
    }, 3000 + Math.random() * 2000); // 3-5秒生成时间
});

// AI头像融合接口 - 将用户绘画与动物特征结合（保留原有接口）
app.post('/api/v1/avatar/fusion', (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    // 模拟AI头像融合处理
    setTimeout(() => {
        // 分析用户绘画特征
        const drawingFeatures = analyzeDrawingFeatures(imageData);
        
        // 基于动物类型和绘画特征生成融合头像描述
        const fusionData = {
            animalType: animalType,
            userFeatures: drawingFeatures,
            fusionStyle: generateFusionStyle(animalType, drawingFeatures),
            colors: extractColorsFromDrawing(imageData),
            personality: inferPersonalityFromDrawing(drawingFeatures),
            uniqueElements: generateUniqueElements(drawingFeatures, animalType)
        };
        
        console.log('头像融合完成:', animalType, '特征数:', drawingFeatures.complexity);
        
        res.json({
            success: true,
            fusionId: Date.now().toString(),
            fusionData: fusionData,
            message: '头像融合生成成功！',
            processingTime: 2000 + Math.random() * 1500
        });
        
    }, 2000 + Math.random() * 1500); // 2-3.5秒融合时间
});

// 分析绘画特征
function analyzeDrawingFeatures(imageData) {
    const dataLength = imageData.length;
    
    return {
        complexity: dataLength > 50000 ? 'high' : dataLength > 20000 ? 'medium' : 'low',
        style: Math.random() > 0.5 ? 'detailed' : 'simple',
        expressiveness: Math.random() > 0.3 ? 'expressive' : 'minimal',
        symmetry: Math.random() > 0.6 ? 'symmetric' : 'asymmetric',
        strokeCount: Math.floor(dataLength / 1000) + Math.floor(Math.random() * 20),
        emotionalTone: ['happy', 'serious', 'playful', 'thoughtful'][Math.floor(Math.random() * 4)]
    };
}

// 生成融合风格
function generateFusionStyle(animalType, features) {
    const baseStyles = {
        'DIVINE': {
            elegance: 'high',
            mystical: 'true',
            refinement: 'sophisticated'
        },
        'PET': {
            friendliness: 'high', 
            playfulness: 'medium',
            warmth: 'cozy'
        },
        'OXHORSE': {
            reliability: 'strong',
            earthiness: 'grounded',
            sincerity: 'honest'
        }
    };
    
    const base = baseStyles[animalType] || baseStyles['OXHORSE'];
    
    // 基于用户绘画特征调整风格
    if (features.complexity === 'high') {
        base.detail = 'rich';
        base.sophistication = 'enhanced';
    }
    
    if (features.expressiveness === 'expressive') {
        base.emotion = 'vivid';
        base.character = 'strong';
    }
    
    return base;
}

// 从绘画中提取颜色信息（模拟）
function extractColorsFromDrawing(imageData) {
    // 模拟颜色提取
    const colorPalettes = [
        ['#FF6B6B', '#4ECDC4', '#45B7D1'],
        ['#FFA726', '#AB47BC', '#26C6DA'], 
        ['#66BB6A', '#FFCA28', '#EF5350'],
        ['#8D6E63', '#78909C', '#A1887F']
    ];
    
    return {
        dominant: colorPalettes[Math.floor(Math.random() * colorPalettes.length)],
        accent: '#' + Math.floor(Math.random()*16777215).toString(16),
        mood: ['warm', 'cool', 'neutral'][Math.floor(Math.random() * 3)]
    };
}

// 从绘画推断性格
function inferPersonalityFromDrawing(features) {
    const personalities = [];
    
    if (features.complexity === 'high') personalities.push('细致', '专注');
    if (features.expressiveness === 'expressive') personalities.push('外向', '生动');
    if (features.symmetry === 'symmetric') personalities.push('平衡', '理性');
    if (features.emotionalTone === 'playful') personalities.push('幽默', '轻松');
    
    return personalities.length > 0 ? personalities : ['真实', '自然'];
}

// 生成独特元素
function generateUniqueElements(features, animalType) {
    const elements = [];
    
    // 基于动物类型
    switch (animalType) {
        case 'DIVINE':
            elements.push('神秘光环', '智慧之眼');
            if (features.complexity === 'high') elements.push('符文装饰');
            break;
        case 'PET':
            elements.push('友善表情', '温暖色调');
            if (features.emotionalTone === 'playful') elements.push('顽皮元素');
            break;
        default:
            elements.push('朴实特征', '坚韧气质');
            if (features.strokeCount > 30) elements.push('丰富细节');
    }
    
    // 基于绘画特征
    if (features.expressiveness === 'expressive') {
        elements.push('动态表现');
    }
    
    return elements;
}

// Gemini 相关辅助函数

// 获取随机动物头像
function getRandomAnimalHead(animalType) {
    const animalHeads = {
        'DIVINE': [
            { name: '金龙', type: 'dragon', features: { headShape: 'majestic', power: 'divine' } },
            { name: '凤凰', type: 'phoenix', features: { headShape: 'elegant', power: 'mystical' } },
            { name: '麒麟', type: 'qilin', features: { headShape: 'noble', power: 'wisdom' } },
            { name: '白虎', type: 'tiger', features: { headShape: 'fierce', power: 'strength' } }
        ],
        'PET': [
            { name: '柴犬', type: 'shiba', features: { headShape: 'round', mood: 'happy' } },
            { name: '橘猫', type: 'cat', features: { headShape: 'cute', mood: 'lazy' } },
            { name: '仓鼠', type: 'hamster', features: { headShape: 'small', mood: 'active' } },
            { name: '兔子', type: 'rabbit', features: { headShape: 'soft', mood: 'gentle' } },
            { name: '狐狸', type: 'fox', features: { headShape: 'clever', mood: 'mischievous' } }
        ],
        'OXHORSE': [
            { name: '黄牛', type: 'ox', features: { headShape: 'sturdy', trait: 'hardworking' } },
            { name: '马', type: 'horse', features: { headShape: 'strong', trait: 'enduring' } },
            { name: '驴', type: 'donkey', features: { headShape: 'honest', trait: 'persistent' } },
            { name: '绵羊', type: 'sheep', features: { headShape: 'gentle', trait: 'obedient' } },
            { name: '山羊', type: 'goat', features: { headShape: 'determined', trait: 'stubborn' } }
        ]
    };
    
    const heads = animalHeads[animalType] || animalHeads['OXHORSE'];
    return heads[Math.floor(Math.random() * heads.length)];
}

// 生成 Gemini 提示词 - 使用指定的中文融合提示词
function generateGeminiPrompt(animalType, drawingFeatures, animalHead) {
    // 基础融合提示词
    const basePrompt = `请将自画像和一个动物的头像结合，形成一个人身动物头的打工人形象，做深度的融合，保持素描。`;
    
    // 根据动物类型给出不同的头像要求
    let animalTypePrompt = '';
    switch (animalType) {
        case 'DIVINE':
            animalTypePrompt = `其中，动物类型为神兽（如龙、凤凰、麒麟、白虎等），体现神圣、优雅、智慧的特质。`;
            break;
        case 'PET':
            animalTypePrompt = `其中，动物类型为宠物（如猫、狗、兔子、仓鼠、狐狸等），体现可爱、友善、温暖的特质。`;
            break;
        case 'OXHORSE':
            animalTypePrompt = `其中，动物类型为牲畜（如牛、马、驴、羊等），体现勤劳、踏实、任劳任怨的特质。`;
            break;
        default:
            animalTypePrompt = `其中，动物类型为牲畜，体现勤劳踏实的打工人特质。`;
    }
    
    // 具体动物头像要求
    const specificAnimalPrompt = `动物头像选择：${animalHead.name}。`;
    
    // 打工人形象要求
    const workerPrompt = `打工人身体穿着职业装，体现办公室工作者的形象。`;
    
    // 表情和情绪
    const expressionPrompt = `面部表情体现${drawingFeatures.emotionalTone}的情绪状态。`;
    
    // 艺术风格要求
    const stylePrompt = `整体采用素描风格，线条清晰，深度融合人物自画像特征与动物头部特征，确保融合自然和谐。`;
    
    // 技术要求
    const technicalPrompt = `输出要求：高质量素描，适合作为移动应用头像，白色背景，全身视图，行走姿态。`;
    
    return basePrompt + animalTypePrompt + specificAnimalPrompt + workerPrompt + expressionPrompt + stylePrompt + technicalPrompt;
}

// 生成模拟头像图片URL
function generateMockAvatarImage(animalType, headType) {
    // 生成基于特征的模拟图片数据URL
    const colors = {
        'DIVINE': '#FFD700',  // 金色
        'PET': '#FF69B4',     // 粉色 
        'OXHORSE': '#8B4513'  // 棕色
    };
    
    const color = colors[animalType] || colors['OXHORSE'];
    
    // 生成简单的SVG头像作为占位符
    const svgData = `
        <svg width="100" height="120" xmlns="http://www.w3.org/2000/svg">
            <!-- 身体 -->
            <rect x="25" y="60" width="50" height="60" fill="#4682B4" stroke="#000" stroke-width="2"/>
            <!-- 头部 -->
            <circle cx="50" cy="40" r="20" fill="${color}" stroke="#000" stroke-width="2"/>
            <!-- 眼睛 -->
            <circle cx="45" cy="35" r="3" fill="#000"/>
            <circle cx="55" cy="35" r="3" fill="#000"/>
            <!-- 嘴巴 -->
            <path d="M 40 45 Q 50 50 60 45" stroke="#000" stroke-width="2" fill="none"/>
            <!-- 手臂 -->
            <rect x="5" y="65" width="20" height="8" fill="#FDBCB4"/>
            <rect x="75" y="65" width="20" height="8" fill="#FDBCB4"/>
            <!-- 腿部 -->
            <rect x="30" y="120" width="8" height="20" fill="#000"/>
            <rect x="62" y="120" width="8" height="20" fill="#000"/>
        </svg>
    `;
    
    return `data:image/svg+xml;base64,${Buffer.from(svgData).toString('base64')}`;
}

// 获取打工人服装
function getWorkerClothing(animalType) {
    const clothing = {
        'DIVINE': { type: 'suit', color: '#1a1a1a', style: 'elegant' },
        'PET': { type: 'casual', color: '#ff6b9d', style: 'comfortable' },
        'OXHORSE': { type: 'uniform', color: '#4682b4', style: 'practical' }
    };
    
    return clothing[animalType] || clothing['OXHORSE'];
}

// 获取打工人配件
function getWorkerAccessories(animalType) {
    const accessories = {
        'DIVINE': ['金框眼镜', '名牌手表', '公文包'],
        'PET': ['可爱徽章', '彩色笔', '便当盒'],
        'OXHORSE': ['工作帽', '胸牌', '保温杯']
    };
    
    const items = accessories[animalType] || accessories['OXHORSE'];
    return items.slice(0, Math.floor(Math.random() * items.length) + 1);
}

// 生成行走精灵动画数据
function generateWalkingSprite(animalType, headType) {
    return {
        frames: 4,
        frameWidth: 64,
        frameHeight: 64,
        animationSpeed: 200,
        directions: ['down', 'left', 'right', 'up'],
        spriteSheet: generateMockSpriteSheet(animalType, headType)
    };
}

// 生成模拟精灵图
function generateMockSpriteSheet(animalType, headType) {
    // 返回模拟精灵图数据
    return {
        url: `sprites/${animalType.toLowerCase()}_${headType}_walk.png`,
        mockData: `data:image/svg+xml;base64,${Buffer.from('<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg"><rect width="256" height="256" fill="#f0f0f0"/><text x="128" y="128" text-anchor="middle" font-family="Arial" font-size="20">行走动画</text></svg>').toString('base64')}`
    };
}

// 头像生成接口 - 供前端TestResultPage调用
app.post('/api/generate-avatar', (req, res) => {
    const { imageData, animalType, userId } = req.body;
    
    console.log('收到头像生成请求:', { animalType, userId, imageDataLength: imageData?.length });
    
    // 模拟头像生成延迟
    setTimeout(() => {
        try {
            // 分析用户绘画特征
            const drawingFeatures = analyzeDrawingFeatures(imageData);
            
            // 选择随机动物头像进行融合
            const animalHead = getRandomAnimalHead(animalType);
            
            // 生成头像URL（这里可以调用真实的头像生成服务）
            const avatarUrl = generateMockAvatarImage(animalType, animalHead.type);
            
            // 构建完整的头像数据
            const avatarData = {
                id: Date.now().toString(),
                animalType,
                animalHead: animalHead.name,
                avatarUrl,
                userId,
                createdAt: new Date().toISOString(),
                features: {
                    headShape: animalHead.features.headShape || animalHead.features.trait,
                    bodyType: 'professional_worker',
                    clothing: getWorkerClothing(animalType),
                    accessories: getWorkerAccessories(animalType),
                    expression: drawingFeatures.emotionalTone,
                    colors: extractColorsFromDrawing(imageData)
                },
                walkingSprite: generateWalkingSprite(animalType, animalHead.type),
                personality: inferPersonalityFromDrawing(drawingFeatures),
                stats: {
                    workEfficiency: Math.floor(Math.random() * 100) + 1,
                    happiness: Math.floor(Math.random() * 100) + 1,
                    energy: Math.floor(Math.random() * 100) + 1,
                    creativity: Math.floor(Math.random() * 100) + 1
                }
            };
            
            console.log('✅ 头像生成成功:', animalType, animalHead.name);
            
            res.json({
                success: true,
                avatarUrl: avatarUrl,
                avatarData: avatarData,
                message: '头像生成成功！'
            });
            
        } catch (error) {
            console.error('❌ 头像生成失败:', error);
            res.status(500).json({
                success: false,
                error: '头像生成失败',
                message: error.message
            });
        }
    }, 2000 + Math.random() * 1000); // 2-3秒生成时间
});

// 动物园相关接口
let zooAnimals = [];

// 获取动物园场景
app.get('/api/v1/zoo/scene', (req, res) => {
    res.json({
        success: true,
        scene: {
            id: 'main-zoo',
            name: '牛马动物园',
            width: 1200,
            height: 800,
            backgroundImage: '/images/zoo-background.jpg'
        },
        animals: zooAnimals,
        message: '动物园场景加载成功'
    });
});

// 添加动物到动物园
app.post('/api/v1/zoo/add-animal', (req, res) => {
    const animalData = req.body;
    animalData.id = animalData.id || Date.now().toString();
    animalData.addedAt = new Date().toISOString();
    
    zooAnimals.push(animalData);
    
    res.json({
        success: true,
        animal: animalData,
        totalAnimals: zooAnimals.length,
        message: '动物已成功添加到动物园'
    });
});

// 更新动物位置
app.put('/api/v1/zoo/update-position/:id', (req, res) => {
    const { id } = req.params;
    const { x, y } = req.body;
    
    const animal = zooAnimals.find(a => a.id === id);
    if (animal) {
        animal.x = x;
        animal.y = y;
        animal.lastUpdated = new Date().toISOString();
        
        res.json({
            success: true,
            animal: animal,
            message: '动物位置更新成功'
        });
    } else {
        res.status(404).json({
            success: false,
            message: '动物未找到'
        });
    }
});

const PORT = 3002;
app.listen(PORT, () => {
    console.log('✅ 模拟后端服务运行在 http://localhost:' + PORT);
    console.log('📚 API文档: http://localhost:' + PORT + '/api/docs');
});