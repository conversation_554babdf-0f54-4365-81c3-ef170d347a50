import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
  ParseBoolPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { ApiResponseWrapper } from '@/common/decorators/api-response.decorator';
import { ZooService } from './zoo.service';
import { AnimalBehaviorService } from './services/animal-behavior.service';
import { ZooAISchedulerService } from './services/zoo-ai-scheduler.service';
import {
  ZooSceneQueryDto,
  ZooSceneResponseDto,
  ZooAnimalsQueryDto,
  ZooAnimalsResponseDto,
  AnimalInteractionDto,
  AnimalInteractionResponseDto,
} from './dto';
import {
  LeaderboardQueryDto,
  LeaderboardResponseDto,
  UserRankingDto,
  ScoreUpdateDto,
  UserScoreStatsDto,
} from './dto/leaderboard.dto';
import { User } from '@/modules/user/entities/user.entity';
import { ScoringService } from './services/scoring.service';

@ApiTags('Zoo - 动物园管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/v1/zoo')
export class ZooController {
  constructor(
    private readonly zooService: ZooService,
    private readonly animalBehaviorService: AnimalBehaviorService,
    private readonly zooAISchedulerService: ZooAISchedulerService,
    private readonly scoringService: ScoringService,
  ) {}

  @Get('scene')
  @ApiOperation({
    summary: '获取动物园场景数据',
    description: '获取当前动物园的完整场景信息，包括动物、环境、设施和事件'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '场景数据获取成功',
    type: ZooSceneResponseDto,
  })
  @ApiResponseWrapper(ZooSceneResponseDto)
  async getZooScene(
    @CurrentUser() user: User,
    @Query() query: ZooSceneQueryDto,
  ): Promise<ZooSceneResponseDto> {
    return this.zooService.getZooScene(user.id, query);
  }

  @Get('animals')
  @ApiOperation({
    summary: '获取用户动物列表',
    description: '分页获取动物园中的动物列表，支持按类型和种类筛选'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '动物列表获取成功',
    type: ZooAnimalsResponseDto,
  })
  @ApiResponseWrapper(ZooAnimalsResponseDto)
  async getZooAnimals(
    @CurrentUser() user: User,
    @Query() query: ZooAnimalsQueryDto,
  ): Promise<ZooAnimalsResponseDto> {
    return this.zooService.getZooAnimals(user.id, query);
  }

  @Post('animals/:id/interact')
  @ApiOperation({
    summary: '与动物互动',
    description: '与指定动物进行各种类型的互动，如喂食、抚摸、玩耍等'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '互动成功',
    type: AnimalInteractionResponseDto,
  })
  @ApiResponseWrapper(AnimalInteractionResponseDto)
  async interactWithAnimal(
    @CurrentUser() user: User,
    @Param('id', ParseUUIDPipe) animalId: string,
    @Body() interactionDto: AnimalInteractionDto,
  ): Promise<AnimalInteractionResponseDto> {
    return this.zooService.interactWithAnimal(user.id, animalId, interactionDto);
  }

  @Get('animals/:id')
  @ApiOperation({
    summary: '获取动物详情',
    description: '获取指定动物的详细信息'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '动物详情获取成功',
  })
  async getAnimalDetails(
    @CurrentUser() user: User,
    @Param('id', ParseUUIDPipe) animalId: string,
  ) {
    // 这里可以调用服务方法获取动物详情
    // 暂时返回基础信息
    return { message: '动物详情接口，待实现具体逻辑' };
  }

  @Post('animals/create-from-avatar/:avatarId')
  @ApiOperation({
    summary: '从头像创建动物园动物',
    description: '基于生成的头像在动物园中创建对应的动物'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '动物创建成功',
  })
  async createAnimalFromAvatar(
    @CurrentUser() user: User,
    @Param('avatarId', ParseUUIDPipe) avatarId: string,
    @Body() createDto?: { name?: string },
  ) {
    return this.zooService.createZooAnimalFromAvatar(
      user.id,
      avatarId,
      createDto?.name
    );
  }

  // ===== AI 行为系统相关端点 =====

  @Get('environment')
  @ApiOperation({
    summary: '获取动物园环境状态',
    description: '获取当前动物园的环境信息，包括天气、时间、事件等'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '环境状态获取成功',
  })
  async getEnvironmentState() {
    return this.zooAISchedulerService.getEnvironmentState();
  }

  @Get('statistics')
  @ApiOperation({
    summary: '获取动物园统计信息',
    description: '获取动物园的综合统计信息，包括动物数量、活跃度等'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '统计信息获取成功',
  })
  async getZooStatistics() {
    return this.zooAISchedulerService.getZooStatistics();
  }

  @Get('animals/:id/behavior')
  @ApiOperation({
    summary: '获取动物行为状态',
    description: '获取指定动物当前的行为状态'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '动物行为状态获取成功',
  })
  async getAnimalBehavior(
    @Param('id', ParseUUIDPipe) animalId: string,
  ) {
    return this.animalBehaviorService.getAnimalBehaviorState(animalId);
  }

  @Post('animals/:id/behavior/stop')
  @ApiOperation({
    summary: '停止动物当前行为',
    description: '强制停止指定动物的当前行为'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '动物行为已停止',
  })
  async stopAnimalBehavior(
    @Param('id', ParseUUIDPipe) animalId: string,
  ) {
    await this.animalBehaviorService.stopAnimalBehavior(animalId);
    return { message: '动物行为已停止' };
  }

  @Post('events/trigger')
  @ApiOperation({
    summary: '触发动物园事件',
    description: '手动触发一个动物园范围的事件'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '事件触发成功',
  })
  async triggerEvent(
    @Body() eventDto: { type: string; duration?: number },
  ) {
    const success = await this.zooAISchedulerService.triggerManualEvent(
      eventDto.type,
      eventDto.duration
    );
    
    return {
      success,
      message: success ? '事件触发成功' : '事件触发失败'
    };
  }

  @Put('ai/scheduler')
  @ApiOperation({
    summary: '控制AI调度器状态',
    description: '启动或暂停动物园AI行为调度器'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'AI调度器状态更新成功',
  })
  async controlScheduler(
    @Body() controlDto: { enabled: boolean },
  ) {
    this.zooAISchedulerService.setSchedulerState(controlDto.enabled);
    return {
      message: `AI调度器已${controlDto.enabled ? '启动' : '暂停'}`
    };
  }

  @Get('debug/behavior-patterns')
  @ApiOperation({
    summary: '获取行为模式调试信息',
    description: '开发用：获取所有动物的行为模式定义'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '行为模式信息获取成功',
  })
  async getBehaviorPatterns() {
    // 这里可以返回行为模式的调试信息
    return {
      message: '行为模式调试信息',
      patterns: {
        dragon: '神兽行为模式：威严巡视、工作指导、冥想修炼、祝福仪式',
        cat: '宠物行为模式：愉快玩耍、舒适休息、友好社交、优雅漫步',
        ox: '工作动物行为模式：勤奋工作、疲惫休息、协作互动、稳重移动'
      }
    };
  }

  // ===== 积分和排行榜系统端点 =====

  @Get('leaderboard')
  @ApiOperation({
    summary: '获取排行榜',
    description: '获取打工人积分排行榜，支持不同时间周期的排名'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '排行榜数据获取成功',
    type: LeaderboardResponseDto,
  })
  @ApiResponseWrapper(LeaderboardResponseDto)
  async getLeaderboard(
    @Query() query: LeaderboardQueryDto,
  ): Promise<LeaderboardResponseDto> {
    const { leaderboard, total } = await this.scoringService.getLeaderboard(
      query.period,
      query.limit,
      query.offset,
    );

    const totalPages = Math.ceil(total / query.limit!);
    const currentPage = Math.floor(query.offset! / query.limit!) + 1;

    return {
      leaderboard,
      total,
      page: currentPage,
      limit: query.limit!,
      totalPages,
      period: query.period!,
      updatedAt: new Date(),
    };
  }

  @Get('my-ranking')
  @ApiOperation({
    summary: '获取我的排名信息',
    description: '获取当前用户在各个时间周期的排名情况'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '排名信息获取成功',
    type: UserRankingDto,
  })
  @ApiResponseWrapper(UserRankingDto)
  async getMyRanking(
    @CurrentUser() user: User,
  ): Promise<UserRankingDto> {
    return this.scoringService.getUserRanking(user.id);
  }

  @Get('my-score-stats')
  @ApiOperation({
    summary: '获取我的积分统计',
    description: '获取当前用户的详细积分统计信息，包括各类积分、等级、徽章等'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '积分统计获取成功',
    type: UserScoreStatsDto,
  })
  @ApiResponseWrapper(UserScoreStatsDto)
  async getMyScoreStats(
    @CurrentUser() user: User,
  ): Promise<UserScoreStatsDto> {
    // 获取用户总积分记录
    const totalScore = await this.scoringService['userScoreRepository'].findOne({
      where: { userId: user.id, scoreType: 'TOTAL' },
    });

    if (!totalScore) {
      // 如果没有积分记录，创建一个初始记录
      const initialScore = await this.scoringService.updateUserScore(user.id, 'social', 0, 0);
      
      const rankings = await this.scoringService.getUserRanking(user.id);

      return {
        userId: user.id,
        totalScore: initialScore.totalScore,
        level: initialScore.level,
        experience: initialScore.experience,
        experienceToNextLevel: initialScore.experienceToNextLevel,
        title: initialScore.title,
        reputation: initialScore.reputation,
        categoryScores: {
          interactionScore: initialScore.interactionScore,
          workPerformanceScore: initialScore.workPerformanceScore,
          socialScore: initialScore.socialScore,
          achievementScore: initialScore.achievementScore,
          creativityScore: initialScore.creativityScore,
        },
        rankings: {
          globalRank: rankings.globalRank,
          dailyRank: rankings.dailyRank,
          weeklyRank: rankings.weeklyRank,
          monthlyRank: rankings.monthlyRank,
        },
        badges: initialScore.badges,
        streaks: initialScore.streaks,
        lastUpdatedAt: initialScore.lastUpdatedAt,
      };
    }

    const rankings = await this.scoringService.getUserRanking(user.id);

    return {
      userId: user.id,
      totalScore: totalScore.totalScore,
      level: totalScore.level,
      experience: totalScore.experience,
      experienceToNextLevel: totalScore.experienceToNextLevel,
      title: totalScore.title,
      reputation: totalScore.reputation,
      categoryScores: {
        interactionScore: totalScore.interactionScore,
        workPerformanceScore: totalScore.workPerformanceScore,
        socialScore: totalScore.socialScore,
        achievementScore: totalScore.achievementScore,
        creativityScore: totalScore.creativityScore,
      },
      rankings: {
        globalRank: rankings.globalRank,
        dailyRank: rankings.dailyRank,
        weeklyRank: rankings.weeklyRank,
        monthlyRank: rankings.monthlyRank,
      },
      badges: totalScore.badges,
      streaks: totalScore.streaks,
      lastUpdatedAt: totalScore.lastUpdatedAt,
    };
  }

  @Post('update-score')
  @ApiOperation({
    summary: '手动更新积分',
    description: '管理员接口：手动为用户增加或减少积分'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '积分更新成功',
  })
  async updateScore(
    @CurrentUser() user: User,
    @Body() scoreUpdateDto: ScoreUpdateDto,
  ) {
    const updatedScore = await this.scoringService.updateUserScore(
      user.id,
      scoreUpdateDto.scoreType,
      scoreUpdateDto.points,
      scoreUpdateDto.experience,
    );

    return {
      success: true,
      message: `积分更新成功，${scoreUpdateDto.reason || '获得'}${scoreUpdateDto.points}分`,
      data: {
        totalScore: updatedScore.totalScore,
        level: updatedScore.level,
        experience: updatedScore.experience,
        title: updatedScore.title,
      },
    };
  }

  @Get('score-history/:userId')
  @ApiOperation({
    summary: '获取积分历史',
    description: '获取指定用户的积分变化历史记录'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '积分历史获取成功',
  })
  async getScoreHistory(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('period') period?: 'daily' | 'weekly' | 'monthly',
    @Query('limit') limit: number = 30,
  ) {
    // 这里可以实现积分历史查询逻辑
    return {
      message: '积分历史接口，待实现具体逻辑',
      userId,
      period,
      limit,
    };
  }

  @Get('achievements')
  @ApiOperation({
    summary: '获取成就系统',
    description: '获取所有可获得的成就和用户当前的成就进度'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成就信息获取成功',
  })
  async getAchievements(
    @CurrentUser() user: User,
  ) {
    // 成就系统定义
    const achievements = [
      {
        id: 'first_interaction',
        name: '初次相遇',
        description: '完成第一次动物互动',
        category: '互动',
        rarity: 'COMMON',
        icon: '🤝',
        requirement: '完成1次互动',
        progress: 0,
        maxProgress: 1,
        reward: { score: 50, experience: 25 },
      },
      {
        id: 'social_butterfly',
        name: '社交达人',
        description: '与10只不同的动物互动',
        category: '社交',
        rarity: 'UNCOMMON',
        icon: '🦋',
        requirement: '与10只不同动物互动',
        progress: 0,
        maxProgress: 10,
        reward: { score: 200, experience: 100 },
      },
      {
        id: 'workaholic',
        name: '工作狂魔',
        description: '累计工作时间达到100小时',
        category: '工作',
        rarity: 'RARE',
        icon: '💼',
        requirement: '工作100小时',
        progress: 0,
        maxProgress: 100,
        reward: { score: 500, experience: 250 },
      },
      {
        id: 'legend_status',
        name: '传奇打工人',
        description: '达到20级并获得10000分',
        category: '成就',
        rarity: 'LEGENDARY',
        icon: '👑',
        requirement: '20级且10000分',
        progress: 0,
        maxProgress: 1,
        reward: { score: 2000, experience: 1000 },
      },
    ];

    return {
      achievements,
      message: '成就系统数据，实际进度需要从数据库计算',
    };
  }

  @Post('claim-achievement/:achievementId')
  @ApiOperation({
    summary: '领取成就奖励',
    description: '领取已完成成就的奖励'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成就奖励领取成功',
  })
  async claimAchievement(
    @CurrentUser() user: User,
    @Param('achievementId') achievementId: string,
  ) {
    // 这里可以实现成就奖励领取逻辑
    return {
      success: true,
      message: `成就"${achievementId}"奖励已领取`,
      reward: {
        score: 100,
        experience: 50,
        badge: {
          name: '成就大师',
          icon: '🏆',
          rarity: 'RARE',
        },
      },
    };
  }
}