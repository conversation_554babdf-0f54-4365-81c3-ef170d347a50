import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { AnimalInteractionRecord } from '../entities/animal-interaction-record.entity';
import { AnimalCategory, AnimalSpecies, ANIMAL_CONFIG } from '@/common/constants/animal-types';

export interface AnimalBehavior {
  type: 'move' | 'idle' | 'interact' | 'work' | 'rest' | 'play' | 'socialize';
  duration: number;
  intensity: number;
  targetPosition?: { x: number; y: number; z: number };
  targetAnimalId?: string;
  animation?: string;
  soundEffect?: string;
  description?: string;
}

export interface BehaviorPattern {
  weight: number;
  conditions?: {
    timeOfDay?: number[];
    energyRange?: [number, number];
    happinessRange?: [number, number];
    nearbyAnimals?: number;
    weatherCondition?: string;
  };
  behavior: AnimalBehavior;
}

@Injectable()
export class AnimalBehaviorService {
  private readonly logger = new Logger(AnimalBehaviorService.name);
  private behaviorPatterns: Map<AnimalSpecies, BehaviorPattern[]> = new Map();
  private activeAnimations: Map<string, NodeJS.Timeout> = new Map();

  constructor(
    @InjectRepository(ZooAnimal)
    private zooAnimalRepository: Repository<ZooAnimal>,
    @InjectRepository(AnimalInteractionRecord)
    private interactionRecordRepository: Repository<AnimalInteractionRecord>,
    private configService: ConfigService,
  ) {
    this.initializeBehaviorPatterns();
  }

  /**
   * 初始化动物行为模式
   */
  private initializeBehaviorPatterns() {
    // 神兽行为模式
    this.behaviorPatterns.set(AnimalSpecies.DRAGON, [
      {
        weight: 0.3,
        conditions: { energyRange: [70, 100] },
        behavior: {
          type: 'move',
          duration: 10000,
          intensity: 0.8,
          animation: 'majestic_flight',
          soundEffect: 'dragon_roar',
          description: '威严地巡视领地'
        }
      },
      {
        weight: 0.2,
        conditions: { timeOfDay: [6, 18] },
        behavior: {
          type: 'work',
          duration: 15000,
          intensity: 0.9,
          animation: 'divine_guidance',
          description: '指导其他动物工作'
        }
      },
      {
        weight: 0.3,
        conditions: { happinessRange: [60, 100] },
        behavior: {
          type: 'idle',
          duration: 8000,
          intensity: 0.5,
          animation: 'meditative_pose',
          description: '冥想修炼'
        }
      },
      {
        weight: 0.2,
        conditions: { nearbyAnimals: 3 },
        behavior: {
          type: 'socialize',
          duration: 12000,
          intensity: 0.7,
          animation: 'blessing_gesture',
          description: '为其他动物祝福'
        }
      }
    ]);

    // 宠物行为模式
    this.behaviorPatterns.set(AnimalSpecies.PERSIAN_CAT, [
      {
        weight: 0.4,
        conditions: { energyRange: [40, 80] },
        behavior: {
          type: 'play',
          duration: 8000,
          intensity: 0.6,
          animation: 'playful_pounce',
          soundEffect: 'cat_meow',
          description: '愉快地玩耍'
        }
      },
      {
        weight: 0.3,
        conditions: { energyRange: [0, 30] },
        behavior: {
          type: 'rest',
          duration: 12000,
          intensity: 0.2,
          animation: 'cat_nap',
          description: '舒适地小憩'
        }
      },
      {
        weight: 0.2,
        conditions: { nearbyAnimals: 1 },
        behavior: {
          type: 'socialize',
          duration: 6000,
          intensity: 0.5,
          animation: 'friendly_approach',
          description: '友好地接近同伴'
        }
      },
      {
        weight: 0.1,
        behavior: {
          type: 'move',
          duration: 5000,
          intensity: 0.4,
          animation: 'graceful_walk',
          description: '优雅地漫步'
        }
      }
    ]);

    // 工作动物行为模式
    this.behaviorPatterns.set(AnimalSpecies.OX, [
      {
        weight: 0.5,
        conditions: { timeOfDay: [8, 18], energyRange: [30, 100] },
        behavior: {
          type: 'work',
          duration: 20000,
          intensity: 0.8,
          animation: 'diligent_work',
          soundEffect: 'ox_grunt',
          description: '勤奋地工作'
        }
      },
      {
        weight: 0.2,
        conditions: { energyRange: [0, 40] },
        behavior: {
          type: 'rest',
          duration: 15000,
          intensity: 0.3,
          animation: 'tired_rest',
          description: '疲惫地休息'
        }
      },
      {
        weight: 0.2,
        conditions: { nearbyAnimals: 2 },
        behavior: {
          type: 'socialize',
          duration: 10000,
          intensity: 0.6,
          animation: 'work_collaboration',
          description: '与同事协作'
        }
      },
      {
        weight: 0.1,
        behavior: {
          type: 'move',
          duration: 8000,
          intensity: 0.5,
          animation: 'steady_walk',
          description: '稳重地移动'
        }
      }
    ]);

    this.logger.log(`Behavior patterns initialized for ${this.behaviorPatterns.size} species`);
  }

  /**
   * 为动物生成下一个行为
   */
  async generateNextBehavior(animal: ZooAnimal, context: {
    timeOfDay: number;
    nearbyAnimals: ZooAnimal[];
    weather: any;
  }): Promise<AnimalBehavior> {
    const patterns = this.behaviorPatterns.get(animal.species) || [];
    
    if (patterns.length === 0) {
      return this.getDefaultBehavior(animal);
    }

    // 评估每个行为模式的适用性
    const evaluatedPatterns = patterns.map(pattern => ({
      ...pattern,
      score: this.evaluateBehaviorPattern(pattern, animal, context)
    }));

    // 按分数排序并选择最适合的行为
    evaluatedPatterns.sort((a, b) => b.score - a.score);
    
    const selectedPattern = evaluatedPatterns[0];
    if (selectedPattern.score > 0) {
      return this.enhanceBehaviorWithContext(selectedPattern.behavior, animal, context);
    }

    return this.getDefaultBehavior(animal);
  }

  /**
   * 评估行为模式的适用性
   */
  private evaluateBehaviorPattern(
    pattern: BehaviorPattern,
    animal: ZooAnimal,
    context: any
  ): number {
    let score = pattern.weight;

    if (pattern.conditions) {
      const conditions = pattern.conditions;
      
      // 检查时间条件
      if (conditions.timeOfDay) {
        const inTimeRange = conditions.timeOfDay.some(hour => 
          Math.abs(context.timeOfDay - hour) <= 1
        );
        score *= inTimeRange ? 1.5 : 0.5;
      }

      // 检查能量条件
      if (conditions.energyRange) {
        const [minEnergy, maxEnergy] = conditions.energyRange;
        const energyFits = animal.currentState.energy >= minEnergy && 
                          animal.currentState.energy <= maxEnergy;
        score *= energyFits ? 1.3 : 0.3;
      }

      // 检查幸福度条件
      if (conditions.happinessRange) {
        const [minHappiness, maxHappiness] = conditions.happinessRange;
        const happinessFits = animal.currentState.happiness >= minHappiness && 
                             animal.currentState.happiness <= maxHappiness;
        score *= happinessFits ? 1.2 : 0.4;
      }

      // 检查附近动物数量
      if (conditions.nearbyAnimals) {
        const nearbyCount = context.nearbyAnimals.length;
        const countMatches = nearbyCount >= conditions.nearbyAnimals;
        score *= countMatches ? 1.4 : 0.6;
      }
    }

    // 基于动物个性调整
    score *= this.getPersonalityMultiplier(animal, pattern.behavior.type);

    return Math.max(0, score);
  }

  /**
   * 根据动物个性调整行为权重
   */
  private getPersonalityMultiplier(animal: ZooAnimal, behaviorType: string): number {
    const config = ANIMAL_CONFIG[animal.species];
    
    switch (behaviorType) {
      case 'work':
        return animal.category === AnimalCategory.WORKING_ANIMAL ? 1.3 : 0.8;
      case 'play':
        return animal.category === AnimalCategory.PET ? 1.4 : 0.7;
      case 'socialize':
        return animal.currentState.happiness > 70 ? 1.2 : 0.8;
      case 'rest':
        return animal.currentState.energy < 50 ? 1.5 : 0.6;
      case 'move':
        return animal.currentState.energy > 60 ? 1.1 : 0.9;
      default:
        return 1.0;
    }
  }

  /**
   * 根据上下文增强行为
   */
  private enhanceBehaviorWithContext(
    behavior: AnimalBehavior,
    animal: ZooAnimal,
    context: any
  ): AnimalBehavior {
    const enhanced = { ...behavior };

    // 根据动物当前位置和目标设置移动路径
    if (behavior.type === 'move') {
      enhanced.targetPosition = this.generateTargetPosition(animal, context);
    }

    // 如果是社交行为，选择目标动物
    if (behavior.type === 'socialize' && context.nearbyAnimals.length > 0) {
      const targetAnimal = this.selectSocialTarget(animal, context.nearbyAnimals);
      if (targetAnimal) {
        enhanced.targetAnimalId = targetAnimal.id;
        enhanced.targetPosition = targetAnimal.position;
      }
    }

    // 根据时间调整行为强度
    if (context.timeOfDay < 6 || context.timeOfDay > 22) {
      enhanced.intensity *= 0.7; // 夜间行为较缓和
    }

    return enhanced;
  }

  /**
   * 生成目标位置
   */
  private generateTargetPosition(animal: ZooAnimal, context: any) {
    const zooConfig = this.configService.get('zoo');
    const worldWidth = zooConfig?.worldWidth || 3200;
    const worldHeight = zooConfig?.worldHeight || 2400;

    // 根据动物类型选择偏好区域
    let preferredZones: { x: [number, number], y: [number, number] }[] = [];

    switch (animal.category) {
      case AnimalCategory.WORKING_ANIMAL:
        preferredZones = [
          { x: [0, worldWidth / 3], y: [0, worldHeight] } // 工作区
        ];
        break;
      case AnimalCategory.DIVINE_BEAST:
        preferredZones = [
          { x: [worldWidth / 3, worldWidth * 2 / 3], y: [0, worldHeight] } // 神兽区
        ];
        break;
      case AnimalCategory.PET:
        preferredZones = [
          { x: [worldWidth * 2 / 3, worldWidth], y: [0, worldHeight] } // 休闲区
        ];
        break;
    }

    if (preferredZones.length > 0) {
      const zone = preferredZones[Math.floor(Math.random() * preferredZones.length)];
      return {
        x: zone.x[0] + Math.random() * (zone.x[1] - zone.x[0]),
        y: zone.y[0] + Math.random() * (zone.y[1] - zone.y[0]),
        z: 0,
      };
    }

    return {
      x: Math.random() * worldWidth,
      y: Math.random() * worldHeight,
      z: 0,
    };
  }

  /**
   * 选择社交目标
   */
  private selectSocialTarget(animal: ZooAnimal, nearbyAnimals: ZooAnimal[]): ZooAnimal | null {
    // 过滤出适合社交的动物
    const suitableTargets = nearbyAnimals.filter(target => {
      if (target.id === animal.id) return false;
      
      // 同类型动物更容易社交
      if (target.category === animal.category) return true;
      
      // 神兽可以与所有动物社交
      if (animal.category === AnimalCategory.DIVINE_BEAST) return true;
      
      // 宠物喜欢与幸福度高的动物社交
      if (animal.category === AnimalCategory.PET && target.currentState.happiness > 60) return true;
      
      return false;
    });

    if (suitableTargets.length === 0) return null;

    // 选择距离最近或最适合的目标
    return suitableTargets.sort((a, b) => {
      const distA = Math.sqrt((a.position.x - animal.position.x) ** 2 + (a.position.y - animal.position.y) ** 2);
      const distB = Math.sqrt((b.position.x - animal.position.x) ** 2 + (b.position.y - animal.position.y) ** 2);
      return distA - distB;
    })[0];
  }

  /**
   * 获取默认行为
   */
  private getDefaultBehavior(animal: ZooAnimal): AnimalBehavior {
    return {
      type: 'idle',
      duration: 5000,
      intensity: 0.3,
      animation: 'default_idle',
      description: '静静地站立'
    };
  }

  /**
   * 执行动物行为
   */
  async executeBehavior(animalId: string, behavior: AnimalBehavior): Promise<void> {
    try {
      const animal = await this.zooAnimalRepository.findOne({
        where: { id: animalId, isActive: true }
      });

      if (!animal) return;

      this.logger.debug(`Executing behavior for ${animal.name}: ${behavior.type} (${behavior.duration}ms)`);

      // 更新动物状态
      await this.updateAnimalStateForBehavior(animal, behavior);

      // 设置行为完成回调
      if (this.activeAnimations.has(animalId)) {
        clearTimeout(this.activeAnimations.get(animalId)!);
      }

      const timeout = setTimeout(async () => {
        await this.onBehaviorComplete(animalId, behavior);
        this.activeAnimations.delete(animalId);
      }, behavior.duration);

      this.activeAnimations.set(animalId, timeout);

    } catch (error) {
      this.logger.error(`Failed to execute behavior for animal ${animalId}:`, error);
    }
  }

  /**
   * 根据行为更新动物状态
   */
  private async updateAnimalStateForBehavior(animal: ZooAnimal, behavior: AnimalBehavior): Promise<void> {
    const stateChanges: Partial<typeof animal.currentState> = {};

    switch (behavior.type) {
      case 'work':
        stateChanges.activity = 'working';
        stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 15);
        stateChanges.workEfficiency = Math.min(100, animal.currentState.workEfficiency + 5);
        break;

      case 'play':
        stateChanges.activity = 'playing';
        stateChanges.happiness = Math.min(100, animal.currentState.happiness + behavior.intensity * 20);
        stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 10);
        break;

      case 'rest':
        stateChanges.activity = 'resting';
        stateChanges.energy = Math.min(100, animal.currentState.energy + behavior.intensity * 25);
        stateChanges.health = Math.min(100, animal.currentState.health + 5);
        break;

      case 'move':
        stateChanges.activity = 'moving';
        stateChanges.energy = Math.max(0, animal.currentState.energy - behavior.intensity * 5);
        if (behavior.targetPosition) {
          stateChanges.location = this.getLocationFromPosition(behavior.targetPosition);
          animal.position = { ...behavior.targetPosition, area: stateChanges.location || 'center' };
        }
        break;

      case 'socialize':
        stateChanges.activity = 'socializing';
        stateChanges.happiness = Math.min(100, animal.currentState.happiness + behavior.intensity * 15);
        break;

      case 'idle':
      default:
        stateChanges.activity = 'idle';
        break;
    }

    // 应用状态变化
    Object.assign(animal.currentState, stateChanges);
    animal.lastActiveAt = new Date();

    await this.zooAnimalRepository.save(animal);
  }

  /**
   * 根据位置确定区域
   */
  private getLocationFromPosition(position: { x: number; y: number; z: number }): string {
    const zooConfig = this.configService.get('zoo');
    const worldWidth = zooConfig?.worldWidth || 3200;

    if (position.x < worldWidth / 3) return 'work_area';
    if (position.x < worldWidth * 2 / 3) return 'divine_area';
    return 'leisure_area';
  }

  /**
   * 行为完成回调
   */
  private async onBehaviorComplete(animalId: string, behavior: AnimalBehavior): Promise<void> {
    try {
      const animal = await this.zooAnimalRepository.findOne({
        where: { id: animalId, isActive: true }
      });

      if (!animal) return;

      // 行为完成后的状态调整
      if (behavior.type === 'work') {
        animal.workStats.totalWorkHours += behavior.duration / (1000 * 60 * 60); // 转换为小时
      }

      // 更新交互统计
      animal.interactionStats.totalInteractions += 1;
      
      await this.zooAnimalRepository.save(animal);

      this.logger.debug(`Behavior completed for ${animal.name}: ${behavior.type}`);

    } catch (error) {
      this.logger.error(`Failed to handle behavior completion for animal ${animalId}:`, error);
    }
  }

  /**
   * 获取动物当前行为状态
   */
  async getAnimalBehaviorState(animalId: string): Promise<{
    currentBehavior?: AnimalBehavior;
    isActive: boolean;
    timeRemaining?: number;
  }> {
    const timeout = this.activeAnimations.get(animalId);
    
    return {
      isActive: !!timeout,
      timeRemaining: timeout ? undefined : 0, // 实际应该从timeout中计算剩余时间
    };
  }

  /**
   * 停止动物的当前行为
   */
  async stopAnimalBehavior(animalId: string): Promise<void> {
    const timeout = this.activeAnimations.get(animalId);
    if (timeout) {
      clearTimeout(timeout);
      this.activeAnimations.delete(animalId);
      
      // 将动物状态设为idle
      const animal = await this.zooAnimalRepository.findOne({
        where: { id: animalId, isActive: true }
      });
      
      if (animal) {
        animal.currentState.activity = 'idle';
        await this.zooAnimalRepository.save(animal);
      }
    }
  }

  /**
   * 清理资源
   */
  onModuleDestroy() {
    this.activeAnimations.forEach(timeout => clearTimeout(timeout));
    this.activeAnimations.clear();
  }
}