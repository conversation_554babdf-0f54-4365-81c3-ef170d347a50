import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { UserScore } from '../entities/user-score.entity';
import { AnimalInteractionRecord } from '../entities/animal-interaction-record.entity';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { InteractionType } from '../dto/animal-interaction.dto';

export interface ScoreCalculationResult {
  baseScore: number;
  bonusScore: number;
  totalScore: number;
  experience: number;
  reason: string;
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatarUrl?: string;
  totalScore: number;
  level: number;
  title: string;
  rank: number;
  badges: Array<{
    name: string;
    icon: string;
    rarity: string;
  }>;
  stats: {
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
  };
}

@Injectable()
export class ScoringService {
  // 积分计算基础值
  private readonly INTERACTION_BASE_SCORES = {
    FEED: 10,
    PET: 8,
    PLAY: 12,
    WORK_TOGETHER: 20,
    REST: 5,
    ENCOURAGE: 15,
  };

  // 等级经验值表
  private readonly LEVEL_EXP_TABLE = [
    0, 100, 250, 450, 700, 1000, 1400, 1850, 2350, 2900, 3500,
    4200, 5000, 5900, 6900, 8000, 9200, 10500, 11900, 13400, 15000
  ];

  // 称号系统
  private readonly TITLES = [
    { minScore: 0, title: '新手打工人' },
    { minScore: 500, title: '勤奋打工人' },
    { minScore: 1500, title: '资深打工人' },
    { minScore: 3000, title: '优秀打工人' },
    { minScore: 5000, title: '精英打工人' },
    { minScore: 8000, title: '模范打工人' },
    { minScore: 12000, title: '传奇打工人' },
    { minScore: 20000, title: '打工人导师' },
    { minScore: 35000, title: '打工人大师' },
    { minScore: 50000, title: '打工人传说' },
  ];

  constructor(
    @InjectRepository(UserScore)
    private userScoreRepository: Repository<UserScore>,
    @InjectRepository(AnimalInteractionRecord)
    private interactionRepository: Repository<AnimalInteractionRecord>,
    @InjectRepository(ZooAnimal)
    private animalRepository: Repository<ZooAnimal>,
  ) {}

  /**
   * 计算互动积分
   */
  async calculateInteractionScore(
    userId: string,
    animalId: string,
    interactionType: InteractionType,
    effectiveness: number,
    duration: number,
  ): Promise<ScoreCalculationResult> {
    const baseScore = this.INTERACTION_BASE_SCORES[interactionType] || 5;
    
    // 效果加成 (0.5 - 2.0倍)
    const effectivenessMultiplier = Math.max(0.5, Math.min(2.0, effectiveness / 5));
    
    // 持续时间加成 (最多1.5倍)
    const durationMultiplier = Math.min(1.5, 1 + (duration - 5) * 0.05);
    
    // 动物类型加成
    const animal = await this.animalRepository.findOne({ where: { id: animalId } });
    let animalTypeMultiplier = 1.0;
    if (animal) {
      switch (animal.category) {
        case AnimalCategory.DIVINE_BEAST:
          animalTypeMultiplier = 1.3; // 神兽交互加成30%
          break;
        case AnimalCategory.WORKING_ANIMAL:
          if (interactionType === InteractionType.WORK_TOGETHER) {
            animalTypeMultiplier = 1.5; // 与工作动物协作加成50%
          }
          break;
        case AnimalCategory.PET:
          if (interactionType === InteractionType.PET || interactionType === InteractionType.PLAY) {
            animalTypeMultiplier = 1.2; // 宠物抚摸/玩耍加成20%
          }
          break;
      }
    }

    // 连击奖励检查
    const recentInteractions = await this.interactionRepository.count({
      where: {
        userId,
        createdAt: MoreThan(new Date(Date.now() - 10 * 60 * 1000)), // 10分钟内
      },
    });
    
    const comboMultiplier = Math.min(2.0, 1 + recentInteractions * 0.1);

    const calculatedScore = Math.round(
      baseScore * effectivenessMultiplier * durationMultiplier * animalTypeMultiplier * comboMultiplier
    );

    const bonusScore = calculatedScore - baseScore;
    const experience = Math.round(calculatedScore * 0.8);

    let reason = `基础${baseScore}分`;
    if (effectivenessMultiplier !== 1) {
      reason += `, 效果${effectivenessMultiplier.toFixed(1)}倍`;
    }
    if (durationMultiplier > 1) {
      reason += `, 持续时间加成`;
    }
    if (animalTypeMultiplier > 1) {
      reason += `, ${animal?.category}类型加成`;
    }
    if (comboMultiplier > 1) {
      reason += `, 连击x${recentInteractions + 1}`;
    }

    return {
      baseScore,
      bonusScore,
      totalScore: calculatedScore,
      experience,
      reason,
    };
  }

  /**
   * 更新用户积分
   */
  async updateUserScore(
    userId: string,
    scoreType: 'interaction' | 'work' | 'social' | 'achievement' | 'creativity',
    points: number,
    experience: number = 0,
  ): Promise<UserScore> {
    const today = new Date().toISOString().split('T')[0];
    const currentWeek = this.getWeekPeriod(new Date());
    const currentMonth = new Date().toISOString().slice(0, 7);

    // 更新各种周期的积分
    const periods = [
      { type: 'TOTAL' as const, period: 'all-time' },
      { type: 'DAILY' as const, period: today },
      { type: 'WEEKLY' as const, period: currentWeek },
      { type: 'MONTHLY' as const, period: currentMonth },
    ];

    const updatedScores: UserScore[] = [];

    for (const { type, period } of periods) {
      let userScore = await this.userScoreRepository.findOne({
        where: { userId, scoreType: type, period },
        relations: ['user'],
      });

      if (!userScore) {
        userScore = this.userScoreRepository.create({
          userId,
          scoreType: type,
          period,
          totalScore: 0,
          interactionScore: 0,
          workPerformanceScore: 0,
          socialScore: 0,
          achievementScore: 0,
          creativityScore: 0,
          interactionStats: {
            totalInteractions: 0,
            feedCount: 0,
            petCount: 0,
            playCount: 0,
            workTogetherCount: 0,
            restCount: 0,
            encourageCount: 0,
            averageEffectiveness: 0,
          },
          workStats: {
            totalWorkHours: 0,
            averageEfficiency: 0,
            completedTasks: 0,
            promotions: 0,
            teamworkScore: 0,
            overtimeHours: 0,
          },
          socialStats: {
            friendsMade: 0,
            helpGiven: 0,
            popularityScore: 0,
            leadershipScore: 0,
            mentorshipScore: 0,
          },
          achievementStats: {
            totalAchievements: 0,
            rareAchievements: 0,
            firstTimeAchievements: 0,
            challengesCompleted: 0,
          },
          creativityStats: {
            avatarsCreated: 0,
            uniqueInteractions: 0,
            innovativeApproaches: 0,
            artisticScore: 0,
          },
          level: 1,
          experience: 0,
          experienceToNextLevel: 100,
          reputation: 100,
          title: '新手打工人',
          badges: [],
          streaks: {
            dailyLogin: 0,
            maxDailyLogin: 0,
            weeklyActive: 0,
            maxWeeklyActive: 0,
            consecutiveWork: 0,
            maxConsecutiveWork: 0,
          },
          globalRank: 9999,
          dailyRank: 9999,
          weeklyRank: 9999,
          monthlyRank: 9999,
          lastUpdatedAt: new Date(),
        });
      }

      // 更新对应类型的积分
      switch (scoreType) {
        case 'interaction':
          userScore.interactionScore += points;
          break;
        case 'work':
          userScore.workPerformanceScore += points;
          break;
        case 'social':
          userScore.socialScore += points;
          break;
        case 'achievement':
          userScore.achievementScore += points;
          break;
        case 'creativity':
          userScore.creativityScore += points;
          break;
      }

      userScore.totalScore += points;
      userScore.experience += experience;

      // 检查升级
      const newLevel = this.calculateLevel(userScore.experience);
      if (newLevel > userScore.level) {
        userScore.level = newLevel;
        // 升级奖励
        userScore.totalScore += newLevel * 50;
      }

      // 更新称号
      userScore.title = this.calculateTitle(userScore.totalScore);

      // 更新经验值到下一级
      userScore.experienceToNextLevel = this.getNextLevelExp(userScore.level) - userScore.experience;

      userScore.lastUpdatedAt = new Date();

      updatedScores.push(await this.userScoreRepository.save(userScore));
    }

    // 返回总积分记录
    return updatedScores.find(s => s.scoreType === 'TOTAL')!;
  }

  /**
   * 计算等级
   */
  private calculateLevel(experience: number): number {
    for (let i = this.LEVEL_EXP_TABLE.length - 1; i >= 0; i--) {
      if (experience >= this.LEVEL_EXP_TABLE[i]) {
        return i + 1;
      }
    }
    return 1;
  }

  /**
   * 获取下一等级所需经验值
   */
  private getNextLevelExp(level: number): number {
    if (level >= this.LEVEL_EXP_TABLE.length) {
      return this.LEVEL_EXP_TABLE[this.LEVEL_EXP_TABLE.length - 1] + (level - this.LEVEL_EXP_TABLE.length + 1) * 2000;
    }
    return this.LEVEL_EXP_TABLE[level] || 100;
  }

  /**
   * 计算称号
   */
  private calculateTitle(totalScore: number): string {
    for (let i = this.TITLES.length - 1; i >= 0; i--) {
      if (totalScore >= this.TITLES[i].minScore) {
        return this.TITLES[i].title;
      }
    }
    return '新手打工人';
  }

  /**
   * 获取周期字符串
   */
  private getWeekPeriod(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const weekDay = date.getDay();
    const monday = new Date(date);
    monday.setDate(date.getDate() - (weekDay === 0 ? 6 : weekDay - 1));
    
    return `${year}-W${String(Math.ceil(monday.getDate() / 7)).padStart(2, '0')}-${month}`;
  }

  /**
   * 获取排行榜
   */
  async getLeaderboard(
    period: 'daily' | 'weekly' | 'monthly' | 'total' = 'total',
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    leaderboard: LeaderboardEntry[];
    total: number;
  }> {
    const scoreType = period === 'daily' ? 'DAILY' : 
                     period === 'weekly' ? 'WEEKLY' : 
                     period === 'monthly' ? 'MONTHLY' : 'TOTAL';
    
    let whereClause: any = { scoreType: scoreType as 'TOTAL' | 'DAILY' | 'WEEKLY' | 'MONTHLY' };
    
    if (period !== 'total') {
      const today = new Date().toISOString().split('T')[0];
      const currentWeek = this.getWeekPeriod(new Date());
      const currentMonth = new Date().toISOString().slice(0, 7);
      
      const periodValue = period === 'daily' ? today : 
                         period === 'weekly' ? currentWeek : currentMonth;
      
      whereClause = { ...whereClause, period: periodValue } as any;
    }

    const [userScores, total] = await this.userScoreRepository.findAndCount({
      where: whereClause,
      relations: ['user'],
      order: { totalScore: 'DESC' },
      take: limit,
      skip: offset,
    });

    const leaderboard: LeaderboardEntry[] = userScores.map((score, index) => ({
      userId: score.userId,
      username: score.user?.username || 'Unknown',
      avatarUrl: (score.user as any)?.profileImage || '',
      totalScore: score.totalScore,
      level: score.level,
      title: score.title,
      rank: offset + index + 1,
      badges: score.badges.slice(0, 3).map(badge => ({
        name: badge.name,
        icon: badge.icon,
        rarity: badge.rarity,
      })),
      stats: {
        interactionScore: score.interactionScore,
        workPerformanceScore: score.workPerformanceScore,
        socialScore: score.socialScore,
        achievementScore: score.achievementScore,
        creativityScore: score.creativityScore,
      },
    }));

    return {
      leaderboard,
      total,
    };
  }

  /**
   * 获取用户排名
   */
  async getUserRanking(userId: string): Promise<{
    globalRank: number;
    dailyRank: number;
    weeklyRank: number;
    monthlyRank: number;
    totalScore: number;
    level: number;
    title: string;
  }> {
    const totalScore = await this.userScoreRepository.findOne({
      where: { userId, scoreType: 'TOTAL' },
    });

    if (!totalScore) {
      return {
        globalRank: 9999,
        dailyRank: 9999,
        weeklyRank: 9999,
        monthlyRank: 9999,
        totalScore: 0,
        level: 1,
        title: '新手打工人',
      };
    }

    // 计算各类排名
    const globalRank = await this.calculateRank('TOTAL', 'all-time', totalScore.totalScore);
    const dailyRank = await this.calculateRank('DAILY', new Date().toISOString().split('T')[0], totalScore.totalScore);
    const weeklyRank = await this.calculateRank('WEEKLY', this.getWeekPeriod(new Date()), totalScore.totalScore);
    const monthlyRank = await this.calculateRank('MONTHLY', new Date().toISOString().slice(0, 7), totalScore.totalScore);

    return {
      globalRank,
      dailyRank,
      weeklyRank,
      monthlyRank,
      totalScore: totalScore.totalScore,
      level: totalScore.level,
      title: totalScore.title,
    };
  }

  /**
   * 计算排名
   */
  private async calculateRank(scoreType: string, period: string, userScore: number): Promise<number> {
    const count = await this.userScoreRepository.count({
      where: {
        scoreType: scoreType as any,
        period: period,
        totalScore: MoreThan(userScore),
      },
    });
    
    return count + 1;
  }

  /**
   * 处理互动积分
   */
  async processInteractionScore(interactionRecord: AnimalInteractionRecord): Promise<void> {
    const scoreResult = await this.calculateInteractionScore(
      interactionRecord.userId,
      interactionRecord.animalId,
      interactionRecord.interactionType,
      interactionRecord.effectiveness,
      interactionRecord.duration,
    );

    await this.updateUserScore(
      interactionRecord.userId,
      'interaction',
      scoreResult.totalScore,
      scoreResult.experience,
    );

    // 更新互动记录中的经验值
    interactionRecord.experienceGained = scoreResult.experience;
    await this.interactionRepository.save(interactionRecord);
  }
}