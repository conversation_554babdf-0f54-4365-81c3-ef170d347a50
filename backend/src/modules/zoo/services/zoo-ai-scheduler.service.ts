import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ZooAnimal } from '../entities/zoo-animal.entity';
import { AnimalBehaviorService, AnimalBehavior } from './animal-behavior.service';
import { AnimalCategory } from '@/common/constants/animal-types';

export interface ZooEnvironmentState {
  timeOfDay: number;
  weather: {
    condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy';
    temperature: number;
    humidity: number;
  };
  crowdLevel: 'low' | 'medium' | 'high';
  events: Array<{
    id: string;
    name: string;
    type: 'feeding' | 'show' | 'cleaning' | 'maintenance';
    startTime: Date;
    duration: number;
    affectedAreas: string[];
  }>;
}

@Injectable()
export class ZooAISchedulerService {
  private readonly logger = new Logger(ZooAISchedulerService.name);
  private isRunning = false;
  private environmentState: ZooEnvironmentState;
  private animalBehaviorQueue: Map<string, AnimalBehavior[]> = new Map();
  private globalEvents: any[] = [];

  constructor(
    @InjectRepository(ZooAnimal)
    private zooAnimalRepository: Repository<ZooAnimal>,
    private animalBehaviorService: AnimalBehaviorService,
    private configService: ConfigService,
  ) {
    this.initializeEnvironment();
    this.startAIScheduler();
  }

  /**
   * 初始化环境状态
   */
  private initializeEnvironment() {
    this.environmentState = {
      timeOfDay: new Date().getHours(),
      weather: this.generateWeather(),
      crowdLevel: this.calculateCrowdLevel(),
      events: []
    };
    
    this.logger.log('Zoo AI environment initialized');
  }

  /**
   * 启动AI调度器
   */
  private startAIScheduler() {
    const zooConfig = this.configService.get('zoo');
    const isEnabled = zooConfig?.enableAIBehavior !== false;
    
    if (!isEnabled) {
      this.logger.warn('Zoo AI behavior is disabled in configuration');
      return;
    }

    this.isRunning = true;
    this.logger.log('Zoo AI Scheduler started');
  }

  /**
   * 每5秒更新动物行为（可配置）
   */
  @Cron('*/5 * * * * *')
  async updateAnimalBehaviors() {
    if (!this.isRunning) return;

    try {
      await this.processAnimalBehaviorCycle();
    } catch (error) {
      this.logger.error('Failed to update animal behaviors:', error);
    }
  }

  /**
   * 每分钟更新环境状态
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async updateEnvironment() {
    if (!this.isRunning) return;

    try {
      await this.updateEnvironmentState();
    } catch (error) {
      this.logger.error('Failed to update environment:', error);
    }
  }

  /**
   * 每小时触发特殊事件
   */
  @Cron(CronExpression.EVERY_HOUR)
  async triggerHourlyEvents() {
    if (!this.isRunning) return;

    try {
      await this.processHourlyEvents();
    } catch (error) {
      this.logger.error('Failed to process hourly events:', error);
    }
  }

  /**
   * 处理动物行为周期
   */
  private async processAnimalBehaviorCycle() {
    // 获取所有活跃动物
    const animals = await this.zooAnimalRepository.find({
      where: { isActive: true },
      relations: ['user']
    });

    if (animals.length === 0) return;

    this.logger.debug(`Processing behavior cycle for ${animals.length} animals`);

    // 为每个动物生成行为
    const behaviorPromises = animals.map(animal => 
      this.processAnimalBehavior(animal, animals)
    );

    await Promise.all(behaviorPromises);
  }

  /**
   * 处理单个动物的行为
   */
  private async processAnimalBehavior(animal: ZooAnimal, allAnimals: ZooAnimal[]) {
    try {
      // 检查动物是否正在执行行为
      const behaviorState = await this.animalBehaviorService.getAnimalBehaviorState(animal.id);
      
      if (behaviorState.isActive) {
        return; // 动物正在执行行为，跳过
      }

      // 获取附近的动物
      const nearbyAnimals = this.getNearbyAnimals(animal, allAnimals);

      // 生成行为上下文
      const context = {
        timeOfDay: this.environmentState.timeOfDay,
        nearbyAnimals,
        weather: this.environmentState.weather,
        crowdLevel: this.environmentState.crowdLevel,
        globalEvents: this.globalEvents
      };

      // 生成下一个行为
      const nextBehavior = await this.animalBehaviorService.generateNextBehavior(animal, context);

      // 应用环境影响
      const modifiedBehavior = this.applyEnvironmentalEffects(nextBehavior, animal, context);

      // 执行行为
      await this.animalBehaviorService.executeBehavior(animal.id, modifiedBehavior);

    } catch (error) {
      this.logger.error(`Failed to process behavior for animal ${animal.id}:`, error);
    }
  }

  /**
   * 获取附近的动物
   */
  private getNearbyAnimals(animal: ZooAnimal, allAnimals: ZooAnimal[], maxDistance = 300): ZooAnimal[] {
    return allAnimals.filter(other => {
      if (other.id === animal.id) return false;
      
      const distance = Math.sqrt(
        (other.position.x - animal.position.x) ** 2 + 
        (other.position.y - animal.position.y) ** 2
      );
      
      return distance <= maxDistance;
    });
  }

  /**
   * 应用环境影响到行为
   */
  private applyEnvironmentalEffects(
    behavior: AnimalBehavior, 
    animal: ZooAnimal, 
    context: any
  ): AnimalBehavior {
    const modified = { ...behavior };

    // 天气影响
    switch (context.weather.condition) {
      case 'rainy':
        if (behavior.type === 'move') {
          modified.intensity *= 0.7; // 雨天移动较慢
        }
        if (behavior.type === 'play') {
          modified.intensity *= 0.5; // 雨天不太想玩
        }
        break;
      
      case 'sunny':
        if (behavior.type === 'play') {
          modified.intensity *= 1.2; // 好天气更愉快
        }
        break;
      
      case 'stormy':
        if (behavior.type !== 'rest') {
          modified.type = 'rest'; // 暴风雨时寻求庇护
          modified.description = '因暴风雨寻求庇护';
        }
        break;
    }

    // 温度影响
    const temp = context.weather.temperature;
    if (temp > 30) {
      // 高温时行为强度降低
      modified.intensity *= 0.8;
      if (behavior.type === 'work') {
        modified.duration *= 0.9; // 工作时间缩短
      }
    } else if (temp < 5) {
      // 低温时倾向于休息
      if (behavior.type === 'play') {
        modified.type = 'rest';
        modified.description = '因寒冷而休息';
      }
    }

    // 时间影响
    const hour = context.timeOfDay;
    if (hour < 6 || hour > 22) {
      // 夜间行为调整
      modified.intensity *= 0.6;
      if (behavior.type === 'work') {
        modified.type = 'rest';
        modified.description = '夜间休息';
      }
    } else if (hour >= 8 && hour <= 18) {
      // 白天工作时间
      if (animal.category === AnimalCategory.WORKING_ANIMAL && behavior.type === 'idle') {
        modified.type = 'work';
        modified.description = '工作时间到了';
      }
    }

    // 人群影响
    if (context.crowdLevel === 'high') {
      if (animal.category === AnimalCategory.PET) {
        // 宠物在人多时更活跃
        modified.intensity *= 1.3;
      } else if (animal.category === AnimalCategory.WORKING_ANIMAL) {
        // 工作动物可能被干扰
        modified.intensity *= 0.9;
      }
    }

    return modified;
  }

  /**
   * 更新环境状态
   */
  private async updateEnvironmentState() {
    // 更新时间
    this.environmentState.timeOfDay = new Date().getHours();
    
    // 随机更新天气（简单实现）
    if (Math.random() < 0.1) { // 10%概率天气变化
      this.environmentState.weather = this.generateWeather();
    }
    
    // 更新人群等级
    this.environmentState.crowdLevel = this.calculateCrowdLevel();
    
    // 清理过期事件
    this.environmentState.events = this.environmentState.events.filter(
      event => new Date().getTime() - event.startTime.getTime() < event.duration
    );

    this.logger.debug(`Environment updated: ${this.environmentState.timeOfDay}:00, ${this.environmentState.weather.condition}, crowd: ${this.environmentState.crowdLevel}`);
  }

  /**
   * 生成天气状态
   */
  private generateWeather() {
    const conditions: Array<'sunny' | 'cloudy' | 'rainy' | 'stormy'> = ['sunny', 'cloudy', 'rainy', 'stormy'];
    const weights = [0.5, 0.3, 0.15, 0.05]; // 权重：晴天最常见
    
    let random = Math.random();
    for (let i = 0; i < conditions.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return {
          condition: conditions[i],
          temperature: 15 + Math.random() * 20, // 15-35度
          humidity: 40 + Math.random() * 40 // 40-80%
        };
      }
    }
    
    return {
      condition: 'sunny' as const,
      temperature: 25,
      humidity: 60
    };
  }

  /**
   * 计算人群等级
   */
  private calculateCrowdLevel(): 'low' | 'medium' | 'high' {
    const hour = new Date().getHours();
    
    // 基于时间的简单人群模型
    if (hour >= 10 && hour <= 16) {
      return 'high'; // 白天人多
    } else if (hour >= 8 && hour <= 18) {
      return 'medium';
    } else {
      return 'low'; // 晚上和早上人少
    }
  }

  /**
   * 处理每小时事件
   */
  private async processHourlyEvents() {
    const hour = this.environmentState.timeOfDay;
    
    // 定时事件
    if (hour === 8) {
      await this.triggerGlobalEvent('morning_exercise', '晨练时光', 30 * 60 * 1000);
    } else if (hour === 12) {
      await this.triggerGlobalEvent('lunch_time', '午餐时间', 60 * 60 * 1000);
    } else if (hour === 18) {
      await this.triggerGlobalEvent('evening_rest', '傍晚休息', 45 * 60 * 1000);
    }

    // 随机事件
    if (Math.random() < 0.3) { // 30%概率触发随机事件
      await this.triggerRandomEvent();
    }
  }

  /**
   * 触发全局事件
   */
  private async triggerGlobalEvent(id: string, name: string, duration: number) {
    const event = {
      id,
      name,
      type: 'show' as const,
      startTime: new Date(),
      duration,
      affectedAreas: ['all']
    };

    this.environmentState.events.push(event);
    this.globalEvents.push(event);

    this.logger.log(`Global event triggered: ${name}`);

    // 通知所有动物参与事件
    await this.notifyAnimalsOfEvent(event);

    // 设置事件结束清理
    setTimeout(() => {
      this.globalEvents = this.globalEvents.filter(e => e.id !== event.id);
      this.logger.log(`Global event ended: ${name}`);
    }, duration);
  }

  /**
   * 触发随机事件
   */
  private async triggerRandomEvent() {
    const randomEvents = [
      { name: '突然雨季', effect: () => { this.environmentState.weather.condition = 'rainy'; } },
      { name: '阳光普照', effect: () => { this.environmentState.weather.condition = 'sunny'; } },
      { name: '游客激增', effect: () => { this.environmentState.crowdLevel = 'high'; } },
      { name: '安静时光', effect: () => { this.environmentState.crowdLevel = 'low'; } }
    ];

    const event = randomEvents[Math.floor(Math.random() * randomEvents.length)];
    event.effect();
    
    this.logger.log(`Random event: ${event.name}`);
  }

  /**
   * 通知动物参与事件
   */
  private async notifyAnimalsOfEvent(event: any) {
    const animals = await this.zooAnimalRepository.find({
      where: { isActive: true }
    });

    // 为参与事件的动物生成特殊行为
    const eventBehavior: AnimalBehavior = {
      type: 'socialize',
      duration: Math.min(event.duration, 15000), // 最长15秒的事件行为
      intensity: 0.8,
      animation: `event_${event.type}`,
      description: `参与${event.name}`
    };

    // 随机选择一些动物参与
    const participants = animals.filter(() => Math.random() < 0.7); // 70%的动物参与

    await Promise.all(
      participants.map(animal => 
        this.animalBehaviorService.executeBehavior(animal.id, eventBehavior)
      )
    );

    this.logger.debug(`${participants.length} animals participating in ${event.name}`);
  }

  /**
   * 获取当前环境状态
   */
  getEnvironmentState(): ZooEnvironmentState {
    return { ...this.environmentState };
  }

  /**
   * 获取动物园统计信息
   */
  async getZooStatistics() {
    const animals = await this.zooAnimalRepository.find({
      where: { isActive: true }
    });

    const totalAnimals = animals.length;
    const activeAnimals = animals.filter(a => a.currentState.activity !== 'resting' && a.currentState.activity !== 'idle').length;
    const averageHappiness = totalAnimals > 0 ? 
      animals.reduce((sum, a) => sum + a.currentState.happiness, 0) / totalAnimals : 0;
    const averageEnergy = totalAnimals > 0 ? 
      animals.reduce((sum, a) => sum + a.currentState.energy, 0) / totalAnimals : 0;

    return {
      totalAnimals,
      activeAnimals,
      averageHappiness: Math.round(averageHappiness),
      averageEnergy: Math.round(averageEnergy),
      environment: this.environmentState,
      activeEvents: this.environmentState.events.length,
      behaviorQueueSize: Array.from(this.animalBehaviorQueue.values()).reduce((sum, queue) => sum + queue.length, 0)
    };
  }

  /**
   * 手动触发动物园事件
   */
  async triggerManualEvent(eventType: string, duration = 60000): Promise<boolean> {
    try {
      await this.triggerGlobalEvent(
        `manual_${Date.now()}`,
        `手动事件：${eventType}`,
        duration
      );
      return true;
    } catch (error) {
      this.logger.error('Failed to trigger manual event:', error);
      return false;
    }
  }

  /**
   * 暂停/恢复AI调度器
   */
  setSchedulerState(running: boolean) {
    this.isRunning = running;
    this.logger.log(`Zoo AI Scheduler ${running ? 'resumed' : 'paused'}`);
  }

  /**
   * 清理资源
   */
  onModuleDestroy() {
    this.isRunning = false;
    this.animalBehaviorQueue.clear();
    this.logger.log('Zoo AI Scheduler destroyed');
  }
}