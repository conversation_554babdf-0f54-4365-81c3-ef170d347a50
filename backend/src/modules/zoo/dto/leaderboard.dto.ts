import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class LeaderboardQueryDto {
  @ApiPropertyOptional({
    description: '排行榜周期类型',
    enum: ['total', 'daily', 'weekly', 'monthly'],
    default: 'total'
  })
  @IsOptional()
  @IsEnum(['total', 'daily', 'weekly', 'monthly'])
  period?: 'total' | 'daily' | 'weekly' | 'monthly' = 'total';

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '偏移量', minimum: 0, default: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number = 0;
}

export class LeaderboardEntryDto {
  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiPropertyOptional({ description: '头像URL' })
  avatarUrl?: string;

  @ApiProperty({ description: '总积分' })
  totalScore: number;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '称号' })
  title: string;

  @ApiProperty({ description: '排名' })
  rank: number;

  @ApiProperty({ 
    description: '徽章列表',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        icon: { type: 'string' },
        rarity: { type: 'string' }
      }
    }
  })
  badges: Array<{
    name: string;
    icon: string;
    rarity: string;
  }>;

  @ApiProperty({ 
    description: '分类积分统计',
    type: 'object',
    properties: {
      interactionScore: { type: 'number' },
      workPerformanceScore: { type: 'number' },
      socialScore: { type: 'number' },
      achievementScore: { type: 'number' },
      creativityScore: { type: 'number' }
    }
  })
  stats: {
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
  };
}

export class LeaderboardResponseDto {
  @ApiProperty({ description: '排行榜列表', type: [LeaderboardEntryDto] })
  leaderboard: LeaderboardEntryDto[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '排行榜周期' })
  period: string;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class UserRankingDto {
  @ApiProperty({ description: '全球排名' })
  globalRank: number;

  @ApiProperty({ description: '每日排名' })
  dailyRank: number;

  @ApiProperty({ description: '每周排名' })
  weeklyRank: number;

  @ApiProperty({ description: '每月排名' })
  monthlyRank: number;

  @ApiProperty({ description: '总积分' })
  totalScore: number;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '称号' })
  title: string;
}

export class ScoreUpdateDto {
  @ApiProperty({ description: '积分类型', enum: ['interaction', 'work', 'social', 'achievement', 'creativity'] })
  @IsEnum(['interaction', 'work', 'social', 'achievement', 'creativity'])
  scoreType: 'interaction' | 'work' | 'social' | 'achievement' | 'creativity';

  @ApiProperty({ description: '积分数值', minimum: -1000, maximum: 10000 })
  @IsNumber()
  @Min(-1000)
  @Max(10000)
  points: number;

  @ApiPropertyOptional({ description: '经验值', minimum: 0, maximum: 5000, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5000)
  experience?: number = 0;

  @ApiPropertyOptional({ description: '积分获得原因' })
  @IsOptional()
  reason?: string;
}

export class UserScoreStatsDto {
  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '总积分' })
  totalScore: number;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '当前经验值' })
  experience: number;

  @ApiProperty({ description: '升级所需经验值' })
  experienceToNextLevel: number;

  @ApiProperty({ description: '称号' })
  title: string;

  @ApiProperty({ description: '声望值' })
  reputation: number;

  @ApiProperty({ 
    description: '分类积分',
    type: 'object',
    properties: {
      interactionScore: { type: 'number' },
      workPerformanceScore: { type: 'number' },
      socialScore: { type: 'number' },
      achievementScore: { type: 'number' },
      creativityScore: { type: 'number' }
    }
  })
  categoryScores: {
    interactionScore: number;
    workPerformanceScore: number;
    socialScore: number;
    achievementScore: number;
    creativityScore: number;
  };

  @ApiProperty({ 
    description: '排名信息',
    type: 'object',
    properties: {
      globalRank: { type: 'number' },
      dailyRank: { type: 'number' },
      weeklyRank: { type: 'number' },
      monthlyRank: { type: 'number' }
    }
  })
  rankings: {
    globalRank: number;
    dailyRank: number;
    weeklyRank: number;
    monthlyRank: number;
  };

  @ApiProperty({ 
    description: '徽章列表',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        category: { type: 'string' },
        rarity: { type: 'string' },
        earnedAt: { type: 'string', format: 'date-time' },
        icon: { type: 'string' }
      }
    }
  })
  badges: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    rarity: string;
    earnedAt: Date;
    icon: string;
  }>;

  @ApiProperty({ 
    description: '连续记录',
    type: 'object',
    properties: {
      dailyLogin: { type: 'number' },
      maxDailyLogin: { type: 'number' },
      weeklyActive: { type: 'number' },
      maxWeeklyActive: { type: 'number' },
      consecutiveWork: { type: 'number' },
      maxConsecutiveWork: { type: 'number' }
    }
  })
  streaks: {
    dailyLogin: number;
    maxDailyLogin: number;
    weeklyActive: number;
    maxWeeklyActive: number;
    consecutiveWork: number;
    maxConsecutiveWork: number;
  };

  @ApiProperty({ description: '最后更新时间' })
  lastUpdatedAt: Date;
}