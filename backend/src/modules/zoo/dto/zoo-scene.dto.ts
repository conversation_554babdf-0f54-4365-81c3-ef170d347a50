import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AnimalCategory } from '@/common/constants/animal-types';

export class ZooSceneQueryDto {
  @ApiProperty({
    description: '场景主题筛选',
    enum: AnimalCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalCategory)
  theme?: AnimalCategory;

  @ApiProperty({
    description: '时间段（小时 0-23）',
    minimum: 0,
    maximum: 23,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(23)
  timeOfDay?: number;
}

export class AnimalInSceneDto {
  @ApiProperty({ description: '动物ID' })
  id: string;

  @ApiProperty({ description: '动物种类' })
  species: string;

  @ApiProperty({ description: '动物名称' })
  name: string;

  @ApiProperty({ description: '在场景中的位置' })
  position: {
    x: number;
    y: number;
    z: number;
  };

  @ApiProperty({ description: '动物状态' })
  state: {
    mood: string;
    activity: string;
    energy: number;
    happiness: number;
  };

  @ApiProperty({ description: '头像图片URL' })
  avatarUrl: string;

  @ApiProperty({ description: '动物类型' })
  category: AnimalCategory;

  @ApiProperty({ description: '是否为用户的动物' })
  isUserAnimal: boolean;
}

export class ZooEnvironmentDto {
  @ApiProperty({ description: '天气状况' })
  weather: {
    condition: string;
    temperature: number;
    humidity: number;
    description: string;
  };

  @ApiProperty({ description: '时间信息' })
  time: {
    hour: number;
    period: string;
    season: string;
  };

  @ApiProperty({ description: '环境背景' })
  background: {
    skyColor: string;
    groundColor: string;
    lighting: string;
    ambience: string;
  };

  @ApiProperty({ description: '环境音效' })
  sounds: string[];
}

export class ZooSceneResponseDto {
  @ApiProperty({ description: '场景ID' })
  sceneId: string;

  @ApiProperty({ description: '场景名称' })
  sceneName: string;

  @ApiProperty({ description: '场景描述' })
  sceneDescription: string;

  @ApiProperty({ description: '环境信息', type: ZooEnvironmentDto })
  environment: ZooEnvironmentDto;

  @ApiProperty({ description: '场景中的动物列表', type: [AnimalInSceneDto] })
  animals: AnimalInSceneDto[];

  @ApiProperty({ description: '场景设施' })
  facilities: Array<{
    id: string;
    name: string;
    type: string;
    position: { x: number; y: number; z: number };
    description: string;
  }>;

  @ApiProperty({ description: '特殊事件' })
  events: Array<{
    id: string;
    name: string;
    description: string;
    startTime: Date;
    duration: number;
    participants: string[];
  }>;

  @ApiProperty({ description: '场景统计' })
  statistics: {
    totalAnimals: number;
    userAnimals: number;
    averageHappiness: number;
    activeAnimals: number;
  };

  @ApiProperty({ description: '最后更新时间' })
  lastUpdated: Date;
}