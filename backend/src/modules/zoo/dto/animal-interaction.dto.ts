import { IsNotEmpty, IsS<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum InteractionType {
  FEED = 'feed',           // 喂食
  PET = 'pet',             // 抚摸
  PLAY = 'play',           // 玩耍
  WORK_TOGETHER = 'work_together', // 一起工作
  ENCOURAGE = 'encourage',  // 鼓励
  REST = 'rest',           // 休息
  TRAIN = 'train',         // 训练
  CHAT = 'chat',           // 聊天
}

export class AnimalInteractionDto {
  @ApiProperty({
    description: '互动类型',
    enum: InteractionType,
  })
  @IsEnum(InteractionType)
  @IsNotEmpty()
  interactionType: InteractionType;

  @ApiProperty({
    description: '互动强度 1-10',
    minimum: 1,
    maximum: 10,
    default: 5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(10)
  intensity?: number = 5;

  @ApiProperty({
    description: '互动消息或备注',
    required: false,
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiProperty({
    description: '互动持续时间（分钟）',
    minimum: 1,
    maximum: 60,
    default: 5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(60)
  duration?: number = 5;
}

export class AnimalInteractionResponseDto {
  @ApiProperty({ description: '互动是否成功' })
  success: boolean;

  @ApiProperty({ description: '互动结果' })
  result: {
    interactionId: string;
    animalId: string;
    animalName: string;
    interactionType: InteractionType;
    effectiveness: number;
    experienceGained: number;
  };

  @ApiProperty({ description: '动物状态变化' })
  stateChanges: {
    before: {
      mood: string;
      energy: number;
      happiness: number;
      health: number;
    };
    after: {
      mood: string;
      energy: number;
      happiness: number;
      health: number;
    };
    changes: {
      mood?: string;
      energy?: number;
      happiness?: number;
      health?: number;
    };
  };

  @ApiProperty({ description: '动物反应' })
  animalReaction: {
    expression: string;
    sound: string;
    animation: string;
    responseText: string;
  };

  @ApiProperty({ description: '获得的奖励' })
  rewards: {
    coins?: number;
    experience?: number;
    items?: Array<{
      name: string;
      quantity: number;
      description: string;
    }>;
    achievements?: Array<{
      name: string;
      description: string;
      rarity: string;
    }>;
  };

  @ApiProperty({ description: '下次互动建议' })
  suggestions: {
    nextBestInteraction: InteractionType;
    cooldownTime?: number;
    tips: string[];
  };

  @ApiProperty({ description: '互动时间' })
  timestamp: Date;

  @ApiProperty({ description: '消息' })
  message: string;
}