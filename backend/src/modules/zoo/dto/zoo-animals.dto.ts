import { <PERSON><PERSON>ption<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

export class ZooAnimalsQueryDto {
  @ApiProperty({
    description: '页码',
    default: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    default: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({
    description: '动物类型筛选',
    enum: AnimalCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalCategory)
  category?: AnimalCategory;

  @ApiProperty({
    description: '动物种类筛选',
    enum: AnimalSpecies,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalSpecies)
  species?: AnimalSpecies;

  @ApiProperty({
    description: '是否只显示用户的动物',
    default: false,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  onlyUserAnimals?: boolean = false;
}

export class ZooAnimalDto {
  @ApiProperty({ description: '动物ID' })
  id: string;

  @ApiProperty({ description: '动物名称（用户自定义或默认）' })
  name: string;

  @ApiProperty({ description: '动物种类' })
  species: AnimalSpecies;

  @ApiProperty({ description: '动物类型' })
  category: AnimalCategory;

  @ApiProperty({ description: '头像图片URL' })
  avatarUrl: string;

  @ApiProperty({ description: '生成头像的base64数据' })
  avatarBase64?: string;

  @ApiProperty({ description: '动物属性' })
  attributes: {
    [key: string]: number;
  };

  @ApiProperty({ description: '当前状态' })
  currentState: {
    mood: string;
    activity: string;
    location: string;
    energy: number;
    happiness: number;
    health: number;
  };

  @ApiProperty({ description: '互动统计' })
  interactions: {
    totalInteractions: number;
    lastInteractionTime?: Date;
    favoriteActivities: string[];
  };

  @ApiProperty({ description: '工作统计' })
  workStats: {
    workEfficiency: number;
    productivity: number;
    attendance: number;
    teamwork: number;
  };

  @ApiProperty({ description: '是否为用户的动物' })
  isUserAnimal: boolean;

  @ApiProperty({ description: '所属用户昵称（如果不是隐私）' })
  ownerNickname?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '最后活动时间' })
  lastActiveAt: Date;
}

export class ZooAnimalsResponseDto {
  @ApiProperty({ description: '动物列表', type: [ZooAnimalDto] })
  animals: ZooAnimalDto[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;

  @ApiProperty({ description: '统计信息' })
  statistics: {
    totalAnimals: number;
    userAnimals: number;
    categoryCounts: Record<AnimalCategory, number>;
    averageHappiness: number;
    mostActiveSpecies: AnimalSpecies;
  };
}