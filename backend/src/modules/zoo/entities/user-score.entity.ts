import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';

@Entity('user_scores')
@Index(['userId', 'scoreType', 'period'])
@Unique(['userId', 'scoreType', 'period'])
export class UserScore {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    type: 'enum',
    enum: ['TOTAL', 'DAILY', 'WEEKLY', 'MONTHLY'],
    comment: '积分周期类型',
  })
  scoreType: 'TOTAL' | 'DAILY' | 'WEEKLY' | 'MONTHLY';

  @Column({ type: 'date', comment: '积分周期' })
  period: string;

  // === 基础积分 ===
  @Column('int', { default: 0, comment: '总积分' })
  totalScore: number;

  @Column('int', { default: 0, comment: '互动积分' })
  interactionScore: number;

  @Column('int', { default: 0, comment: '工作表现积分' })
  workPerformanceScore: number;

  @Column('int', { default: 0, comment: '社交积分' })
  socialScore: number;

  @Column('int', { default: 0, comment: '成就积分' })
  achievementScore: number;

  @Column('int', { default: 0, comment: '创造力积分' })
  creativityScore: number;

  // === 详细统计 ===
  @Column('json', { comment: '互动统计详情' })
  interactionStats: {
    totalInteractions: number;
    feedCount: number;
    petCount: number;
    playCount: number;
    workTogetherCount: number;
    restCount: number;
    encourageCount: number;
    averageEffectiveness: number;
  };

  @Column('json', { comment: '工作统计详情' })
  workStats: {
    totalWorkHours: number;
    averageEfficiency: number;
    completedTasks: number;
    promotions: number;
    teamworkScore: number;
    overtimeHours: number;
  };

  @Column('json', { comment: '社交统计详情' })
  socialStats: {
    friendsMade: number;
    helpGiven: number;
    popularityScore: number;
    leadershipScore: number;
    mentorshipScore: number;
  };

  @Column('json', { comment: '成就统计详情' })
  achievementStats: {
    totalAchievements: number;
    rareAchievements: number;
    firstTimeAchievements: number;
    challengesCompleted: number;
  };

  @Column('json', { comment: '创造力统计详情' })
  creativityStats: {
    avatarsCreated: number;
    uniqueInteractions: number;
    innovativeApproaches: number;
    artisticScore: number;
  };

  // === 附加信息 ===
  @Column('int', { default: 0, comment: '当前等级' })
  level: number;

  @Column('int', { default: 0, comment: '当前等级内经验值' })
  experience: number;

  @Column('int', { default: 0, comment: '升级所需总经验值' })
  experienceToNextLevel: number;

  @Column('int', { default: 100, comment: '声望值' })
  reputation: number;

  @Column({ length: 50, default: '新手打工人', comment: '打工人称号' })
  title: string;

  @Column('json', { comment: '徽章列表' })
  badges: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
    earnedAt: Date;
    icon: string;
  }>;

  @Column('json', { comment: '连续记录' })
  streaks: {
    dailyLogin: number;
    maxDailyLogin: number;
    weeklyActive: number;
    maxWeeklyActive: number;
    consecutiveWork: number;
    maxConsecutiveWork: number;
  };

  @Column('int', { default: 1, comment: '全球排名' })
  globalRank: number;

  @Column('int', { default: 1, comment: '每日排名' })
  dailyRank: number;

  @Column('int', { default: 1, comment: '每周排名' })
  weeklyRank: number;

  @Column('int', { default: 1, comment: '每月排名' })
  monthlyRank: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '最后更新时间' })
  lastUpdatedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}