import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { ZooAnimal } from './zoo-animal.entity';
import { InteractionType } from '../dto/animal-interaction.dto';

@Entity('animal_interaction_records')
@Index(['userId', 'createdAt'])
@Index(['animalId', 'createdAt'])
@Index(['interactionType', 'createdAt'])
export class AnimalInteractionRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'animal_id' })
  animalId: string;

  @ManyToOne(() => ZooAnimal, (animal) => animal.interactions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'animal_id' })
  animal: ZooAnimal;

  @Column({
    type: 'enum',
    enum: InteractionType,
    comment: '互动类型',
  })
  interactionType: InteractionType;

  @Column('int', { default: 5, comment: '互动强度 1-10' })
  intensity: number;

  @Column('int', { default: 5, comment: '互动持续时间（分钟）' })
  duration: number;

  @Column({ length: 200, nullable: true, comment: '互动消息或备注' })
  message?: string;

  @Column('decimal', { precision: 5, scale: 2, default: 0, comment: '互动效果评分' })
  effectiveness: number;

  @Column('int', { default: 0, comment: '获得的经验值' })
  experienceGained: number;

  @Column('json', { comment: '互动前动物状态' })
  stateBefore: {
    mood: string;
    energy: number;
    happiness: number;
    health: number;
  };

  @Column('json', { comment: '互动后动物状态' })
  stateAfter: {
    mood: string;
    energy: number;
    happiness: number;
    health: number;
  };

  @Column('json', { comment: '状态变化' })
  stateChanges: {
    mood?: string;
    energy?: number;
    happiness?: number;
    health?: number;
  };

  @Column('json', { comment: '动物反应' })
  animalReaction: {
    expression: string;
    sound: string;
    animation: string;
    responseText: string;
  };

  @Column('json', { nullable: true, comment: '获得的奖励' })
  rewards?: {
    coins?: number;
    experience?: number;
    items?: Array<{
      name: string;
      quantity: number;
      description: string;
    }>;
    achievements?: Array<{
      name: string;
      description: string;
      rarity: string;
    }>;
  };

  @Column({ default: true, comment: '是否成功的互动' })
  isSuccessful: boolean;

  @Column('json', { nullable: true, comment: '扩展数据' })
  metadata?: {
    weather?: string;
    timeOfDay?: number;
    otherParticipants?: string[];
    specialEvents?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}