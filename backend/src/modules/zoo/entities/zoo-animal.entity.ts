import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { GeneratedAvatar } from '@/modules/drawing/entities/generated-avatar.entity';
import { AnimalInteractionRecord } from './animal-interaction-record.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

@Entity('zoo_animals')
@Index(['userId', 'createdAt'])
@Index(['category', 'species'])
@Index(['isActive', 'lastActiveAt'])
export class ZooAnimal {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'avatar_id', nullable: true })
  avatarId?: string;

  @ManyToOne(() => GeneratedAvatar, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'avatar_id' })
  avatar?: GeneratedAvatar;

  @Column({ length: 100, comment: '动物名称（用户自定义或默认）' })
  name: string;

  @Column({
    type: 'enum',
    enum: AnimalSpecies,
    comment: '动物种类',
  })
  species: AnimalSpecies;

  @Column({
    type: 'enum',
    enum: AnimalCategory,
    comment: '动物类型',
  })
  category: AnimalCategory;

  @Column({ length: 500, nullable: true, comment: '头像图片URL' })
  avatarUrl?: string;

  @Column('text', { nullable: true, comment: '头像base64数据' })
  avatarBase64?: string;

  @Column('json', { comment: '动物基础属性' })
  attributes: {
    [key: string]: number;
  };

  @Column('json', { comment: '当前状态' })
  currentState: {
    mood: string;
    activity: string;
    location: string;
    energy: number;
    happiness: number;
    health: number;
    workEfficiency: number;
  };

  @Column('json', { comment: '在动物园中的位置' })
  position: {
    x: number;
    y: number;
    z: number;
    area: string;
  };

  @Column('json', { comment: '工作统计数据' })
  workStats: {
    workEfficiency: number;
    productivity: number;
    attendance: number;
    teamwork: number;
    totalWorkHours: number;
    promotions: number;
  };

  @Column('json', { comment: '互动统计' })
  interactionStats: {
    totalInteractions: number;
    dailyInteractions: number;
    weeklyInteractions: number;
    favoriteActivities: string[];
    bestFriends: string[];
  };

  @Column('json', { nullable: true, comment: '个性化设置' })
  preferences: {
    favoriteFood?: string[];
    preferredActivities?: string[];
    workPreferences?: string[];
    personality?: string[];
  };

  @Column({ default: true, comment: '是否激活状态' })
  isActive: boolean;

  @Column({ default: false, comment: '是否为明星动物' })
  isStarAnimal: boolean;

  @Column({ default: 0, comment: '等级' })
  level: number;

  @Column({ default: 0, comment: '经验值' })
  experience: number;

  @Column({ default: 100, comment: '声望值' })
  reputation: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '最后活动时间' })
  lastActiveAt: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '最后互动时间' })
  lastInteractionAt?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '最后工作时间' })
  lastWorkAt?: Date;

  @Column('json', { nullable: true, comment: '扩展元数据' })
  metadata?: {
    originalPrompt?: string;
    generationMethod?: string;
    specialTraits?: string[];
    achievements?: Array<{
      name: string;
      earnedAt: Date;
      description: string;
    }>;
  };

  @OneToMany(() => AnimalInteractionRecord, (record) => record.animal)
  interactions: AnimalInteractionRecord[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}