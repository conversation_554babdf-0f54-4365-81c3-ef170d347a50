import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { ZooController } from './zoo.controller';
import { ZooService } from './zoo.service';
import { AnimalBehaviorService } from './services/animal-behavior.service';
import { ZooAISchedulerService } from './services/zoo-ai-scheduler.service';
import { ScoringService } from './services/scoring.service';
import { ZooAnimal } from './entities/zoo-animal.entity';
import { AnimalInteractionRecord } from './entities/animal-interaction-record.entity';
import { UserScore } from './entities/user-score.entity';
import { GeneratedAvatar } from '@/modules/drawing/entities/generated-avatar.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ZooAnimal,
      AnimalInteractionRecord,
      UserScore,
      GeneratedAvatar,
    ]),
    ConfigModule,
    ScheduleModule.forRoot(), // 启用定时任务支持
  ],
  controllers: [ZooController],
  providers: [
    ZooService,
    AnimalBehaviorService,
    ZooAISchedulerService,
    ScoringService,
  ],
  exports: [
    ZooService,
    AnimalBehaviorService,
    ZooAISchedulerService,
    ScoringService,
  ],
})
export class ZooModule {}