import { Injectable, Logger, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { ZooAnimal } from './entities/zoo-animal.entity';
import { AnimalInteractionRecord } from './entities/animal-interaction-record.entity';
import { GeneratedAvatar } from '@/modules/drawing/entities/generated-avatar.entity';
import { ScoringService } from './services/scoring.service';
import {
  ZooSceneQueryDto,
  ZooSceneResponseDto,
  ZooAnimalsQueryDto,
  ZooAnimalsResponseDto,
  ZooAnimalDto,
  AnimalInteractionDto,
  AnimalInteractionResponseDto,
  InteractionType,
} from './dto';
import { AnimalCategory, AnimalSpecies, ANIMAL_CONFIG } from '@/common/constants/animal-types';

@Injectable()
export class ZooService {
  private readonly logger = new Logger(ZooService.name);

  constructor(
    @InjectRepository(ZooAnimal)
    private zooAnimalRepository: Repository<ZooAnimal>,
    @InjectRepository(AnimalInteractionRecord)
    private interactionRecordRepository: Repository<AnimalInteractionRecord>,
    @InjectRepository(GeneratedAvatar)
    private generatedAvatarRepository: Repository<GeneratedAvatar>,
    private configService: ConfigService,
    @Inject(forwardRef(() => ScoringService))
    private scoringService: ScoringService,
  ) {}

  /**
   * 获取动物园场景数据
   */
  async getZooScene(userId: string, query: ZooSceneQueryDto): Promise<ZooSceneResponseDto> {
    try {
      this.logger.log(`Fetching zoo scene for user ${userId}`);
      
      const currentHour = query.timeOfDay || new Date().getHours();
      
      // 获取场景中的动物
      const queryBuilder = this.zooAnimalRepository
        .createQueryBuilder('animal')
        .leftJoinAndSelect('animal.user', 'user')
        .leftJoinAndSelect('animal.avatar', 'avatar')
        .where('animal.isActive = :isActive', { isActive: true });
      
      // 如果指定主题，按动物类型筛选
      if (query.theme) {
        queryBuilder.andWhere('animal.category = :category', { category: query.theme });
      }
      
      // 限制动物数量，优先显示用户自己的动物
      queryBuilder
        .orderBy('CASE WHEN animal.userId = :userId THEN 0 ELSE 1 END', 'ASC')
        .addOrderBy('animal.lastActiveAt', 'DESC')
        .setParameter('userId', userId)
        .limit(50);
      
      const animals = await queryBuilder.getMany();
      
      // 转换为场景动物数据
      const sceneAnimals = animals.map((animal) => this.convertToSceneAnimal(animal, userId));
      
      // 生成环境信息
      const environment = this.generateEnvironment(currentHour, query.theme);
      
      // 生成场景设施
      const facilities = this.generateFacilities(query.theme);
      
      // 生成特殊事件
      const events = this.generateEvents(currentHour, animals.length);
      
      // 计算统计信息
      const statistics = this.calculateSceneStatistics(animals, userId);
      
      const sceneResponse: ZooSceneResponseDto = {
        sceneId: `scene_${Date.now()}`,
        sceneName: this.getSceneName(query.theme, currentHour),
        sceneDescription: this.getSceneDescription(query.theme, currentHour, animals.length),
        environment,
        animals: sceneAnimals,
        facilities,
        events,
        statistics,
        lastUpdated: new Date(),
      };
      
      this.logger.log(`Zoo scene fetched with ${animals.length} animals`);
      return sceneResponse;
      
    } catch (error) {
      this.logger.error(`Failed to fetch zoo scene for user ${userId}:`, error);
      throw new BadRequestException(`获取动物园场景失败: ${error.message}`);
    }
  }

  /**
   * 获取用户动物列表
   */
  async getZooAnimals(userId: string, query: ZooAnimalsQueryDto): Promise<ZooAnimalsResponseDto> {
    try {
      this.logger.log(`Fetching zoo animals for user ${userId}`);
      
      const queryBuilder = this.zooAnimalRepository
        .createQueryBuilder('animal')
        .leftJoinAndSelect('animal.user', 'user')
        .leftJoinAndSelect('animal.avatar', 'avatar');
      
      // 如果只显示用户动物
      if (query.onlyUserAnimals) {
        queryBuilder.where('animal.userId = :userId', { userId });
      } else {
        queryBuilder.where('animal.isActive = :isActive', { isActive: true });
      }
      
      // 按类型筛选
      if (query.category) {
        queryBuilder.andWhere('animal.category = :category', { category: query.category });
      }
      
      // 按种类筛选
      if (query.species) {
        queryBuilder.andWhere('animal.species = :species', { species: query.species });
      }
      
      // 排序：用户的动物优先，然后按最后活动时间
      if (!query.onlyUserAnimals) {
        queryBuilder.orderBy('CASE WHEN animal.userId = :userId THEN 0 ELSE 1 END', 'ASC');
        queryBuilder.setParameter('userId', userId);
      }
      queryBuilder.addOrderBy('animal.lastActiveAt', 'DESC');
      
      // 分页
      const limit = query.limit || 20;
      const page = query.page || 1;
      const skip = (page - 1) * limit;
      
      const [animals, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .getManyAndCount();
      
      // 转换为DTO格式
      const animalDtos = animals.map((animal) => this.convertToAnimalDto(animal, userId));
      
      // 生成统计信息
      const statistics = await this.generateAnimalStatistics(userId);
      
      const totalPages = Math.ceil(total / limit);
      
      const response: ZooAnimalsResponseDto = {
        animals: animalDtos,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        statistics,
      };
      
      this.logger.log(`Found ${total} zoo animals for user ${userId}`);
      return response;
      
    } catch (error) {
      this.logger.error(`Failed to fetch zoo animals for user ${userId}:`, error);
      throw new BadRequestException(`获取动物列表失败: ${error.message}`);
    }
  }

  /**
   * 与动物互动
   */
  async interactWithAnimal(
    userId: string,
    animalId: string,
    interactionDto: AnimalInteractionDto,
  ): Promise<AnimalInteractionResponseDto> {
    try {
      this.logger.log(`User ${userId} interacting with animal ${animalId}`);
      
      // 查找动物
      const animal = await this.zooAnimalRepository.findOne({
        where: { id: animalId, isActive: true },
        relations: ['user', 'avatar'],
      });
      
      if (!animal) {
        throw new NotFoundException('动物不存在或已失效');
      }
      
      // 检查互动冷却时间（防止过于频繁的互动）
      const lastInteraction = await this.interactionRecordRepository.findOne({
        where: { userId, animalId },
        order: { createdAt: 'DESC' },
      });
      
      if (lastInteraction) {
        const cooldownMinutes = this.getInteractionCooldown(interactionDto.interactionType);
        const timeSinceLastInteraction = Date.now() - lastInteraction.createdAt.getTime();
        const cooldownMs = cooldownMinutes * 60 * 1000;
        
        if (timeSinceLastInteraction < cooldownMs) {
          const remainingTime = Math.ceil((cooldownMs - timeSinceLastInteraction) / 60000);
          throw new BadRequestException(`请等待 ${remainingTime} 分钟后再进行互动`);
        }
      }
      
      // 记录互动前状态
      const stateBefore = { ...animal.currentState };
      
      // 计算互动效果
      const effectiveness = this.calculateInteractionEffectiveness(
        animal,
        interactionDto,
        userId === animal.userId
      );
      
      // 应用状态变化
      const stateChanges = this.applyInteractionEffects(animal, interactionDto, effectiveness);
      const stateAfter = { ...animal.currentState };
      
      // 生成动物反应
      const animalReaction = this.generateAnimalReaction(animal, interactionDto, effectiveness);
      
      // 计算奖励
      const rewards = this.calculateRewards(animal, interactionDto, effectiveness, userId === animal.userId);
      
      // 获得经验值
      const experienceGained = Math.floor(effectiveness * interactionDto.intensity * 10);
      animal.experience += experienceGained;
      
      // 更新动物状态
      animal.interactionStats.totalInteractions += 1;
      animal.interactionStats.dailyInteractions += 1;
      animal.lastInteractionAt = new Date();
      animal.lastActiveAt = new Date();
      
      // 更新喜好活动
      this.updateAnimalPreferences(animal, interactionDto);
      
      // 保存动物状态更新
      await this.zooAnimalRepository.save(animal);
      
      // 创建互动记录
      const interactionRecord = this.interactionRecordRepository.create({
        userId,
        animalId: animal.id,
        interactionType: interactionDto.interactionType,
        intensity: interactionDto.intensity,
        duration: interactionDto.duration,
        message: interactionDto.message,
        effectiveness,
        experienceGained,
        stateBefore,
        stateAfter,
        stateChanges,
        animalReaction,
        rewards,
        isSuccessful: effectiveness > 0.5,
        metadata: {
          weather: this.getCurrentWeather().condition,
          timeOfDay: new Date().getHours(),
        },
      } as any);
      
      const savedInteractionRecord = await this.interactionRecordRepository.save(interactionRecord);
      
      // 处理积分计算
      try {
        await this.scoringService.processInteractionScore(savedInteractionRecord);
        this.logger.log(`Processed interaction score for user ${userId}`);
      } catch (error) {
        this.logger.error(`Failed to process interaction score for user ${userId}:`, error);
        // 不抛出错误，积分计算失败不应影响正常互动
      }
      
      // 生成下次互动建议
      const suggestions = this.generateInteractionSuggestions(animal, interactionDto);
      
      const response: AnimalInteractionResponseDto = {
        success: true,
        result: {
          interactionId: interactionRecord.id,
          animalId: animal.id,
          animalName: animal.name,
          interactionType: interactionDto.interactionType,
          effectiveness,
          experienceGained,
        },
        stateChanges: {
          before: stateBefore,
          after: stateAfter,
          changes: stateChanges,
        },
        animalReaction,
        rewards,
        suggestions,
        timestamp: interactionRecord.createdAt,
        message: this.getInteractionSuccessMessage(interactionDto.interactionType, effectiveness),
      };
      
      this.logger.log(`Interaction completed: ${interactionDto.interactionType} with effectiveness ${effectiveness}`);
      return response;
      
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Interaction failed for user ${userId} and animal ${animalId}:`, error);
      throw new BadRequestException(`互动失败: ${error.message}`);
    }
  }

  /**
   * 为用户创建动物园动物（基于生成的头像）
   */
  async createZooAnimalFromAvatar(userId: string, avatarId: string, customName?: string): Promise<ZooAnimal> {
    try {
      // 查找头像
      const avatar = await this.generatedAvatarRepository.findOne({
        where: { id: avatarId, userId },
      });
      
      if (!avatar) {
        throw new NotFoundException('头像不存在');
      }
      
      // 检查是否已经创建了对应的动物
      const existingAnimal = await this.zooAnimalRepository.findOne({
        where: { userId, avatarId },
      });
      
      if (existingAnimal) {
        return existingAnimal;
      }
      
      // 创建动物园动物
      const animalConfig = ANIMAL_CONFIG[avatar.animalSpecies];
      const animalName = customName || `${animalConfig.name}_${Date.now().toString().slice(-4)}`;
      
      const zooAnimal = this.zooAnimalRepository.create({
        userId,
        avatarId: avatar.id,
        name: animalName,
        species: avatar.animalSpecies,
        category: avatar.animalType,
        avatarUrl: avatar.avatarImageUrl,
        avatarBase64: avatar.avatarImageData,
        attributes: animalConfig.attributes,
        currentState: {
          mood: this.getInitialMood(avatar.animalType),
          activity: 'resting',
          location: 'main_area',
          energy: 80,
          happiness: 70,
          health: 100,
          workEfficiency: avatar.stats.workEfficiency,
        },
        position: this.getRandomPosition('main_area'),
        workStats: {
          workEfficiency: avatar.stats.workEfficiency,
          productivity: avatar.stats.creativity,
          attendance: 90,
          teamwork: avatar.stats.happiness,
          totalWorkHours: 0,
          promotions: 0,
        },
        interactionStats: {
          totalInteractions: 0,
          dailyInteractions: 0,
          weeklyInteractions: 0,
          favoriteActivities: [],
          bestFriends: [],
        },
        preferences: this.getInitialPreferences(avatar.animalSpecies),
        level: 1,
        experience: 0,
        reputation: 50,
        metadata: {
          originalPrompt: avatar.generationPrompt,
          generationMethod: avatar.generationMethod,
          specialTraits: [],
          achievements: [],
        },
      });
      
      const savedAnimal = await this.zooAnimalRepository.save(zooAnimal);
      this.logger.log(`Created zoo animal ${savedAnimal.id} from avatar ${avatarId}`);
      
      return savedAnimal;
      
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to create zoo animal from avatar ${avatarId}:`, error);
      throw new BadRequestException(`创建动物园动物失败: ${error.message}`);
    }
  }

  // 私有辅助方法
  private convertToSceneAnimal(animal: ZooAnimal, currentUserId: string) {
    return {
      id: animal.id,
      species: animal.species,
      name: animal.name,
      position: animal.position,
      state: {
        mood: animal.currentState.mood,
        activity: animal.currentState.activity,
        energy: animal.currentState.energy,
        happiness: animal.currentState.happiness,
      },
      avatarUrl: animal.avatarUrl || `data:image/png;base64,${animal.avatarBase64}`,
      category: animal.category,
      isUserAnimal: animal.userId === currentUserId,
    };
  }

  private convertToAnimalDto(animal: ZooAnimal, currentUserId: string): ZooAnimalDto {
    const animalConfig = ANIMAL_CONFIG[animal.species];
    
    return {
      id: animal.id,
      name: animal.name,
      species: animal.species,
      category: animal.category,
      avatarUrl: animal.avatarUrl || `data:image/png;base64,${animal.avatarBase64}`,
      avatarBase64: animal.avatarBase64,
      attributes: animal.attributes,
      currentState: animal.currentState,
      interactions: {
        totalInteractions: animal.interactionStats.totalInteractions,
        lastInteractionTime: animal.lastInteractionAt,
        favoriteActivities: animal.interactionStats.favoriteActivities,
      },
      workStats: animal.workStats,
      isUserAnimal: animal.userId === currentUserId,
      ownerNickname: animal.userId === currentUserId ? undefined : animal.user?.username || '神秘用户',
      createdAt: animal.createdAt,
      lastActiveAt: animal.lastActiveAt,
    };
  }

  private generateEnvironment(hour: number, theme?: AnimalCategory) {
    const weather = this.getCurrentWeather();
    const period = hour < 6 ? 'night' : hour < 12 ? 'morning' : hour < 18 ? 'afternoon' : 'evening';
    const season = this.getCurrentSeason();
    
    return {
      weather,
      time: { hour, period, season },
      background: this.getEnvironmentBackground(hour, theme),
      sounds: this.getEnvironmentSounds(period, theme),
    };
  }

  private generateFacilities(theme?: AnimalCategory) {
    const baseFacilities = [
      { id: 'fountain', name: '智慧之泉', type: 'water', position: { x: 0, y: 0, z: 0 }, description: '为动物们提供清新的水源' },
      { id: 'food_station', name: '美食广场', type: 'feeding', position: { x: 100, y: 0, z: 0 }, description: '各种美味食物的供应点' },
      { id: 'rest_area', name: '休息区', type: 'rest', position: { x: -100, y: 0, z: 0 }, description: '舒适的休息和放松场所' },
      { id: 'playground', name: '游乐区', type: 'play', position: { x: 0, y: 100, z: 0 }, description: '充满乐趣的游戏设施' },
    ];
    
    if (theme === AnimalCategory.DIVINE_BEAST) {
      baseFacilities.push({ id: 'temple', name: '神兽圣殿', type: 'special', position: { x: 0, y: 0, z: 50 }, description: '神兽们的专属神圣场所' });
    } else if (theme === AnimalCategory.PET) {
      baseFacilities.push({ id: 'toy_area', name: '玩具天地', type: 'play', position: { x: 50, y: 50, z: 0 }, description: '各种有趣玩具的聚集地' });
    } else if (theme === AnimalCategory.WORKING_ANIMAL) {
      baseFacilities.push({ id: 'office', name: '办公区', type: 'work', position: { x: 0, y: -100, z: 0 }, description: '认真工作的办公场所' });
    }
    
    return baseFacilities;
  }

  private generateEvents(hour: number, animalCount: number) {
    const events = [];
    
    if (hour >= 8 && hour <= 10) {
      events.push({
        id: 'morning_exercise',
        name: '晨练时光',
        description: '所有动物一起进行晨练活动',
        startTime: new Date(),
        duration: 30,
        participants: [],
      });
    }
    
    if (hour >= 12 && hour <= 13) {
      events.push({
        id: 'lunch_time',
        name: '午餐时间',
        description: '动物们聚集在美食广场享用午餐',
        startTime: new Date(),
        duration: 60,
        participants: [],
      });
    }
    
    if (animalCount > 20) {
      events.push({
        id: 'animal_festival',
        name: '动物狂欢节',
        description: '庆祝动物园繁荣的特殊活动',
        startTime: new Date(),
        duration: 120,
        participants: [],
      });
    }
    
    return events;
  }

  private calculateSceneStatistics(animals: ZooAnimal[], userId: string) {
    const userAnimals = animals.filter(a => a.userId === userId);
    const totalHappiness = animals.reduce((sum, a) => sum + a.currentState.happiness, 0);
    const activeAnimals = animals.filter(a => a.currentState.activity !== 'sleeping' && a.currentState.activity !== 'resting').length;
    
    return {
      totalAnimals: animals.length,
      userAnimals: userAnimals.length,
      averageHappiness: animals.length ? Math.round(totalHappiness / animals.length) : 0,
      activeAnimals,
    };
  }

  private async generateAnimalStatistics(userId: string) {
    const allAnimalsQuery = this.zooAnimalRepository.createQueryBuilder('animal')
      .where('animal.isActive = true');
    
    const userAnimalsQuery = this.zooAnimalRepository.createQueryBuilder('animal')
      .where('animal.userId = :userId AND animal.isActive = true', { userId });
    
    const [allAnimals, userAnimals] = await Promise.all([
      allAnimalsQuery.getMany(),
      userAnimalsQuery.getMany()
    ]);
    
    // 按类型统计
    const categoryCounts = {
      [AnimalCategory.DIVINE_BEAST]: allAnimals.filter(a => a.category === AnimalCategory.DIVINE_BEAST).length,
      [AnimalCategory.PET]: allAnimals.filter(a => a.category === AnimalCategory.PET).length,
      [AnimalCategory.WORKING_ANIMAL]: allAnimals.filter(a => a.category === AnimalCategory.WORKING_ANIMAL).length,
    };
    
    // 计算平均幸福度
    const totalHappiness = allAnimals.reduce((sum, a) => sum + a.currentState.happiness, 0);
    const averageHappiness = allAnimals.length ? Math.round(totalHappiness / allAnimals.length) : 0;
    
    // 找出最活跃的种类
    const speciesCounts: Record<string, number> = {};
    allAnimals.forEach(animal => {
      speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;
    });
    const mostActiveSpecies = Object.entries(speciesCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as AnimalSpecies || AnimalSpecies.OX;
    
    return {
      totalAnimals: allAnimals.length,
      userAnimals: userAnimals.length,
      categoryCounts,
      averageHappiness,
      mostActiveSpecies,
    };
  }

  private getSceneName(theme?: AnimalCategory, hour?: number) {
    const period = !hour ? '动物园' : hour < 6 ? '夜晚动物园' : hour < 12 ? '晨光动物园' : hour < 18 ? '阳光动物园' : '黄昏动物园';
    
    if (theme === AnimalCategory.DIVINE_BEAST) return `神兽${period}`;
    if (theme === AnimalCategory.PET) return `宠物${period}`;
    if (theme === AnimalCategory.WORKING_ANIMAL) return `打工${period}`;
    
    return `牛马${period}`;
  }

  private getSceneDescription(theme?: AnimalCategory, hour?: number, animalCount?: number) {
    const baseDesc = `这里是繁华的牛马动物园，目前有${animalCount || 0}只可爱的动物们在此生活和工作。`;
    
    if (theme === AnimalCategory.DIVINE_BEAST) {
      return baseDesc + '神兽们散发着神圣的光芒，在此修行和指导着其他动物。';
    } else if (theme === AnimalCategory.PET) {
      return baseDesc + '宠物们在这里快乐地玩耍，给整个动物园带来温馨的氛围。';
    } else if (theme === AnimalCategory.WORKING_ANIMAL) {
      return baseDesc + '勤劳的打工动物们在这里努力工作，展现着不屈不挠的精神。';
    }
    
    return baseDesc + '每一只动物都有着自己独特的故事和梦想。';
  }

  private getInitialMood(category: AnimalCategory): string {
    switch (category) {
      case AnimalCategory.DIVINE_BEAST: return 'serene';
      case AnimalCategory.PET: return 'playful';
      case AnimalCategory.WORKING_ANIMAL: return 'determined';
      default: return 'content';
    }
  }

  private getRandomPosition(area: string) {
    return {
      x: Math.floor(Math.random() * 400) - 200,
      y: Math.floor(Math.random() * 400) - 200,
      z: 0,
      area,
    };
  }

  private getInitialPreferences(species: AnimalSpecies) {
    const config = ANIMAL_CONFIG[species];
    return {
      favoriteFood: ['grass', 'water'],
      preferredActivities: ['rest', 'work'],
      workPreferences: ['teamwork'],
      personality: [config.description.substring(0, 10)],
    };
  }

  private calculateInteractionEffectiveness(
    animal: ZooAnimal,
    interaction: AnimalInteractionDto,
    isOwner: boolean
  ): number {
    let base = 0.5;
    
    // 拥有者互动更有效
    if (isOwner) base += 0.2;
    
    // 根据动物当前状态调整
    if (animal.currentState.energy < 30 && interaction.interactionType === InteractionType.REST) {
      base += 0.3;
    }
    if (animal.currentState.happiness < 50 && interaction.interactionType === InteractionType.PLAY) {
      base += 0.2;
    }
    if (animal.currentState.health < 70 && interaction.interactionType === InteractionType.FEED) {
      base += 0.25;
    }
    
    // 强度影响
    base += (interaction.intensity - 5) * 0.05;
    
    // 添加随机因素
    base += (Math.random() - 0.5) * 0.2;
    
    return Math.max(0, Math.min(1, base));
  }

  private applyInteractionEffects(
    animal: ZooAnimal,
    interaction: AnimalInteractionDto,
    effectiveness: number
  ): any {
    const changes: any = {};
    const multiplier = effectiveness * interaction.intensity / 5;
    
    switch (interaction.interactionType) {
      case InteractionType.FEED:
        changes.health = Math.min(100, animal.currentState.health + 15 * multiplier);
        changes.happiness = Math.min(100, animal.currentState.happiness + 10 * multiplier);
        break;
      case InteractionType.PLAY:
        changes.happiness = Math.min(100, animal.currentState.happiness + 20 * multiplier);
        changes.energy = Math.max(0, animal.currentState.energy - 10 * multiplier);
        break;
      case InteractionType.REST:
        changes.energy = Math.min(100, animal.currentState.energy + 25 * multiplier);
        break;
      case InteractionType.PET:
        changes.happiness = Math.min(100, animal.currentState.happiness + 15 * multiplier);
        break;
      case InteractionType.WORK_TOGETHER:
        changes.workEfficiency = Math.min(100, animal.currentState.workEfficiency + 5 * multiplier);
        changes.energy = Math.max(0, animal.currentState.energy - 15 * multiplier);
        break;
    }
    
    // 应用变化到动物状态
    Object.keys(changes).forEach(key => {
      animal.currentState[key] = changes[key];
    });
    
    return changes;
  }

  private generateAnimalReaction(animal: ZooAnimal, interaction: AnimalInteractionDto, effectiveness: number) {
    const reactions = {
      high: {
        expression: 'delighted',
        sound: 'happy_sound',
        animation: 'jump_joy',
        responseText: `${animal.name}看起来非常开心！`
      },
      medium: {
        expression: 'content',
        sound: 'content_sound',
        animation: 'gentle_move',
        responseText: `${animal.name}感到很舒适。`
      },
      low: {
        expression: 'neutral',
        sound: 'neutral_sound',
        animation: 'slight_move',
        responseText: `${animal.name}注意到了你的互动。`
      }
    };
    
    if (effectiveness > 0.7) return reactions.high;
    if (effectiveness > 0.4) return reactions.medium;
    return reactions.low;
  }

  private calculateRewards(animal: ZooAnimal, interaction: AnimalInteractionDto, effectiveness: number, isOwner: boolean) {
    const baseReward = Math.floor(effectiveness * 100);
    
    return {
      coins: isOwner ? baseReward : Math.floor(baseReward * 0.5),
      experience: Math.floor(effectiveness * interaction.intensity * 20),
      items: effectiveness > 0.8 ? [{ name: '友谊徽章', quantity: 1, description: '与动物建立深厚友谊的证明' }] : [],
      achievements: effectiveness > 0.9 ? [{ name: '动物好友', description: '与动物建立了极佳的关系', rarity: 'rare' }] : [],
    };
  }

  private generateInteractionSuggestions(animal: ZooAnimal, lastInteraction: AnimalInteractionDto) {
    let nextBest = InteractionType.PET;
    
    if (animal.currentState.energy < 40) nextBest = InteractionType.REST;
    else if (animal.currentState.happiness < 50) nextBest = InteractionType.PLAY;
    else if (animal.currentState.health < 70) nextBest = InteractionType.FEED;
    
    return {
      nextBestInteraction: nextBest,
      cooldownTime: this.getInteractionCooldown(lastInteraction.interactionType),
      tips: this.getInteractionTips(animal, nextBest),
    };
  }

  private getInteractionCooldown(type: InteractionType): number {
    const cooldowns = {
      [InteractionType.FEED]: 30,
      [InteractionType.PET]: 5,
      [InteractionType.PLAY]: 15,
      [InteractionType.REST]: 60,
      [InteractionType.WORK_TOGETHER]: 45,
      [InteractionType.ENCOURAGE]: 10,
      [InteractionType.TRAIN]: 30,
      [InteractionType.CHAT]: 5,
    };
    return cooldowns[type] || 10;
  }

  private getInteractionTips(animal: ZooAnimal, suggestedType: InteractionType): string[] {
    const tips = [];
    
    if (animal.currentState.energy < 50) {
      tips.push('动物看起来有些疲惫，建议让它休息一下');
    }
    if (animal.currentState.happiness < 60) {
      tips.push('动物的心情不太好，试试和它玩耍');
    }
    if (animal.currentState.health < 80) {
      tips.push('动物需要营养，给它一些食物吧');
    }
    
    return tips.length > 0 ? tips : ['动物状态良好，可以进行任何互动'];
  }

  private getInteractionSuccessMessage(type: InteractionType, effectiveness: number): string {
    const typeMessages = {
      [InteractionType.FEED]: '喂食',
      [InteractionType.PET]: '抚摸',
      [InteractionType.PLAY]: '玩耍',
      [InteractionType.REST]: '休息',
      [InteractionType.WORK_TOGETHER]: '工作',
      [InteractionType.ENCOURAGE]: '鼓励',
      [InteractionType.TRAIN]: '训练',
      [InteractionType.CHAT]: '聊天',
    };
    
    const effectivenessSuffix = effectiveness > 0.8 ? '非常成功！' : effectiveness > 0.5 ? '成功！' : '还不错！';
    
    return `${typeMessages[type]}互动${effectivenessSuffix}`;
  }

  private updateAnimalPreferences(animal: ZooAnimal, interaction: AnimalInteractionDto) {
    const activities = animal.interactionStats.favoriteActivities;
    const activityName = interaction.interactionType;
    
    const existingIndex = activities.indexOf(activityName);
    if (existingIndex > -1) {
      // 移到最前面
      activities.splice(existingIndex, 1);
      activities.unshift(activityName);
    } else {
      // 添加新活动
      activities.unshift(activityName);
      if (activities.length > 5) {
        activities.pop();
      }
    }
  }

  // 环境相关的辅助方法
  private getCurrentWeather() {
    const weathers = [
      { condition: 'sunny', temperature: 25, humidity: 60, description: '阳光明媚' },
      { condition: 'cloudy', temperature: 22, humidity: 70, description: '多云' },
      { condition: 'rainy', temperature: 18, humidity: 85, description: '细雨绵绵' },
    ];
    return weathers[Math.floor(Math.random() * weathers.length)];
  }

  private getCurrentSeason(): string {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  private getEnvironmentBackground(hour: number, theme?: AnimalCategory) {
    const timeBasedColors = {
      skyColor: hour < 6 ? '#1a1a2e' : hour < 18 ? '#87ceeb' : '#ff6b35',
      groundColor: '#8fbc8f',
      lighting: hour < 6 ? 'moonlight' : hour < 18 ? 'sunlight' : 'sunset',
      ambience: hour < 6 ? 'peaceful_night' : hour < 12 ? 'fresh_morning' : hour < 18 ? 'vibrant_day' : 'warm_evening',
    };
    
    return timeBasedColors;
  }

  private getEnvironmentSounds(period: string, theme?: AnimalCategory): string[] {
    const baseSounds = ['bird_chirping', 'wind_rustling', 'water_flowing'];
    
    if (period === 'night') {
      baseSounds.push('cricket_sounds', 'owl_hooting');
    } else {
      baseSounds.push('animal_chatter', 'footsteps');
    }
    
    if (theme === AnimalCategory.DIVINE_BEAST) {
      baseSounds.push('mystical_chimes');
    } else if (theme === AnimalCategory.PET) {
      baseSounds.push('playful_barks');
    } else if (theme === AnimalCategory.WORKING_ANIMAL) {
      baseSounds.push('work_sounds');
    }
    
    return baseSounds;
  }
}