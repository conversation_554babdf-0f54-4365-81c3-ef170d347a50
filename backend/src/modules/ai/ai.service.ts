import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { OpenAI } from 'openai';
import { createCanvas, loadImage } from 'canvas';
import * as sharp from 'sharp';
import axios from 'axios';
import {
  AnimalCategory,
  AnimalSpecies,
  ANIMAL_CONFIG,
} from '@/common/constants/animal-types';

export interface AnalysisResult {
  description: string;
  faceFeatures: {
    faceShape: string;
    expression: string;
    style: string;
    complexity: string;
    lineCount?: number;
    darkRatio?: number;
  };
  pixelAnalysis?: {
    width: number;
    height: number;
    aspectRatio: number;
    lineComplexity: number;
    dominantColors: string[];
  };
  confidence: number;
  detectedAnimalType: AnimalCategory;
  recommendedSpecies: AnimalSpecies;
}

export interface AvatarGenerationResult {
  imageBase64: string;
  imageUrl?: string;
  generationMethod: string;
  processingTime: number;
  stats: {
    workEfficiency: number;
    happiness: number;
    energy: number;
    creativity: number;
  };
  prompt?: string;
}

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private genAI: GoogleGenerativeAI;
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    // 从配置中获取AI服务设置
    const aiConfig = this.configService.get('ai');
    const geminiApiKey = aiConfig?.gemini?.apiKey || this.configService.get<string>('GEMINI_API_KEY');
    const openaiApiKey = aiConfig?.openai?.apiKey || this.configService.get<string>('OPENAI_API_KEY');

    if (geminiApiKey) {
      this.genAI = new GoogleGenerativeAI(geminiApiKey);
      this.logger.log('Gemini AI service initialized');
    } else {
      this.logger.warn('Gemini API key not found');
    }

    if (openaiApiKey) {
      this.openai = new OpenAI({ 
        apiKey: openaiApiKey,
        timeout: aiConfig?.general?.defaultTimeout || 30000,
        maxRetries: aiConfig?.general?.maxRetries || 3,
      });
      this.logger.log('OpenAI service initialized');
    } else {
      this.logger.warn('OpenAI API key not found');
    }

    this.logger.log(`AI Service initialized with fallback: ${aiConfig?.general?.fallbackToCanvas !== false}`);
  }

  /**
   * 使用Gemini AI分析用户绘画
   */
  async analyzeDrawing(imageData: string): Promise<AnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 尝试使用Gemini API分析
      if (this.genAI) {
        this.logger.log('Using Gemini API for drawing analysis');
        const result = await this.analyzeWithGemini(imageData);
        const processingTime = Date.now() - startTime;
        this.logger.log(`Gemini analysis completed in ${processingTime}ms`);
        return result;
      }
    } catch (error) {
      this.logger.warn('Gemini API analysis failed, falling back to pixel analysis:', error.message);
    }

    // 回退到像素分析
    this.logger.log('Using fallback pixel analysis');
    const result = await this.analyzeWithPixelData(imageData);
    const processingTime = Date.now() - startTime;
    this.logger.log(`Pixel analysis completed in ${processingTime}ms`);
    return result;
  }

  /**
   * 生成动物头像
   */
  async generateAvatar(
    imageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult
  ): Promise<AvatarGenerationResult> {
    const startTime = Date.now();
    
    try {
      // 优先使用DALL-E生成
      if (this.openai) {
        this.logger.log('Attempting avatar generation with DALL-E 3');
        const result = await this.generateWithDallE(imageData, animalType, animalSpecies, analysisResult);
        const processingTime = Date.now() - startTime;
        this.logger.log(`DALL-E generation completed in ${processingTime}ms`);
        return { ...result, processingTime };
      }
    } catch (error) {
      this.logger.warn('DALL-E generation failed, falling back to Canvas:', error.message);
    }

    // 回退到Canvas生成
    this.logger.log('Using Canvas fallback generation');
    const result = await this.generateWithCanvas(imageData, animalType, animalSpecies, analysisResult);
    const processingTime = Date.now() - startTime;
    this.logger.log(`Canvas generation completed in ${processingTime}ms`);
    return { ...result, processingTime };
  }

  /**
   * 生成打工人动物版头像（深度融合用户自画像与动物特征）
   */
  async generateWorkerAnimalAvatar(
    userImageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult,
    options?: {
      fusionStrength?: number; // 融合强度 0-1
      keepUserFeatures?: boolean; // 是否保留用户特征
      workEnvironment?: 'office' | 'factory' | 'field' | 'creative'; // 工作环境
      clothingStyle?: 'formal' | 'casual' | 'uniform' | 'creative'; // 服装风格
    }
  ): Promise<AvatarGenerationResult> {
    const startTime = Date.now();
    const fusionOptions = {
      fusionStrength: 0.7,
      keepUserFeatures: true,
      workEnvironment: 'office' as const,
      clothingStyle: 'formal' as const,
      ...options,
    };
    
    this.logger.log(`Generating worker animal avatar: ${animalType} with fusion strength ${fusionOptions.fusionStrength}`);
    
    try {
      // 首先分析用户自画像（如果还没有分析结果）
      let userAnalysis = analysisResult;
      if (!userAnalysis) {
        userAnalysis = await this.analyzeDrawing(userImageData);
      }
      
      // 尝试使用Gemini 2.0 Flash进行图像生成（如果支持）
      if (this.genAI) {
        try {
          const geminiResult = await this.generateWithGeminiFlash(
            userImageData, 
            animalType, 
            animalSpecies, 
            userAnalysis, 
            fusionOptions
          );
          if (geminiResult) {
            const processingTime = Date.now() - startTime;
            this.logger.log(`Gemini Flash generation completed in ${processingTime}ms`);
            return { ...geminiResult, processingTime };
          }
        } catch (error) {
          this.logger.warn('Gemini Flash generation failed:', error.message);
        }
      }
      
      // 使用DALL-E 3生成高质量融合图像
      if (this.openai) {
        try {
          const dalleResult = await this.generateWorkerAvatarWithDallE(
            userImageData,
            animalType,
            animalSpecies,
            userAnalysis,
            fusionOptions
          );
          const processingTime = Date.now() - startTime;
          this.logger.log(`DALL-E worker avatar generation completed in ${processingTime}ms`);
          return { ...dalleResult, processingTime };
        } catch (error) {
          this.logger.warn('DALL-E worker avatar generation failed:', error.message);
        }
      }
      
      // 最终回退到增强Canvas生成
      const canvasResult = await this.generateWorkerAvatarWithCanvas(
        userImageData,
        animalType,
        animalSpecies,
        userAnalysis,
        fusionOptions
      );
      const processingTime = Date.now() - startTime;
      this.logger.log(`Canvas worker avatar generation completed in ${processingTime}ms`);
      return { ...canvasResult, processingTime };
      
    } catch (error) {
      this.logger.error('Worker animal avatar generation failed:', error);
      throw new Error(`动物版头像生成失败: ${error.message}`);
    }
  }

  private async analyzeWithGemini(imageData: string): Promise<AnalysisResult> {
    const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const userDrawingBase64 = imageData.split(',')[1] || imageData;
    const imagePart = {
      inlineData: {
        data: userDrawingBase64,
        mimeType: 'image/png'
      }
    };

    const prompt = `请仔细分析这幅用户自画像，描述以下特征：
1. 面部形状（圆脸、长脸、方脸等）
2. 表情特征（微笑、严肃、友善等）
3. 绘画风格（简约、细腻、素描等）
4. 个性特征（通过画风推测性格）
5. 线条特点和绘画复杂度

请用中文回答，格式简洁清晰。

基于以上特征，推荐最适合的动物类型：
- 如果体现出高雅、智慧、领导力特质，推荐神兽类（DIVINE_BEAST）
- 如果体现出可爱、友善、活泼特质，推荐宠物类（PET）
- 如果体现出勤劳、坚韧、务实特质，推荐牛马类（WORKING_ANIMAL）`;

    const result = await model.generateContent([prompt, imagePart]);
    const response = await result.response;
    const analysisText = response.text();

    // 解析Gemini分析结果
    const faceFeatures = this.extractFaceFeatures(analysisText);
    const detectedAnimalType = this.detectAnimalType(analysisText);
    const recommendedSpecies = this.getRecommendedSpecies(detectedAnimalType, faceFeatures);

    return {
      description: analysisText,
      faceFeatures,
      confidence: 85,
      detectedAnimalType,
      recommendedSpecies,
    };
  }

  private async analyzeWithPixelData(imageData: string): Promise<AnalysisResult> {
    const userDrawingBase64 = imageData.split(',')[1] || imageData;
    const imageBuffer = Buffer.from(userDrawingBase64, 'base64');
    
    // 加载图像分析像素
    const img = await loadImage(imageBuffer);
    const canvas = createCanvas(img.width, img.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0);
    
    const imageDataObj = ctx.getImageData(0, 0, img.width, img.height);
    const pixelAnalysis = this.analyzePixelFeatures(imageDataObj.data, img.width, img.height);
    
    const faceFeatures = this.extractFeaturesFromPixels(pixelAnalysis);
    const detectedAnimalType = this.detectAnimalTypeFromFeatures(faceFeatures);
    const recommendedSpecies = this.getRecommendedSpecies(detectedAnimalType, faceFeatures);

    return {
      description: this.generateAnalysisDescription(faceFeatures, pixelAnalysis),
      faceFeatures,
      pixelAnalysis,
      confidence: 70,
      detectedAnimalType,
      recommendedSpecies,
    };
  }

  private analyzePixelFeatures(pixels: Uint8ClampedArray, width: number, height: number) {
    const totalPixels = width * height;
    let darkPixels = 0;
    let edgePixels = 0;
    
    const colorCount: Record<string, number> = {};
    
    // 分析每个像素
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      
      const brightness = (r + g + b) / 3;
      if (brightness < 128) {
        darkPixels++;
      }
      
      const colorKey = `${Math.floor(r/32)}_${Math.floor(g/32)}_${Math.floor(b/32)}`;
      colorCount[colorKey] = (colorCount[colorKey] || 0) + 1;
    }

    // 计算边缘检测
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const current = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / 3;
        
        const neighbors = [
          (pixels[((y-1) * width + x) * 4] + pixels[((y-1) * width + x) * 4 + 1] + pixels[((y-1) * width + x) * 4 + 2]) / 3,
          (pixels[(y * width + (x-1)) * 4] + pixels[(y * width + (x-1)) * 4 + 1] + pixels[(y * width + (x-1)) * 4 + 2]) / 3,
          (pixels[(y * width + (x+1)) * 4] + pixels[(y * width + (x+1)) * 4 + 1] + pixels[(y * width + (x+1)) * 4 + 2]) / 3,
          (pixels[((y+1) * width + x) * 4] + pixels[((y+1) * width + x) * 4 + 1] + pixels[((y+1) * width + x) * 4 + 2]) / 3
        ];
        
        const maxDiff = Math.max(...neighbors.map(n => Math.abs(current - n)));
        if (maxDiff > 50) {
          edgePixels++;
        }
      }
    }

    const darkRatio = darkPixels / totalPixels;
    const aspectRatio = width / height;
    const lineComplexity = edgePixels / totalPixels;
    const uniqueColors = Object.keys(colorCount).length;
    
    const dominantColors = Object.entries(colorCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([color]) => color);

    return {
      width,
      height,
      darkRatio,
      aspectRatio,
      lineComplexity,
      uniqueColors,
      dominantColors,
      edgePixels,
    };
  }

  private extractFaceFeatures(analysisText: string) {
    const features = {
      faceShape: 'oval',
      expression: 'neutral',
      style: 'sketch',
      complexity: 'medium',
    };

    // 从分析文本中提取特征
    if (analysisText.includes('圆脸')) features.faceShape = 'round';
    else if (analysisText.includes('方脸')) features.faceShape = 'square';
    else if (analysisText.includes('长脸')) features.faceShape = 'long';
    else if (analysisText.includes('瓜子脸')) features.faceShape = 'heart';
    
    if (analysisText.includes('疲惫')) features.expression = 'tired';
    else if (analysisText.includes('活力') || analysisText.includes('精神')) features.expression = 'energetic';
    else if (analysisText.includes('专注') || analysisText.includes('认真')) features.expression = 'focused';
    else if (analysisText.includes('友善') || analysisText.includes('温和')) features.expression = 'gentle';
    else if (analysisText.includes('微笑') || analysisText.includes('开心')) features.expression = 'happy';
    
    if (analysisText.includes('简约')) features.style = 'minimalist';
    else if (analysisText.includes('写实')) features.style = 'realistic';
    else if (analysisText.includes('卡通')) features.style = 'cartoon';
    
    if (analysisText.includes('复杂') || analysisText.includes('详细')) features.complexity = 'high';
    else if (analysisText.includes('简单') || analysisText.includes('简洁')) features.complexity = 'low';

    return features;
  }

  private extractFeaturesFromPixels(pixelAnalysis: any) {
    const features = {
      faceShape: 'oval',
      expression: 'neutral',
      style: 'sketch',
      complexity: 'medium',
      lineCount: pixelAnalysis.edgePixels,
      darkRatio: pixelAnalysis.darkRatio,
    };

    // 基于像素分析推测特征
    if (pixelAnalysis.aspectRatio > 1.3) {
      features.faceShape = 'long';
    } else if (pixelAnalysis.aspectRatio < 0.8) {
      features.faceShape = 'wide';
    } else if (pixelAnalysis.lineComplexity < 0.1) {
      features.faceShape = 'round';
    }

    if (pixelAnalysis.lineComplexity > 0.4) {
      features.expression = 'focused';
      features.complexity = 'high';
    } else if (pixelAnalysis.lineComplexity > 0.2) {
      features.complexity = 'medium';
    } else {
      features.expression = 'gentle';
      features.complexity = 'low';
    }

    if (pixelAnalysis.darkRatio > 0.5) {
      features.expression = 'tired';
    } else if (pixelAnalysis.darkRatio < 0.2) {
      features.expression = 'energetic';
    }

    if (pixelAnalysis.uniqueColors > 20) {
      features.style = 'realistic';
    } else if (pixelAnalysis.uniqueColors < 10) {
      features.style = 'minimalist';
    }

    return features;
  }

  private detectAnimalType(analysisText: string): AnimalCategory {
    if (analysisText.includes('神兽') || analysisText.includes('DIVINE_BEAST') || 
        analysisText.includes('高雅') || analysisText.includes('智慧') || 
        analysisText.includes('领导')) {
      return AnimalCategory.DIVINE_BEAST;
    }
    
    if (analysisText.includes('宠物') || analysisText.includes('PET') ||
        analysisText.includes('可爱') || analysisText.includes('友善') ||
        analysisText.includes('活泼')) {
      return AnimalCategory.PET;
    }
    
    return AnimalCategory.WORKING_ANIMAL;
  }

  private detectAnimalTypeFromFeatures(faceFeatures: any): AnimalCategory {
    // 基于特征推测动物类型
    if (faceFeatures.expression === 'energetic' && faceFeatures.style === 'realistic' && faceFeatures.complexity === 'high') {
      return AnimalCategory.DIVINE_BEAST;
    }
    
    if (faceFeatures.expression === 'happy' || faceFeatures.expression === 'gentle') {
      return AnimalCategory.PET;
    }
    
    return AnimalCategory.WORKING_ANIMAL;
  }

  private getRecommendedSpecies(animalType: AnimalCategory, features: any): AnimalSpecies {
    switch (animalType) {
      case AnimalCategory.DIVINE_BEAST:
        return features.expression === 'energetic' ? AnimalSpecies.DRAGON : AnimalSpecies.QILIN_DEER;
      
      case AnimalCategory.PET:
        return features.faceShape === 'round' ? AnimalSpecies.GOLDEN_RETRIEVER : AnimalSpecies.PERSIAN_CAT;
      
      case AnimalCategory.WORKING_ANIMAL:
      default:
        if (features.expression === 'focused') return AnimalSpecies.HORSE;
        if (features.complexity === 'high') return AnimalSpecies.OX;
        return AnimalSpecies.DONKEY;
    }
  }

  private generateAnalysisDescription(faceFeatures: any, pixelAnalysis: any): string {
    const complexityDesc = faceFeatures.complexity === 'high' ? '线条丰富，细节详细' : 
                          faceFeatures.complexity === 'low' ? '线条简洁，风格简约' : '线条适中，层次分明';
    
    return `[智能像素分析] 面部形状：${faceFeatures.faceShape}；表情神态：${faceFeatures.expression}；` +
           `绘画风格：${faceFeatures.style}；${complexityDesc}。` +
           `图像尺寸：${pixelAnalysis.width}x${pixelAnalysis.height}，暗像素比例：${(pixelAnalysis.darkRatio * 100).toFixed(1)}%，` +
           `线条复杂度：${(pixelAnalysis.lineComplexity * 100).toFixed(1)}%。` +
           `这幅自画像很好地展现了用户的个人特色，非常适合与动物头像进行深度融合创作。`;
  }

  private async generateWithDallE(
    imageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult
  ): Promise<Omit<AvatarGenerationResult, 'processingTime'>> {
    const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
    const animalConfig = ANIMAL_CONFIG[species];
    
    const prompt = this.buildDallEPrompt(animalType, species, animalConfig, analysisResult);
    
    this.logger.log(`Generating with DALL-E 3, prompt: ${prompt.substring(0, 100)}...`);
    
    const response = await this.openai.images.generate({
      model: 'dall-e-3',
      prompt,
      n: 1,
      size: '1024x1024',
      quality: 'standard',
      style: 'natural'
    });

    const imageUrl = response.data[0].url;
    const imageBase64 = await this.convertUrlToBase64(imageUrl);
    const sketchBase64 = await this.convertToSketch(imageBase64);
    
    return {
      imageBase64: sketchBase64,
      imageUrl,
      generationMethod: 'OpenAI DALL-E 3',
      stats: this.generateWorkerStats(),
      prompt,
    };
  }

  private async generateWithCanvas(
    imageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult
  ): Promise<Omit<AvatarGenerationResult, 'processingTime'>> {
    const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
    const animalConfig = ANIMAL_CONFIG[species];
    
    this.logger.log(`Generating with Canvas for ${species}`);
    
    const canvas = createCanvas(512, 768);
    const ctx = canvas.getContext('2d');
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 512, 768);
    
    // 绘制基础身体
    this.drawProfessionalBody(ctx);
    
    // 绘制动物头部
    this.drawAnimalHead(ctx, animalType, species, analysisResult?.faceFeatures);
    
    // 应用素描纹理
    this.applySketchTexture(ctx);
    
    const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
    
    return {
      imageBase64,
      generationMethod: 'Canvas Local Generation',
      stats: this.generateWorkerStats(),
    };
  }

  private buildDallEPrompt(
    animalType: AnimalCategory,
    species: AnimalSpecies,
    animalConfig: any,
    analysisResult?: AnalysisResult
  ): string {
    const animalName = animalConfig.name;
    const basePrompt = `Professional pencil sketch portrait of a single office worker with ${animalName} head and human body, ` +
                      `standing upright in formal business suit, ` +
                      `black and white line art style, clean sketch lines, ` +
                      `single character full body view, no duplicates, only one person`;
    
    if (analysisResult?.faceFeatures) {
      const features = analysisResult.faceFeatures;
      return `${basePrompt}, ${features.faceShape} face shape, ${features.expression} expression, ${features.style} artistic approach`;
    }
    
    return basePrompt;
  }

  private getDefaultSpecies(animalType: AnimalCategory): AnimalSpecies {
    switch (animalType) {
      case AnimalCategory.DIVINE_BEAST:
        return AnimalSpecies.DRAGON;
      case AnimalCategory.PET:
        return AnimalSpecies.GOLDEN_RETRIEVER;
      case AnimalCategory.WORKING_ANIMAL:
      default:
        return AnimalSpecies.OX;
    }
  }

  private generateWorkerStats() {
    return {
      workEfficiency: Math.floor(Math.random() * 40) + 60,
      happiness: Math.floor(Math.random() * 40) + 40,
      energy: Math.floor(Math.random() * 40) + 50,
      creativity: Math.floor(Math.random() * 40) + 45
    };
  }

  private async convertUrlToBase64(url: string): Promise<string> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data).toString('base64');
  }

  private async convertToSketch(imageBase64: string): Promise<string> {
    try {
      const imageBuffer = Buffer.from(imageBase64, 'base64');
      
      const processedBuffer = await sharp(imageBuffer)
        .resize(512, 768, { fit: 'contain', background: { r: 255, g: 255, b: 255 } })
        .greyscale()
        .normalize()
        .linear(1.2, -(128 * 1.2) + 128)
        .blur(0.3)
        .sharpen({ sigma: 1.5 })
        .toBuffer();
      
      return processedBuffer.toString('base64');
    } catch (error) {
      this.logger.error('Sketch conversion failed:', error);
      return imageBase64;
    }
  }

  private drawProfessionalBody(ctx: any) {
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 西装外套
    ctx.beginPath();
    ctx.moveTo(180, 320);
    ctx.lineTo(160, 450);
    ctx.lineTo(170, 550);
    ctx.lineTo(342, 550);
    ctx.lineTo(352, 450);
    ctx.lineTo(332, 320);
    ctx.lineTo(300, 300);
    ctx.lineTo(256, 280);
    ctx.lineTo(212, 300);
    ctx.closePath();
    ctx.stroke();
    
    // 领带
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(256, 300);
    ctx.lineTo(250, 420);
    ctx.lineTo(256, 430);
    ctx.lineTo(262, 420);
    ctx.closePath();
    ctx.fillStyle = '#444';
    ctx.fill();
    ctx.stroke();
    
    // 手臂和腿部
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(180, 350);
    ctx.lineTo(130, 480);
    ctx.moveTo(332, 350);
    ctx.lineTo(382, 480);
    ctx.moveTo(200, 550);
    ctx.lineTo(195, 720);
    ctx.moveTo(312, 550);
    ctx.lineTo(317, 720);
    ctx.stroke();
  }

  private drawAnimalHead(ctx: any, animalType: AnimalCategory, species: AnimalSpecies, faceFeatures?: any) {
    const centerX = 256;
    const centerY = 200;
    const headSize = 80;
    
    // 绘制头部轮廓
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 根据动物类型绘制特征
    switch (animalType) {
      case AnimalCategory.DIVINE_BEAST:
        this.drawDivineBeastFeatures(ctx, centerX, centerY, headSize);
        break;
      case AnimalCategory.PET:
        this.drawPetFeatures(ctx, centerX, centerY, headSize, species);
        break;
      case AnimalCategory.WORKING_ANIMAL:
      default:
        this.drawWorkingAnimalFeatures(ctx, centerX, centerY, headSize, species);
        break;
    }
    
    // 绘制基本五官
    this.drawBasicFacialFeatures(ctx, centerX, centerY);
  }

  private drawDivineBeastFeatures(ctx: any, centerX: number, centerY: number, headSize: number) {
    // 龙角
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(centerX - 40, centerY - headSize);
    ctx.lineTo(centerX - 50, centerY - headSize - 40);
    ctx.moveTo(centerX + 40, centerY - headSize);
    ctx.lineTo(centerX + 50, centerY - headSize - 40);
    ctx.stroke();
  }

  private drawPetFeatures(ctx: any, centerX: number, centerY: number, headSize: number, species: AnimalSpecies) {
    if (species === AnimalSpecies.GOLDEN_RETRIEVER) {
      // 狗耳朵
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.ellipse(centerX - 50, centerY - 20, 20, 30, -0.3, 0, Math.PI * 2);
      ctx.ellipse(centerX + 50, centerY - 20, 20, 30, 0.3, 0, Math.PI * 2);
      ctx.stroke();
    } else {
      // 猫耳朵
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.moveTo(centerX - 35, centerY - headSize);
      ctx.lineTo(centerX - 50, centerY - headSize - 30);
      ctx.lineTo(centerX - 20, centerY - headSize + 10);
      ctx.closePath();
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(centerX + 35, centerY - headSize);
      ctx.lineTo(centerX + 50, centerY - headSize - 30);
      ctx.lineTo(centerX + 20, centerY - headSize + 10);
      ctx.closePath();
      ctx.stroke();
    }
  }

  private drawWorkingAnimalFeatures(ctx: any, centerX: number, centerY: number, headSize: number, species: AnimalSpecies) {
    if (species === AnimalSpecies.OX || species === AnimalSpecies.BUFFALO) {
      // 牛角
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 4;
      ctx.beginPath();
      ctx.moveTo(centerX - 30, centerY - headSize);
      ctx.quadraticCurveTo(centerX - 40, centerY - headSize - 20, centerX - 35, centerY - headSize - 40);
      ctx.moveTo(centerX + 30, centerY - headSize);
      ctx.quadraticCurveTo(centerX + 40, centerY - headSize - 20, centerX + 35, centerY - headSize - 40);
      ctx.stroke();
    } else {
      // 马耳朵
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.ellipse(centerX - 45, centerY - headSize + 20, 15, 25, -0.2, 0, Math.PI * 2);
      ctx.ellipse(centerX + 45, centerY - headSize + 20, 15, 25, 0.2, 0, Math.PI * 2);
      ctx.stroke();
    }
  }

  private drawBasicFacialFeatures(ctx: any, centerX: number, centerY: number) {
    // 眼睛
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(centerX - 25, centerY - 15, 6, 0, Math.PI * 2);
    ctx.arc(centerX + 25, centerY - 15, 6, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻子
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.ellipse(centerX, centerY + 5, 8, 6, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 嘴巴
    ctx.beginPath();
    ctx.arc(centerX, centerY + 25, 12, 0.2 * Math.PI, 0.8 * Math.PI);
    ctx.stroke();
  }

  private applySketchTexture(ctx: any) {
    ctx.strokeStyle = 'rgba(51, 51, 51, 0.3)';
    ctx.lineWidth = 0.5;
    
    for (let i = 0; i < 50; i++) {
      const x1 = Math.random() * 512;
      const y1 = Math.random() * 768;
      const x2 = x1 + (Math.random() - 0.5) * 20;
      const y2 = y1 + (Math.random() - 0.5) * 20;
      
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.stroke();
    }
  }

  // ===== 新增的打工人动物版头像生成方法 =====

  /**
   * 使用Gemini 2.0 Flash生成融合图像
   */
  private async generateWithGeminiFlash(
    userImageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult,
    options?: any
  ): Promise<Omit<AvatarGenerationResult, 'processingTime'> | null> {
    const aiConfig = this.configService.get('ai');
    const model = this.genAI.getGenerativeModel({ 
      model: aiConfig?.gemini?.imageModel || 'gemini-2.0-flash-exp',
      generationConfig: {
        temperature: aiConfig?.gemini?.temperature || 1.0,
        topP: aiConfig?.gemini?.topP || 0.95,
        topK: aiConfig?.gemini?.topK || 40,
        maxOutputTokens: aiConfig?.gemini?.maxTokens || 8192,
        responseMimeType: 'image/png',
      }
    });

    const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
    const animalConfig = ANIMAL_CONFIG[species];
    const userDrawingBase64 = userImageData.split(',')[1] || userImageData;

    // 构建详细的融合提示词
    const prompt = this.buildGeminiWorkerPrompt(animalType, species, animalConfig, analysisResult, options);

    const parts = [
      { text: prompt },
      {
        inlineData: {
          mimeType: 'image/png',
          data: userDrawingBase64
        }
      }
    ];

    try {
      this.logger.log('Calling Gemini 2.0 Flash for image generation...');
      const result = await model.generateContent(parts);
      const response = await result.response;

      // 提取生成的图像
      let generatedImageBase64 = null;
      if (response.candidates && response.candidates[0]) {
        const candidate = response.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.mimeType.includes('image')) {
              generatedImageBase64 = part.inlineData.data;
              break;
            }
          }
        }
      }

      if (!generatedImageBase64) {
        this.logger.warn('Gemini 2.0 Flash did not return image data');
        return null;
      }

      // 应用素描效果
      const sketchImageBase64 = await this.convertToSketch(generatedImageBase64);

      return {
        imageBase64: sketchImageBase64,
        generationMethod: 'Google Gemini 2.0 Flash',
        stats: this.generateWorkerStats(),
        prompt,
      };

    } catch (error) {
      this.logger.warn('Gemini Flash generation failed:', error.message);
      return null;
    }
  }

  /**
   * 使用DALL-E生成打工人动物版头像
   */
  private async generateWorkerAvatarWithDallE(
    userImageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult,
    options?: any
  ): Promise<Omit<AvatarGenerationResult, 'processingTime'>> {
    const aiConfig = this.configService.get('ai');
    const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
    const animalConfig = ANIMAL_CONFIG[species];
    
    const prompt = this.buildDallEWorkerPrompt(animalType, species, animalConfig, analysisResult, options);
    
    this.logger.log(`Generating worker avatar with DALL-E 3, prompt: ${prompt.substring(0, 100)}...`);

    const response = await this.openai.images.generate({
      model: aiConfig?.openai?.model || 'dall-e-3',
      prompt,
      n: 1,
      size: (aiConfig?.openai?.imageSize || '1024x1024') as any,
      quality: (aiConfig?.openai?.imageQuality || 'standard') as any,
      style: (aiConfig?.openai?.imageStyle || 'natural') as any,
    });

    const imageUrl = response.data[0].url;
    const imageBase64 = await this.convertUrlToBase64(imageUrl);
    
    // 融合用户原始绘画特征
    const fusedImageBase64 = await this.fuseUserFeaturesWithAI(imageBase64, userImageData, animalType, options);
    
    // 转换为素描风格
    const sketchBase64 = await this.convertToSketch(fusedImageBase64);
    
    return {
      imageBase64: sketchBase64,
      imageUrl,
      generationMethod: 'OpenAI DALL-E 3 + User Feature Fusion',
      stats: this.generateWorkerStats(),
      prompt,
    };
  }

  /**
   * 使用Canvas生成打工人动物版头像（增强版）
   */
  private async generateWorkerAvatarWithCanvas(
    userImageData: string,
    animalType: AnimalCategory,
    animalSpecies?: AnimalSpecies,
    analysisResult?: AnalysisResult,
    options?: any
  ): Promise<Omit<AvatarGenerationResult, 'processingTime'>> {
    const species = animalSpecies || analysisResult?.recommendedSpecies || this.getDefaultSpecies(animalType);
    const animalConfig = ANIMAL_CONFIG[species];
    
    this.logger.log(`Generating worker avatar with enhanced Canvas for ${species}`);
    
    const canvas = createCanvas(512, 768);
    const ctx = canvas.getContext('2d');
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 512, 768);
    
    // 首先融合用户画像到画布
    await this.blendUserImageToCanvas(ctx, userImageData, analysisResult?.pixelAnalysis);
    
    // 绘制专业工作身体
    this.drawProfessionalWorkerBody(ctx, options?.workEnvironment, options?.clothingStyle);
    
    // 绘制增强的动物头部特征
    this.drawEnhancedAnimalHead(ctx, animalType, species, analysisResult?.faceFeatures, options);
    
    // 添加工作环境相关细节
    this.addWorkEnvironmentDetails(ctx, options?.workEnvironment);
    
    // 应用基于用户特征的纹理
    this.applyUserBasedSketchTexture(ctx, analysisResult?.pixelAnalysis);
    
    const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
    
    return {
      imageBase64,
      generationMethod: 'Enhanced Canvas + User Feature Fusion',
      stats: this.generateWorkerStats(),
    };
  }

  /**
   * 构建Gemini Flash的提示词
   */
  private buildGeminiWorkerPrompt(
    animalType: AnimalCategory,
    species: AnimalSpecies,
    animalConfig: any,
    analysisResult?: AnalysisResult,
    options?: any
  ): string {
    const animalName = animalConfig.name;
    const environment = options?.workEnvironment || 'office';
    const clothing = options?.clothingStyle || 'formal';
    const fusionStrength = options?.fusionStrength || 0.7;
    
    let basePrompt = `创建一个打工人形象的素描画，要求：
1. 头部是${animalName}的头部特征，保持动物的标志性特征
2. 身体是人类的身体，穿着${clothing === 'formal' ? '正式的商务装' : clothing === 'casual' ? '休闲工作装' : clothing === 'uniform' ? '工作制服' : '创意工作服装'}
3. 整体风格为黑白素描，线条清晰
4. 融合程度：${fusionStrength > 0.8 ? '深度融合，动物特征明显' : fusionStrength > 0.5 ? '平衡融合，人类和动物特征并重' : '轻度融合，保留更多人类特征'}
5. 工作环境：${environment === 'office' ? '办公室背景' : environment === 'factory' ? '工厂车间背景' : environment === 'field' ? '田野工作背景' : '创意工作空间背景'}`;

    if (analysisResult?.faceFeatures) {
      const features = analysisResult.faceFeatures;
      basePrompt += `\n6. 融合用户面部特征：${features.faceShape}脸型，${features.expression}表情，${features.style}风格`;
    }

    basePrompt += '\n7. 确保输出是单一角色，全身立像，专业的素描风格';

    return basePrompt;
  }

  /**
   * 构建DALL-E的提示词
   */
  private buildDallEWorkerPrompt(
    animalType: AnimalCategory,
    species: AnimalSpecies,
    animalConfig: any,
    analysisResult?: AnalysisResult,
    options?: any
  ): string {
    const animalName = animalConfig.name;
    const environment = options?.workEnvironment || 'office';
    const clothing = options?.clothingStyle || 'formal';
    
    let prompt = `Professional pencil sketch portrait of a single office worker with ${animalName} head and human body, ` +
                `standing upright in ${clothing === 'formal' ? 'formal business suit' : clothing === 'casual' ? 'casual work attire' : clothing === 'uniform' ? 'work uniform' : 'creative work clothes'}, ` +
                `${environment === 'office' ? 'office environment' : environment === 'factory' ? 'factory setting' : environment === 'field' ? 'field work environment' : 'creative workspace'} background, ` +
                `black and white line art style, clean sketch lines, ` +
                `single character full body view, no duplicates, only one person`;

    if (analysisResult?.faceFeatures) {
      const features = analysisResult.faceFeatures;
      prompt += `, incorporating ${features.faceShape} face shape, ${features.expression} expression, ${features.style} artistic approach`;
    }

    return prompt;
  }

  /**
   * 融合用户特征到AI生成图像
   */
  private async fuseUserFeaturesWithAI(
    aiImageBase64: string,
    userImageData: string,
    animalType: AnimalCategory,
    options?: any
  ): Promise<string> {
    try {
      const fusionStrength = options?.fusionStrength || 0.7;
      
      // 创建融合画布
      const canvas = createCanvas(512, 768);
      const ctx = canvas.getContext('2d');
      
      // 加载AI生成的图像
      const aiImageBuffer = Buffer.from(aiImageBase64, 'base64');
      const aiImage = await loadImage(aiImageBuffer);
      
      // 加载用户原始绘画
      const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
      const userImageBuffer = Buffer.from(userDrawingBase64, 'base64');
      const userImage = await loadImage(userImageBuffer);
      
      // 绘制AI生成的图像作为基础
      ctx.drawImage(aiImage, 0, 0, 512, 768);
      
      // 以适当的透明度叠加用户特征
      ctx.save();
      ctx.globalAlpha = fusionStrength * 0.3; // 调整融合强度
      ctx.globalCompositeOperation = 'multiply';
      
      // 将用户画像缩放并定位到头部区域
      const headX = 256 - 80;
      const headY = 150 - 100;
      const headWidth = 160;
      const headHeight = 200;
      
      ctx.drawImage(userImage, headX, headY, headWidth, headHeight);
      ctx.restore();
      
      return canvas.toDataURL('image/png').split(',')[1];
      
    } catch (error) {
      this.logger.warn('User feature fusion failed:', error.message);
      return aiImageBase64; // 返回原始AI图像
    }
  }

  /**
   * 将用户画像融合到Canvas
   */
  private async blendUserImageToCanvas(ctx: any, userImageData: string, pixelAnalysis?: any): Promise<void> {
    try {
      const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
      const imageBuffer = Buffer.from(userDrawingBase64, 'base64');
      const userImage = await loadImage(imageBuffer);
      
      ctx.save();
      ctx.globalAlpha = 0.4;
      ctx.globalCompositeOperation = 'multiply';
      
      // 将用户画像作为纹理叠加在整个画布上
      ctx.drawImage(userImage, 100, 50, 312, 400);
      
      ctx.restore();
    } catch (error) {
      this.logger.warn('User image blending failed:', error.message);
    }
  }

  /**
   * 绘制专业工作身体
   */
  private drawProfessionalWorkerBody(ctx: any, environment?: string, clothingStyle?: string): void {
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    if (clothingStyle === 'uniform') {
      // 工作制服
      this.drawWorkUniform(ctx);
    } else if (clothingStyle === 'casual') {
      // 休闲装
      this.drawCasualWear(ctx);
    } else if (clothingStyle === 'creative') {
      // 创意装
      this.drawCreativeWear(ctx);
    } else {
      // 默认正装
      this.drawProfessionalSuit(ctx);
    }
  }

  private drawProfessionalSuit(ctx: any): void {
    // 西装外套 - 更精细的线条
    ctx.beginPath();
    ctx.moveTo(180, 320);
    ctx.lineTo(160, 450);
    ctx.lineTo(170, 550);
    ctx.lineTo(342, 550);
    ctx.lineTo(352, 450);
    ctx.lineTo(332, 320);
    ctx.lineTo(300, 300);
    ctx.lineTo(256, 280);
    ctx.lineTo(212, 300);
    ctx.closePath();
    ctx.stroke();
    
    // 西装细节
    ctx.beginPath();
    ctx.moveTo(200, 350);
    ctx.lineTo(190, 420);
    ctx.moveTo(312, 350);
    ctx.lineTo(322, 420);
    ctx.stroke();
    
    // 精致的领带
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(256, 300);
    ctx.lineTo(250, 420);
    ctx.lineTo(256, 430);
    ctx.lineTo(262, 420);
    ctx.closePath();
    ctx.fillStyle = '#444';
    ctx.fill();
    ctx.stroke();
    
    // 衬衫袖扣
    ctx.fillStyle = '#888';
    ctx.beginPath();
    ctx.arc(140, 470, 4, 0, Math.PI * 2);
    ctx.arc(372, 470, 4, 0, Math.PI * 2);
    ctx.fill();
  }

  private drawWorkUniform(ctx: any): void {
    // 简单的工作服
    ctx.beginPath();
    ctx.moveTo(170, 320);
    ctx.lineTo(150, 450);
    ctx.lineTo(160, 550);
    ctx.lineTo(352, 550);
    ctx.lineTo(362, 450);
    ctx.lineTo(342, 320);
    ctx.lineTo(256, 280);
    ctx.closePath();
    ctx.stroke();
    
    // 工作服口袋
    ctx.strokeRect(200, 380, 30, 25);
    ctx.strokeRect(282, 380, 30, 25);
  }

  private drawCasualWear(ctx: any): void {
    // 休闲T恤
    ctx.beginPath();
    ctx.moveTo(170, 320);
    ctx.lineTo(150, 500);
    ctx.lineTo(362, 500);
    ctx.lineTo(342, 320);
    ctx.lineTo(300, 290);
    ctx.lineTo(212, 290);
    ctx.closePath();
    ctx.stroke();
    
    // T恤图案
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;
    ctx.strokeRect(230, 350, 52, 30);
  }

  private drawCreativeWear(ctx: any): void {
    // 创意工作服
    ctx.beginPath();
    ctx.moveTo(175, 320);
    ctx.lineTo(155, 480);
    ctx.lineTo(357, 480);
    ctx.lineTo(337, 320);
    ctx.lineTo(300, 295);
    ctx.lineTo(212, 295);
    ctx.closePath();
    ctx.stroke();
    
    // 创意元素
    ctx.strokeStyle = '#888';
    ctx.lineWidth = 1;
    for (let i = 0; i < 5; i++) {
      ctx.beginPath();
      ctx.arc(200 + i * 20, 400, 3, 0, Math.PI * 2);
      ctx.stroke();
    }
  }

  /**
   * 绘制增强的动物头部
   */
  private drawEnhancedAnimalHead(
    ctx: any,
    animalType: AnimalCategory,
    species: AnimalSpecies,
    faceFeatures?: any,
    options?: any
  ): void {
    const centerX = 256;
    const centerY = 200;
    const headSize = 90;
    
    // 根据动物类型应用不同的绘制风格
    switch (animalType) {
      case AnimalCategory.DIVINE_BEAST:
        this.drawEnhancedDivineHead(ctx, centerX, centerY, headSize, species);
        break;
      case AnimalCategory.PET:
        this.drawEnhancedPetHead(ctx, centerX, centerY, headSize, species);
        break;
      case AnimalCategory.WORKING_ANIMAL:
      default:
        this.drawEnhancedWorkingAnimalHead(ctx, centerX, centerY, headSize, species);
        break;
    }
  }

  private drawEnhancedDivineHead(ctx: any, centerX: number, centerY: number, headSize: number, species: AnimalSpecies): void {
    // 神兽头部特征更加威严
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    
    // 基础头型
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // 神兽角
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 5;
    ctx.beginPath();
    ctx.moveTo(centerX - 50, centerY - headSize);
    ctx.bezierCurveTo(centerX - 60, centerY - headSize - 30, centerX - 45, centerY - headSize - 50, centerX - 40, centerY - headSize - 60);
    ctx.moveTo(centerX + 50, centerY - headSize);
    ctx.bezierCurveTo(centerX + 60, centerY - headSize - 30, centerX + 45, centerY - headSize - 50, centerX + 40, centerY - headSize - 60);
    ctx.stroke();
    
    // 神兽眼睛
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 3;
    ctx.fillStyle = '#FFF8DC';
    ctx.beginPath();
    ctx.ellipse(centerX - 30, centerY - 15, 12, 8, 0, 0, Math.PI * 2);
    ctx.ellipse(centerX + 30, centerY - 15, 12, 8, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 瞳孔
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(centerX - 30, centerY - 15, 4, 0, Math.PI * 2);
    ctx.arc(centerX + 30, centerY - 15, 4, 0, Math.PI * 2);
    ctx.fill();
  }

  private drawEnhancedPetHead(ctx: any, centerX: number, centerY: number, headSize: number, species: AnimalSpecies): void {
    // 宠物头部特征更加可爱
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    
    // 基础头型
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, headSize * 0.9, headSize, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    if (species === AnimalSpecies.PERSIAN_CAT) {
      // 猫耳朵
      ctx.fillStyle = '#FFC0CB';
      ctx.beginPath();
      ctx.moveTo(centerX - 45, centerY - headSize);
      ctx.lineTo(centerX - 60, centerY - headSize - 40);
      ctx.lineTo(centerX - 25, centerY - headSize + 15);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(centerX + 45, centerY - headSize);
      ctx.lineTo(centerX + 60, centerY - headSize - 40);
      ctx.lineTo(centerX + 25, centerY - headSize + 15);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
      
      // 猫须
      ctx.strokeStyle = '#666';
      ctx.lineWidth = 2;
      for (let i = 0; i < 3; i++) {
        const y = centerY + i * 8;
        ctx.beginPath();
        ctx.moveTo(centerX - 30, y);
        ctx.lineTo(centerX - 70, y - 5);
        ctx.moveTo(centerX + 30, y);
        ctx.lineTo(centerX + 70, y - 5);
        ctx.stroke();
      }
    } else {
      // 狗耳朵
      ctx.fillStyle = '#DEB887';
      ctx.beginPath();
      ctx.ellipse(centerX - 55, centerY - 10, 25, 40, -0.3, 0, Math.PI * 2);
      ctx.ellipse(centerX + 55, centerY - 10, 25, 40, 0.3, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
    }
    
    // 可爱的眼睛
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(centerX - 25, centerY - 15, 8, 0, Math.PI * 2);
    ctx.arc(centerX + 25, centerY - 15, 8, 0, Math.PI * 2);
    ctx.fill();
    
    // 眼中高光
    ctx.fillStyle = '#FFF';
    ctx.beginPath();
    ctx.arc(centerX - 22, centerY - 18, 3, 0, Math.PI * 2);
    ctx.arc(centerX + 28, centerY - 18, 3, 0, Math.PI * 2);
    ctx.fill();
  }

  private drawEnhancedWorkingAnimalHead(ctx: any, centerX: number, centerY: number, headSize: number, species: AnimalSpecies): void {
    // 工作动物头部特征更加结实
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 4;
    
    // 基础头型
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, headSize, headSize * 1.1, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    if (species === AnimalSpecies.OX || species === AnimalSpecies.BUFFALO) {
      // 牛角
      ctx.strokeStyle = '#8B4513';
      ctx.lineWidth = 5;
      ctx.beginPath();
      ctx.moveTo(centerX - 40, centerY - headSize);
      ctx.quadraticCurveTo(centerX - 55, centerY - headSize - 25, centerX - 45, centerY - headSize - 50);
      ctx.moveTo(centerX + 40, centerY - headSize);
      ctx.quadraticCurveTo(centerX + 55, centerY - headSize - 25, centerX + 45, centerY - headSize - 50);
      ctx.stroke();
      
      // 牛鼻环
      ctx.strokeStyle = '#666';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.arc(centerX, centerY + 25, 12, Math.PI * 0.2, Math.PI * 0.8);
      ctx.stroke();
    } else {
      // 马耳朵
      ctx.strokeStyle = '#8B4513';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.ellipse(centerX - 50, centerY - headSize + 30, 18, 35, -0.2, 0, Math.PI * 2);
      ctx.ellipse(centerX + 50, centerY - headSize + 30, 18, 35, 0.2, 0, Math.PI * 2);
      ctx.stroke();
    }
    
    // 坚毅的眼神
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.ellipse(centerX - 25, centerY - 15, 10, 6, 0, 0, Math.PI * 2);
    ctx.ellipse(centerX + 25, centerY - 15, 10, 6, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(centerX - 25, centerY - 15, 4, 0, Math.PI * 2);
    ctx.arc(centerX + 25, centerY - 15, 4, 0, Math.PI * 2);
    ctx.fill();
  }

  /**
   * 添加工作环境细节
   */
  private addWorkEnvironmentDetails(ctx: any, environment?: string): void {
    ctx.strokeStyle = 'rgba(102, 102, 102, 0.5)';
    ctx.lineWidth = 1;
    
    switch (environment) {
      case 'office':
        // 添加办公室元素
        ctx.strokeRect(450, 100, 50, 30); // 文件柜
        ctx.strokeRect(460, 110, 10, 8); // 抽屉把手
        break;
      case 'factory':
        // 添加工厂元素
        ctx.beginPath();
        ctx.moveTo(450, 150);
        ctx.lineTo(500, 120);
        ctx.lineTo(500, 180);
        ctx.closePath();
        ctx.stroke(); // 机械臂
        break;
      case 'field':
        // 添加田野元素
        for (let i = 0; i < 5; i++) {
          ctx.beginPath();
          ctx.moveTo(420 + i * 15, 200);
          ctx.lineTo(425 + i * 15, 180);
          ctx.stroke(); // 麦穗
        }
        break;
      case 'creative':
        // 添加创意元素
        ctx.strokeStyle = '#888';
        ctx.beginPath();
        ctx.arc(470, 150, 15, 0, Math.PI * 2);
        ctx.moveTo(455, 135);
        ctx.lineTo(485, 165);
        ctx.moveTo(485, 135);
        ctx.lineTo(455, 165);
        ctx.stroke(); // 创意灯泡
        break;
    }
  }

  /**
   * 应用基于用户特征的素描纹理
   */
  private applyUserBasedSketchTexture(ctx: any, pixelAnalysis?: any): void {
    ctx.strokeStyle = 'rgba(51, 51, 51, 0.3)';
    
    if (pixelAnalysis) {
      const textureCount = Math.floor(pixelAnalysis.lineComplexity * 100) + 30;
      const lineWidth = pixelAnalysis.darkRatio > 0.4 ? 0.8 : 0.4;
      
      ctx.lineWidth = lineWidth;
      
      for (let i = 0; i < textureCount; i++) {
        const x1 = Math.random() * 512;
        const y1 = Math.random() * 768;
        const lineLength = pixelAnalysis.lineComplexity * 30 + 5;
        const x2 = x1 + (Math.random() - 0.5) * lineLength;
        const y2 = y1 + (Math.random() - 0.5) * lineLength;
        
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      }
    } else {
      // 默认纹理
      ctx.lineWidth = 0.5;
      for (let i = 0; i < 60; i++) {
        const x1 = Math.random() * 512;
        const y1 = Math.random() * 768;
        const x2 = x1 + (Math.random() - 0.5) * 20;
        const y2 = y1 + (Math.random() - 0.5) * 20;
        
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      }
    }
  }
}