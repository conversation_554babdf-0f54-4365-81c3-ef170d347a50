import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';

export enum ContentType {
  USER = 'user',
  POST = 'post',
  COMMENT = 'comment',
}

export enum CommentStatus {
  ACTIVE = 1,
  HIDDEN = 2,
  DELETED = 3,
}

@Entity('comments')
@Tree('materialized-path')
@Index(['contentId', 'contentType', 'status'])
@Index(['userId', 'createdAt'])
export class Comment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  contentId: string;

  @Column({
    type: 'enum',
    enum: ContentType,
  })
  contentType: ContentType;

  @Column({ type: 'text' })
  content: string;

  @Column({ default: 0 })
  likeCount: number;

  @Column({ default: 0 })
  replyCount: number;

  @Column({
    type: 'enum',
    enum: CommentStatus,
    default: CommentStatus.ACTIVE,
  })
  status: CommentStatus;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    mentions?: string[]; // @用户
    hashtags?: string[]; // #话题
    emotions?: string[]; // 表情符号
    images?: string[];   // 图片链接
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.comments, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: User;

  // 树形结构 - 支持评论嵌套
  @TreeParent()
  parent?: Comment;

  @TreeChildren()
  children?: Comment[];
}