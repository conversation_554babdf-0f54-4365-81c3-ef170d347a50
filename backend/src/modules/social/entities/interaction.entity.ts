import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';

export enum InteractionType {
  LIKE = 1,
  FEED = 2,
  FOLLOW = 3,
}

@Entity('interactions')
@Index(['targetUserId', 'interactionType', 'createdAt'])
@Index(['userId', 'interactionType', 'createdAt'])
@Unique(['userId', 'targetUserId', 'interactionType', 'createdDate'])
export class Interaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  targetUserId: string;

  @Column({
    type: 'enum',
    enum: InteractionType,
  })
  interactionType: InteractionType;

  @Column({ type: 'date', default: () => 'CURRENT_DATE' })
  createdDate: Date; // 用于唯一约束，确保每天每种互动只能一次

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    message?: string;
    amount?: number; // 投喂数量
    location?: {
      x: number;
      y: number;
      z: number;
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.interactions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => User, (user) => user.receivedInteractions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'targetUserId' })
  targetUser: User;
}