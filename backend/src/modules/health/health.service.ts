import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { InjectConnection } from '@nestjs/mongoose';
import { DataSource } from 'typeorm';
import { Connection } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { HealthCheckDto, ServiceStatus } from './dto/health-check.dto';
import * as Redis from 'redis';

@Injectable()
export class HealthService {
  private redis: Redis.RedisClientType;

  constructor(
    @InjectDataSource() private dataSource: DataSource,
    @InjectConnection() private mongoConnection: Connection,
    private configService: ConfigService,
  ) {
    // 初始化 Redis 客户端
    this.redis = Redis.createClient({
      host: this.configService.get('REDIS_HOST'),
      port: this.configService.get('REDIS_PORT'),
      password: this.configService.get('REDIS_PASSWORD'),
    });
  }

  async check(): Promise<HealthCheckDto> {
    const checks: HealthCheckDto = {
      status: ServiceStatus.HEALTHY,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: this.configService.get('APP_VERSION', '1.0.0'),
      environment: this.configService.get('NODE_ENV', 'development'),
      memory: process.memoryUsage(),
      services: {
        database: ServiceStatus.UNKNOWN,
        mongodb: ServiceStatus.UNKNOWN,
        redis: ServiceStatus.UNKNOWN,
      },
    };

    // 检查 PostgreSQL
    try {
      await this.dataSource.query('SELECT 1');
      checks.services.database = ServiceStatus.HEALTHY;
    } catch (error) {
      checks.services.database = ServiceStatus.UNHEALTHY;
      checks.status = ServiceStatus.UNHEALTHY;
    }

    // 检查 MongoDB
    try {
      if (this.mongoConnection.readyState === 1) {
        checks.services.mongodb = ServiceStatus.HEALTHY;
      } else {
        checks.services.mongodb = ServiceStatus.UNHEALTHY;
        checks.status = ServiceStatus.UNHEALTHY;
      }
    } catch (error) {
      checks.services.mongodb = ServiceStatus.UNHEALTHY;
      checks.status = ServiceStatus.UNHEALTHY;
    }

    // 检查 Redis
    try {
      if (!this.redis.isOpen) {
        await this.redis.connect();
      }
      await this.redis.ping();
      checks.services.redis = ServiceStatus.HEALTHY;
    } catch (error) {
      checks.services.redis = ServiceStatus.UNHEALTHY;
      checks.status = ServiceStatus.UNHEALTHY;
    }

    if (checks.status === ServiceStatus.UNHEALTHY) {
      throw new ServiceUnavailableException('Service is unhealthy');
    }

    return checks;
  }

  async ready() {
    const health = await this.check();
    return {
      status: 'ready',
      timestamp: new Date().toISOString(),
      checks: health.services,
    };
  }

  async live() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
    };
  }
}