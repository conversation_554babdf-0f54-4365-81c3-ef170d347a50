import { ApiProperty } from '@nestjs/swagger';

export enum ServiceStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown',
}

export class HealthCheckDto {
  @ApiProperty({
    description: '服务整体健康状态',
    enum: ServiceStatus,
  })
  status: ServiceStatus;

  @ApiProperty({
    description: '检查时间戳',
    example: '2025-01-15T10:30:00Z',
  })
  timestamp: string;

  @ApiProperty({
    description: '服务运行时间（秒）',
    example: 3600,
  })
  uptime: number;

  @ApiProperty({
    description: '应用版本',
    example: '1.0.0',
  })
  version: string;

  @ApiProperty({
    description: '运行环境',
    example: 'production',
  })
  environment: string;

  @ApiProperty({
    description: '内存使用情况',
    example: {
      rss: 41943040,
      heapTotal: 7684096,
      heapUsed: 4194304,
      external: 1089536,
      arrayBuffers: 17632,
    },
  })
  memory: NodeJS.MemoryUsage;

  @ApiProperty({
    description: '各服务健康状态',
    example: {
      database: 'healthy',
      mongodb: 'healthy',
      redis: 'healthy',
    },
  })
  services: {
    database: ServiceStatus;
    mongodb: ServiceStatus;
    redis: ServiceStatus;
  };
}