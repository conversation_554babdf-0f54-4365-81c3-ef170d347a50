import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import { Public } from '@/common/decorators/public.decorator';
import { HealthCheckDto } from './dto/health-check.dto';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @Public()
  @ApiOperation({
    summary: '健康检查',
    description: '检查服务和依赖项的健康状态',
  })
  @ApiResponse({
    status: 200,
    description: '服务健康',
    type: HealthCheckDto,
  })
  @ApiResponse({
    status: 503,
    description: '服务不健康',
  })
  async check(): Promise<HealthCheckDto> {
    return this.healthService.check();
  }

  @Get('ready')
  @Public()
  @ApiOperation({
    summary: '就绪检查',
    description: '检查服务是否准备好接收流量',
  })
  async ready() {
    return this.healthService.ready();
  }

  @Get('live')
  @Public()
  @ApiOperation({
    summary: '存活检查',
    description: '检查服务是否存活',
  })
  async live() {
    return this.healthService.live();
  }
}