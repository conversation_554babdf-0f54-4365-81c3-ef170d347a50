import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import * as Canvas from 'canvas'; // Temporarily disabled due to architecture compatibility
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';

export interface OptimizedFusionResult {
  success: boolean;
  imageUrl: string;
  imageBase64?: string;
  filePath?: string;
  fusionDetails: {
    method: string;
    animalType: string;
    style: string;
    features: any;
  };
}

export interface BodyStructure {
  width: number;
  height: number;
  proportions: {
    headRatio: number;
    shoulderRatio: number;
    waistRatio: number;
    legRatio: number;
  };
  pose: {
    type: string;
    direction: string;
    armPosition: string;
  };
  style: {
    lineWeight: string;
    detail: string;
    shading: string;
  };
}

@Injectable()
export class OptimizedFusionGeneratorService {
  private readonly logger = new Logger(OptimizedFusionGeneratorService.name);

  constructor(private configService: ConfigService) {}

  /**
   * 主函数：生成优化的人身动物融合图像
   */
  async generateOptimizedFusion(
    userDrawingBase64: string,
    animalType: 'OXHORSE' | 'PET' | 'DIVINE',
    analysisResult: any = {}
  ): Promise<OptimizedFusionResult> {
    // Temporarily disabled due to Canvas compatibility issues
    return {
      success: false,
      imageUrl: '',
      fusionDetails: {
        method: 'disabled',
        animalType: animalType,
        style: 'unavailable',
        features: {}
      }
    };
    this.logger.log('🎨 开始优化融合生成...');
    this.logger.log(`动物类型: ${animalType}`);
    this.logger.log(`Base64数据长度: ${userDrawingBase64 ? userDrawingBase64.length : 'null'}`);
    
    // 创建画布
    const canvas = Canvas.createCanvas(1024, 1024);
    const ctx = canvas.getContext('2d');
    
    // 设置背景
    const gradient = ctx.createLinearGradient(0, 0, 0, 1024);
    gradient.addColorStop(0, '#f0f4f8');
    gradient.addColorStop(1, '#e2e8f0');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1024, 1024);
    
    try {
      // 1. 加载并分析用户原画像
      this.logger.log('步骤1: 分析用户画像...');
      const userImage = await this.loadUserDrawing(userDrawingBase64);
      const bodyStructure = this.analyzeBodyStructure(userImage);
      
      // 2. 重绘优化的身体轮廓
      this.logger.log('步骤2: 重绘优化身体...');
      const bodyInfo = await this.drawOptimizedBody(ctx, bodyStructure, animalType);
      
      // 3. 融合动物头部特征
      this.logger.log('步骤3: 融合动物头部...');
      await this.drawFusedAnimalHead(ctx, animalType, bodyInfo, analysisResult);
      
      // 4. 添加细节和装饰
      this.logger.log('步骤4: 添加细节装饰...');
      this.addDetailsAndAccessories(ctx, animalType, bodyInfo);
      
      // 5. 应用艺术风格
      this.logger.log('步骤5: 应用艺术风格...');
      this.applyArtStyle(ctx, analysisResult.style || 'cartoon');
      
      // 保存图像到文件系统
      const avatarId = Date.now().toString();
      const filename = `avatar-optimized-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
      const uploadDir = path.join(process.cwd(), 'uploads', 'generated-avatars');
      const filePath = path.join(uploadDir, filename);
      
      // 确保目录存在
      try {
        await fs.access(uploadDir);
      } catch {
        await fs.mkdir(uploadDir, { recursive: true });
      }
      
      // 将canvas数据写入文件
      const buffer = canvas.toBuffer('image/png');
      await fs.writeFile(filePath, buffer);
      
      // 生成结果
      const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
      const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
      
      this.logger.log('📷 生成图像成功');
      this.logger.log(`📷 图像数据长度: ${imageBase64.length}`);
      this.logger.log(`💾 图像已保存到: ${filePath}`);
      
      return {
        success: true,
        imageUrl: imageUrl,
        imageBase64: imageBase64,
        filePath: filePath,
        fusionDetails: {
          method: 'Optimized Fusion',
          animalType,
          style: 'Smooth Hybrid',
          features: {
            bodyRedraw: true,
            headFusion: true,
            smoothTransition: true
          }
        }
      };
      
    } catch (error) {
      this.logger.error(`优化融合失败: ${error.message}`, error.stack);
      return this.generateFallback(canvas, ctx, animalType);
    }
  }

  /**
   * 加载用户绘画
   */
  private async loadUserDrawing(base64Data: string): Promise<Canvas.Image> {
    try {
      if (!base64Data || typeof base64Data !== 'string') {
        throw new Error('Invalid base64 data');
      }
      
      const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
      const buffer = Buffer.from(imageData, 'base64');
      const image = await Canvas.loadImage(buffer);
      
      return image;
    } catch (error) {
      // 创建默认图像
      const canvas = Canvas.createCanvas(300, 400);
      const ctx = canvas.getContext('2d');
      this.drawDefaultFigure(ctx);
      return canvas as any;
    }
  }

  /**
   * 绘制默认人物轮廓
   */
  private drawDefaultFigure(ctx: Canvas.CanvasRenderingContext2D) {
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 300, 400);
    
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // 头部
    ctx.arc(150, 60, 30, 0, Math.PI * 2);
    // 身体
    ctx.moveTo(150, 90);
    ctx.lineTo(150, 250);
    // 手臂
    ctx.moveTo(120, 130);
    ctx.lineTo(180, 130);
    // 腿
    ctx.moveTo(150, 250);
    ctx.lineTo(130, 320);
    ctx.moveTo(150, 250);
    ctx.lineTo(170, 320);
    ctx.stroke();
    
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('默认人形模板', 150, 350);
  }

  /**
   * 分析身体结构
   */
  private analyzeBodyStructure(image: Canvas.Image): BodyStructure {
    // 分析原图像的身体结构特征
    return {
      width: image.width,
      height: image.height,
      proportions: {
        headRatio: 0.15,
        shoulderRatio: 0.25,
        waistRatio: 0.5,
        legRatio: 0.7
      },
      pose: this.detectPose(image),
      style: this.detectDrawingStyle(image)
    };
  }

  /**
   * 检测姿势
   */
  private detectPose(image: Canvas.Image) {
    // 简化的姿势检测
    return {
      type: 'standing',
      direction: 'front',
      armPosition: 'sides'
    };
  }

  /**
   * 检测绘画风格
   */
  private detectDrawingStyle(image: Canvas.Image) {
    // 简化的风格检测
    return {
      lineWeight: 'medium',
      detail: 'moderate',
      shading: 'light'
    };
  }

  /**
   * 绘制优化的身体
   */
  private async drawOptimizedBody(ctx: Canvas.CanvasRenderingContext2D, structure: BodyStructure, animalType: string) {
    const centerX = 512;
    const centerY = 512;
    const scale = Math.min(600 / structure.width, 700 / structure.height);
    
    const bodyWidth = 180 * scale;
    const bodyHeight = 400 * scale;
    
    ctx.save();
    
    // 设置绘画风格
    ctx.strokeStyle = '#2d3748';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 绘制身体轮廓（流畅曲线）
    ctx.beginPath();
    
    // 肩部
    const shoulderY = centerY - bodyHeight * 0.3;
    const shoulderWidth = bodyWidth * 0.8;
    
    ctx.moveTo(centerX - shoulderWidth/2, shoulderY);
    ctx.bezierCurveTo(
      centerX - shoulderWidth/2, shoulderY - 20,
      centerX + shoulderWidth/2, shoulderY - 20,
      centerX + shoulderWidth/2, shoulderY
    );
    
    // 躯干（自然曲线）
    const waistY = centerY;
    const waistWidth = bodyWidth * 0.6;
    
    ctx.bezierCurveTo(
      centerX + shoulderWidth/2, shoulderY + 40,
      centerX + waistWidth/2, waistY - 20,
      centerX + waistWidth/2, waistY
    );
    
    // 臀部和腿部连接
    const hipY = centerY + bodyHeight * 0.1;
    const hipWidth = bodyWidth * 0.7;
    
    ctx.bezierCurveTo(
      centerX + waistWidth/2, waistY + 20,
      centerX + hipWidth/2, hipY - 10,
      centerX + hipWidth/2, hipY
    );
    
    // 腿部轮廓
    const legEndY = centerY + bodyHeight * 0.4;
    
    // 右腿
    ctx.lineTo(centerX + bodyWidth * 0.2, legEndY);
    ctx.lineTo(centerX + bodyWidth * 0.15, legEndY + 20);
    
    // 脚部连接
    ctx.lineTo(centerX - bodyWidth * 0.15, legEndY + 20);
    ctx.lineTo(centerX - bodyWidth * 0.2, legEndY);
    
    // 左腿
    ctx.lineTo(centerX - hipWidth/2, hipY);
    
    // 左侧身体轮廓（镜像）
    ctx.bezierCurveTo(
      centerX - hipWidth/2, hipY - 10,
      centerX - waistWidth/2, waistY + 20,
      centerX - waistWidth/2, waistY
    );
    
    ctx.bezierCurveTo(
      centerX - waistWidth/2, waistY - 20,
      centerX - shoulderWidth/2, shoulderY + 40,
      centerX - shoulderWidth/2, shoulderY
    );
    
    ctx.closePath();
    ctx.stroke();
    
    // 添加手臂
    this.drawArms(ctx, centerX, shoulderY, shoulderWidth, bodyHeight * 0.3);
    
    ctx.restore();
    
    return {
      centerX,
      centerY,
      shoulderY,
      bodyWidth,
      bodyHeight,
      scale
    };
  }

  /**
   * 绘制手臂
   */
  private drawArms(ctx: Canvas.CanvasRenderingContext2D, centerX: number, shoulderY: number, shoulderWidth: number, armLength: number) {
    ctx.beginPath();
    
    // 左臂
    ctx.moveTo(centerX - shoulderWidth/2, shoulderY);
    ctx.lineTo(centerX - shoulderWidth/2 - 20, shoulderY + armLength * 0.6);
    ctx.lineTo(centerX - shoulderWidth/2 - 15, shoulderY + armLength);
    
    // 右臂
    ctx.moveTo(centerX + shoulderWidth/2, shoulderY);
    ctx.lineTo(centerX + shoulderWidth/2 + 20, shoulderY + armLength * 0.6);
    ctx.lineTo(centerX + shoulderWidth/2 + 15, shoulderY + armLength);
    
    ctx.stroke();
  }

  /**
   * 绘制融合的动物头部
   */
  private async drawFusedAnimalHead(ctx: Canvas.CanvasRenderingContext2D, animalType: string, bodyInfo: any, analysisResult: any) {
    const headSize = bodyInfo.bodyWidth * 0.9;
    const headY = bodyInfo.shoulderY - headSize * 0.8;
    
    ctx.save();
    
    // 根据动物类型绘制头部
    switch(animalType) {
      case 'DIVINE':
        this.drawDivineHead(ctx, bodyInfo.centerX, headY, headSize);
        break;
      case 'PET':
        this.drawPetHead(ctx, bodyInfo.centerX, headY, headSize);
        break;
      case 'OXHORSE':
      default:
        this.drawOxHorseHead(ctx, bodyInfo.centerX, headY, headSize);
        break;
    }
    
    ctx.restore();
  }

  /**
   * 绘制牛马头部
   */
  private drawOxHorseHead(ctx: Canvas.CanvasRenderingContext2D, x: number, y: number, size: number) {
    // 头部轮廓
    ctx.strokeStyle = '#4a4a4a';
    ctx.fillStyle = '#f5f5f5';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // 马脸形状
    ctx.ellipse(x, y + size * 0.4, size * 0.35, size * 0.4, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 耳朵
    ctx.beginPath();
    ctx.ellipse(x - size * 0.25, y + size * 0.1, size * 0.1, size * 0.15, -0.3, 0, Math.PI * 2);
    ctx.ellipse(x + size * 0.25, y + size * 0.1, size * 0.1, size * 0.15, 0.3, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 眼睛
    ctx.fillStyle = '#2c2c2c';
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.3, size * 0.06, 0, Math.PI * 2);
    ctx.arc(x + size * 0.15, y + size * 0.3, size * 0.06, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻孔
    ctx.fillStyle = '#1a1a1a';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.05, y + size * 0.55, size * 0.02, size * 0.03, 0, 0, Math.PI * 2);
    ctx.ellipse(x + size * 0.05, y + size * 0.55, size * 0.02, size * 0.03, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 嘴巴
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x, y + size * 0.65, size * 0.08, 0.2, Math.PI - 0.2);
    ctx.stroke();
  }

  /**
   * 绘制宠物头部
   */
  private drawPetHead(ctx: Canvas.CanvasRenderingContext2D, x: number, y: number, size: number) {
    // 圆形猫脸
    ctx.strokeStyle = '#5a5a5a';
    ctx.fillStyle = '#f8f8f8';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(x, y + size * 0.4, size * 0.4, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 三角形耳朵
    ctx.beginPath();
    ctx.moveTo(x - size * 0.3, y + size * 0.1);
    ctx.lineTo(x - size * 0.35, y - size * 0.1);
    ctx.lineTo(x - size * 0.15, y + size * 0.05);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + size * 0.3, y + size * 0.1);
    ctx.lineTo(x + size * 0.35, y - size * 0.1);
    ctx.lineTo(x + size * 0.15, y + size * 0.05);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 大眼睛
    ctx.fillStyle = '#2c2c2c';
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
    ctx.arc(x + size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
    ctx.fill();
    
    // 粉色鼻子
    ctx.fillStyle = '#ff69b4';
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.45);
    ctx.lineTo(x - size * 0.03, y + size * 0.41);
    ctx.lineTo(x + size * 0.03, y + size * 0.41);
    ctx.closePath();
    ctx.fill();
  }

  /**
   * 绘制神兽头部
   */
  private drawDivineHead(ctx: Canvas.CanvasRenderingContext2D, x: number, y: number, size: number) {
    // 威严的龙头
    ctx.strokeStyle = '#8b4513';
    ctx.fillStyle = '#daa520';
    ctx.lineWidth = 4;
    ctx.beginPath();
    
    // 龙头形状
    ctx.ellipse(x, y + size * 0.4, size * 0.4, size * 0.35, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 龙角
    ctx.beginPath();
    ctx.moveTo(x - size * 0.2, y);
    ctx.quadraticCurveTo(x - size * 0.25, y - size * 0.25, x - size * 0.15, y - size * 0.3);
    ctx.moveTo(x + size * 0.2, y);
    ctx.quadraticCurveTo(x + size * 0.25, y - size * 0.25, x + size * 0.15, y - size * 0.3);
    ctx.stroke();
    
    // 威严的眼睛
    ctx.fillStyle = '#ff4500';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.15, y + size * 0.3, size * 0.08, size * 0.06, 0, 0, Math.PI * 2);
    ctx.ellipse(x + size * 0.15, y + size * 0.3, size * 0.08, size * 0.06, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 瞳孔
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.15, y + size * 0.3, size * 0.03, size * 0.04, 0, 0, Math.PI * 2);
    ctx.ellipse(x + size * 0.15, y + size * 0.3, size * 0.03, size * 0.04, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 龙鼻孔
    ctx.fillStyle = '#2a2a2a';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.06, y + size * 0.5, size * 0.04, size * 0.02, 0, 0, Math.PI * 2);
    ctx.ellipse(x + size * 0.06, y + size * 0.5, size * 0.04, size * 0.02, 0, 0, Math.PI * 2);
    ctx.fill();
  }

  /**
   * 添加细节和装饰
   */
  private addDetailsAndAccessories(ctx: Canvas.CanvasRenderingContext2D, animalType: string, bodyInfo: any) {
    const centerX = bodyInfo.centerX;
    const neckY = bodyInfo.shoulderY;
    
    // 添加领带
    ctx.save();
    ctx.fillStyle = '#2563eb';
    ctx.strokeStyle = '#1d4ed8';
    ctx.lineWidth = 2;
    
    // 领带结
    ctx.beginPath();
    ctx.moveTo(centerX - 15, neckY);
    ctx.lineTo(centerX + 15, neckY);
    ctx.lineTo(centerX + 12, neckY + 15);
    ctx.lineTo(centerX - 12, neckY + 15);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 领带主体
    ctx.beginPath();
    ctx.moveTo(centerX - 12, neckY + 15);
    ctx.lineTo(centerX - 8, neckY + 120);
    ctx.lineTo(centerX, neckY + 130);
    ctx.lineTo(centerX + 8, neckY + 120);
    ctx.lineTo(centerX + 12, neckY + 15);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 添加工牌（针对牛马类型）
    if (animalType === 'OXHORSE') {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(centerX + 60, neckY + 30, 70, 90);
      ctx.strokeStyle = '#333';
      ctx.strokeRect(centerX + 60, neckY + 30, 70, 90);
      
      ctx.fillStyle = '#333';
      ctx.font = '10px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('工号: 996', centerX + 65, neckY + 50);
      ctx.fillText('部门: 码农', centerX + 65, neckY + 70);
      ctx.fillText('等级: P0', centerX + 65, neckY + 90);
      ctx.fillText('状态: 搬砖', centerX + 65, neckY + 110);
    }
    
    ctx.restore();
  }

  /**
   * 应用艺术风格
   */
  private applyArtStyle(ctx: Canvas.CanvasRenderingContext2D, style: string) {
    const imageData = ctx.getImageData(0, 0, 1024, 1024);
    const data = imageData.data;
    
    switch(style) {
      case 'cartoon':
        // 卡通风格：增强色彩饱和度
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.min(255, data[i] * 1.2);     // R
          data[i + 1] = Math.min(255, data[i + 1] * 1.2); // G
          data[i + 2] = Math.min(255, data[i + 2] * 1.2); // B
        }
        break;
      case 'sketch':
        // 素描风格：转换为灰度并增加对比度
        for (let i = 0; i < data.length; i += 4) {
          const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
          data[i] = gray;
          data[i + 1] = gray;
          data[i + 2] = gray;
        }
        break;
      default:
        // 默认风格：轻微增强对比度
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.min(255, data[i] * 1.1);
          data[i + 1] = Math.min(255, data[i + 1] * 1.1);
          data[i + 2] = Math.min(255, data[i + 2] * 1.1);
        }
    }
    
    ctx.putImageData(imageData, 0, 0);
    
    // 添加纹理线条
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.08)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i < 20; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * 1024, Math.random() * 1024);
      ctx.lineTo(Math.random() * 1024, Math.random() * 1024);
      ctx.stroke();
    }
  }

  /**
   * 生成降级方案
   */
  private generateFallback(
    canvas: Canvas.Canvas, 
    ctx: Canvas.CanvasRenderingContext2D, 
    animalType: string
  ): OptimizedFusionResult {
    // 简单的降级图像
    ctx.fillStyle = '#f0f4f8';
    ctx.fillRect(0, 0, 1024, 1024);
    
    ctx.strokeStyle = '#4a5568';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // 简单身体轮廓
    ctx.moveTo(412, 400);
    ctx.lineTo(412, 700);
    ctx.lineTo(612, 700);
    ctx.lineTo(612, 400);
    ctx.closePath();
    ctx.stroke();
    
    // 简单头部
    ctx.beginPath();
    ctx.arc(512, 350, 50, 0, Math.PI * 2);
    ctx.stroke();
    
    ctx.fillStyle = '#4a5568';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('简化版融合图像', 512, 800);
    ctx.fillText(`动物类型: ${animalType}`, 512, 820);
    
    const imageBase64 = canvas.toDataURL('image/png').split(',')[1];
    
    return {
      success: true,
      imageUrl: `data:image/png;base64,${imageBase64}`,
      imageBase64: imageBase64,
      fusionDetails: {
        method: 'Fallback Optimized Fusion',
        animalType,
        style: 'Simple',
        features: {
          fallback: true
        }
      }
    };
  }
}