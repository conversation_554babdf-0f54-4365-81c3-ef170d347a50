import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { setGlobalDispatcher, ProxyAgent } from 'undici';

export interface AIFusionResult {
  success: boolean;
  avatar: {
    avatarId: string;
    animalType: string;
    generationMethod: string;
    imageUrl: string;
    filePath?: string;
    prompt?: string;
    fusionDetails: {
      method: string;
      animalType: string;
      style: string;
      model?: string;
    };
  };
  message?: string;
  processingTime?: number;
}

export interface DrawingAnalysis {
  animalType: 'OXHORSE' | 'PET' | 'DIVINE';
  confidence: number;
  features: {
    complexity: number;
    emotionalTone: string;
    dominantColors: string[];
    style: string;
  };
  recommendation: {
    primaryChoice: string;
    reasoning: string;
    alternatives: string[];
  };
}

@Injectable()
export class AIFusionService {
  private readonly logger = new Logger(AIFusionService.name);
  private geminiAPI: GoogleGenerativeAI | null = null;
  private isEnabled: boolean;

  constructor(private configService: ConfigService) {
    this.initializeService();
  }

  private initializeService() {
    const proxyUrl = process.env.HTTP_PROXY || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
    
    // 设置 undici 全局代理 (用于 Google Generative AI)
    const undiciProxyAgent = new ProxyAgent(proxyUrl);
    setGlobalDispatcher(undiciProxyAgent);

    // 为Gemini配置代理
    const geminiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (geminiKey) {
      this.geminiAPI = new GoogleGenerativeAI(geminiKey);
      this.logger.log(`✅ Gemini API 已配置，使用 undici 代理: ${proxyUrl}`);
    } else {
      this.logger.warn('❌ Gemini API Key 未配置');
      this.geminiAPI = null;
    }

    this.isEnabled = this.configService.get<string>('AI_GENERATION_ENABLED') === 'true';
    this.logger.log(`AI服务初始化完成 - 启用状态: ${this.isEnabled}`);
  }

  /**
   * 分析用户绘画
   */
  async analyzeUserDrawing(imageBase64: string): Promise<DrawingAnalysis> {
    try {
      if (!this.isEnabled || !this.geminiAPI) {
        return this.getFallbackAnalysis();
      }

      const model = this.geminiAPI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      const prompt = `作为专业的艺术分析师，请详细分析这幅用户自画像，为后续的动物融合生成提供精确指导。

      请从以下维度进行分析并返回JSON格式：

      COMPOSITION_ANALYSIS: {
          "headPosition": "头部在画面中的位置和大小比例",
          "bodyPosture": "身体姿势描述（站立/坐着/倾斜等）",
          "frameComposition": "整体构图特点",
          "proportions": "头身比例关系"
      }

      STYLE_ANALYSIS: {
          "drawingStyle": "绘画技法（简笔画/写实/卡通/素描等）",
          "lineQuality": "线条特征（粗细/流畅度/风格）",
          "detailLevel": "细节丰富程度（简单/中等/复杂）",
          "colorUsage": "色彩运用情况"
      }

      PERSONALITY_TRAITS: {
          "expression": "面部表情特征",
          "mood": "整体情绪表达",
          "confidence": "自信程度体现",
          "personality": "性格特征推断"
      }

      DISTINCTIVE_FEATURES: {
          "uniqueElements": "独特的个人特征（眼镜/发型/服装等）",
          "artisticChoices": "特殊的艺术表现手法",
          "personalStyle": "个人风格标识"
      }

      FUSION_GUIDANCE: {
          "preserveElements": "需要在融合中保持的关键元素",
          "adaptationStrategy": "动物特征融合策略建议",
          "styleMatching": "如何匹配用户的绘画风格"
      }

      请确保分析结果能够指导AI准确理解用户的个人特色，而不是生成通用的动物头像。`;

      const result = await model.generateContent([
        {
          inlineData: {
            data: imageBase64 ? imageBase64.replace(/^data:image\/\w+;base64,/, '') : '',
            mimeType: 'image/png'
          }
        },
        prompt
      ]);

      const response = result.response;
      const analysisText = response.text();

      // 解析AI返回的分析结果
      return this.parseAnalysisResult(analysisText);

    } catch (error) {
      this.logger.error('分析用户绘画失败:', error);
      return this.getFallbackAnalysis();
    }
  }

  /**
   * 生成融合头像
   */
  async generateFusionAvatar(
    userDrawingBase64: string,
    animalType: 'OXHORSE' | 'PET' | 'DIVINE',
    analysisData?: any
  ): Promise<AIFusionResult> {
    try {
      if (!this.isEnabled) {
        throw new Error('AI生成服务未启用');
      }

      // 优先使用Gemini 2.0 Flash进行图像生成
      if (this.geminiAPI) {
        // 先分析用户绘画特征
        this.logger.log('🔍 开始分析用户自画像特征...');
        const detailedAnalysis = await this.analyzeUserDrawing(userDrawingBase64);
        this.logger.log('📊 用户绘画分析结果:', JSON.stringify(detailedAnalysis, null, 2));
        
        return await this.generateWithGemini(userDrawingBase64, animalType, detailedAnalysis);
      }

      throw new Error('没有可用的AI生成服务');

    } catch (error) {
      this.logger.error('融合头像生成失败:', error);
      // 返回一个失败的结果而不是抛出异常
      return {
        success: false,
        avatar: {
          avatarId: Date.now().toString(),
          animalType,
          generationMethod: 'Failed',
          imageUrl: '',
          fusionDetails: {
            method: 'Failed',
            animalType,
            style: 'N/A'
          }
        },
        message: `头像生成失败: ${error.message}`
      };
    }
  }

  /**
   * 使用Gemini 2.0 Flash生成融合头像
   */
  private async generateWithGemini(
    userDrawingBase64: string,
    animalType: 'OXHORSE' | 'PET' | 'DIVINE',
    analysisData: DrawingAnalysis
  ): Promise<AIFusionResult> {
    // 使用 Gemini 2.0 Flash Preview Image Generation 进行图像生成
    const model = this.geminiAPI!.getGenerativeModel({ 
      model: "gemini-2.0-flash-preview-image-generation",
      generationConfig: {
        temperature: 0.7,
        // responseModalities: ["TEXT", "IMAGE"] as any // 指定需要文本和图像响应
      }
    });

    const animalDescriptions = {
      OXHORSE: 'a dedicated ox or horse head representing the hardworking "996" office worker spirit',
      PET: 'a cute cat or dog head representing a friendly and loyal office companion',
      DIVINE: 'a powerful dragon or phoenix head representing ambitious career goals and leadership'
    };

    const prompt = `CRITICAL TASK: Create a faithful transformation of the provided user self-portrait by adding animal features while preserving 90% of the original drawing.

    MANDATORY PRESERVATION RULES:
    1. BODY STRUCTURE: Keep the exact same body shape, size, and posture from the original drawing
    2. LEGS & FEET: Preserve the original leg positions, length, and foot details exactly as drawn
    3. ARMS & HANDS: Maintain the original arm positions and hand placement
    4. CLOTHING: Keep any clothing or garments exactly as shown in the original
    5. OVERALL COMPOSITION: Use the same framing, positioning, and layout
    6. ARTISTIC STYLE: Match the exact drawing technique, line quality, and color scheme

    COLOR & STYLE MATCHING:
    - If original is BLACK & WHITE sketch → Generate BLACK & WHITE sketch with same line style
    - If original is COLORED drawing → Generate COLORED version matching the original palette
    - If original uses simple lines → Keep lines simple and clean
    - If original is detailed → Maintain the same level of detail
    - NEVER change the artistic medium or complexity level

    MINIMAL ANIMAL MODIFICATIONS (Only 10% of the image):
    - Replace ONLY the head/face area with ${animalDescriptions[animalType]}
    - Keep the animal head the same size and position as the original human head
    - If the original has unique features (glasses, hat, etc.) → Add them to the animal head
    - Add minimal professional elements (small tie, collar) that fit the original style

    TECHNICAL EXECUTION:
    - Start with the user's exact drawing as the base template
    - Modify ONLY the head area, leaving everything else untouched
    - The result should look like "the user's drawing but with an animal head"
    - NOT a new creation, but a direct modification of their artwork

    USER ANALYSIS INSIGHTS: ${JSON.stringify(analysisData, null, 2)}

    EXECUTION CHECKLIST based on analysis:
    ✓ Preserve EXACT body structure from COMPOSITION_ANALYSIS  
    ✓ Match EXACT drawing style from STYLE_ANALYSIS (especially line quality and color usage)
    ✓ Keep the same emotional expression from PERSONALITY_TRAITS
    ✓ Maintain all DISTINCTIVE_FEATURES in animal form (glasses → animal glasses, etc.)
    ✓ Follow FUSION_GUIDANCE for style consistency

    FINAL OUTPUT REQUIREMENTS:
    - 90% identical to original drawing (body, legs, arms, clothing, style)
    - 10% animal head modification that respects original proportions
    - Same artistic medium (sketch/color/style) as original
    - Result should be recognizable as "their drawing + animal head"

    CRITICAL: This is NOT creating a new image. This is MODIFYING their existing self-portrait by replacing only the head with ${animalDescriptions[animalType]} while keeping everything else exactly the same.`;

    try {
      this.logger.log('正在使用 Gemini 2.0 Flash Preview Image Generation 生成图像...');
      
      const result = await model.generateContent([
        prompt,
        {
          inlineData: {
            data: userDrawingBase64 ? userDrawingBase64.replace(/^data:image\/\w+;base64,/, '') : '',
            mimeType: 'image/png'
          }
        }
      ]);

      const response = result.response;
      
      this.logger.log('📊 Gemini 2.0 Flash 响应结构:');
      this.logger.log('- 候选数量:', response.candidates?.length || 0);
      
      // 检查是否有图像数据返回
      if (response.candidates && response.candidates[0]) {
        const candidate = response.candidates[0];
        
        // 如果返回的是图像数据
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.includes('image')) {
              this.logger.log('✅ Gemini 2.0 Flash 成功生成图像');
              
              // 保存图像到文件系统
              const avatarId = Date.now().toString();
              const filename = `avatar-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
              const filePath = path.join(process.cwd(), 'uploads', 'generated-avatars', filename);
              
              // 确保目录存在
              const uploadDir = path.dirname(filePath);
              if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
              }
              
              // 将base64数据写入文件
              const imageBuffer = Buffer.from(part.inlineData.data, 'base64');
              fs.writeFileSync(filePath, imageBuffer);
              
              this.logger.log('💾 图像已保存到:', filePath);
              
              // 返回完整的文件URL
              const imageUrl = `http://localhost:3002/uploads/generated-avatars/${filename}`;
              
              return {
                success: true,
                avatar: {
                  avatarId: avatarId,
                  animalType: animalType,
                  generationMethod: 'Gemini 2.0 Flash Image Generation',
                  imageUrl: imageUrl,
                  filePath: filePath,
                  fusionDetails: {
                    method: 'Gemini 2.0 Flash Direct Generation',
                    animalType: animalType,
                    style: analysisData.features.style || 'Professional',
                    model: 'gemini-2.0-flash-preview-image-generation'
                  }
                }
              };
            }
          }
        }
        
        this.logger.warn('🧠 Gemini图像生成未返回图像数据');
      }
      
      throw new Error('Gemini未返回有效的图像数据');
      
    } catch (error) {
      this.logger.error('🧠 Gemini生成失败:', error);
      throw error;
    }
  }

  /**
   * 解析AI分析结果
   */
  private parseAnalysisResult(analysisText: string): DrawingAnalysis {
    try {
      // 尝试解析JSON结果
      const parsedResult = JSON.parse(analysisText);
      
      // 根据分析结果推荐动物类型
      let animalType: 'OXHORSE' | 'PET' | 'DIVINE' = 'OXHORSE';
      let confidence = 0.7;

      // 基于分析内容智能推荐
      const text = analysisText.toLowerCase();
      if (text.includes('神兽') || text.includes('威严') || text.includes('领导') || text.includes('自信')) {
        animalType = 'DIVINE';
        confidence = 0.8;
      } else if (text.includes('可爱') || text.includes('温和') || text.includes('友善') || text.includes('宠物')) {
        animalType = 'PET';
        confidence = 0.75;
      }

      return {
        animalType,
        confidence,
        features: {
          complexity: this.extractComplexity(analysisText),
          emotionalTone: this.extractEmotionalTone(analysisText),
          dominantColors: this.extractColors(analysisText),
          style: this.extractStyle(analysisText)
        },
        recommendation: {
          primaryChoice: animalType,
          reasoning: `基于AI分析结果推荐${animalType}类型`,
          alternatives: ['OXHORSE', 'PET', 'DIVINE'].filter(t => t !== animalType)
        }
      };

    } catch (error) {
      this.logger.warn('解析AI分析结果失败，使用默认分析:', error);
      return this.getFallbackAnalysis();
    }
  }

  /**
   * 获取回退分析结果
   */
  private getFallbackAnalysis(): DrawingAnalysis {
    return {
      animalType: 'OXHORSE',
      confidence: 0.6,
      features: {
        complexity: 50,
        emotionalTone: 'neutral',
        dominantColors: ['#000000', '#FFFFFF'],
        style: 'sketch'
      },
      recommendation: {
        primaryChoice: 'OXHORSE',
        reasoning: '默认推荐牛马类型，适合大部分打工人',
        alternatives: ['PET', 'DIVINE']
      }
    };
  }

  /**
   * 提取复杂度
   */
  private extractComplexity(text: string): number {
    if (text.includes('复杂') || text.includes('详细')) return 80;
    if (text.includes('中等') || text.includes('适中')) return 50;
    if (text.includes('简单') || text.includes('简笔')) return 30;
    return 50;
  }

  /**
   * 提取情感色调
   */
  private extractEmotionalTone(text: string): string {
    if (text.includes('快乐') || text.includes('开心')) return 'happy';
    if (text.includes('悲伤') || text.includes('忧郁')) return 'sad';
    if (text.includes('愤怒') || text.includes('生气')) return 'angry';
    if (text.includes('平静') || text.includes('中性')) return 'calm';
    return 'neutral';
  }

  /**
   * 提取颜色
   */
  private extractColors(text: string): string[] {
    const colors = [];
    if (text.includes('黑') || text.includes('black')) colors.push('#000000');
    if (text.includes('白') || text.includes('white')) colors.push('#FFFFFF');
    if (text.includes('红') || text.includes('red')) colors.push('#FF0000');
    if (text.includes('蓝') || text.includes('blue')) colors.push('#0000FF');
    if (text.includes('绿') || text.includes('green')) colors.push('#00FF00');
    
    return colors.length > 0 ? colors : ['#000000', '#FFFFFF'];
  }

  /**
   * 提取风格
   */
  private extractStyle(text: string): string {
    if (text.includes('写实') || text.includes('realistic')) return 'realistic';
    if (text.includes('卡通') || text.includes('cartoon')) return 'cartoon';
    if (text.includes('素描') || text.includes('sketch')) return 'sketch';
    if (text.includes('简笔') || text.includes('simple')) return 'simple';
    return 'sketch';
  }

  /**
   * 获取服务状态
   */
  getServiceStatus() {
    return {
      enabled: this.isEnabled,
      geminiAvailable: !!this.geminiAPI,
      status: this.isEnabled ? 'active' : 'disabled'
    };
  }
}