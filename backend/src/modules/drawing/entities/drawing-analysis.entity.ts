import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

@Entity('drawing_analyses')
@Index(['userId', 'createdAt'])
@Index(['animalType', 'createdAt'])
export class DrawingAnalysis {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column('text', { comment: '原始绘画图像base64数据' })
  originalImageData: string;

  @Column({ length: 500, nullable: true, comment: '原始图像存储URL' })
  originalImageUrl?: string;

  @Column('text', { comment: 'AI分析结果详细描述' })
  analysisResult: string;

  @Column({
    type: 'enum',
    enum: AnimalCategory,
    comment: '检测到的动物类型',
  })
  animalType: AnimalCategory;

  @Column({
    type: 'enum',
    enum: AnimalSpecies,
    nullable: true,
    comment: '具体动物种类',
  })
  animalSpecies?: AnimalSpecies;

  @Column('json', { comment: '面部特征分析JSON数据' })
  faceFeatures: {
    faceShape: string;
    expression: string;
    style: string;
    complexity: string;
    lineCount?: number;
    darkRatio?: number;
  };

  @Column('decimal', { precision: 5, scale: 2, comment: '置信度评分 0-100' })
  confidence: number;

  @Column('json', { nullable: true, comment: '像素分析数据' })
  pixelAnalysis?: {
    width: number;
    height: number;
    aspectRatio: number;
    lineComplexity: number;
    dominantColors: string[];
  };

  @Column({ length: 100, default: 'gemini_analysis', comment: '分析方法' })
  analysisMethod: string;

  @Column('int', { nullable: true, comment: '处理时间（毫秒）' })
  processingTime?: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}