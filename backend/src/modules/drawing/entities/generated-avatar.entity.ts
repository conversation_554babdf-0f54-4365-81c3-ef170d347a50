import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToOne,
  Index,
} from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { DrawingAnalysis } from './drawing-analysis.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

@Entity('generated_avatars')
@Index(['userId', 'createdAt'])
@Index(['animalType', 'createdAt'])
export class GeneratedAvatar {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'analysis_id', nullable: true })
  analysisId?: string;

  @OneToOne(() => DrawingAnalysis, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'analysis_id' })
  analysis?: DrawingAnalysis;

  @Column({
    type: 'enum',
    enum: AnimalCategory,
    comment: '动物类型',
  })
  animalType: AnimalCategory;

  @Column({
    type: 'enum',
    enum: AnimalSpecies,
    comment: '具体动物种类',
  })
  animalSpecies: AnimalSpecies;

  @Column('text', { comment: '生成的头像图像base64数据' })
  avatarImageData: string;

  @Column({ length: 500, nullable: true, comment: '头像图像存储URL' })
  avatarImageUrl?: string;

  @Column({ length: 100, default: 'sketch', comment: '头像风格' })
  style: string;

  @Column('json', { comment: '头像特性配置' })
  features: {
    fusion: string;
    quality: string;
    type: string;
  };

  @Column('json', { comment: '生成的打工人属性' })
  stats: {
    workEfficiency: number;
    happiness: number;
    energy: number;
    creativity: number;
  };

  @Column({ length: 100, comment: '生成方法' })
  generationMethod: string;

  @Column('text', { nullable: true, comment: '生成过程描述' })
  generationPrompt?: string;

  @Column('int', { nullable: true, comment: '生成时间（毫秒）' })
  processingTime?: number;

  @Column('json', { nullable: true, comment: '额外的生成参数' })
  metadata?: {
    modelVersion?: string;
    temperature?: number;
    guidanceScale?: number;
    steps?: number;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}