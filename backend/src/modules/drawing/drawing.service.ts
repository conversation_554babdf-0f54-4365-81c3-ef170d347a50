import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AIService, AnalysisResult, AvatarGenerationResult } from '@/modules/ai/ai.service';
import { AIFusionService, AIFusionResult } from './services/ai-fusion.service';
import { DrawingAnalysis } from './entities/drawing-analysis.entity';
import { GeneratedAvatar } from './entities/generated-avatar.entity';
import {
  AnalyzeDrawingDto,
  AnalyzeDrawingResponseDto,
  GenerateAvatarDto,
  GenerateAvatarResponseDto,
  DrawingHistoryQueryDto,
  DrawingHistoryResponseDto,
  DrawingHistoryItemDto,
} from './dto';
import { AnimalCategory, AnimalSpecies, ANIMAL_CONFIG } from '@/common/constants/animal-types';

@Injectable()
export class DrawingService {
  private readonly logger = new Logger(DrawingService.name);

  constructor(
    @InjectRepository(DrawingAnalysis)
    private drawingAnalysisRepository: Repository<DrawingAnalysis>,
    @InjectRepository(GeneratedAvatar)
    private generatedAvatarRepository: Repository<GeneratedAvatar>,
    private aiService: AIService,
    private aiFusionService: AIFusionService,
    private configService: ConfigService,
  ) {}

  /**
   * 分析用户绘画
   */
  async analyzeDrawing(
    userId: string,
    analyzeDrawingDto: AnalyzeDrawingDto,
  ): Promise<AnalyzeDrawingResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Starting drawing analysis for user ${userId}`);
      
      // 验证图像数据格式
      this.validateImageData(analyzeDrawingDto.imageData);
      
      // 使用AI服务分析绘画
      const analysisResult: AnalysisResult = await this.aiService.analyzeDrawing(
        analyzeDrawingDto.imageData
      );
      
      const processingTime = Date.now() - startTime;
      
      // 保存分析结果到数据库
      const drawingAnalysis = await this.saveAnalysisResult(userId, analyzeDrawingDto, analysisResult, processingTime);
      
      // 获取推荐动物的详细信息
      const recommendedAnimalConfig = ANIMAL_CONFIG[analysisResult.recommendedSpecies];
      
      this.logger.log(`Drawing analysis completed for user ${userId} in ${processingTime}ms`);
      
      return {
        success: true,
        analysisId: drawingAnalysis.id,
        detectedAnimalType: analysisResult.detectedAnimalType,
        analysisDescription: analysisResult.description,
        confidence: analysisResult.confidence,
        faceFeatures: analysisResult.faceFeatures,
        recommendedAnimal: {
          species: analysisResult.recommendedSpecies,
          name: recommendedAnimalConfig.name,
          description: recommendedAnimalConfig.description,
          color: recommendedAnimalConfig.color,
        },
        timestamp: drawingAnalysis.createdAt,
      };
    } catch (error) {
      this.logger.error(`Drawing analysis failed for user ${userId}:`, error);
      throw new BadRequestException(`绘画分析失败: ${error.message}`);
    }
  }

  /**
   * 生成动物版自画像
   */
  async generateAvatar(
    userId: string,
    generateAvatarDto: GenerateAvatarDto,
  ): Promise<GenerateAvatarResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Starting avatar generation for user ${userId}`);
      
      // 验证图像数据格式
      this.validateImageData(generateAvatarDto.imageData);
      
      let analysisResult: AnalysisResult | undefined;
      
      // 如果提供了分析ID，从数据库获取分析结果
      if (generateAvatarDto.analysisId) {
        const existingAnalysis = await this.drawingAnalysisRepository.findOne({
          where: { id: generateAvatarDto.analysisId, userId },
        });
        
        if (existingAnalysis) {
          analysisResult = {
            description: existingAnalysis.analysisResult,
            faceFeatures: existingAnalysis.faceFeatures,
            pixelAnalysis: existingAnalysis.pixelAnalysis,
            confidence: Number(existingAnalysis.confidence),
            detectedAnimalType: existingAnalysis.animalType,
            recommendedSpecies: existingAnalysis.animalSpecies || this.getDefaultSpecies(existingAnalysis.animalType),
          };
        }
      }
      
      // 如果没有现有分析，进行快速分析
      if (!analysisResult) {
        this.logger.log('No existing analysis found, performing quick analysis');
        analysisResult = await this.aiService.analyzeDrawing(generateAvatarDto.imageData);
      }
      
      // 使用AI服务生成头像
      const avatarResult: AvatarGenerationResult = await this.aiService.generateAvatar(
        generateAvatarDto.imageData,
        generateAvatarDto.animalType,
        generateAvatarDto.animalSpecies,
        analysisResult
      );
      
      const totalProcessingTime = Date.now() - startTime;
      
      // 确定最终动物种类
      const finalSpecies = generateAvatarDto.animalSpecies || 
                          analysisResult.recommendedSpecies ||
                          this.getDefaultSpecies(generateAvatarDto.animalType);
      
      // 保存生成结果到数据库
      const generatedAvatar = await this.saveGeneratedAvatarLegacy(
        userId,
        generateAvatarDto,
        avatarResult,
        finalSpecies,
        totalProcessingTime,
        generateAvatarDto.analysisId
      );
      
      this.logger.log(`Avatar generation completed for user ${userId} in ${totalProcessingTime}ms`);
      
      return {
        success: true,
        avatar: {
          avatarId: generatedAvatar.id,
          animalType: generateAvatarDto.animalType,
          animalSpecies: finalSpecies,
          imageUrl: generatedAvatar.avatarImageUrl || `data:image/png;base64,${avatarResult.imageBase64}`,
          imageBase64: avatarResult.imageBase64,
          style: generatedAvatar.style,
          features: generatedAvatar.features,
          stats: generatedAvatar.stats,
        },
        generationMethod: avatarResult.generationMethod,
        processingTime: totalProcessingTime,
        message: '动物版自画像生成成功！',
        timestamp: generatedAvatar.createdAt,
      };
    } catch (error) {
      this.logger.error(`Avatar generation failed for user ${userId}:`, error);
      throw new BadRequestException(`头像生成失败: ${error.message}`);
    }
  }

  /**
   * 获取用户绘画历史
   */
  async getDrawingHistory(
    userId: string,
    query: DrawingHistoryQueryDto,
  ): Promise<DrawingHistoryResponseDto> {
    try {
      this.logger.log(`Fetching drawing history for user ${userId}`);
      
      const queryBuilder = this.generatedAvatarRepository
        .createQueryBuilder('avatar')
        .leftJoinAndSelect('avatar.analysis', 'analysis')
        .where('avatar.userId = :userId', { userId })
        .orderBy('avatar.createdAt', 'DESC');
      
      // 按动物类型筛选
      if (query.animalType) {
        queryBuilder.andWhere('avatar.animalType = :animalType', {
          animalType: query.animalType,
        });
      }
      
      // 分页
      const limit = query.limit || 10;
      const page = query.page || 1;
      const skip = (page - 1) * limit;
      
      queryBuilder.skip(skip).take(limit);
      
      const [avatars, total] = await queryBuilder.getManyAndCount();
      
      // 转换为DTO格式
      const items: DrawingHistoryItemDto[] = avatars.map((avatar) => {
        const animalConfig = ANIMAL_CONFIG[avatar.animalSpecies];
        
        return {
          id: avatar.id,
          originalImageUrl: avatar.analysis?.originalImageUrl || 
                           `data:image/png;base64,${avatar.analysis?.originalImageData || ''}`,
          avatarImageUrl: avatar.avatarImageUrl || 
                         `data:image/png;base64,${avatar.avatarImageData}`,
          animalType: avatar.animalType,
          animalName: animalConfig?.name || avatar.animalSpecies,
          analysisResult: avatar.analysis?.analysisResult || '未知分析结果',
          generationMethod: avatar.generationMethod,
          confidence: avatar.analysis ? Number(avatar.analysis.confidence) : 0,
          createdAt: avatar.createdAt,
          updatedAt: avatar.updatedAt,
        };
      });
      
      const totalPages = Math.ceil(total / limit);
      
      this.logger.log(`Found ${total} drawing history items for user ${userId}`);
      
      return {
        items,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch drawing history for user ${userId}:`, error);
      throw new BadRequestException(`获取绘画历史失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取分析结果
   */
  async getAnalysisById(userId: string, analysisId: string): Promise<DrawingAnalysis> {
    const analysis = await this.drawingAnalysisRepository.findOne({
      where: { id: analysisId, userId },
    });
    
    if (!analysis) {
      throw new NotFoundException('分析结果不存在');
    }
    
    return analysis;
  }

  /**
   * 根据ID获取生成的头像
   */
  async getAvatarById(userId: string, avatarId: string): Promise<GeneratedAvatar> {
    const avatar = await this.generatedAvatarRepository.findOne({
      where: { id: avatarId, userId },
      relations: ['analysis'],
    });
    
    if (!avatar) {
      throw new NotFoundException('头像不存在');
    }
    
    return avatar;
  }

  private validateImageData(imageData: string): void {
    if (!imageData) {
      throw new BadRequestException('图像数据不能为空');
    }
    
    // 检查是否是base64格式
    const base64Pattern = /^data:image\/(png|jpeg|jpg|gif|webp);base64,/;
    const isDataUrl = base64Pattern.test(imageData);
    
    if (!isDataUrl && !imageData.match(/^[A-Za-z0-9+/]*={0,2}$/)) {
      throw new BadRequestException('图像数据格式不正确，请提供有效的base64数据');
    }
    
    // 检查数据大小（限制为10MB）
    const sizeInBytes = (imageData.length * 3) / 4;
    const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
    
    if (sizeInBytes > maxSizeInBytes) {
      throw new BadRequestException('图像数据过大，请提供小于10MB的图像');
    }
  }

  private async saveAnalysisResult(
    userId: string,
    dto: AnalyzeDrawingDto,
    result: AnalysisResult,
    processingTime: number,
  ): Promise<DrawingAnalysis> {
    const drawingAnalysis = this.drawingAnalysisRepository.create({
      userId,
      originalImageData: dto.imageData,
      analysisResult: result.description,
      animalType: result.detectedAnimalType,
      animalSpecies: result.recommendedSpecies,
      faceFeatures: result.faceFeatures,
      pixelAnalysis: result.pixelAnalysis,
      confidence: result.confidence,
      analysisMethod: 'ai_service',
      processingTime,
    });
    
    return this.drawingAnalysisRepository.save(drawingAnalysis);
  }

  private async saveGeneratedAvatarLegacy(
    userId: string,
    dto: GenerateAvatarDto,
    result: AvatarGenerationResult,
    animalSpecies: AnimalSpecies,
    processingTime: number,
    analysisId?: string,
  ): Promise<GeneratedAvatar> {
    const generatedAvatar = this.generatedAvatarRepository.create({
      userId,
      analysisId,
      animalType: dto.animalType,
      animalSpecies,
      avatarImageData: result.imageBase64,
      avatarImageUrl: result.imageUrl,
      style: 'sketch',
      features: {
        fusion: 'deep',
        quality: 'high',
        type: 'human_animal_hybrid',
      },
      stats: result.stats,
      generationMethod: result.generationMethod,
      generationPrompt: result.prompt,
      processingTime,
      metadata: {
        modelVersion: '1.0.0',
      },
    });
    
    return this.generatedAvatarRepository.save(generatedAvatar);
  }

  private getDefaultSpecies(animalType: AnimalCategory): AnimalSpecies {
    switch (animalType) {
      case AnimalCategory.DIVINE_BEAST:
        return AnimalSpecies.DRAGON;
      case AnimalCategory.PET:
        return AnimalSpecies.GOLDEN_RETRIEVER;
      case AnimalCategory.WORKING_ANIMAL:
      default:
        return AnimalSpecies.OX;
    }
  }

  /**
   * 保存AI融合生成的头像 (兼容Controller调用)
   */
  public async saveGeneratedAvatar(
    userId: string,
    fusionDto: { animalType: string; imageData: string; analysisData?: any },
    fusionResult: AIFusionResult,
  ): Promise<GeneratedAvatar> {
    try {
      // 转换动物类型
      let animalCategory: AnimalCategory;
      let animalSpecies: AnimalSpecies;

      switch (fusionDto.animalType) {
        case 'OXHORSE':
          animalCategory = AnimalCategory.WORKING_ANIMAL;
          animalSpecies = AnimalSpecies.OX;
          break;
        case 'PET':
          animalCategory = AnimalCategory.PET;
          animalSpecies = AnimalSpecies.GOLDEN_RETRIEVER;
          break;
        case 'DIVINE':
          animalCategory = AnimalCategory.DIVINE_BEAST;
          animalSpecies = AnimalSpecies.DRAGON;
          break;
        default:
          animalCategory = AnimalCategory.WORKING_ANIMAL;
          animalSpecies = AnimalSpecies.OX;
      }

      const generatedAvatar = this.generatedAvatarRepository.create({
        userId,
        analysisId: undefined, // AI Fusion 不需要预先的分析ID
        animalType: animalCategory,
        animalSpecies,
        avatarImageData: fusionResult.avatar?.imageUrl || '',
        avatarImageUrl: fusionResult.avatar?.imageUrl || '',
        style: 'ai_fusion',
        features: {
          fusion: fusionResult.avatar?.fusionDetails?.method || 'ai_fusion',
          quality: 'high',
          type: 'ai_generated_fusion',
          animalType: fusionResult.avatar?.fusionDetails?.animalType || fusionDto.animalType,
        },
        stats: {
          processingTime: fusionResult.processingTime || 0,
          confidence: 0.95, // AI Fusion 默认高置信度
        },
        generationMethod: fusionResult.avatar?.generationMethod || 'AI Fusion',
        generationPrompt: '',
        processingTime: fusionResult.processingTime || 0,
        metadata: {
          modelVersion: '1.0.0',
          fusionType: fusionResult.avatar?.fusionDetails?.method || 'ai_fusion',
          originalAnimalType: fusionDto.animalType,
        },
      });

      const savedAvatar = await this.generatedAvatarRepository.save(generatedAvatar);
      
      this.logger.log(`AI融合头像已保存: ${savedAvatar.id}, 用户: ${userId}, 动物类型: ${fusionDto.animalType}`);
      
      return savedAvatar;

    } catch (error) {
      this.logger.error(`保存AI融合头像失败: ${error.message}`, error.stack);
      throw new BadRequestException(`保存头像失败: ${error.message}`);
    }
  }
}