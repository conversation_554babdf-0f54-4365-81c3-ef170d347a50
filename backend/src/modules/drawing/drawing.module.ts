import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DrawingController, AvatarController, GenerateAvatarController } from './drawing.controller';
import { DrawingService } from './drawing.service';
import { AIFusionService } from './services/ai-fusion.service';
import { EnhancedFusionGeneratorService } from './services/enhanced-fusion-generator.service';
import { OptimizedFusionGeneratorService } from './services/optimized-fusion-generator.service';
import { DrawingAnalysis } from './entities/drawing-analysis.entity';
import { GeneratedAvatar } from './entities/generated-avatar.entity';
import { AIModule } from '@/modules/ai/ai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([DrawingAnalysis, GeneratedAvatar]),
    ConfigModule,
    AIModule,
  ],
  controllers: [DrawingController, AvatarController, GenerateAvatarController],
  providers: [
    DrawingService, 
    AIFusionService, 
    EnhancedFusionGeneratorService, 
    OptimizedFusionGeneratorService
  ],
  exports: [
    DrawingService, 
    AIFusionService, 
    EnhancedFusionGeneratorService, 
    OptimizedFusionGeneratorService
  ],
})
export class DrawingModule {}