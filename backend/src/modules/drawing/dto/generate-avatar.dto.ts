import { IsNotEmpty, IsString, IsOptional, IsUUID, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

export class GenerateAvatarDto {
  @ApiProperty({
    description: '用户绘画的base64图像数据',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAK...',
  })
  @IsString()
  @IsNotEmpty()
  imageData: string;

  @ApiProperty({
    description: '动物类型',
    enum: AnimalCategory,
  })
  @IsEnum(AnimalCategory)
  @IsNotEmpty()
  animalType: AnimalCategory;

  @ApiProperty({
    description: '具体动物种类（可选）',
    enum: AnimalSpecies,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalSpecies)
  animalSpecies?: AnimalSpecies;

  @ApiProperty({
    description: '分析结果ID（如果已有分析结果）',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  analysisId?: string;
}

export class GenerateAvatarResponseDto {
  @ApiProperty({ description: '生成是否成功' })
  success: boolean;

  @ApiProperty({ description: '生成的头像数据' })
  avatar: {
    avatarId: string;
    animalType: AnimalCategory;
    animalSpecies: AnimalSpecies;
    imageUrl: string;
    imageBase64: string;
    style: string;
    features: {
      fusion: string;
      quality: string;
      type: string;
    };
    stats: {
      workEfficiency: number;
      happiness: number;
      energy: number;
      creativity: number;
    };
  };

  @ApiProperty({ description: '生成方法' })
  generationMethod: string;

  @ApiProperty({ description: '处理时间（毫秒）' })
  processingTime: number;

  @ApiProperty({ description: '消息' })
  message: string;

  @ApiProperty({ description: '生成时间戳' })
  timestamp: Date;
}