import { IsNotEmpty, IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AnimalCategory } from '@/common/constants/animal-types';

export class AnalyzeDrawingDto {
  @ApiProperty({
    description: '用户绘画的base64图像数据',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoAAAAK...',
  })
  @IsString()
  @IsNotEmpty()
  imageData: string;

  @ApiProperty({
    description: '可选的动物类型偏好',
    enum: AnimalCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalCategory)
  preferredAnimalType?: AnimalCategory;
}

export class AnalyzeDrawingResponseDto {
  @ApiProperty({ description: '分析是否成功' })
  success: boolean;

  @ApiProperty({ description: '分析结果ID' })
  analysisId: string;

  @ApiProperty({ description: '检测到的动物类型' })
  detectedAnimalType: AnimalCategory;

  @ApiProperty({ description: 'AI分析的详细描述' })
  analysisDescription: string;

  @ApiProperty({ description: '置信度评分 (0-100)' })
  confidence: number;

  @ApiProperty({ description: '面部特征分析' })
  faceFeatures: {
    faceShape: string;
    expression: string;
    style: string;
    complexity: string;
  };

  @ApiProperty({ description: '推荐的动物特征' })
  recommendedAnimal: {
    species: string;
    name: string;
    description: string;
    color: string;
  };

  @ApiProperty({ description: '分析时间戳' })
  timestamp: Date;
}