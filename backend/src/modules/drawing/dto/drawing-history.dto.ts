import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { AnimalCategory } from '@/common/constants/animal-types';

export class DrawingHistoryQueryDto {
  @ApiProperty({
    description: '页码',
    default: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    default: 10,
    minimum: 1,
    maximum: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiProperty({
    description: '筛选动物类型',
    enum: AnimalCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnimalCategory)
  animalType?: AnimalCategory;
}

export class DrawingHistoryItemDto {
  @ApiProperty({ description: '绘画记录ID' })
  id: string;

  @ApiProperty({ description: '原始绘画图像URL' })
  originalImageUrl: string;

  @ApiProperty({ description: '生成的头像URL' })
  avatarImageUrl: string;

  @ApiProperty({ description: '动物类型' })
  animalType: AnimalCategory;

  @ApiProperty({ description: '动物种类名称' })
  animalName: string;

  @ApiProperty({ description: 'AI分析结果' })
  analysisResult: string;

  @ApiProperty({ description: '生成方法' })
  generationMethod: string;

  @ApiProperty({ description: '置信度' })
  confidence: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '最后更新时间' })
  updatedAt: Date;
}

export class DrawingHistoryResponseDto {
  @ApiProperty({ description: '绘画历史列表', type: [DrawingHistoryItemDto] })
  items: DrawingHistoryItemDto[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;
}