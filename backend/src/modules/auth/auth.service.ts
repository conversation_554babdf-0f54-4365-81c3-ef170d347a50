import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

import { User, AuthProvider, UserStatus } from '../user/entities/user.entity';
import { UserProfile } from '../user/entities/user-profile.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { CurrentUserInfo } from '@/common/decorators/current-user.decorator';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    private jwtService: JwtService,
    private configService: ConfigService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { username, email, password, phone } = registerDto;

    // 检查用户名是否存在
    const existingUser = await this.userRepository.findOne({
      where: [{ username }, { email }, ...(phone ? [{ phone }] : [])],
    });

    if (existingUser) {
      throw new ConflictException('用户名、邮箱或手机号已存在');
    }

    // 哈希密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = this.userRepository.create({
      username,
      email,
      phone,
      passwordHash,
      authProvider: AuthProvider.LOCAL,
      status: UserStatus.ACTIVE,
    });

    const savedUser = await this.userRepository.save(user);

    // 创建用户档案
    const profile = this.userProfileRepository.create({
      user: savedUser,
      points: 100, // 新用户奖励积分
      level: 1,
      settings: {
        privacy: {
          profileVisibility: 'public',
          allowMessages: true,
          allowFriendRequests: true,
        },
        notifications: {
          email: true,
          push: true,
          likes: true,
          comments: true,
          feeds: true,
          system: true,
        },
      },
    });

    await this.userProfileRepository.save(profile);

    // 生成令牌
    const tokens = await this.generateTokens(savedUser);

    return {
      user: {
        id: savedUser.id,
        username: savedUser.username,
        email: savedUser.email,
        nickname: savedUser.nickname,
        avatarUrl: savedUser.avatarUrl,
        roles: ['user'], // 默认角色
      },
      ...tokens,
    };
  }

  async login(
    loginDto: LoginDto,
    context?: { userAgent: string; ip: string },
  ): Promise<AuthResponseDto> {
    const { identifier, password } = loginDto;

    // 查找用户（支持用户名、邮箱登录）
    const user = await this.userRepository
      .createQueryBuilder('user')
      .addSelect('user.passwordHash')
      .where('user.username = :identifier', { identifier })
      .orWhere('user.email = :identifier', { identifier })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('登录凭证无效');
    }

    // 检查用户状态
    if (user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('账号已被禁用');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('登录凭证无效');
    }

    // 记录登录信息
    if (context) {
      await this.recordLoginActivity(user.id, context);
    }

    // 生成令牌
    const tokens = await this.generateTokens(user);

    return {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        nickname: user.nickname,
        avatarUrl: user.avatarUrl,
        roles: ['user'],
      },
      ...tokens,
    };
  }

  async refreshTokens(refreshToken: string): Promise<AuthResponseDto> {
    try {
      // 验证刷新令牌
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      // 检查令牌是否在缓存中（黑名单检查）
      const isBlacklisted = await this.cacheManager.get(`blacklist:${refreshToken}`);
      if (isBlacklisted) {
        throw new UnauthorizedException('刷新令牌已失效');
      }

      // 获取用户信息
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new UnauthorizedException('用户不存在或已被禁用');
      }

      // 将旧的刷新令牌加入黑名单
      const refreshTTL = 7 * 24 * 60 * 60; // 7天
      await this.cacheManager.set(`blacklist:${refreshToken}`, true, refreshTTL);

      // 生成新令牌
      const tokens = await this.generateTokens(user);

      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          nickname: user.nickname,
          avatarUrl: user.avatarUrl,
          roles: ['user'],
        },
        ...tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('刷新令牌无效');
    }
  }

  async logout(userId: string): Promise<void> {
    // 可以在这里实现令牌黑名单逻辑
    // 或者删除服务器端的会话信息
    await this.cacheManager.del(`user_session:${userId}`);
  }

  async forgotPassword(email: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 生成重置令牌
    const resetToken = uuidv4();
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 1); // 1小时后过期

    // 存储重置令牌（使用缓存）
    await this.cacheManager.set(
      `reset_token:${resetToken}`,
      { userId: user.id, email },
      3600, // 1小时 TTL
    );

    // TODO: 发送重置密码邮件
    // await this.emailService.sendPasswordResetEmail(email, resetToken);

    console.log(`Reset token for ${email}: ${resetToken}`); // 开发环境日志
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, newPassword } = resetPasswordDto;

    // 验证重置令牌
    const resetData = await this.cacheManager.get(`reset_token:${token}`);
    if (!resetData) {
      throw new BadRequestException('重置令牌无效或已过期');
    }

    const { userId } = resetData as { userId: string; email: string };

    // 哈希新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新用户密码
    await this.userRepository.update(userId, { passwordHash });

    // 删除重置令牌
    await this.cacheManager.del(`reset_token:${token}`);

    // 清除用户所有会话
    await this.cacheManager.del(`user_session:${userId}`);
  }

  async getProfile(userId: string): Promise<CurrentUserInfo> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      roles: ['user'],
    };
  }

  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: ['user'],
    };

    // 生成访问令牌
    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '1h'),
    });

    // 生成刷新令牌
    const refreshToken = this.jwtService.sign(
      { sub: user.id },
      {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
        expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
      },
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: 3600, // 1小时
    };
  }

  private async recordLoginActivity(
    userId: string,
    context: { userAgent: string; ip: string },
  ): Promise<void> {
    const loginRecord = {
      userId,
      timestamp: new Date(),
      userAgent: context.userAgent,
      ip: context.ip,
    };

    // 记录到缓存，用于安全监控
    await this.cacheManager.set(
      `last_login:${userId}`,
      loginRecord,
      24 * 60 * 60, // 24小时
    );
  }
}