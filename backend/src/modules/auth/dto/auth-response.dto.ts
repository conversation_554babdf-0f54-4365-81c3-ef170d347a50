import { ApiProperty } from '@nestjs/swagger';

export class UserInfo {
  @ApiProperty({
    description: '用户ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'john_doe',
  })
  username: string;

  @ApiProperty({
    description: '邮箱',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiProperty({
    description: '昵称',
    example: '打工人小王',
  })
  nickname?: string;

  @ApiProperty({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
  })
  avatarUrl?: string;

  @ApiProperty({
    description: '用户角色',
    example: ['user'],
    isArray: true,
  })
  roles: string[];
}

export class AuthResponseDto {
  @ApiProperty({
    description: '用户信息',
    type: UserInfo,
  })
  user: UserInfo;

  @ApiProperty({
    description: '访问令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: '令牌过期时间（秒）',
    example: 3600,
  })
  expiresIn: number;
}