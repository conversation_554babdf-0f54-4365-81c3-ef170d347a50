import { IsString, IsNotEmpty, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: '用户名或邮箱',
    example: 'john_doe',
  })
  @IsString()
  @IsNotEmpty({ message: '用户名或邮箱不能为空' })
  identifier: string;

  @ApiProperty({
    description: '密码',
    example: 'Password123!',
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty({ message: '密码不能为空' })
  @MinLength(6, { message: '密码长度至少6位' })
  password: string;
}