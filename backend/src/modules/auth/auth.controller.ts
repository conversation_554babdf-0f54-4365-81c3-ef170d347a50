import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Request } from 'express';

import { AuthService } from './auth.service';
import { Public } from '@/common/decorators/public.decorator';
import { CurrentUser, CurrentUserInfo } from '@/common/decorators/current-user.decorator';
import { ApiResponseWrapper, ApiErrorResponse } from '@/common/decorators/api-response.decorator';

// DTOs
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { CurrentUserInfoDto } from './dto/current-user-info.dto';
import { AuthResponseDto } from './dto/auth-response.dto';

@ApiTags('认证')
@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @Public()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: '用户注册',
    description: '创建新用户账号',
  })
  @ApiResponseWrapper(AuthResponseDto, 201, '注册成功')
  @ApiErrorResponse(400, '注册参数错误')
  @ApiErrorResponse(409, '用户名或邮箱已存在')
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
    return this.authService.register(registerDto);
  }

  @Post('login')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '用户登录',
    description: '用户名/邮箱密码登录',
  })
  @ApiResponseWrapper(AuthResponseDto, 200, '登录成功')
  @ApiErrorResponse(401, '登录凭证无效')
  @ApiErrorResponse(429, '登录尝试过于频繁')
  async login(@Body() loginDto: LoginDto, @Req() req: Request): Promise<AuthResponseDto> {
    const userAgent = req.get('User-Agent') || '';
    const ip = req.ip;
    return this.authService.login(loginDto, { userAgent, ip });
  }

  @Post('refresh')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '刷新令牌',
    description: '使用刷新令牌获取新的访问令牌',
  })
  @ApiResponseWrapper(AuthResponseDto, 200, '令牌刷新成功')
  @ApiErrorResponse(401, '刷新令牌无效')
  async refreshTokens(@Body() refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto> {
    return this.authService.refreshTokens(refreshTokenDto.refreshToken);
  }

  @Post('logout')
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '用户登出',
    description: '注销当前用户会话',
  })
  @ApiResponse({
    status: 200,
    description: '登出成功',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '登出成功' },
      },
    },
  })
  async logout(@CurrentUser() user: CurrentUserInfo): Promise<{ message: string }> {
    await this.authService.logout(user.id);
    return { message: '登出成功' };
  }

  @Post('forgot-password')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '忘记密码',
    description: '发送重置密码邮件',
  })
  @ApiResponse({
    status: 200,
    description: '重置密码邮件已发送',
  })
  @ApiErrorResponse(404, '用户不存在')
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    await this.authService.forgotPassword(forgotPasswordDto.email);
    return { message: '重置密码邮件已发送' };
  }

  @Post('reset-password')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '重置密码',
    description: '使用重置令牌设置新密码',
  })
  @ApiResponse({
    status: 200,
    description: '密码重置成功',
  })
  @ApiErrorResponse(400, '重置令牌无效或已过期')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    await this.authService.resetPassword(resetPasswordDto);
    return { message: '密码重置成功' };
  }

  @Get('profile')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '获取当前用户信息',
    description: '获取当前登录用户的基本信息',
  })
  @ApiResponseWrapper(CurrentUserInfoDto, 200, '获取成功')
  async getProfile(@CurrentUser() user: CurrentUserInfo): Promise<CurrentUserInfo> {
    return this.authService.getProfile(user.id);
  }

  // OAuth 登录端点
  @Get('google')
  @Public()
  @ApiOperation({
    summary: 'Google OAuth登录',
    description: '重定向到Google OAuth授权页面',
  })
  async googleAuth() {
    // 实现 Google OAuth 重定向
    // 这里通常会重定向到 Google OAuth URL
    throw new Error('Google OAuth not implemented yet');
  }

  @Get('google/callback')
  @Public()
  @ApiOperation({
    summary: 'Google OAuth回调',
    description: '处理Google OAuth回调',
  })
  async googleAuthCallback(@Req() req: Request) {
    // 处理 Google OAuth 回调
    throw new Error('Google OAuth callback not implemented yet');
  }

  @Get('wechat')
  @Public()
  @ApiOperation({
    summary: '微信OAuth登录',
    description: '重定向到微信OAuth授权页面',
  })
  async wechatAuth() {
    // 实现微信 OAuth 重定向
    throw new Error('WeChat OAuth not implemented yet');
  }

  @Get('wechat/callback')
  @Public()
  @ApiOperation({
    summary: '微信OAuth回调',
    description: '处理微信OAuth回调',
  })
  async wechatAuthCallback(@Req() req: Request) {
    // 处理微信 OAuth 回调
    throw new Error('WeChat OAuth callback not implemented yet');
  }
}