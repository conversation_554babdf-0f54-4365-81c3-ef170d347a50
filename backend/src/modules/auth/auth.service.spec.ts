import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { AuthService } from './auth.service';
import { User } from '../user/entities/user.entity';
import { UserProfile } from '../user/entities/user-profile.entity';
import { TestDataFactory } from '../../../test/utils/test-data-factory';
import { UnauthorizedException, ConflictException } from '@nestjs/common';

// Mock bcrypt
jest.mock('bcryptjs');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: jest.Mocked<Repository<User>>;
  let userProfileRepository: jest.Mocked<Repository<UserProfile>>;
  let jwtService: jest.Mocked<JwtService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserProfile),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get(getRepositoryToken(User));
    userProfileRepository = module.get(getRepositoryToken(UserProfile));
    jwtService = module.get(JwtService);
  });

  describe('register', () => {
    it('应该成功注册新用户', async () => {
      // Arrange
      const registerDto = TestDataFactory.createRegisterDto();
      const hashedPassword = 'hashed_password';
      const user = TestDataFactory.createUser({
        username: registerDto.username,
        email: registerDto.email,
        passwordHash: hashedPassword,
      });
      const profile = TestDataFactory.createUserProfile(user.id);

      userRepository.findOne.mockResolvedValue(null); // 用户不存在
      mockedBcrypt.hash.mockResolvedValue(hashedPassword);
      userRepository.create.mockReturnValue(user);
      userRepository.save.mockResolvedValue(user);
      userProfileRepository.create.mockReturnValue(profile);
      userProfileRepository.save.mockResolvedValue(profile);
      jwtService.sign.mockReturnValue('jwt_token');

      // Act
      const result = await service.register(registerDto);

      // Assert
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: [
          { email: registerDto.email },
          { username: registerDto.username },
        ],
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith(registerDto.password, 12);
      expect(userRepository.save).toHaveBeenCalledWith(user);
      expect(userProfileRepository.save).toHaveBeenCalledWith(profile);
      expect(result).toEqual({
        user: expect.objectContaining({
          id: user.id,
          username: user.username,
          email: user.email,
        }),
        accessToken: 'jwt_token',
      });
    });

    it('应该抛出冲突异常当用户已存在', async () => {
      // Arrange
      const registerDto = TestDataFactory.createRegisterDto();
      const existingUser = TestDataFactory.createUser();
      userRepository.findOne.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('应该抛出错误当密码不匹配', async () => {
      // Arrange
      const registerDto = TestDataFactory.createRegisterDto({
        password: 'password1',
        confirmPassword: 'password2',
      });

      // Act & Assert
      await expect(service.register(registerDto)).rejects.toThrow('密码确认不匹配');
    });
  });

  describe('login', () => {
    it('应该成功登录用户', async () => {
      // Arrange
      const loginDto = TestDataFactory.createLoginDto();
      const user = TestDataFactory.createUser({
        email: loginDto.email,
        passwordHash: 'hashed_password',
      });

      userRepository.findOne.mockResolvedValue(user);
      mockedBcrypt.compare.mockResolvedValue(true);
      jwtService.sign.mockReturnValue('jwt_token');

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { email: loginDto.email },
        relations: ['profile'],
      });
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(loginDto.password, user.passwordHash);
      expect(result).toEqual({
        user: expect.objectContaining({
          id: user.id,
          email: user.email,
        }),
        accessToken: 'jwt_token',
      });
    });

    it('应该抛出未授权异常当用户不存在', async () => {
      // Arrange
      const loginDto = TestDataFactory.createLoginDto();
      userRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('应该抛出未授权异常当密码错误', async () => {
      // Arrange
      const loginDto = TestDataFactory.createLoginDto();
      const user = TestDataFactory.createUser({
        email: loginDto.email,
        passwordHash: 'hashed_password',
      });

      userRepository.findOne.mockResolvedValue(user);
      mockedBcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('validateUser', () => {
    it('应该返回用户当凭据有效', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password';
      const user = TestDataFactory.createUser({
        email,
        passwordHash: 'hashed_password',
      });

      userRepository.findOne.mockResolvedValue(user);
      mockedBcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: user.id,
          email: user.email,
        })
      );
      expect(result.passwordHash).toBeUndefined(); // 密码应该被移除
    });

    it('应该返回null当凭据无效', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password';
      const user = TestDataFactory.createUser({
        email,
        passwordHash: 'hashed_password',
      });

      userRepository.findOne.mockResolvedValue(user);
      mockedBcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('refreshToken', () => {
    it('应该返回新的访问令牌', async () => {
      // Arrange
      const refreshToken = 'refresh_token';
      const payload = { sub: 'user_id', email: '<EMAIL>' };
      const user = TestDataFactory.createUser({
        id: payload.sub,
        email: payload.email,
      });

      jwtService.verify.mockReturnValue(payload);
      userRepository.findOne.mockResolvedValue(user);
      jwtService.sign.mockReturnValue('new_access_token');

      // Act
      const result = await service.refreshToken(refreshToken);

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith(refreshToken);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: payload.sub },
      });
      expect(result).toEqual({
        accessToken: 'new_access_token',
      });
    });

    it('应该抛出未授权异常当令牌无效', async () => {
      // Arrange
      const refreshToken = 'invalid_token';
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(service.refreshToken(refreshToken)).rejects.toThrow(UnauthorizedException);
    });
  });
});