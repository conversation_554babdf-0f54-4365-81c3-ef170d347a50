import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { AnimalCategory, AnimalSpecies } from '@/common/constants/animal-types';

@Entity('user_test_results')
@Index(['userId', 'createdAt'])
export class UserTestResult {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column({
    type: 'enum',
    enum: AnimalCategory,
  })
  animalType: AnimalCategory;

  @Column({
    type: 'enum',
    enum: AnimalSpecies,
  })
  animalSpecies: AnimalSpecies;

  @Column({ type: 'jsonb' })
  testAnswers: {
    questionId: string;
    answer: number | string;
    timeSpent?: number; // 答题时间(秒)
  }[];

  @Column({ type: 'jsonb' })
  personalityScore: {
    workload: number;      // 工作量承受度
    stress: number;        // 压力水平  
    creativity: number;    // 创造力
    leadership: number;    // 领导力
    socialSkill: number;   // 社交能力
    patience: number;      // 耐心程度
  };

  @Column({ type: 'jsonb', nullable: true })
  animalAttributes?: {
    wisdom?: number;
    calmness?: number;
    leadership?: number;
    endurance?: number;
    loyalty?: number;
    optimism?: number;
    sociability?: number;
    independence?: number;
    elegance?: number;
    reliability?: number;
    speed?: number;
    agility?: number;
    stubbornness?: number;
  };

  @Column({ length: 10, default: 'v1.0' })
  testVersion: string;

  @Column({ type: 'text', nullable: true })
  personalityDescription?: string;

  @Column({ type: 'jsonb', nullable: true })
  recommendations?: {
    career: string[];
    skills: string[];
    improvement: string[];
  };

  @CreateDateColumn()
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.testResults, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: User;
}