import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PostDocument = Post & Document;

export enum PostStatus {
  PUBLISHED = 1,
  DRAFT = 2,
  REVIEWING = 3,
  REJECTED = 4,
  DELETED = 5,
}

export enum PostType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  MIXED = 'mixed',
}

@Schema({
  collection: 'posts',
  timestamps: true,
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class Post {
  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true, maxlength: 2000 })
  content: string;

  @Prop({
    type: String,
    enum: PostType,
    default: PostType.TEXT,
  })
  type: PostType;

  @Prop([String])
  images?: string[];

  @Prop([String])
  videos?: string[];

  @Prop([String])
  tags?: string[];

  @Prop({ default: false })
  isAnonymous: boolean;

  @Prop({ default: 0, index: true })
  likeCount: number;

  @Prop({ default: 0 })
  commentCount: number;

  @Prop({ default: 0 })
  shareCount: number;

  @Prop({ default: 0 })
  viewCount: number;

  @Prop({
    type: Number,
    enum: PostStatus,
    default: PostStatus.PUBLISHED,
    index: true,
  })
  status: PostStatus;

  @Prop({
    type: Object,
    default: null,
  })
  aiModeration?: {
    score: number;
    labels: string[];
    suggestion: 'pass' | 'review' | 'block';
    checkedAt: Date;
  };

  @Prop({
    type: Object,
    default: null,
  })
  location?: {
    name: string;
    coordinates: [number, number]; // [longitude, latitude]
  };

  @Prop({
    type: Object,
    default: {},
  })
  metadata?: {
    device?: string;
    platform?: string;
    version?: string;
    ip?: string;
  };

  @Prop({ index: true })
  createdAt?: Date;

  @Prop()
  updatedAt?: Date;

  @Prop({ index: true, sparse: true })
  deletedAt?: Date;
}

export const PostSchema = SchemaFactory.createForClass(Post);

// 创建索引
PostSchema.index({ userId: 1, createdAt: -1 });
PostSchema.index({ tags: 1, status: 1 });
PostSchema.index({ createdAt: -1, likeCount: -1 });
PostSchema.index({ status: 1, createdAt: -1 });
PostSchema.index({ 'location.coordinates': '2dsphere' });

// 创建虚拟字段
PostSchema.virtual('id').get(function () {
  return this._id.toHexString();
});

// 中间件：软删除
PostSchema.pre(['find', 'findOne', 'findOneAndUpdate'], function () {
  this.where({ deletedAt: { $exists: false } });
});

PostSchema.pre('countDocuments', function () {
  this.where({ deletedAt: { $exists: false } });
});