import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserTestResult } from '../../test/entities/user-test-result.entity';
import { UserProfile } from './user-profile.entity';
import { Interaction } from '../../social/entities/interaction.entity';
import { Comment } from '../../social/entities/comment.entity';

export enum UserStatus {
  ACTIVE = 1,
  DISABLED = 2,
  DELETED = 3,
}

export enum AuthProvider {
  LOCAL = 'local',
  GOOGLE = 'google',
  WECHAT = 'wechat',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 50 })
  username: string;

  @Column({ unique: true, nullable: true })
  email?: string;

  @Column({ unique: true, nullable: true })
  phone?: string;

  @Column({ select: false })
  @Exclude()
  passwordHash: string;

  @Column({ nullable: true, length: 500 })
  avatarUrl?: string;

  @Column({ length: 50, nullable: true })
  nickname?: string;

  @Column({ type: 'text', nullable: true })
  bio?: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({
    type: 'enum',
    enum: AuthProvider,
    default: AuthProvider.LOCAL,
  })
  authProvider: AuthProvider;

  @Column({ nullable: true })
  providerId?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => UserProfile, (profile) => profile.user, {
    cascade: true,
    eager: false,
  })
  @JoinColumn()
  profile?: UserProfile;

  @OneToMany(() => UserTestResult, (result) => result.user)
  testResults?: UserTestResult[];

  @OneToMany(() => Interaction, (interaction) => interaction.user)
  interactions?: Interaction[];

  @OneToMany(() => Interaction, (interaction) => interaction.targetUser)
  receivedInteractions?: Interaction[];

  @OneToMany(() => Comment, (comment) => comment.user)
  comments?: Comment[];
}