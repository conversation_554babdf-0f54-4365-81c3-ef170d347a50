import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: 0 })
  points: number;

  @Column({ default: 1 })
  level: number;

  @Column({ default: 0 })
  totalLikesReceived: number;

  @Column({ default: 0 })
  totalLikesGiven: number;

  @Column({ default: 0 })
  totalCommentsReceived: number;

  @Column({ default: 0 })
  totalCommentsGiven: number;

  @Column({ default: 0 })
  totalFeedsReceived: number;

  @Column({ default: 0 })
  totalFeedsGiven: number;

  @Column({ default: 0 })
  totalPostsPublished: number;

  @Column({ type: 'jsonb', nullable: true })
  achievements?: {
    badgeId: string;
    badgeName: string;
    description: string;
    unlockedAt: Date;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  settings?: {
    privacy: {
      profileVisibility: 'public' | 'friends' | 'private';
      allowMessages: boolean;
      allowFriendRequests: boolean;
    };
    notifications: {
      email: boolean;
      push: boolean;
      likes: boolean;
      comments: boolean;
      feeds: boolean;
      system: boolean;
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => User, (user) => user.profile)
  user: User;
}