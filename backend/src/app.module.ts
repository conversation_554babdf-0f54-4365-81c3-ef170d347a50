import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { CacheModule } from '@nestjs/cache-manager';
import { ThrottlerModule } from '@nestjs/throttler';
import { BullModule } from '@nestjs/bull';
import { WinstonModule } from 'nest-winston';

// 配置
import configuration from '@/config/configuration';
import { typeOrmConfig } from '@/config/database.config';
import { redisConfig } from '@/config/redis.config';
import { loggerConfig } from '@/config/logger.config';

// 模块
import { AuthModule } from '@/modules/auth/auth.module';
import { UserModule } from '@/modules/user/user.module';
// import { TestModule } from '@/modules/test/test.module';
import { ZooModule } from '@/modules/zoo/zoo.module';
// import { SocialModule } from '@/modules/social/social.module';
// import { RankingModule } from '@/modules/ranking/ranking.module';
// import { ContentModule } from '@/modules/content/content.module';
// import { FileModule } from '@/modules/file/file.module';
// import { NotificationModule } from '@/modules/notification/notification.module';
import { DrawingModule } from '@/modules/drawing/drawing.module';
import { AIModule } from '@/modules/ai/ai.module';

// 健康检查
import { HealthModule } from '@/modules/health/health.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),

    // 日志模块
    WinstonModule.forRootAsync({
      useFactory: () => loggerConfig,
    }),

    // 数据库连接
    TypeOrmModule.forRootAsync({
      useFactory: typeOrmConfig,
      inject: [ConfigModule],
    }),

    // MongoDB 连接
    MongooseModule.forRootAsync({
      useFactory: async () => ({
        uri: process.env.MONGODB_URI,
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
    }),

    // Redis 缓存
    CacheModule.registerAsync({
      useFactory: redisConfig,
      isGlobal: true,
    }),

    // 限流模块
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        ttl: parseInt(process.env.THROTTLE_TTL) || 60,
        limit: parseInt(process.env.THROTTLE_LIMIT) || 10,
      }),
    }),

    // 队列模块
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT) || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
        },
      }),
    }),

    // 业务模块
    HealthModule,
    AuthModule,
    UserModule,
    // TestModule,
    ZooModule,
    // SocialModule,
    // RankingModule,
    // ContentModule,
    // FileModule,
    // NotificationModule,
    DrawingModule,
    AIModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}