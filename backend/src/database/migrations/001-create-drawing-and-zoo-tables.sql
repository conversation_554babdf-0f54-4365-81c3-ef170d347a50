-- Create tables for Drawing and Zoo modules

-- Drawing Analysis table
CREATE TABLE drawing_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    original_image_data TEXT NOT NULL,
    original_image_url VARCHAR(500),
    analysis_result TEXT NOT NULL,
    animal_type VARCHAR(50) NOT NULL CHECK (animal_type IN ('divine_beast', 'pet', 'working_animal')),
    animal_species VARCHAR(50),
    face_features JSONB NOT NULL,
    confidence DECIMAL(5,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
    pixel_analysis JSONB,
    analysis_method VARCHAR(100) DEFAULT 'gemini_analysis',
    processing_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_drawing_analyses_user FOREIGN KEY (user_id) 
        REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    CONSTRAINT idx_drawing_analyses_user_created UNIQUE (user_id, created_at),
    CONSTRAINT idx_drawing_analyses_animal_created UNIQUE (animal_type, created_at)
);

-- Generated Avatar table
CREATE TABLE generated_avatars (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    analysis_id UUID,
    animal_type VARCHAR(50) NOT NULL CHECK (animal_type IN ('divine_beast', 'pet', 'working_animal')),
    animal_species VARCHAR(50) NOT NULL,
    avatar_image_data TEXT NOT NULL,
    avatar_image_url VARCHAR(500),
    style VARCHAR(100) DEFAULT 'sketch',
    features JSONB NOT NULL,
    stats JSONB NOT NULL,
    generation_method VARCHAR(100) NOT NULL,
    generation_prompt TEXT,
    processing_time INTEGER,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_generated_avatars_user FOREIGN KEY (user_id) 
        REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_generated_avatars_analysis FOREIGN KEY (analysis_id) 
        REFERENCES drawing_analyses(id) ON DELETE SET NULL,
    
    -- Indexes
    CONSTRAINT idx_generated_avatars_user_created UNIQUE (user_id, created_at),
    CONSTRAINT idx_generated_avatars_animal_created UNIQUE (animal_type, created_at)
);

-- Zoo Animals table
CREATE TABLE zoo_animals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    avatar_id UUID,
    name VARCHAR(100) NOT NULL,
    species VARCHAR(50) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('divine_beast', 'pet', 'working_animal')),
    avatar_url VARCHAR(500),
    avatar_base64 TEXT,
    attributes JSONB NOT NULL,
    current_state JSONB NOT NULL,
    position JSONB NOT NULL,
    work_stats JSONB NOT NULL,
    interaction_stats JSONB NOT NULL,
    preferences JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    is_star_animal BOOLEAN DEFAULT FALSE,
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    reputation INTEGER DEFAULT 100,
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    last_work_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_zoo_animals_user FOREIGN KEY (user_id) 
        REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_zoo_animals_avatar FOREIGN KEY (avatar_id) 
        REFERENCES generated_avatars(id) ON DELETE SET NULL,
    
    -- Indexes
    CONSTRAINT idx_zoo_animals_user_created UNIQUE (user_id, created_at),
    CONSTRAINT idx_zoo_animals_category_species UNIQUE (category, species),
    CONSTRAINT idx_zoo_animals_active_last_active UNIQUE (is_active, last_active_at)
);

-- Animal Interaction Records table
CREATE TABLE animal_interaction_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    animal_id UUID NOT NULL,
    interaction_type VARCHAR(50) NOT NULL CHECK (
        interaction_type IN ('feed', 'pet', 'play', 'work_together', 'encourage', 'rest', 'train', 'chat')
    ),
    intensity INTEGER DEFAULT 5 CHECK (intensity >= 1 AND intensity <= 10),
    duration INTEGER DEFAULT 5 CHECK (duration >= 1 AND duration <= 60),
    message VARCHAR(200),
    effectiveness DECIMAL(5,2) DEFAULT 0,
    experience_gained INTEGER DEFAULT 0,
    state_before JSONB NOT NULL,
    state_after JSONB NOT NULL,
    state_changes JSONB NOT NULL,
    animal_reaction JSONB NOT NULL,
    rewards JSONB,
    is_successful BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_animal_interactions_user FOREIGN KEY (user_id) 
        REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_animal_interactions_animal FOREIGN KEY (animal_id) 
        REFERENCES zoo_animals(id) ON DELETE CASCADE,
    
    -- Indexes
    CONSTRAINT idx_animal_interactions_user_created UNIQUE (user_id, created_at),
    CONSTRAINT idx_animal_interactions_animal_created UNIQUE (animal_id, created_at),
    CONSTRAINT idx_animal_interactions_type_created UNIQUE (interaction_type, created_at)
);

-- Create indexes for better performance
CREATE INDEX idx_drawing_analyses_user_id ON drawing_analyses(user_id);
CREATE INDEX idx_drawing_analyses_animal_type ON drawing_analyses(animal_type);
CREATE INDEX idx_drawing_analyses_created_at ON drawing_analyses(created_at DESC);

CREATE INDEX idx_generated_avatars_user_id ON generated_avatars(user_id);
CREATE INDEX idx_generated_avatars_animal_type ON generated_avatars(animal_type);
CREATE INDEX idx_generated_avatars_created_at ON generated_avatars(created_at DESC);

CREATE INDEX idx_zoo_animals_user_id ON zoo_animals(user_id);
CREATE INDEX idx_zoo_animals_category ON zoo_animals(category);
CREATE INDEX idx_zoo_animals_species ON zoo_animals(species);
CREATE INDEX idx_zoo_animals_is_active ON zoo_animals(is_active);
CREATE INDEX idx_zoo_animals_last_active_at ON zoo_animals(last_active_at DESC);

CREATE INDEX idx_animal_interactions_user_id ON animal_interaction_records(user_id);
CREATE INDEX idx_animal_interactions_animal_id ON animal_interaction_records(animal_id);
CREATE INDEX idx_animal_interactions_type ON animal_interaction_records(interaction_type);
CREATE INDEX idx_animal_interactions_created_at ON animal_interaction_records(created_at DESC);

-- Update functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_drawing_analyses_updated_at BEFORE UPDATE ON drawing_analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generated_avatars_updated_at BEFORE UPDATE ON generated_avatars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_zoo_animals_updated_at BEFORE UPDATE ON zoo_animals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE drawing_analyses IS '用户绘画分析记录表';
COMMENT ON TABLE generated_avatars IS '生成的动物头像表';
COMMENT ON TABLE zoo_animals IS '动物园动物表';
COMMENT ON TABLE animal_interaction_records IS '动物互动记录表';