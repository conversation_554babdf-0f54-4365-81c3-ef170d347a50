import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from '@/common/filters/http-exception.filter';
import { TransformInterceptor } from '@/common/interceptors/transform.interceptor';
import { LoggingInterceptor } from '@/common/interceptors/logging.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3000);
  const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // 全局前缀（注意：我们的控制器已经定义了完整路径，所以这里不需要重复）
  // app.setGlobalPrefix('api/v1');

  // API 版本控制
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // 安全中间件
  app.use(helmet());
  app.use(compression());

  // 跨域配置
  app.enableCors({
    origin: nodeEnv === 'production' 
      ? ['https://your-domain.com', 'https://www.your-domain.com'] 
      : true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // 全局管道 - 数据验证
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 剥离非装饰器属性
      forbidNonWhitelisted: true, // 如果有非白名单属性则抛出错误
      transform: true, // 自动类型转换
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 全局过滤器 - 异常处理
  app.useGlobalFilters(new HttpExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
  );

  // Swagger API 文档配置
  if (nodeEnv !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('牛马动物园 API')
      .setDescription('打工人自嘲互动娱乐平台后端API文档')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addTag('认证', '用户认证相关接口')
      .addTag('用户', '用户管理相关接口')
      .addTag('测试', '打工人分类测试相关接口')
      .addTag('绘画分析', '用户绘画分析和头像生成相关接口')
      .addTag('动物园', '3D动物园场景相关接口')
      .addTag('社交', '社交互动相关接口')
      .addTag('排行榜', '排行榜相关接口')
      .addTag('内容', '内容管理相关接口')
      .addTag('文件', '文件上传相关接口')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    console.log(`📚 API文档地址: http://localhost:${port}/api/docs`);
  }

  await app.listen(port);

  console.log(`🚀 牛马动物园后端服务已启动`);
  console.log(`🌍 服务地址: http://localhost:${port}`);
  console.log(`🔧 运行环境: ${nodeEnv}`);
  console.log(`📦 应用版本: ${configService.get('APP_VERSION', '1.0.0')}`);
}

bootstrap();