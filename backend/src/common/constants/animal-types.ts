// 动物分类常量
export enum AnimalCategory {
  DIVINE_BEAST = 'divine_beast', // 神兽 5%
  PET = 'pet',                  // 宠物 15%  
  WORKING_ANIMAL = 'working_animal', // 牛马 80%
}

// 具体动物类型
export enum AnimalSpecies {
  // 神兽系列
  QILIN_DEER = 'qilin_deer',     // 麒麟鹿
  DRAGON = 'dragon',             // 祥龙
  PHOENIX = 'phoenix',           // 凤凰
  UNICORN = 'unicorn',           // 独角兽

  // 宠物系列  
  GOLDEN_RETRIEVER = 'golden_retriever', // 金毛狗
  PERSIAN_CAT = 'persian_cat',           // 波斯猫
  RABBIT = 'rabbit',                     // 垂耳兔
  HAMSTER = 'hamster',                   // 仓鼠
  PARROT = 'parrot',                     // 鹦鹉

  // 牛马系列
  OX = 'ox',                     // 黄牛
  HORSE = 'horse',               // 棕马
  DONKEY = 'donkey',             // 毛驴
  MULE = 'mule',                 // 骡子
  BUFFALO = 'buffalo',           // 水牛
  SHEEP = 'sheep',               // 绵羊
  GOAT = 'goat',                 // 山羊
}

// 动物属性配置
export const ANIMAL_CONFIG = {
  [AnimalSpecies.QILIN_DEER]: {
    category: AnimalCategory.DIVINE_BEAST,
    name: '麒麟鹿',
    rarity: 0.02, // 2%
    attributes: {
      wisdom: 95,
      calmness: 90,
      leadership: 85,
      endurance: 70,
    },
    description: '温文尔雅的智者，在职场中如鱼得水，是同事眼中的大神',
    color: '#FFD700', // 金色
  },
  
  [AnimalSpecies.DRAGON]: {
    category: AnimalCategory.DIVINE_BEAST,
    name: '祥龙',
    rarity: 0.02,
    attributes: {
      wisdom: 90,
      leadership: 95,
      calmness: 80,
      endurance: 85,
    },
    description: '威严而温和的领导者，能力出众但乐于助人',
    color: '#FF69B4', // 彩色渐变
  },

  [AnimalSpecies.GOLDEN_RETRIEVER]: {
    category: AnimalCategory.PET,
    name: '金毛狗',
    rarity: 0.08, // 8%
    attributes: {
      loyalty: 95,
      optimism: 90,
      sociability: 85,
      endurance: 60,
    },
    description: '忠诚乐观的好同事，人见人爱，总是给团队带来正能量',
    color: '#FF69B4', // 粉色
  },

  [AnimalSpecies.PERSIAN_CAT]: {
    category: AnimalCategory.PET,
    name: '波斯猫',
    rarity: 0.05,
    attributes: {
      independence: 90,
      elegance: 95,
      selectivity: 85,
      endurance: 50,
    },
    description: '优雅独立的精致主义者，有品味但不易接近',
    color: '#FF69B4',
  },

  [AnimalSpecies.OX]: {
    category: AnimalCategory.WORKING_ANIMAL,
    name: '黄牛',
    rarity: 0.35, // 35%
    attributes: {
      endurance: 95,
      reliability: 90,
      patience: 85,
      speed: 40,
    },
    description: '任劳任怨的老实人，承担着最多的工作量，是团队的中坚力量',
    color: '#8B4513', // 棕色
  },

  [AnimalSpecies.HORSE]: {
    category: AnimalCategory.WORKING_ANIMAL,
    name: '棕马',
    rarity: 0.25,
    attributes: {
      endurance: 85,
      speed: 90,
      reliability: 80,
      agility: 75,
    },
    description: '勤劳快速的执行者，效率很高但压力也大',
    color: '#8B4513',
  },

  [AnimalSpecies.DONKEY]: {
    category: AnimalCategory.WORKING_ANIMAL,
    name: '毛驴',
    rarity: 0.15,
    attributes: {
      endurance: 80,
      stubbornness: 90,
      reliability: 75,
      speed: 35,
    },
    description: '固执但可靠的员工，虽然速度慢但从不掉链子',
    color: '#8B4513',
  },
};

// 根据测试分数计算动物类型
export function calculateAnimalType(scores: {
  workload: number;
  stress: number; 
  creativity: number;
  leadership: number;
  socialSkill: number;
  patience: number;
}): AnimalSpecies {
  const {
    workload,
    stress,
    creativity,
    leadership,
    socialSkill,
    patience,
  } = scores;

  // 计算综合得分
  const totalScore = workload + creativity + leadership + socialSkill - stress;
  const leadershipScore = leadership + creativity;
  const socialScore = socialSkill + (100 - stress);
  const workScore = workload + patience;

  // 神兽判定 (5%)
  if (
    totalScore >= 350 && 
    leadershipScore >= 140 && 
    socialScore >= 140
  ) {
    if (creativity >= 80 && socialSkill >= 85) {
      return AnimalSpecies.DRAGON;
    }
    return AnimalSpecies.QILIN_DEER;
  }

  // 宠物判定 (15%)  
  if (
    socialScore >= 130 && 
    stress <= 60 && 
    workload <= 70
  ) {
    if (socialSkill >= 80) {
      return AnimalSpecies.GOLDEN_RETRIEVER;
    }
    if (creativity >= 70) {
      return AnimalSpecies.PERSIAN_CAT;
    }
    return AnimalSpecies.RABBIT;
  }

  // 牛马判定 (80%)
  if (workload >= 80 && patience >= 70) {
    if (workload >= 90) {
      return AnimalSpecies.OX; // 黄牛 - 最辛苦
    }
    if (workload >= 75 && socialSkill >= 60) {
      return AnimalSpecies.HORSE; // 棕马 - 高效执行
    }
    return AnimalSpecies.DONKEY; // 毛驴 - 默默承受
  }

  // 默认返回黄牛
  return AnimalSpecies.OX;
}