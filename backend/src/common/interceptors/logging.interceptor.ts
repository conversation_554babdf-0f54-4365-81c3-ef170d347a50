import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  Call<PERSON><PERSON><PERSON>,
  Inject,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';
    const userId = request.user?.id;

    const now = Date.now();

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const responseTime = Date.now() - now;

        // 记录访问日志
        this.logger.info('HTTP Request', {
          method,
          url,
          statusCode,
          responseTime: `${responseTime}ms`,
          ip,
          userAgent,
          userId,
        });

        // 记录慢查询
        if (responseTime > 1000) {
          this.logger.warn('Slow Request', {
            method,
            url,
            responseTime: `${responseTime}ms`,
            userId,
          });
        }
      }),
    );
  }
}