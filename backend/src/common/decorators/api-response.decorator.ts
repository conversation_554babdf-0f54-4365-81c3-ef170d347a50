import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';

export const ApiResponseWrapper = <TModel extends Type<any>>(
  model: TModel,
  status: number = 200,
  description?: string,
  isArray: boolean = false,
) => {
  return applyDecorators(
    ApiResponse({
      status,
      description: description || 'Success',
      schema: {
        allOf: [
          {
            properties: {
              success: {
                type: 'boolean',
                example: true,
              },
              message: {
                type: 'string',
                example: 'Success',
              },
              timestamp: {
                type: 'string',
                example: '2025-01-15T10:30:00Z',
              },
              path: {
                type: 'string',
                example: '/api/v1/users',
              },
              method: {
                type: 'string',
                example: 'GET',
              },
              data: isArray
                ? {
                    type: 'array',
                    items: { $ref: getSchemaPath(model) },
                  }
                : { $ref: getSchemaPath(model) },
            },
          },
        ],
      },
    }),
  );
};

export const ApiErrorResponse = (status: number, description: string) => {
  return ApiResponse({
    status,
    description,
    schema: {
      properties: {
        success: {
          type: 'boolean',
          example: false,
        },
        error: {
          type: 'object',
          properties: {
            code: {
              type: 'number',
              example: status,
            },
            name: {
              type: 'string',
              example: 'BadRequestException',
            },
            message: {
              type: 'string',
              example: description,
            },
            timestamp: {
              type: 'string',
              example: '2025-01-15T10:30:00Z',
            },
            path: {
              type: 'string',
              example: '/api/v1/users',
            },
            method: {
              type: 'string',
              example: 'POST',
            },
          },
        },
        data: {
          type: 'null',
          example: null,
        },
      },
    },
  });
};