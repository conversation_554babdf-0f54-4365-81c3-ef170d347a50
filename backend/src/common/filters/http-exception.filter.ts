import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse() as any;

      message = exceptionResponse.message || exception.message;
      error = exceptionResponse.error || exception.name;

      // 处理验证错误
      if (Array.isArray(exceptionResponse.message)) {
        message = exceptionResponse.message.join('; ');
      }
    }

    // 记录错误日志
    const errorInfo = {
      timestamp: new Date().toISOString(),
      url: request.url,
      method: request.method,
      status,
      error,
      message,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      userId: (request as any).user?.id,
    };

    if (status >= 500) {
      this.logger.error('Server Error', { ...errorInfo, stack: exception });
    } else {
      this.logger.warn('Client Error', errorInfo);
    }

    // 返回统一错误响应格式
    const errorResponse = {
      success: false,
      error: {
        code: status,
        name: error,
        message,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
      },
      data: null,
    };

    // 生产环境隐藏敏感信息
    if (process.env.NODE_ENV === 'production' && status >= 500) {
      errorResponse.error.message = 'Internal server error';
    }

    response.status(status).json(errorResponse);
  }
}