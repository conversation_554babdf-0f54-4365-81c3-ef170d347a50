# 牛马动物园后端API服务

一个基于NestJS框架的现代化后端API服务，为"牛马动物园"打工人自嘲互动平台提供全栈支持。

## 项目特性

### 🚀 技术栈
- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + MongoDB + Redis
- **认证**: JWT + OAuth (Google, 微信)
- **文档**: Swagger/OpenAPI 3.0
- **部署**: Docker + Kubernetes
- **监控**: Winston + Prometheus + Grafana

### 📋 核心功能
- 用户注册登录与认证授权
- 打工人分类测试与AI算法
- 3D虚拟动物园场景管理  
- 社交互动（点赞、投喂、评论）
- 内容管理（吐槽墙、审核）
- 实时排行榜与统计分析
- 文件上传与CDN分发

## 快速开始

### 环境要求
- Node.js >= 18
- PostgreSQL >= 14
- MongoDB >= 5.0
- Redis >= 7.0
- Docker & Docker Compose

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置信息
vim .env
```

### 数据库设置
```bash
# 启动数据库服务（使用Docker）
npm run docker:dev

# 运行数据库迁移
npm run db:migrate

# 运行种子数据
npm run seed
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 访问服务
- API服务: http://localhost:3000
- API文档: http://localhost:3000/api/docs
- 健康检查: http://localhost:3000/api/v1/health

## API 接口文档

### 认证接口
```http
POST /api/v1/auth/register     # 用户注册
POST /api/v1/auth/login        # 用户登录
POST /api/v1/auth/refresh      # 刷新令牌
POST /api/v1/auth/logout       # 用户登出
GET  /api/v1/auth/profile      # 获取用户信息
```

### 测试接口
```http
GET  /api/v1/test/questions    # 获取测试题目
POST /api/v1/test/submit       # 提交测试答案
GET  /api/v1/test/result/:id   # 获取测试结果
```

### 动物园接口
```http
GET  /api/v1/zoo/animals       # 获取动物园动物列表
GET  /api/v1/zoo/scene         # 获取3D场景数据
POST /api/v1/zoo/interact      # 动物互动操作
```

### 社交接口
```http
POST /api/v1/social/like       # 点赞操作
POST /api/v1/social/feed       # 投喂操作
GET  /api/v1/social/comments   # 获取评论列表
POST /api/v1/social/comments   # 发表评论
```

### 内容接口
```http
GET  /api/v1/content/posts     # 获取吐槽墙内容
POST /api/v1/content/posts     # 发布吐槽内容
GET  /api/v1/content/rankings  # 获取排行榜数据
```

## 项目结构

```
src/
├── common/                 # 公共组件
│   ├── constants/         # 常量定义
│   ├── decorators/        # 自定义装饰器
│   ├── dto/              # 数据传输对象
│   ├── filters/          # 异常过滤器
│   ├── guards/           # 守卫
│   └── interceptors/     # 拦截器
├── config/               # 配置文件
│   ├── database.config.ts
│   ├── redis.config.ts
│   └── logger.config.ts
├── modules/              # 业务模块
│   ├── auth/            # 认证模块
│   ├── user/            # 用户管理
│   ├── test/            # 测试系统
│   ├── zoo/             # 动物园
│   ├── social/          # 社交互动
│   ├── content/         # 内容管理
│   ├── ranking/         # 排行榜
│   ├── file/            # 文件服务
│   ├── notification/    # 通知服务
│   └── health/          # 健康检查
├── database/             # 数据库相关
│   ├── migrations/      # 迁移文件
│   └── seeds/           # 种子数据
├── app.module.ts        # 应用模块
└── main.ts              # 应用入口
```

## 数据库设计

### PostgreSQL 表结构
- `users` - 用户基本信息
- `user_profiles` - 用户档案
- `user_test_results` - 测试结果
- `interactions` - 社交互动
- `comments` - 评论系统

### MongoDB 集合
- `posts` - 吐槽内容
- `system_configs` - 系统配置

### Redis 用途
- 会话存储
- 缓存热点数据
- 排行榜计算
- 限流记录

## 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -t niuma-zoo-api .

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes 部署
```bash
# 应用配置
kubectl apply -f k8s/

# 检查状态
kubectl get pods -l app=niuma-zoo-api
```

## 开发指南

### 代码规范
- 使用 ESLint + Prettier 自动格式化
- 遵循 TypeScript 严格类型检查
- API 接口必须包含 Swagger 文档
- 单元测试覆盖率要求 > 80%

### 提交规范
```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构代码
test: 测试相关
chore: 构建或辅助工具变动
```

### 测试
```bash
# 单元测试
npm run test

# 集成测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 性能优化

### 数据库优化
- 合理使用索引
- 查询分页限制
- 读写分离
- 连接池配置

### 缓存策略
- Redis 多层缓存
- API 响应缓存
- 静态资源 CDN
- 数据库查询缓存

### API 优化  
- 请求限流
- 响应压缩
- 批量操作
- 异步处理

## 监控告警

### 健康检查
- `/api/v1/health` - 服务健康状态
- `/api/v1/health/ready` - 就绪检查
- `/api/v1/health/live` - 存活检查

### 日志记录
- 结构化日志输出
- 错误堆栈追踪
- 性能指标监控
- 用户行为分析

## 安全考虑

### 认证授权
- JWT 双令牌机制
- OAuth 第三方登录
- 权限角色控制
- 会话管理

### 数据安全
- 敏感信息加密
- SQL 注入防护
- XSS 攻击防护
- CSRF 保护

### API 安全
- 请求频率限制
- 参数验证
- 内容审核
- 访问日志

## 常见问题

### Q: 如何添加新的动物类型？
A: 在 `src/common/constants/animal-types.ts` 中添加新的枚举值和配置。

### Q: 如何自定义测试题目？
A: 修改 MongoDB 中的 `system_configs` 集合中的 `test_questions` 配置。

### Q: 如何扩展排行榜算法？
A: 在 `src/modules/ranking` 模块中添加新的计算规则。

## 联系方式

- 项目负责人: Claude Code
- 技术支持: <EMAIL>
- 项目地址: https://github.com/niuma/zoo-backend

## 许可证

MIT License