/**
 * 增强版融合生成器 - 真正实现人身动物头效果
 * 参考9001端口静态页面的融合逻辑
 */

const sharp = require('sharp');
const Canvas = require('canvas');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

/**
 * 核心融合算法：将用户自画像的身体与动物头部结合
 * @param {string} userDrawingBase64 - 用户自画像的base64数据
 * @param {string} animalType - 动物类型 (DIVINE/PET/OXHORSE)
 * @param {object} analysisResult - AI分析结果
 * @returns {Promise<object>} 融合后的图像数据
 */
async function generateFusionAvatar(userDrawingBase64, animalType, analysisResult) {
    console.log('\n🎨 开始生成人身动物头融合图像...');
    console.log('动物类型:', animalType);
    console.log('用户特征:', analysisResult);

    // 创建画布
    const canvas = Canvas.createCanvas(1024, 1024);
    const ctx = canvas.getContext('2d');

    // 设置背景
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, 1024, 1024);

    try {
        console.log('开始融合生成流程...');
        
        // 1. 加载并分析用户自画像
        console.log('步骤1: 加载用户绘画数据...');
        const userImage = await loadUserDrawing(userDrawingBase64);
        console.log('用户绘画加载成功');
        
        const bodyFeatures = extractBodyFeatures(userImage, ctx);
        console.log('身体特征提取完成:', bodyFeatures);
        
        // 2. 绘制用户身体（保留用户原始绘画的身体部分）
        console.log('步骤2: 绘制用户身体...');
        await drawUserBody(ctx, userImage, bodyFeatures);
        
        // 3. 根据动物类型绘制相应的动物头部
        console.log('步骤3: 绘制动物头部...');
        const animalHead = await drawAnimalHead(ctx, animalType, analysisResult, bodyFeatures);
        
        // 4. 添加融合过渡效果，让头部和身体自然结合
        console.log('步骤4: 应用融合效果...');
        applyFusionTransition(ctx, bodyFeatures, animalHead);
        
        // 5. 添加打工人元素（领带、工牌等）
        console.log('步骤5: 添加打工人元素...');
        await addWorkerElements(ctx, animalType, bodyFeatures);
        
        // 6. 应用素描风格滤镜
        console.log('步骤6: 应用风格滤镜...');
        applySketchEffect(ctx);
        
        console.log('融合生成完成！');

        // 保存图像到文件系统
        const avatarId = Date.now().toString();
        const filename = `avatar-canvas-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
        const filePath = path.join(__dirname, 'uploads', 'generated-avatars', filename);
        
        // 确保目录存在
        const uploadDir = path.dirname(filePath);
        try {
            await fs.access(uploadDir);
        } catch {
            await fs.mkdir(uploadDir, { recursive: true });
        }
        
        // 将canvas数据写入文件
        const buffer = canvas.toBuffer('image/png');
        await fs.writeFile(filePath, buffer);
        
        console.log('💾 Canvas图像已保存到:', filePath);
        
        // 返回完整的文件URL
        const imageUrl = `http://localhost:3000/uploads/generated-avatars/${filename}`;
        
        return {
            success: true,
            imageUrl: imageUrl,
            filePath: filePath,
            fusionDetails: {
                method: 'Enhanced Canvas Fusion',
                animalType,
                bodySource: 'User Drawing',
                headSource: `${animalType} Animal Head`,
                style: 'Sketch',
                features: {
                    ...analysisResult,
                    bodyRetained: true,
                    headReplaced: true,
                    fusionQuality: 'Natural'
                }
            }
        };
        
    } catch (error) {
        console.error('融合生成失败:', error.message);
        console.error('错误堆栈:', error.stack);
        
        // 降级方案：返回简单的合成图像
        console.log('启用降级方案，生成fallback图像...');
        try {
            return await generateFallbackFusion(canvas, ctx, animalType);
        } catch (fallbackError) {
            console.error('降级方案也失败:', fallbackError.message);
            
            // 最终降级：返回纯文本提示图像
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 1024, 1024);
            
            ctx.fillStyle = '#333333';
            ctx.font = '32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('图像生成暂时不可用', 512, 400);
            ctx.fillText('请稍后重试', 512, 450);
            ctx.fillText(`动物类型: ${animalType}`, 512, 500);
            
            // 保存错误图像到文件系统
            const avatarId = Date.now().toString();
            const filename = `avatar-error-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
            const filePath = path.join(__dirname, 'uploads', 'generated-avatars', filename);
            
            // 确保目录存在
            const uploadDir = path.dirname(filePath);
            try {
                await fs.access(uploadDir);
            } catch {
                await fs.mkdir(uploadDir, { recursive: true });
            }
            
            // 将canvas数据写入文件
            const buffer = canvas.toBuffer('image/png');
            await fs.writeFile(filePath, buffer);
            
            console.log('💾 错误图像已保存到:', filePath);
            
            // 返回完整的文件URL
            const imageUrl = `http://localhost:3000/uploads/generated-avatars/${filename}`;
            
            return {
                success: false,
                imageUrl: imageUrl,
                filePath: filePath,
                error: error.message,
                fallbackUsed: true,
                fusionDetails: {
                    method: 'Error Fallback',
                    animalType,
                    error: '图像处理失败，返回占位符图像'
                }
            };
        }
    }
}

/**
 * 加载用户绘画
 */
async function loadUserDrawing(base64Data) {
    try {
        // 验证base64数据格式
        if (!base64Data || typeof base64Data !== 'string') {
            throw new Error('Invalid base64 data format');
        }

        // 提取base64数据（支持多种图片格式）
        const imageData = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
        
        // 验证base64字符串是否有效
        if (!imageData || imageData.length === 0) {
            throw new Error('Empty base64 image data');
        }

        // 验证base64格式
        const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        if (!base64Regex.test(imageData)) {
            throw new Error('Invalid base64 format');
        }

        // 创建buffer并验证
        const buffer = Buffer.from(imageData, 'base64');
        if (buffer.length === 0) {
            throw new Error('Failed to decode base64 data');
        }

        // 检查PNG文件头
        const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
        if (buffer.length >= 8 && !buffer.subarray(0, 8).equals(pngSignature)) {
            console.warn('Warning: Image may not be PNG format, attempting to load anyway...');
        }

        // 尝试加载图像
        const image = await Canvas.loadImage(buffer);
        
        // 验证图像是否正确加载
        if (!image || !image.width || !image.height) {
            throw new Error('Failed to load image: invalid dimensions');
        }

        console.log(`Successfully loaded user drawing: ${image.width}x${image.height}`);
        return image;

    } catch (error) {
        console.error('Error loading user drawing:', error.message);
        
        // 创建一个默认的占位图像
        const canvas = Canvas.createCanvas(300, 400);
        const ctx = canvas.getContext('2d');
        
        // 绘制一个简单的人形轮廓作为fallback
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 300, 400);
        
        ctx.fillStyle = '#666';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('用户绘画加载失败', 150, 200);
        ctx.fillText('使用默认模板', 150, 220);
        
        // 绘制简单人形
        ctx.strokeStyle = '#999';
        ctx.lineWidth = 3;
        ctx.beginPath();
        // 头部
        ctx.arc(150, 60, 30, 0, Math.PI * 2);
        // 身体
        ctx.moveTo(150, 90);
        ctx.lineTo(150, 250);
        // 手臂
        ctx.moveTo(120, 130);
        ctx.lineTo(180, 130);
        // 腿
        ctx.moveTo(150, 250);
        ctx.lineTo(130, 320);
        ctx.moveTo(150, 250);
        ctx.lineTo(170, 320);
        ctx.stroke();
        
        return canvas;
    }
}

/**
 * 提取身体特征（找到头部和身体的分界线）
 */
function extractBodyFeatures(image, ctx) {
    // 临时绘制图像来分析
    const tempCanvas = Canvas.createCanvas(image.width, image.height);
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.drawImage(image, 0, 0);
    
    const imageData = tempCtx.getImageData(0, 0, image.width, image.height);
    const pixels = imageData.data;
    
    // 分析图像找到颈部位置（通常在上方1/4到1/3处）
    const neckY = Math.floor(image.height * 0.28);
    const shoulderY = Math.floor(image.height * 0.35);
    
    return {
        neckPosition: { x: image.width / 2, y: neckY },
        shoulderPosition: { y: shoulderY },
        bodyWidth: image.width * 0.6,
        imageWidth: image.width,
        imageHeight: image.height
    };
}

/**
 * 绘制用户身体（保留颈部以下的部分）
 */
async function drawUserBody(ctx, userImage, bodyFeatures) {
    // 计算绘制位置，居中显示
    const x = (1024 - userImage.width) / 2;
    const y = (1024 - userImage.height) / 2;
    
    // 创建剪切路径，只显示身体部分
    ctx.save();
    ctx.beginPath();
    ctx.rect(0, y + bodyFeatures.neckPosition.y, 1024, userImage.height);
    ctx.clip();
    
    // 绘制用户原图的身体部分
    ctx.drawImage(userImage, x, y);
    ctx.restore();
    
    return { x, y };
}

/**
 * 绘制动物头部（替换原来的人头）
 */
async function drawAnimalHead(ctx, animalType, analysisResult, bodyFeatures) {
    const centerX = 512;
    const headY = (1024 - bodyFeatures.imageHeight) / 2;
    const headSize = bodyFeatures.bodyWidth * 0.8;
    
    ctx.save();
    
    // 根据动物类型绘制不同的头部
    switch(animalType) {
        case 'DIVINE':
            drawDivineHead(ctx, centerX, headY, headSize, analysisResult);
            break;
        case 'PET':
            drawPetHead(ctx, centerX, headY, headSize, analysisResult);
            break;
        case 'OXHORSE':
        default:
            drawOxHorseHead(ctx, centerX, headY, headSize, analysisResult);
            break;
    }
    
    ctx.restore();
    
    return {
        centerX,
        topY: headY,
        bottomY: headY + headSize,
        size: headSize
    };
}

/**
 * 绘制牛马头部
 */
function drawOxHorseHead(ctx, x, y, size, features) {
    // 头部轮廓（马脸形状）
    ctx.strokeStyle = '#4a4a4a';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // 马脸形状 - 上窄下宽
    ctx.moveTo(x - size * 0.3, y + size * 0.2);
    ctx.quadraticCurveTo(x - size * 0.35, y, x, y - size * 0.1);
    ctx.quadraticCurveTo(x + size * 0.35, y, x + size * 0.3, y + size * 0.2);
    ctx.quadraticCurveTo(x + size * 0.4, y + size * 0.5, x + size * 0.2, y + size * 0.8);
    ctx.lineTo(x - size * 0.2, y + size * 0.8);
    ctx.quadraticCurveTo(x - size * 0.4, y + size * 0.5, x - size * 0.3, y + size * 0.2);
    ctx.stroke();
    
    // 耳朵
    ctx.beginPath();
    ctx.moveTo(x - size * 0.25, y);
    ctx.lineTo(x - size * 0.3, y - size * 0.2);
    ctx.lineTo(x - size * 0.15, y - size * 0.05);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + size * 0.25, y);
    ctx.lineTo(x + size * 0.3, y - size * 0.2);
    ctx.lineTo(x + size * 0.15, y - size * 0.05);
    ctx.stroke();
    
    // 眼睛（疲惫的眼神）
    ctx.fillStyle = '#2c2c2c';
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.25, size * 0.06, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(x + size * 0.15, y + size * 0.25, size * 0.06, 0, Math.PI * 2);
    ctx.fill();
    
    // 添加眼袋（显示疲劳）
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.3, size * 0.05, 0, Math.PI);
    ctx.stroke();
    ctx.beginPath();
    ctx.arc(x + size * 0.15, y + size * 0.3, size * 0.05, 0, Math.PI);
    ctx.stroke();
    
    // 鼻孔（马的特征）
    ctx.fillStyle = '#1a1a1a';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.05, y + size * 0.6, size * 0.03, size * 0.05, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.ellipse(x + size * 0.05, y + size * 0.6, size * 0.03, size * 0.05, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 嘴巴（苦笑）
    ctx.strokeStyle = '#4a4a4a';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x - size * 0.1, y + size * 0.7);
    ctx.quadraticCurveTo(x, y + size * 0.72, x + size * 0.1, y + size * 0.7);
    ctx.stroke();
}

/**
 * 绘制宠物头部（猫）
 */
function drawPetHead(ctx, x, y, size, features) {
    // 头部轮廓（圆形猫脸）
    ctx.strokeStyle = '#5a5a5a';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(x, y + size * 0.4, size * 0.45, 0, Math.PI * 2);
    ctx.stroke();
    
    // 猫耳朵
    ctx.beginPath();
    ctx.moveTo(x - size * 0.35, y + size * 0.1);
    ctx.lineTo(x - size * 0.4, y - size * 0.15);
    ctx.lineTo(x - size * 0.2, y);
    ctx.closePath();
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + size * 0.35, y + size * 0.1);
    ctx.lineTo(x + size * 0.4, y - size * 0.15);
    ctx.lineTo(x + size * 0.2, y);
    ctx.closePath();
    ctx.stroke();
    
    // 眼睛（大而圆）
    ctx.fillStyle = '#2c2c2c';
    ctx.beginPath();
    ctx.arc(x - size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(x + size * 0.15, y + size * 0.35, size * 0.08, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻子（倒三角）
    ctx.fillStyle = '#ff69b4';
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.5);
    ctx.lineTo(x - size * 0.04, y + size * 0.45);
    ctx.lineTo(x + size * 0.04, y + size * 0.45);
    ctx.closePath();
    ctx.fill();
    
    // 嘴巴（W形）
    ctx.strokeStyle = '#5a5a5a';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x, y + size * 0.5);
    ctx.lineTo(x - size * 0.05, y + size * 0.55);
    ctx.moveTo(x, y + size * 0.5);
    ctx.lineTo(x + size * 0.05, y + size * 0.55);
    ctx.stroke();
    
    // 胡须
    ctx.strokeStyle = '#6a6a6a';
    ctx.lineWidth = 1;
    // 左边胡须
    ctx.beginPath();
    ctx.moveTo(x - size * 0.25, y + size * 0.4);
    ctx.lineTo(x - size * 0.45, y + size * 0.35);
    ctx.moveTo(x - size * 0.25, y + size * 0.45);
    ctx.lineTo(x - size * 0.45, y + size * 0.45);
    ctx.stroke();
    // 右边胡须
    ctx.beginPath();
    ctx.moveTo(x + size * 0.25, y + size * 0.4);
    ctx.lineTo(x + size * 0.45, y + size * 0.35);
    ctx.moveTo(x + size * 0.25, y + size * 0.45);
    ctx.lineTo(x + size * 0.45, y + size * 0.45);
    ctx.stroke();
}

/**
 * 绘制神兽头部（龙）
 */
function drawDivineHead(ctx, x, y, size, features) {
    // 头部轮廓（威严的龙头）
    ctx.strokeStyle = '#8b4513';
    ctx.lineWidth = 4;
    ctx.beginPath();
    
    // 龙头形状
    ctx.moveTo(x - size * 0.3, y + size * 0.3);
    ctx.quadraticCurveTo(x - size * 0.4, y, x, y - size * 0.05);
    ctx.quadraticCurveTo(x + size * 0.4, y, x + size * 0.3, y + size * 0.3);
    ctx.quadraticCurveTo(x + size * 0.35, y + size * 0.6, x, y + size * 0.75);
    ctx.quadraticCurveTo(x - size * 0.35, y + size * 0.6, x - size * 0.3, y + size * 0.3);
    ctx.stroke();
    
    // 龙角
    ctx.strokeStyle = '#daa520';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(x - size * 0.2, y - size * 0.05);
    ctx.quadraticCurveTo(x - size * 0.25, y - size * 0.25, x - size * 0.15, y - size * 0.3);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(x + size * 0.2, y - size * 0.05);
    ctx.quadraticCurveTo(x + size * 0.25, y - size * 0.25, x + size * 0.15, y - size * 0.3);
    ctx.stroke();
    
    // 眼睛（威严有神）
    ctx.fillStyle = '#ff4500';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.15, y + size * 0.25, size * 0.08, size * 0.06, -0.2, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.ellipse(x + size * 0.15, y + size * 0.25, size * 0.08, size * 0.06, 0.2, 0, Math.PI * 2);
    ctx.fill();
    
    // 瞳孔
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.15, y + size * 0.25, size * 0.03, size * 0.04, -0.2, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.ellipse(x + size * 0.15, y + size * 0.25, size * 0.03, size * 0.04, 0.2, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻孔（龙的特征）
    ctx.fillStyle = '#2a2a2a';
    ctx.beginPath();
    ctx.ellipse(x - size * 0.06, y + size * 0.5, size * 0.04, size * 0.03, -0.3, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.ellipse(x + size * 0.06, y + size * 0.5, size * 0.04, size * 0.03, 0.3, 0, Math.PI * 2);
    ctx.fill();
    
    // 龙须
    ctx.strokeStyle = '#8b4513';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x - size * 0.25, y + size * 0.4);
    ctx.quadraticCurveTo(x - size * 0.4, y + size * 0.45, x - size * 0.45, y + size * 0.5);
    ctx.moveTo(x + size * 0.25, y + size * 0.4);
    ctx.quadraticCurveTo(x + size * 0.4, y + size * 0.45, x + size * 0.45, y + size * 0.5);
    ctx.stroke();
}

/**
 * 应用融合过渡效果
 */
function applyFusionTransition(ctx, bodyFeatures, animalHead) {
    // 在颈部位置创建渐变过渡
    const gradient = ctx.createLinearGradient(
        animalHead.centerX, 
        animalHead.bottomY - 20,
        animalHead.centerX,
        animalHead.bottomY + 40
    );
    
    gradient.addColorStop(0, 'rgba(248, 249, 250, 0)');
    gradient.addColorStop(0.5, 'rgba(248, 249, 250, 0.3)');
    gradient.addColorStop(1, 'rgba(248, 249, 250, 0)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(
        animalHead.centerX - bodyFeatures.bodyWidth / 2,
        animalHead.bottomY - 20,
        bodyFeatures.bodyWidth,
        60
    );
}

/**
 * 添加打工人元素
 */
async function addWorkerElements(ctx, animalType, bodyFeatures) {
    const centerX = 512;
    const neckY = (1024 - bodyFeatures.imageHeight) / 2 + bodyFeatures.neckPosition.y;
    
    // 绘制领带
    ctx.save();
    ctx.strokeStyle = '#1a237e';
    ctx.fillStyle = '#3f51b5';
    ctx.lineWidth = 2;
    
    // 领带结
    ctx.beginPath();
    ctx.moveTo(centerX - 20, neckY);
    ctx.lineTo(centerX + 20, neckY);
    ctx.lineTo(centerX + 15, neckY + 20);
    ctx.lineTo(centerX - 15, neckY + 20);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // 领带主体
    ctx.beginPath();
    ctx.moveTo(centerX - 15, neckY + 20);
    ctx.lineTo(centerX - 8, neckY + 150);
    ctx.lineTo(centerX, neckY + 160);
    ctx.lineTo(centerX + 8, neckY + 150);
    ctx.lineTo(centerX + 15, neckY + 20);
    ctx.fill();
    ctx.stroke();
    
    // 添加工牌
    if (animalType === 'OXHORSE') {
        ctx.fillStyle = '#fff';
        ctx.fillRect(centerX + 80, neckY + 50, 80, 100);
        ctx.strokeStyle = '#333';
        ctx.strokeRect(centerX + 80, neckY + 50, 80, 100);
        
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.fillText('工号: 996', centerX + 90, neckY + 80);
        ctx.fillText('部门: 搬砖', centerX + 90, neckY + 100);
        ctx.fillText('级别: P3', centerX + 90, neckY + 120);
    }
    
    ctx.restore();
}

/**
 * 应用素描效果
 */
function applySketchEffect(ctx) {
    const imageData = ctx.getImageData(0, 0, 1024, 1024);
    const data = imageData.data;
    
    // 简单的素描效果：增加对比度，减少颜色饱和度
    for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.3 + data[i + 1] * 0.59 + data[i + 2] * 0.11;
        const contrast = 1.5;
        
        data[i] = Math.min(255, gray * contrast);
        data[i + 1] = Math.min(255, gray * contrast * 0.95);
        data[i + 2] = Math.min(255, gray * contrast * 0.9);
    }
    
    ctx.putImageData(imageData, 0, 0);
    
    // 添加素描线条
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i < 30; i++) {
        ctx.beginPath();
        ctx.moveTo(Math.random() * 1024, Math.random() * 1024);
        ctx.lineTo(Math.random() * 1024, Math.random() * 1024);
        ctx.stroke();
    }
}

/**
 * 生成降级方案的融合图像
 */
async function generateFallbackFusion(canvas, ctx, animalType) {
    // 清空画布
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, 1024, 1024);
    
    // 绘制简单的人身
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.beginPath();
    // 身体轮廓
    ctx.moveTo(412, 400);
    ctx.lineTo(412, 700);
    ctx.lineTo(462, 800);
    ctx.lineTo(562, 800);
    ctx.lineTo(612, 700);
    ctx.lineTo(612, 400);
    ctx.stroke();
    
    // 绘制简单的动物头
    const headSize = 150;
    drawOxHorseHead(ctx, 512, 250, headSize, {});
    
    // 添加领带
    ctx.fillStyle = '#3f51b5';
    ctx.fillRect(492, 400, 40, 200);
    
    // 保存fallback图像到文件系统
    const avatarId = Date.now().toString();
    const filename = `avatar-fallback-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
    const filePath = path.join(__dirname, 'uploads', 'generated-avatars', filename);
    
    // 确保目录存在
    const uploadDir = path.dirname(filePath);
    try {
        await fs.access(uploadDir);
    } catch {
        await fs.mkdir(uploadDir, { recursive: true });
    }
    
    // 将canvas数据写入文件
    const buffer = canvas.toBuffer('image/png');
    await fs.writeFile(filePath, buffer);
    
    console.log('💾 Fallback图像已保存到:', filePath);
    
    // 返回完整的文件URL
    const imageUrl = `http://localhost:3000/uploads/generated-avatars/${filename}`;

    return {
        success: true,
        imageUrl: imageUrl,
        filePath: filePath,
        fusionDetails: {
            method: 'Fallback Simple Fusion',
            animalType,
            style: 'Simple Sketch'
        }
    };
}

module.exports = {
    generateFusionAvatar
};