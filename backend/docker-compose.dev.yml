version: '3.8'

services:
  # 后端服务
  api:
    build:
      context: .
      target: builder
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_HOST=postgres
      - MONGODB_URI=mongodb://mongo:27017/niuma_zoo
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - mongo
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run start:dev

  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: niuma_zoo
      POSTGRES_USER: niuma
      POSTGRES_PASSWORD: niuma123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  # MongoDB
  mongo:
    image: mongo:5.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: niuma
      MONGO_INITDB_ROOT_PASSWORD: niuma123
      MONGO_INITDB_DATABASE: niuma_zoo
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx/dev.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api

volumes:
  postgres_data:
  mongo_data:
  redis_data: