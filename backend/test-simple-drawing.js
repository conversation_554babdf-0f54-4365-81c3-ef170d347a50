// 创建一个简单清晰的测试绘画用于头部替换测试
const { createCanvas } = require('canvas');
const fs = require('fs');
const axios = require('axios');

function createSimpleTestDrawing() {
    const canvas = createCanvas(400, 500);
    const ctx = canvas.getContext('2d');
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 400, 500);
    
    // 黑色线条
    ctx.strokeStyle = '#000000';
    ctx.fillStyle = '#000000';
    ctx.lineWidth = 3;
    
    // 头部 - 圆形
    ctx.beginPath();
    ctx.arc(200, 120, 60, 0, Math.PI * 2);
    ctx.stroke();
    
    // 眼睛
    ctx.beginPath();
    ctx.arc(180, 110, 5, 0, Math.PI * 2);
    ctx.arc(220, 110, 5, 0, Math.PI * 2);
    ctx.fill();
    
    // 嘴巴 - 微笑
    ctx.beginPath();
    ctx.arc(200, 125, 15, 0.1 * Math.PI, 0.9 * Math.PI);
    ctx.stroke();
    
    // 脖子
    ctx.beginPath();
    ctx.moveTo(200, 180);
    ctx.lineTo(200, 220);
    ctx.stroke();
    
    // 身体 - 矩形
    ctx.strokeRect(160, 220, 80, 120);
    
    // 胳膊
    ctx.beginPath();
    ctx.moveTo(160, 250);
    ctx.lineTo(120, 300);
    ctx.moveTo(240, 250);
    ctx.lineTo(280, 300);
    ctx.stroke();
    
    // 腿
    ctx.beginPath();
    ctx.moveTo(180, 340);
    ctx.lineTo(180, 420);
    ctx.moveTo(220, 340);
    ctx.lineTo(220, 420);
    ctx.stroke();
    
    // 脚
    ctx.beginPath();
    ctx.moveTo(180, 420);
    ctx.lineTo(170, 430);
    ctx.moveTo(220, 420);
    ctx.lineTo(230, 430);
    ctx.stroke();
    
    return canvas.toDataURL('image/png');
}

async function testWithSimpleDrawing() {
    console.log('🎨 创建简单测试绘画...');
    const testImage = createSimpleTestDrawing();
    
    // 保存测试图像
    const imageBuffer = Buffer.from(testImage.split(',')[1], 'base64');
    fs.writeFileSync('/Users/<USER>/WorkSpace/niuma/simple_test_drawing.png', imageBuffer);
    console.log('✅ 测试绘画已保存: simple_test_drawing.png');
    
    try {
        console.log('🔄 发送API请求测试头部替换...');
        const response = await axios.post('http://localhost:3005/api/v1/avatar/real-generate', {
            imageData: testImage,
            animalType: 'OXHORSE'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000
        });
        
        if (response.data.success) {
            const base64Data = response.data.avatar.imageUrl.replace(/^data:image\/png;base64,/, '');
            fs.writeFileSync('/Users/<USER>/WorkSpace/niuma/final_head_replacement.png', Buffer.from(base64Data, 'base64'));
            
            console.log('✅ 头部替换成功!');
            console.log('📊 生成方法:', response.data.avatar.generationMethod);
            console.log('📁 结果保存: final_head_replacement.png');
            console.log('📏 文件大小:', (Buffer.from(base64Data, 'base64').length / 1024).toFixed(1), 'KB');
            
            // 显示用到的提示词
            console.log('\n🎯 Gemini分析结果:');
            console.log(response.data.avatar.geminiAnalysis.substring(0, 200) + '...');
            
        } else {
            console.log('❌ API返回错误:', response.data.error);
        }
        
    } catch (error) {
        console.log('❌ 请求失败:', error.message);
    }
}

testWithSimpleDrawing();