import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { TestDataFactory } from './utils/test-data-factory';
import { testDb } from './utils/test-db';

describe('TestController (e2e)', () => {
  let app: INestApplication;
  let accessToken: string;
  let userId: string;

  beforeAll(async () => {
    await testDb.initialize();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api/v1');
    await app.init();
  });

  beforeEach(async () => {
    await testDb.cleanup();
    
    // 注册并登录获取token
    const registerDto = TestDataFactory.createRegisterDto();
    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send(registerDto);

    accessToken = registerResponse.body.data.accessToken;
    userId = registerResponse.body.data.user.id;
  });

  afterAll(async () => {
    await app.close();
    await testDb.destroy();
  });

  describe('/test/questions (GET)', () => {
    it('应该返回打工人分类测试题目', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/test/questions')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: {
          questions: expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(Number),
              question: expect.any(String),
              options: expect.arrayContaining([
                expect.objectContaining({
                  id: expect.any(Number),
                  text: expect.any(String),
                  score: expect.any(Number),
                }),
              ]),
            }),
          ]),
          totalQuestions: expect.any(Number),
        },
      });

      // 验证题目数量
      expect(response.body.data.questions).toHaveLength(20);
      
      // 验证每个题目都有4个选项
      response.body.data.questions.forEach((question: any) => {
        expect(question.options).toHaveLength(4);
      });
    });

    it('应该返回401当没有提供token', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/test/questions')
        .expect(401);
    });
  });

  describe('/test/submit (POST)', () => {
    it('应该成功提交测试答案并返回结果', async () => {
      const answers = TestDataFactory.createTestAnswers();

      const response = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(201);

      expect(response.body).toEqual({
        success: true,
        data: {
          result: expect.objectContaining({
            id: expect.any(String),
            userId: userId,
            totalScore: expect.any(Number),
            animalType: expect.stringMatching(/神兽|宠物|牛马/),
            animalSubtype: expect.any(String),
            personality: expect.any(String),
            description: expect.any(String),
            ranking: expect.objectContaining({
              percentage: expect.any(Number),
              rank: expect.any(Number),
              total: expect.any(Number),
            }),
            createdAt: expect.any(String),
          }),
        },
      });

      // 验证分数在合理范围内
      expect(response.body.data.result.totalScore).toBeGreaterThanOrEqual(0);
      expect(response.body.data.result.totalScore).toBeLessThanOrEqual(100);
    });

    it('应该返回400当答案数量不正确', async () => {
      const answers = [
        { questionId: 1, answerId: 1 },
        { questionId: 2, answerId: 2 },
      ]; // 只有2个答案，应该有20个

      const response = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(400);

      expect(response.body.message).toContain('答案数量不正确');
    });

    it('应该返回400当questionId无效', async () => {
      const answers = TestDataFactory.createTestAnswers();
      answers[0].questionId = 999; // 无效的题目ID

      const response = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(400);

      expect(response.body.message).toContain('无效的题目ID');
    });

    it('应该返回400当answerId无效', async () => {
      const answers = TestDataFactory.createTestAnswers();
      answers[0].answerId = 999; // 无效的选项ID

      const response = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(400);

      expect(response.body.message).toContain('无效的选项ID');
    });

    it('应该返回409当用户已经测试过', async () => {
      const answers = TestDataFactory.createTestAnswers();

      // 第一次提交
      await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(201);

      // 重复提交
      const response = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers })
        .expect(409);

      expect(response.body.message).toContain('您已经完成过测试');
    });
  });

  describe('/test/result/:id (GET)', () => {
    let testResultId: string;

    beforeEach(async () => {
      // 提交测试获取结果ID
      const answers = TestDataFactory.createTestAnswers();
      const submitResponse = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers });

      testResultId = submitResponse.body.data.result.id;
    });

    it('应该返回指定的测试结果', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/test/result/${testResultId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          id: testResultId,
          userId: userId,
          totalScore: expect.any(Number),
          animalType: expect.stringMatching(/神兽|宠物|牛马/),
          animalSubtype: expect.any(String),
          personality: expect.any(String),
          description: expect.any(String),
        }),
      });
    });

    it('应该返回404当测试结果不存在', async () => {
      const nonexistentId = '00000000-0000-0000-0000-000000000000';

      await request(app.getHttpServer())
        .get(`/api/v1/test/result/${nonexistentId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });

    it('应该返回403当访问他人的测试结果', async () => {
      // 创建另一个用户并提交测试
      const otherUserDto = TestDataFactory.createRegisterDto();
      const otherUserResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(otherUserDto);

      const otherAccessToken = otherUserResponse.body.data.accessToken;
      const answers = TestDataFactory.createTestAnswers();
      const otherTestResponse = await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${otherAccessToken}`)
        .send({ answers });

      const otherTestResultId = otherTestResponse.body.data.result.id;

      // 尝试用原用户的token访问他人的测试结果
      await request(app.getHttpServer())
        .get(`/api/v1/test/result/${otherTestResultId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(403);
    });
  });

  describe('/test/retake (POST)', () => {
    beforeEach(async () => {
      // 先完成一次测试
      const answers = TestDataFactory.createTestAnswers();
      await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers });
    });

    it('应该允许用户重新测试', async () => {
      const newAnswers = TestDataFactory.createTestAnswers();
      
      const response = await request(app.getHttpServer())
        .post('/api/v1/test/retake')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers: newAnswers })
        .expect(201);

      expect(response.body).toEqual({
        success: true,
        data: {
          result: expect.objectContaining({
            id: expect.any(String),
            userId: userId,
            totalScore: expect.any(Number),
            animalType: expect.stringMatching(/神兽|宠物|牛马/),
            isRetake: true,
          }),
        },
      });
    });

    it('应该返回429当重测过于频繁', async () => {
      const newAnswers = TestDataFactory.createTestAnswers();

      // 第一次重测
      await request(app.getHttpServer())
        .post('/api/v1/test/retake')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers: newAnswers })
        .expect(201);

      // 立即再次重测
      const response = await request(app.getHttpServer())
        .post('/api/v1/test/retake')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers: newAnswers })
        .expect(429);

      expect(response.body.message).toContain('重测过于频繁');
    });
  });

  describe('/test/certificate (GET)', () => {
    beforeEach(async () => {
      // 先完成测试
      const answers = TestDataFactory.createTestAnswers();
      await request(app.getHttpServer())
        .post('/api/v1/test/submit')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ answers });
    });

    it('应该生成测试证书', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/test/certificate')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: {
          certificate: expect.objectContaining({
            imageUrl: expect.any(String),
            shareUrl: expect.any(String),
            metadata: expect.objectContaining({
              username: expect.any(String),
              animalType: expect.any(String),
              animalSubtype: expect.any(String),
              generatedAt: expect.any(String),
            }),
          }),
        },
      });
    });

    it('应该返回404当用户未完成测试', async () => {
      // 创建新用户（未完成测试）
      const newUserDto = TestDataFactory.createRegisterDto();
      const newUserResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(newUserDto);

      const newAccessToken = newUserResponse.body.data.accessToken;

      await request(app.getHttpServer())
        .get('/api/v1/test/certificate')
        .set('Authorization', `Bearer ${newAccessToken}`)
        .expect(404);
    });
  });

  describe('/test/statistics (GET)', () => {
    beforeEach(async () => {
      // 创建多个用户并完成测试以生成统计数据
      for (let i = 0; i < 5; i++) {
        const userDto = TestDataFactory.createRegisterDto();
        const userResponse = await request(app.getHttpServer())
          .post('/api/v1/auth/register')
          .send(userDto);

        const userToken = userResponse.body.data.accessToken;
        const answers = TestDataFactory.createTestAnswers();
        
        await request(app.getHttpServer())
          .post('/api/v1/test/submit')
          .set('Authorization', `Bearer ${userToken}`)
          .send({ answers });
      }
    });

    it('应该返回测试统计数据', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/test/statistics')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: {
          totalTests: expect.any(Number),
          animalDistribution: expect.objectContaining({
            神兽: expect.any(Number),
            宠物: expect.any(Number),
            牛马: expect.any(Number),
          }),
          averageScore: expect.any(Number),
          dailyTests: expect.any(Array),
          popularSubtypes: expect.any(Array),
        },
      });

      expect(response.body.data.totalTests).toBeGreaterThanOrEqual(5);
    });
  });
});