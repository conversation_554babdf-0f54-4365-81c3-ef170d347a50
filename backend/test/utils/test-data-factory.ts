import { faker } from '@faker-js/faker';
import { User } from '../../src/modules/user/entities/user.entity';
import { UserProfile } from '../../src/modules/user/entities/user-profile.entity';
import { RegisterDto } from '../../src/modules/auth/dto/register.dto';
import { LoginDto } from '../../src/modules/auth/dto/login.dto';

export class TestDataFactory {
  /**
   * 生成测试用户数据
   */
  static createUser(override?: Partial<User>): User {
    const user = new User();
    user.id = faker.string.uuid();
    user.username = faker.internet.userName();
    user.email = faker.internet.email();
    user.passwordHash = faker.internet.password();
    user.isActive = true;
    user.isVerified = false;
    user.createdAt = new Date();
    user.updatedAt = new Date();
    
    return Object.assign(user, override);
  }

  /**
   * 生成用户资料数据
   */
  static createUserProfile(userId: string, override?: Partial<UserProfile>): UserProfile {
    const profile = new UserProfile();
    profile.id = faker.string.uuid();
    profile.userId = userId;
    profile.nickname = faker.person.firstName();
    profile.avatar = faker.image.avatar();
    profile.bio = faker.person.bio();
    profile.animalType = faker.helpers.arrayElement(['神兽', '宠物', '牛马']);
    profile.animalSubtype = faker.animal.type();
    profile.testScore = faker.number.int({ min: 0, max: 100 });
    profile.createdAt = new Date();
    profile.updatedAt = new Date();
    
    return Object.assign(profile, override);
  }

  /**
   * 生成注册DTO
   */
  static createRegisterDto(override?: Partial<RegisterDto>): RegisterDto {
    return {
      username: faker.internet.userName(),
      email: faker.internet.email(),
      password: 'Test123456!',
      confirmPassword: 'Test123456!',
      ...override,
    };
  }

  /**
   * 生成登录DTO
   */
  static createLoginDto(override?: Partial<LoginDto>): LoginDto {
    return {
      email: faker.internet.email(),
      password: 'Test123456!',
      ...override,
    };
  }

  /**
   * 生成打工人测试答案
   */
  static createTestAnswers(): Array<{ questionId: number; answerId: number }> {
    const answers = [];
    for (let i = 1; i <= 20; i++) {
      answers.push({
        questionId: i,
        answerId: faker.number.int({ min: 1, max: 4 }),
      });
    }
    return answers;
  }

  /**
   * 生成吐槽内容
   */
  static createComplaintContent() {
    return {
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraphs(2),
      tags: faker.helpers.arrayElements(['加班', '摸鱼', 'PUA', '996', '内卷'], 2),
      isAnonymous: faker.datatype.boolean(),
    };
  }

  /**
   * 生成评论内容
   */
  static createComment() {
    return {
      content: faker.lorem.sentence(),
      parentId: null,
    };
  }

  /**
   * 生成多个用户数据
   */
  static createUsers(count: number): User[] {
    return Array.from({ length: count }, () => this.createUser());
  }

  /**
   * 生成JWT Token
   */
  static createJwtToken(payload: any = {}): string {
    const defaultPayload = {
      sub: faker.string.uuid(),
      email: faker.internet.email(),
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
    };
    
    // 简单的假Token，实际测试中会被Mock
    return `mock.jwt.token.${Buffer.from(JSON.stringify({ ...defaultPayload, ...payload })).toString('base64')}`;
  }

  /**
   * 清理所有测试数据
   */
  static cleanup(): void {
    faker.seed(Date.now());
  }
}