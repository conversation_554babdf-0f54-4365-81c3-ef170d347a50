{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.ts", "!src/**/*.interface.ts", "!src/**/*.dto.ts", "!src/**/*.entity.ts", "!src/main.ts"], "coverageDirectory": "./coverage", "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "testTimeout": 30000}