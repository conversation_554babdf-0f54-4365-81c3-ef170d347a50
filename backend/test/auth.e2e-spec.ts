import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { TestDataFactory } from './utils/test-data-factory';
import { testDb } from './utils/test-db';

describe('AuthController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // 初始化测试数据库
    await testDb.initialize();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // 配置应用
    app.setGlobalPrefix('api/v1');
    
    await app.init();
  });

  beforeEach(async () => {
    // 清理测试数据
    await testDb.cleanup();
  });

  afterAll(async () => {
    await app.close();
    await testDb.destroy();
  });

  describe('/auth/register (POST)', () => {
    it('应该成功注册新用户', async () => {
      const registerDto = TestDataFactory.createRegisterDto();

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(201);

      expect(response.body).toEqual({
        success: true,
        data: {
          user: expect.objectContaining({
            id: expect.any(String),
            username: registerDto.username,
            email: registerDto.email,
            isActive: true,
            isVerified: false,
          }),
          accessToken: expect.any(String),
        },
      });

      // 验证密码不应该在响应中
      expect(response.body.data.user.passwordHash).toBeUndefined();
    });

    it('应该返回400当邮箱格式无效', async () => {
      const registerDto = TestDataFactory.createRegisterDto({
        email: 'invalid-email',
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(400);

      expect(response.body.message).toContain('邮箱格式不正确');
    });

    it('应该返回400当密码强度不足', async () => {
      const registerDto = TestDataFactory.createRegisterDto({
        password: '123',
        confirmPassword: '123',
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(400);

      expect(response.body.message).toContain('密码强度不足');
    });

    it('应该返回409当用户已存在', async () => {
      const registerDto = TestDataFactory.createRegisterDto();

      // 第一次注册
      await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(201);

      // 重复注册
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(409);

      expect(response.body.message).toContain('用户已存在');
    });

    it('应该返回400当密码确认不匹配', async () => {
      const registerDto = TestDataFactory.createRegisterDto({
        password: 'Test123456!',
        confirmPassword: 'Different123456!',
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto)
        .expect(400);

      expect(response.body.message).toContain('密码确认不匹配');
    });
  });

  describe('/auth/login (POST)', () => {
    let registeredUser: any;

    beforeEach(async () => {
      // 预先注册一个用户
      const registerDto = TestDataFactory.createRegisterDto();
      const registerResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto);

      registeredUser = {
        ...registerResponse.body.data.user,
        password: registerDto.password,
      };
    });

    it('应该成功登录用户', async () => {
      const loginDto = {
        email: registeredUser.email,
        password: registeredUser.password,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(loginDto)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: {
          user: expect.objectContaining({
            id: registeredUser.id,
            email: registeredUser.email,
          }),
          accessToken: expect.any(String),
        },
      });
    });

    it('应该返回401当邮箱不存在', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(loginDto)
        .expect(401);

      expect(response.body.message).toContain('邮箱或密码错误');
    });

    it('应该返回401当密码错误', async () => {
      const loginDto = {
        email: registeredUser.email,
        password: 'wrong-password',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(loginDto)
        .expect(401);

      expect(response.body.message).toContain('邮箱或密码错误');
    });

    it('应该返回400当邮箱格式无效', async () => {
      const loginDto = {
        email: 'invalid-email',
        password: 'password',
      };

      await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(loginDto)
        .expect(400);
    });
  });

  describe('/auth/refresh (POST)', () => {
    let accessToken: string;
    let refreshToken: string;

    beforeEach(async () => {
      // 注册并登录获取token
      const registerDto = TestDataFactory.createRegisterDto();
      const registerResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto);

      accessToken = registerResponse.body.data.accessToken;
      
      // 模拟refresh token (实际实现中应该在登录时返回)
      refreshToken = 'mock_refresh_token';
    });

    it('应该成功刷新访问令牌', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: {
          accessToken: expect.any(String),
        },
      });
    });

    it('应该返回401当refresh token无效', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid_token' })
        .expect(401);
    });
  });

  describe('/auth/logout (POST)', () => {
    let accessToken: string;

    beforeEach(async () => {
      // 注册并登录获取token
      const registerDto = TestDataFactory.createRegisterDto();
      const registerResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto);

      accessToken = registerResponse.body.data.accessToken;
    });

    it('应该成功登出用户', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: '登出成功',
      });
    });

    it('应该返回401当没有提供token', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/auth/logout')
        .expect(401);
    });

    it('应该返回401当token无效', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/auth/logout')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);
    });
  });

  describe('/auth/profile (GET)', () => {
    let accessToken: string;
    let userId: string;

    beforeEach(async () => {
      // 注册并登录获取token
      const registerDto = TestDataFactory.createRegisterDto();
      const registerResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(registerDto);

      accessToken = registerResponse.body.data.accessToken;
      userId = registerResponse.body.data.user.id;
    });

    it('应该返回当前用户信息', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          id: userId,
          email: expect.any(String),
          username: expect.any(String),
        }),
      });
    });

    it('应该返回401当没有提供token', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/auth/profile')
        .expect(401);
    });
  });
});