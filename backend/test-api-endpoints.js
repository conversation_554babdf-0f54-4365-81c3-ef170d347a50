// API端点测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_USER_TOKEN = 'test-jwt-token'; // 实际使用时需要获取真实token

// 测试用的base64图像数据（简单的1x1像素PNG）
const TEST_IMAGE_BASE64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

async function testAPI() {
  console.log('🧪 开始测试牛马动物园后端API...\n');
  
  const headers = {
    'Authorization': `Bearer ${TEST_USER_TOKEN}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // 1. 测试健康检查
    console.log('1️⃣ 测试健康检查 API');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/v1/health`, { headers });
      console.log('✅ 健康检查成功:', healthResponse.data);
    } catch (error) {
      console.log('❌ 健康检查失败:', error.message);
    }
    
    // 2. 测试绘画分析 API
    console.log('\n2️⃣ 测试绘画分析 API');
    let analysisId;
    try {
      const analyzeResponse = await axios.post(`${BASE_URL}/api/v1/drawing/analyze`, {
        imageData: TEST_IMAGE_BASE64,
        preferredAnimalType: 'working_animal'
      }, { headers });
      
      console.log('✅ 绘画分析成功:', {
        success: analyzeResponse.data.success,
        analysisId: analyzeResponse.data.analysisId,
        detectedAnimalType: analyzeResponse.data.detectedAnimalType,
        confidence: analyzeResponse.data.confidence
      });
      analysisId = analyzeResponse.data.analysisId;
    } catch (error) {
      console.log('❌ 绘画分析失败:', error.response?.data || error.message);
    }
    
    // 3. 测试头像生成 API
    console.log('\n3️⃣ 测试头像生成 API');
    let avatarId;
    try {
      const generateResponse = await axios.post(`${BASE_URL}/api/v1/drawing/generate-avatar`, {
        imageData: TEST_IMAGE_BASE64,
        animalType: 'working_animal',
        animalSpecies: 'ox',
        analysisId: analysisId
      }, { headers });
      
      console.log('✅ 头像生成成功:', {
        success: generateResponse.data.success,
        avatarId: generateResponse.data.avatar.avatarId,
        animalType: generateResponse.data.avatar.animalType,
        generationMethod: generateResponse.data.generationMethod,
        processingTime: generateResponse.data.processingTime
      });
      avatarId = generateResponse.data.avatar.avatarId;
    } catch (error) {
      console.log('❌ 头像生成失败:', error.response?.data || error.message);
    }
    
    // 4. 测试绘画历史 API
    console.log('\n4️⃣ 测试绘画历史 API');
    try {
      const historyResponse = await axios.get(`${BASE_URL}/api/v1/drawing/history?page=1&limit=5`, { headers });
      console.log('✅ 绘画历史获取成功:', {
        total: historyResponse.data.total,
        page: historyResponse.data.page,
        itemsCount: historyResponse.data.items.length
      });
    } catch (error) {
      console.log('❌ 绘画历史获取失败:', error.response?.data || error.message);
    }
    
    // 5. 测试动物园场景 API
    console.log('\n5️⃣ 测试动物园场景 API');
    try {
      const sceneResponse = await axios.get(`${BASE_URL}/api/v1/zoo/scene?timeOfDay=14`, { headers });
      console.log('✅ 动物园场景获取成功:', {
        sceneId: sceneResponse.data.sceneId,
        sceneName: sceneResponse.data.sceneName,
        animalsCount: sceneResponse.data.animals.length,
        facilitiesCount: sceneResponse.data.facilities.length,
        statistics: sceneResponse.data.statistics
      });
    } catch (error) {
      console.log('❌ 动物园场景获取失败:', error.response?.data || error.message);
    }
    
    // 6. 测试动物列表 API
    console.log('\n6️⃣ 测试动物列表 API');
    try {
      const animalsResponse = await axios.get(`${BASE_URL}/api/v1/zoo/animals?page=1&limit=10`, { headers });
      console.log('✅ 动物列表获取成功:', {
        total: animalsResponse.data.total,
        animalsCount: animalsResponse.data.animals.length,
        statistics: animalsResponse.data.statistics
      });
    } catch (error) {
      console.log('❌ 动物列表获取失败:', error.response?.data || error.message);
    }
    
    // 7. 如果有头像，尝试创建动物园动物
    if (avatarId) {
      console.log('\n7️⃣ 测试从头像创建动物园动物 API');
      let animalId;
      try {
        const createAnimalResponse = await axios.post(`${BASE_URL}/api/v1/zoo/animals/create-from-avatar/${avatarId}`, {
          name: '测试小牛'
        }, { headers });
        
        console.log('✅ 动物创建成功:', {
          animalId: createAnimalResponse.data.id,
          name: createAnimalResponse.data.name,
          species: createAnimalResponse.data.species,
          category: createAnimalResponse.data.category
        });
        animalId = createAnimalResponse.data.id;
        
        // 8. 测试动物互动 API
        if (animalId) {
          console.log('\n8️⃣ 测试动物互动 API');
          try {
            const interactResponse = await axios.post(`${BASE_URL}/api/v1/zoo/animals/${animalId}/interact`, {
              interactionType: 'feed',
              intensity: 7,
              duration: 10,
              message: '给小牛喂食'
            }, { headers });
            
            console.log('✅ 动物互动成功:', {
              success: interactResponse.data.success,
              interactionType: interactResponse.data.result.interactionType,
              effectiveness: interactResponse.data.result.effectiveness,
              animalReaction: interactResponse.data.animalReaction.responseText,
              rewards: interactResponse.data.rewards
            });
          } catch (error) {
            console.log('❌ 动物互动失败:', error.response?.data || error.message);
          }
        }
      } catch (error) {
        console.log('❌ 动物创建失败:', error.response?.data || error.message);
      }
    }
    
    console.log('\n🎉 API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 执行测试
if (require.main === module) {
  testAPI().catch(console.error);
}

module.exports = { testAPI };