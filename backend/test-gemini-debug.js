/**
 * Gemini 2.5 Flash 调试测试脚本
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const { HttpsProxyAgent } = require('https-proxy-agent');

// 配置代理
const proxyUrl = process.env.HTTP_PROXY || 'http://127.0.0.1:7890';
const globalAgent = require('global-agent');

console.log('🔧 开始 Gemini 2.5 Flash 调试测试...');
console.log('📡 代理设置:', proxyUrl);
console.log('🔑 API密钥状态:', process.env.GEMINI_API_KEY ? `已设置 (${process.env.GEMINI_API_KEY.substring(0, 10)}...)` : '未设置');

// 设置全局代理
globalAgent.bootstrap();
global.GLOBAL_AGENT.HTTP_PROXY = proxyUrl;
global.GLOBAL_AGENT.HTTPS_PROXY = proxyUrl;

async function testGeminiConnection() {
    console.log('\n=== 测试 1: 基础连接测试 ===');
    
    if (!process.env.GEMINI_API_KEY) {
        console.error('❌ 错误: GEMINI_API_KEY 环境变量未设置');
        return false;
    }

    try {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        // 测试基础文本生成
        console.log('📝 测试基础文本生成...');
        const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });
        
        const prompt = "Say hello and confirm you are working properly.";
        console.log('发送提示词:', prompt);
        
        const result = await model.generateContent(prompt);
        const response = result.response;
        const text = response.text();
        
        console.log('✅ 基础连接成功!');
        console.log('📄 响应:', text);
        return true;
        
    } catch (error) {
        console.error('❌ 基础连接失败:', error);
        console.error('错误详情:', error.message);
        return false;
    }
}

async function testGeminiImageModel() {
    console.log('\n=== 测试 2: Gemini 2.5 Flash Image 模型测试 ===');
    
    try {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        // 测试 2.5 Flash Image 模型
        console.log('🖼️ 测试 Gemini 2.5 Flash Image 模型...');
        const imageModel = genAI.getGenerativeModel({ 
            model: "gemini-2.5-flash-image-preview",
            generationConfig: {
                temperature: 0.7,
            }
        });
        
        const prompt = "Generate a simple professional business portrait of a person in a suit.";
        console.log('发送图像生成提示词:', prompt);
        
        const result = await imageModel.generateContent(prompt);
        const response = result.response;
        
        console.log('📊 响应结构分析:');
        console.log('- 候选数量:', response.candidates?.length || 0);
        
        if (response.candidates && response.candidates[0]) {
            const candidate = response.candidates[0];
            console.log('- 内容部分数量:', candidate.content?.parts?.length || 0);
            
            if (candidate.content && candidate.content.parts) {
                candidate.content.parts.forEach((part, index) => {
                    console.log(`- 部分 ${index + 1}:`, {
                        hasText: !!part.text,
                        hasInlineData: !!part.inlineData,
                        mimeType: part.inlineData?.mimeType || 'none'
                    });
                });
            }
            
            // 检查是否有图像数据
            const hasImageData = candidate.content?.parts?.some(part => 
                part.inlineData && part.inlineData.mimeType?.includes('image')
            );
            
            if (hasImageData) {
                console.log('✅ 图像生成成功！发现图像数据');
                return true;
            } else {
                console.log('⚠️ 模型响应了，但没有图像数据');
                console.log('完整响应:', JSON.stringify(response, null, 2));
                return false;
            }
        } else {
            console.log('❌ 没有候选响应');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Gemini 2.5 Flash Image 测试失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return false;
    }
}

async function testNetworkConnectivity() {
    console.log('\n=== 测试 3: 网络连接测试 ===');
    
    try {
        // 测试直接连接 Google API
        const https = require('https');
        const { URL } = require('url');
        
        console.log('🌐 测试直接连接到 Google API...');
        
        return new Promise((resolve) => {
            const req = https.request('https://generativelanguage.googleapis.com/', {
                method: 'HEAD',
                timeout: 10000,
            }, (res) => {
                console.log('✅ 直接连接成功, 状态码:', res.statusCode);
                resolve(true);
            });
            
            req.on('error', (error) => {
                console.log('❌ 直接连接失败:', error.message);
                console.log('🔧 可能需要代理连接');
                resolve(false);
            });
            
            req.on('timeout', () => {
                console.log('❌ 连接超时');
                req.destroy();
                resolve(false);
            });
            
            req.end();
        });
        
    } catch (error) {
        console.error('❌ 网络测试失败:', error);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 开始完整的 Gemini 2.5 Flash 调试测试\n');
    
    // 测试网络连接
    const networkOk = await testNetworkConnectivity();
    
    // 测试基础连接
    const basicOk = await testGeminiConnection();
    
    // 测试图像生成
    let imageOk = false;
    if (basicOk) {
        imageOk = await testGeminiImageModel();
    } else {
        console.log('⏭️ 跳过图像测试，因为基础连接失败');
    }
    
    // 总结报告
    console.log('\n=== 测试总结 ===');
    console.log('网络连接:', networkOk ? '✅ 正常' : '❌ 失败');
    console.log('基础文本生成:', basicOk ? '✅ 正常' : '❌ 失败');
    console.log('图像生成:', imageOk ? '✅ 正常' : '❌ 失败');
    
    if (!basicOk && !networkOk) {
        console.log('\n🔧 建议检查项目:');
        console.log('1. 确保代理服务器 127.0.0.1:7890 正在运行');
        console.log('2. 检查 GEMINI_API_KEY 是否正确');
        console.log('3. 确认网络连接正常');
    } else if (basicOk && !imageOk) {
        console.log('\n🔧 图像生成问题分析:');
        console.log('1. Gemini 2.5 Flash Image 模型可能不可用');
        console.log('2. 模型名称可能不正确');
        console.log('3. 图像生成需要特殊权限');
    }
    
    return { networkOk, basicOk, imageOk };
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testGeminiConnection, testGeminiImageModel };