// Gemini 2.0 Flash Preview 真实集成方案
const express = require('express');
const cors = require('cors');
const axios = require('axios');
const sharp = require('sharp');
const app = express();

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));

// Gemini API 配置
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'YOUR_API_KEY_HERE';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview:generateContent';

// 真实的 Gemini 图像生成接口
app.post('/api/v1/avatar/gemini-fusion', async (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    try {
        // 1. 提取用户自画像的base64数据
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        
        // 2. 根据动物类型选择对应的动物头像
        const animalHead = selectAnimalHead(animalType);
        
        // 3. 构建 Gemini 深度融合提示词
        const fusionPrompt = buildGeminiFusionPrompt(animalType, animalHead);
        
        // 4. 准备 Gemini API 请求
        const geminiRequest = {
            contents: [
                {
                    parts: [
                        {
                            text: fusionPrompt
                        },
                        {
                            inline_data: {
                                mime_type: "image/png",
                                data: userDrawingBase64
                            }
                        },
                        {
                            text: `请基于上述自画像，与${animalHead.name}头像进行深度融合。要求：
                            1. 保持人物自画像的面部特征（如脸型、五官位置）
                            2. 将头部替换为${animalHead.name}的头部，但保留人物的表情和神态
                            3. 身体保持为穿着职业装的打工人形象
                            4. 整体风格为素描风格，线条清晰
                            5. 确保融合自然，不是简单的拼接
                            6. 输出一个完整的站立或行走姿态的全身像`
                        }
                    ]
                }
            ],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 8192,
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_NONE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH", 
                    threshold: "BLOCK_NONE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_NONE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_NONE"
                }
            ]
        };
        
        // 5. 调用 Gemini API
        console.log('正在调用 Gemini 2.0 Flash API...');
        const response = await axios.post(
            `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
            geminiRequest,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // 6. 处理 Gemini 响应
        const generatedImage = extractImageFromResponse(response.data);
        
        // 7. 如果需要，可以使用 sharp 进行后处理
        const processedImage = await postProcessImage(generatedImage);
        
        // 8. 返回生成结果
        res.json({
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: animalType,
                animalHead: animalHead.name,
                bodyType: 'human_worker',
                imageUrl: `data:image/png;base64,${processedImage}`,
                prompt: fusionPrompt,
                features: {
                    headShape: animalHead.features.headShape,
                    bodyPosture: 'professional_worker',
                    clothing: getWorkerClothing(animalType),
                    accessories: getWorkerAccessories(animalType),
                    expression: analysisData.expression || 'neutral'
                },
                stats: generateWorkerStats(),
                geminiResponse: response.data // 保存原始响应供调试
            },
            message: 'Gemini 2.0 Flash 深度融合成功！'
        });
        
    } catch (error) {
        console.error('Gemini API 调用失败:', error);
        
        // 如果 Gemini API 失败，返回详细错误信息
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'Gemini 图像生成失败，请检查 API 密钥和网络连接',
            fallback: generateFallbackAvatar(animalType) // 提供备用方案
        });
    }
});

// 构建 Gemini 深度融合提示词
function buildGeminiFusionPrompt(animalType, animalHead) {
    // 使用您指定的中文提示词
    const basePrompt = `请将自画像和一个动物的头像结合，形成一个人身动物头的打工人形象，做深度的融合，保持素描。`;
    
    let animalTypePrompt = '';
    switch (animalType) {
        case 'DIVINE':
            animalTypePrompt = `其中，动物类型为神兽（${animalHead.name}），体现神圣、优雅、智慧的特质。`;
            break;
        case 'PET':
            animalTypePrompt = `其中，动物类型为宠物（${animalHead.name}），体现可爱、友善、温暖的特质。`;
            break;
        case 'OXHORSE':
            animalTypePrompt = `其中，动物类型为牲畜（${animalHead.name}），体现勤劳、踏实、任劳任怨的特质。`;
            break;
    }
    
    const stylePrompt = `
    具体要求：
    1. 深度融合：不是简单的头部替换，而是将人物特征与动物特征自然融合
    2. 保持素描风格：使用铅笔素描的线条和阴影技法
    3. 打工人形象：身体穿着正装或职业装，体现办公室工作者
    4. 姿态自然：全身像，可以是站立或轻松行走的姿态
    5. 表情生动：保留人物的表情特征，融入动物的神态
    `;
    
    return basePrompt + animalTypePrompt + stylePrompt;
}

// 选择动物头像
function selectAnimalHead(animalType) {
    const animalHeads = {
        'DIVINE': [
            { name: '龙', features: { headShape: 'majestic', power: 'divine' } },
            { name: '凤凰', features: { headShape: 'elegant', power: 'mystical' } },
            { name: '麒麟', features: { headShape: 'noble', power: 'wisdom' } },
            { name: '白虎', features: { headShape: 'fierce', power: 'strength' } }
        ],
        'PET': [
            { name: '柴犬', features: { headShape: 'round', mood: 'happy' } },
            { name: '橘猫', features: { headShape: 'cute', mood: 'lazy' } },
            { name: '兔子', features: { headShape: 'soft', mood: 'gentle' } },
            { name: '仓鼠', features: { headShape: 'small', mood: 'active' } }
        ],
        'OXHORSE': [
            { name: '黄牛', features: { headShape: 'sturdy', trait: 'hardworking' } },
            { name: '马', features: { headShape: 'strong', trait: 'enduring' } },
            { name: '驴', features: { headShape: 'honest', trait: 'persistent' } },
            { name: '羊', features: { headShape: 'gentle', trait: 'obedient' } }
        ]
    };
    
    const heads = animalHeads[animalType] || animalHeads['OXHORSE'];
    return heads[Math.floor(Math.random() * heads.length)];
}

// 从 Gemini 响应中提取图像
function extractImageFromResponse(response) {
    // Gemini 2.0 Flash 返回的图像数据格式
    if (response.candidates && response.candidates[0]) {
        const candidate = response.candidates[0];
        if (candidate.content && candidate.content.parts) {
            for (const part of candidate.content.parts) {
                if (part.inline_data && part.inline_data.data) {
                    return part.inline_data.data;
                }
            }
        }
    }
    
    throw new Error('无法从 Gemini 响应中提取图像');
}

// 使用 sharp 进行图像后处理
async function postProcessImage(base64Image) {
    try {
        const buffer = Buffer.from(base64Image, 'base64');
        
        // 应用素描效果增强
        const processedBuffer = await sharp(buffer)
            .resize(512, 640, { fit: 'contain', background: { r: 255, g: 255, b: 255 } })
            .greyscale() // 转为灰度图以增强素描效果
            .normalize() // 标准化对比度
            .sharpen() // 锐化线条
            .toBuffer();
        
        return processedBuffer.toString('base64');
    } catch (error) {
        console.error('图像后处理失败:', error);
        return base64Image; // 返回原始图像
    }
}

// 获取打工人服装
function getWorkerClothing(animalType) {
    const clothing = {
        'DIVINE': { type: '西装', color: '#1a1a1a', style: '优雅正装' },
        'PET': { type: '休闲装', color: '#ff6b9d', style: '舒适便装' },
        'OXHORSE': { type: '工作服', color: '#4682b4', style: '实用工装' }
    };
    return clothing[animalType] || clothing['OXHORSE'];
}

// 获取打工人配件
function getWorkerAccessories(animalType) {
    const accessories = {
        'DIVINE': ['金框眼镜', '名牌手表', '公文包'],
        'PET': ['可爱徽章', '彩色笔', '便当盒'],
        'OXHORSE': ['工作证', '保温杯', '笔记本']
    };
    const items = accessories[animalType] || accessories['OXHORSE'];
    return items.slice(0, Math.floor(Math.random() * items.length) + 1);
}

// 生成打工人属性
function generateWorkerStats() {
    return {
        workEfficiency: Math.floor(Math.random() * 40) + 60, // 60-100
        happiness: Math.floor(Math.random() * 40) + 40, // 40-80
        energy: Math.floor(Math.random() * 40) + 50, // 50-90
        creativity: Math.floor(Math.random() * 40) + 45 // 45-85
    };
}

// 生成备用头像
function generateFallbackAvatar(animalType) {
    // 返回一个简单的 SVG 作为备用方案
    const colors = {
        'DIVINE': '#FFD700',
        'PET': '#FF69B4',
        'OXHORSE': '#8B4513'
    };
    
    const color = colors[animalType] || '#8B4513';
    
    const svg = `
    <svg width="200" height="240" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="240" fill="white"/>
        <text x="100" y="120" text-anchor="middle" font-size="16" fill="#666">
            Gemini API 暂时不可用
        </text>
        <text x="100" y="140" text-anchor="middle" font-size="14" fill="#999">
            请检查 API 密钥配置
        </text>
    </svg>`;
    
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
}

// 健康检查
app.get('/api/v1/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'Gemini Integration Service',
        apiKeyConfigured: !!GEMINI_API_KEY && GEMINI_API_KEY !== 'YOUR_API_KEY_HERE'
    });
});

const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
    console.log(`✅ Gemini 集成服务运行在 http://localhost:${PORT}`);
    console.log(`📌 请设置环境变量 GEMINI_API_KEY 来使用真实的 Gemini API`);
    console.log(`📌 示例: export GEMINI_API_KEY=your_actual_api_key`);
});

module.exports = app;