#!/bin/bash

# 牛马动物园后端服务启动脚本
# Usage: ./scripts/start-dev.sh

set -e

echo "🚀 启动牛马动物园后端开发环境..."

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查环境配置文件
if [ ! -f .env ]; then
    echo "📋 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 创建必要的目录
mkdir -p logs uploads

echo "🛠️  安装项目依赖..."
npm install

echo "🐳 启动数据库服务..."
docker-compose -f docker-compose.dev.yml up -d postgres mongo redis

# 等待数据库启动
echo "⏳ 等待数据库启动完成..."
sleep 10

# 检查数据库连接
echo "🔍 检查数据库连接..."
until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U niuma; do
  echo "等待 PostgreSQL 启动..."
  sleep 2
done

until docker-compose -f docker-compose.dev.yml exec -T mongo mongosh --eval "db.runCommand('ping').ok"; do
  echo "等待 MongoDB 启动..."
  sleep 2
done

until docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping; do
  echo "等待 Redis 启动..."
  sleep 2
done

echo "✅ 所有数据库服务已启动完成"

# 运行数据库迁移（如果需要）
# echo "🔄 运行数据库迁移..."
# npm run db:migrate

echo "🌟 启动后端服务..."
npm run start:dev &

echo "📚 服务启动完成！"
echo ""
echo "🔗 访问地址:"
echo "   API服务: http://localhost:3000"
echo "   API文档: http://localhost:3000/api/docs" 
echo "   健康检查: http://localhost:3000/api/v1/health"
echo ""
echo "🗃️  数据库连接:"
echo "   PostgreSQL: localhost:5432"
echo "   MongoDB: localhost:27017"
echo "   Redis: localhost:6379"
echo ""
echo "🛑 停止服务: Ctrl+C 或运行 ./scripts/stop-dev.sh"
echo ""

# 等待用户输入停止
wait