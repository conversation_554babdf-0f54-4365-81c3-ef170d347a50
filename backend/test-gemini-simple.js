/**
 * 简单的 Gemini API 测试
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testSimpleGemini() {
    console.log('🔑 API Key:', process.env.GEMINI_API_KEY ? '已设置' : '未设置');
    
    if (!process.env.GEMINI_API_KEY) {
        console.error('❌ GEMINI_API_KEY 未设置');
        return;
    }
    
    try {
        console.log('📡 初始化 GoogleGenerativeAI...');
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        console.log('🤖 获取 gemini-pro 模型...');
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        
        console.log('💭 发送测试提示...');
        const result = await model.generateContent("Hello! Please respond with 'Gemini is working!'");
        
        console.log('📤 获取响应...');
        const response = result.response;
        const text = response.text();
        
        console.log('✅ 成功! 响应:', text);
        
        return true;
        
    } catch (error) {
        console.error('❌ 错误:', error);
        console.error('错误消息:', error.message);
        console.error('错误代码:', error.code);
        console.error('错误状态:', error.status);
        
        return false;
    }
}

// 直接执行
testSimpleGemini();