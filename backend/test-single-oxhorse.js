// 测试单个牛马头像生成
const axios = require('axios');
const fs = require('fs');
const { createCanvas } = require('canvas');

// 使用之前成功的简单绘画
function createSimpleTestDrawing() {
    const canvas = createCanvas(400, 500);
    const ctx = canvas.getContext('2d');
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 400, 500);
    
    // 黑色线条
    ctx.strokeStyle = '#000000';
    ctx.fillStyle = '#000000';
    ctx.lineWidth = 3;
    
    // 头部 - 圆形
    ctx.beginPath();
    ctx.arc(200, 120, 60, 0, Math.PI * 2);
    ctx.stroke();
    
    // 眼睛
    ctx.beginPath();
    ctx.arc(180, 110, 5, 0, Math.PI * 2);
    ctx.arc(220, 110, 5, 0, Math.PI * 2);
    ctx.fill();
    
    // 嘴巴 - 微笑
    ctx.beginPath();
    ctx.arc(200, 125, 15, 0.1 * Math.PI, 0.9 * Math.PI);
    ctx.stroke();
    
    // 脖子
    ctx.beginPath();
    ctx.moveTo(200, 180);
    ctx.lineTo(200, 220);
    ctx.stroke();
    
    // 身体 - 矩形
    ctx.strokeRect(160, 220, 80, 120);
    
    // 胳膊
    ctx.beginPath();
    ctx.moveTo(160, 250);
    ctx.lineTo(120, 300);
    ctx.moveTo(240, 250);
    ctx.lineTo(280, 300);
    ctx.stroke();
    
    // 腿
    ctx.beginPath();
    ctx.moveTo(180, 340);
    ctx.lineTo(180, 420);
    ctx.moveTo(220, 340);
    ctx.lineTo(220, 420);
    ctx.stroke();
    
    // 脚
    ctx.beginPath();
    ctx.moveTo(180, 420);
    ctx.lineTo(170, 430);
    ctx.moveTo(220, 420);
    ctx.lineTo(230, 430);
    ctx.stroke();
    
    return canvas.toDataURL('image/png');
}

async function testSingleOxHorse() {
    console.log('🐂 测试单个牛马头像生成...\n');
    
    const testImage = createSimpleTestDrawing();
    console.log(`✅ 创建测试画像: ${(testImage.length / 1024).toFixed(1)}KB`);
    
    try {
        console.log('🔄 发送API请求...');
        const startTime = Date.now();
        
        const response = await axios.post('http://localhost:3005/api/v1/avatar/real-generate', {
            imageData: testImage,
            animalType: 'OXHORSE'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 60000
        });
        
        const duration = Date.now() - startTime;
        console.log(`⚡ 生成速度: ${duration}ms`);
        
        if (response.data.success) {
            const base64Data = response.data.avatar.imageUrl.replace(/^data:image\/png;base64,/, '');
            fs.writeFileSync('/Users/<USER>/WorkSpace/niuma/single_oxhorse_test.png', Buffer.from(base64Data, 'base64'));
            
            console.log('✅ 牛马头像生成成功!');
            console.log('📊 生成方法:', response.data.avatar.generationMethod);
            console.log('📁 结果保存: single_oxhorse_test.png');
            console.log('📏 文件大小:', (Buffer.from(base64Data, 'base64').length / 1024).toFixed(1), 'KB');
            
            console.log('\n🔍 Gemini分析结果:');
            console.log(response.data.avatar.geminiAnalysis);
            
            console.log('\n🎨 DALL-E描述:');
            console.log(response.data.avatar.characterDescription);
            
            console.log('\n🎯 检查关键特征:');
            const desc = response.data.avatar.characterDescription.toLowerCase();
            console.log('- 牛头特征:', desc.includes('bull') || desc.includes('bovine') ? '✅' : '❌');
            console.log('- 办公室工作者:', desc.includes('office') || desc.includes('professional') ? '✅' : '❌');
            console.log('- 素描风格:', desc.includes('pencil') || desc.includes('sketch') ? '✅' : '❌');
            console.log('- 人体保持:', desc.includes('human body') || desc.includes('standing') ? '✅' : '❌');
            
        } else {
            console.log('❌ API返回错误:', response.data.error);
        }
        
    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        if (error.response) {
            console.log('响应状态:', error.response.status);
            console.log('响应数据:', error.response.data);
        }
    }
}

testSingleOxHorse();