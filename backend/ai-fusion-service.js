/**
 * 真实AI融合服务 - 集成Gemini和DALL-E3进行自画像+动物头像融合
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');
const crypto = require('crypto');

// 使用 undici 代理
const { setGlobalDispatcher, ProxyAgent } = require('undici');

class AIFusionService {
    constructor() {
        const proxyUrl = process.env.HTTP_PROXY || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
        
        // 设置 undici 全局代理 (用于 Google Generative AI)
        const undiciProxyAgent = new ProxyAgent(proxyUrl);
        setGlobalDispatcher(undiciProxyAgent);
        
        // 设置 https-proxy-agent (用于 OpenAI)
        const httpsProxyAgent = new HttpsProxyAgent(proxyUrl);
        
        // 为Gemini配置代理
        if (process.env.GEMINI_API_KEY) {
            this.geminiAPI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
            console.log('✅ Gemini API 已配置，使用 undici 代理:', proxyUrl);
        } else {
            this.geminiAPI = null;
        }
            
        // 为OpenAI配置代理
        this.openaiAPI = process.env.OPENAI_API_KEY ? 
            new OpenAI({ 
                apiKey: process.env.OPENAI_API_KEY,
                httpAgent: httpsProxyAgent
            }) : null;
            
        this.isEnabled = process.env.AI_GENERATION_ENABLED === 'true';
    }

    /**
     * 分析用户绘画特征
     */
    async analyzeUserDrawing(imageBase64) {
        try {
            if (!this.isEnabled || !this.geminiAPI) {
                return this.getFallbackAnalysis();
            }

            const model = this.geminiAPI.getGenerativeModel({ model: "gemini-1.5-flash" });
            
            const prompt = `分析这幅自画像，提取以下特征：
            1. 面部形状（圆形、方形、椭圆形等）
            2. 身体姿势（站立、坐着等）
            3. 绘画风格（简笔画、写实、卡通等）
            4. 情感表达（快乐、严肃、友善等）
            5. 推荐匹配的动物类型（OXHORSE牛马、PET宠物、DIVINE神兽）

            请返回JSON格式结果。`;

            const result = await model.generateContent([
                {
                    inlineData: {
                        data: imageBase64.replace(/^data:image\/\w+;base64,/, ''),
                        mimeType: 'image/png'
                    }
                },
                prompt
            ]);

            const analysis = this.parseGeminiResponse(result.response.text());
            return analysis;

        } catch (error) {
            console.error('Gemini分析失败:', error);
            return this.getFallbackAnalysis();
        }
    }

    /**
     * 生成融合头像 - 自画像身体 + 动物头部
     */
    async generateFusionAvatar(userDrawingBase64, animalType, analysisData) {
        try {
            if (!this.isEnabled) {
                throw new Error('AI生成服务未启用');
            }

            // 优先使用Gemini 2.5 Flash进行图像生成
            if (this.geminiAPI) {
                return await this.generateWithGemini(userDrawingBase64, animalType, analysisData);
            }
            
            // 备选方案：使用DALL-E3
            if (this.openaiAPI) {
                return await this.generateWithDALLE3(userDrawingBase64, animalType, analysisData);
            }

            throw new Error('没有可用的AI生成服务');

        } catch (error) {
            console.error('AI融合生成失败:', error);
            throw error;
        }
    }

    /**
     * 使用DALL-E3生成融合头像
     */
    async generateWithDALLE3(userDrawingBase64, animalType, analysisData) {
        const animalPrompts = {
            OXHORSE: '牛马头部，具有牛的稳重和马的灵动特征',
            PET: '可爱宠物头部，猫狗等萌宠的温暖特征',
            DIVINE: '神兽头部，龙凤等神话动物的威严特征'
        };

        const prompt = `创建一个专业的打工人头像，要求：
        1. 保持用户绘画中的身体姿势和服装风格
        2. 将头部替换为${animalPrompts[animalType]}
        3. 添加职场元素：领带、工牌、正装
        4. 整体风格：${analysisData.style || '简洁专业'}
        5. 表情：${analysisData.expression || '专注认真'}
        6. 背景：简洁的办公环境
        
        艺术风格：现代插画，线条清晰，色彩和谐，适合作为职场头像使用。`;

        try {
            const response = await this.openaiAPI.images.generate({
                model: "dall-e-3",
                prompt: prompt,
                n: 1,
                size: "1024x1024",
                quality: "standard",
                response_format: "b64_json"
            });

            // 保存DALL-E3生成的图像到文件系统
            const avatarId = Date.now().toString();
            const filename = `avatar-dalle3-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
            const filePath = path.join(__dirname, 'uploads', 'generated-avatars', filename);
            
            // 确保目录存在
            const uploadDir = path.dirname(filePath);
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }
            
            // 将base64数据写入文件
            const imageBuffer = Buffer.from(response.data[0].b64_json, 'base64');
            fs.writeFileSync(filePath, imageBuffer);
            
            console.log('💾 DALL-E3图像已保存到:', filePath);
            
            // 返回文件URL而不是base64数据
            const imageUrl = `/uploads/generated-avatars/${filename}`;

            return {
                success: true,
                avatar: {
                    avatarId: avatarId,
                    animalType: animalType,
                    generationMethod: 'DALL-E3 AI Fusion',
                    imageUrl: imageUrl,
                    filePath: filePath,
                    prompt: prompt,
                    fusionDetails: {
                        method: 'DALL-E3 Fusion Generation',
                        animalType: animalType,
                        bodySource: 'User Drawing Style',
                        headSource: `${animalType} Animal Head`,
                        style: analysisData.style || 'Professional',
                        features: {
                            aiGenerated: true,
                            fusionQuality: 'High',
                            professionalElements: true
                        }
                    }
                }
            };

        } catch (error) {
            console.error('DALL-E3生成失败:', error);
            throw error;
        }
    }

    /**
     * 使用Gemini 2.0 Flash生成融合头像
     */
    async generateWithGemini(userDrawingBase64, animalType, analysisData) {
        // 使用 Gemini 2.0 Flash Preview Image Generation 进行图像生成
        const model = this.geminiAPI.getGenerativeModel({ 
            model: "gemini-2.0-flash-preview-image-generation",
            generationConfig: {
                temperature: 0.7,
                responseModalities: ["TEXT", "IMAGE"] // 指定需要文本和图像响应
            },
            requestOptions: {
                timeout: 30000, // 30秒超时
            }
        });

        const animalDescriptions = {
            OXHORSE: 'a hardworking ox-horse hybrid head showing dedication and diligence',
            PET: 'a friendly pet head (cat or dog) with warm and approachable features',
            DIVINE: 'a majestic divine beast head (dragon or phoenix) with professional authority'
        };

        const prompt = `Create a professional workplace avatar (512x512px) by combining the user's body drawing with ${animalDescriptions[animalType]}.

        Scene: A polished business portrait showing a person in professional attire from chest up, photographed against a clean, neutral background with soft, professional lighting.

        Requirements:
        - Preserve the exact body posture and proportions from the user's original drawing
        - Replace only the head with ${animalDescriptions[animalType]}
        - Add professional workplace elements: business suit, tie, company ID badge
        - Facial expression: ${analysisData.expression || 'focused and professional'}
        - Art style: ${analysisData.style || 'clean business illustration'}, optimized for web use
        - Lighting: Soft, professional headshot lighting
        - Background: Clean, neutral professional background
        - Output: Medium quality, web-optimized PNG format

        Generate a professional image suitable for LinkedIn profile or workplace identification.`;

        try {
            console.log('正在使用 Gemini 2.0 Flash Preview Image Generation 生成图像...');
            
            const result = await model.generateContent([
                prompt,
                {
                    inlineData: {
                        data: userDrawingBase64 ? userDrawingBase64.replace(/^data:image\/\w+;base64,/, '') : '',
                        mimeType: 'image/png'
                    }
                }
            ]);

            const response = result.response;
            
            console.log('📊 Gemini 2.0 Flash 响应结构:');
            console.log('- 候选数量:', response.candidates?.length || 0);
            
            // 检查是否有图像数据返回
            if (response.candidates && response.candidates[0]) {
                const candidate = response.candidates[0];
                
                // 如果返回的是图像数据
                if (candidate.content && candidate.content.parts) {
                    for (const part of candidate.content.parts) {
                        if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.includes('image')) {
                            console.log('✅ Gemini 2.0 Flash 成功生成图像');
                            
                            // 保存图像到文件系统
                            const avatarId = Date.now().toString();
                            const filename = `avatar-${avatarId}-${crypto.randomBytes(6).toString('hex')}.png`;
                            const filePath = path.join(__dirname, 'uploads', 'generated-avatars', filename);
                            
                            // 确保目录存在
                            const uploadDir = path.dirname(filePath);
                            if (!fs.existsSync(uploadDir)) {
                                fs.mkdirSync(uploadDir, { recursive: true });
                            }
                            
                            // 将base64数据写入文件
                            const imageBuffer = Buffer.from(part.inlineData.data, 'base64');
                            fs.writeFileSync(filePath, imageBuffer);
                            
                            console.log('💾 图像已保存到:', filePath);
                            
                            // 返回文件URL而不是base64数据
                            const imageUrl = `/uploads/generated-avatars/${filename}`;
                            
                            return {
                                success: true,
                                avatar: {
                                    avatarId: avatarId,
                                    animalType: animalType,
                                    generationMethod: 'Gemini 2.0 Flash Image Generation',
                                    imageUrl: imageUrl,
                                    filePath: filePath,
                                    fusionDetails: {
                                        method: 'Gemini 2.0 Flash Direct Generation',
                                        animalType: animalType,
                                        style: analysisData.style || 'Professional',
                                        model: 'gemini-2.0-flash-preview-image-generation'
                                    }
                                }
                            };
                        }
                    }
                }
                
                // 如果返回的是文本描述
                if (candidate.content && candidate.content.parts && candidate.content.parts[0].text) {
                    const text = candidate.content.parts[0].text;
                    console.log('📝 Gemini 返回了文本描述:', text);
                    
                    // 返回带有AI描述的结果，但仍然抛出错误让系统回退到Canvas
                    console.log('⚠️ 没有收到图像数据，将回退到Canvas生成');
                    throw new Error('Gemini 2.0 Flash 返回了文本而不是图像');
                }
            }
            
            console.log('❌ Gemini 2.0 Flash 响应格式异常');
            throw new Error('Gemini 2.0 Flash 响应格式异常');

        } catch (error) {
            console.error('Gemini 2.0 Flash 生成失败:', error);
            console.error('错误详情:', error.message);
            throw error;
        }
    }

    /**
     * 解析Gemini响应
     */
    parseGeminiResponse(text) {
        try {
            // 尝试解析JSON响应
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }

            // 如果不是JSON，返回基础分析
            return {
                animalType: text.includes('牛马') ? 'OXHORSE' : 
                           text.includes('宠物') ? 'PET' : 'DIVINE',
                confidence: 0.8,
                traits: ['professional', 'focused'],
                suggestions: {
                    style: 'professional',
                    expression: 'serious'
                }
            };
        } catch (error) {
            console.error('解析Gemini响应失败:', error);
            return this.getFallbackAnalysis();
        }
    }

    /**
     * 备选分析结果
     */
    getFallbackAnalysis() {
        return {
            animalType: Math.random() > 0.6 ? 'PET' : (Math.random() > 0.3 ? 'OXHORSE' : 'DIVINE'),
            confidence: 0.75,
            traits: ['professional', 'dedicated', 'focused'],
            suggestions: {
                bodyPosture: 'standing',
                expression: 'serious',
                style: 'professional'
            }
        };
    }

    /**
     * 检查服务可用性
     */
    getServiceStatus() {
        return {
            enabled: this.isEnabled,
            geminiAvailable: !!this.geminiAPI,
            openaiAvailable: !!this.openaiAPI,
            preferredService: this.openaiAPI ? 'DALL-E3' : (this.geminiAPI ? 'Gemini' : 'None')
        };
    }
}

module.exports = { AIFusionService };