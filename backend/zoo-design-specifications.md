# 牛马动物园 (Ox-<PERSON> Zoo) - 2D Layout Design Specifications

## 1. Overall Layout Structure

### Dimensions and Grid System
- **Canvas Size**: 1920x1200px (16:10 aspect ratio for desktop optimization)
- **Grid System**: 24x15 tiles (80px per tile)
- **Coordinate System**: Top-left origin (0,0), X increases right, Y increases down
- **Viewport**: Supports responsive scaling for mobile (minimum 800x600)

### Zone Distribution
```
+------------------+------------------+------------------+
|   ENTRANCE       |    GRASSLAND     |     FOREST       |
|   (4x3 tiles)    |    (8x6 tiles)   |   (8x6 tiles)    |
+------------------+------------------+------------------+
|   DESERT         |    CENTRAL       |     MOUNTAIN     |
|   (6x4 tiles)    |    POND          |   (6x4 tiles)    |
|                  |   (4x4 tiles)    |                  |
+------------------+------------------+------------------+
|   FACILITIES     |    PATHWAYS      |   REST AREAS     |
|   (4x2 tiles)    |   (connecting)   |   (4x2 tiles)    |
+------------------+------------------+------------------+
```

## 2. Detailed Zone Specifications

### 2.1 Entrance Area (入口区)
- **Position**: Top-left corner (0, 0) to (320, 240)
- **Background**: Warm brick pathway with welcome signage
- **Elements**:
  - Zoo entrance gate (Chinese-style paifang architecture)
  - "牛马动物园" signage with cartoon mascot
  - Ticket booth (decorative only)
  - Welcome mat with paw prints
- **Color Scheme**: Warm browns (#8B4513), gold accents (#FFD700)

### 2.2 Grassland (草地区) - Primary Working Animal Habitat
- **Position**: Center-left (320, 0) to (960, 480)
- **Background**: Lush green grass texture with gentle rolling hills
- **Target Animals**: 牛马系列 (Ox, Horse, Donkey, Sheep, Goat)
- **Elements**:
  - Scattered hay bales
  - Small wooden fences (boundaries, not barriers)
  - Wildflowers and butterflies
  - Feeding troughs
  - Small barn structure
- **Color Scheme**: Multiple greens (#90EE90, #32CD32, #228B22)
- **Movement Paths**: Open roaming with designated walkways

### 2.3 Forest Area (森林区) - Pet Habitat
- **Position**: Top-right (960, 0) to (1600, 480)
- **Background**: Dense tree canopy with dappled sunlight
- **Target Animals**: 宠物系列 (Cats, Dogs, Rabbits, Hamsters, Parrots)
- **Elements**:
  - Various tree types (oak, pine, birch)
  - Hollow logs and tree houses
  - Mushrooms and forest flowers
  - Bird perches and nesting boxes
  - Small streams and stepping stones
- **Color Scheme**: Forest greens (#006400), brown bark (#8B4513), dappled yellow sunlight (#FFFF99)

### 2.4 Central Pond (中央池塘) - Community Hub
- **Position**: Center (800, 480) to (1120, 800)
- **Background**: Clear blue water with lily pads
- **Purpose**: Central gathering and interaction point
- **Elements**:
  - Circular pond with decorative bridge
  - Lily pads and lotus flowers
  - Small waterfall feature
  - Surrounding stone pathways
  - Benches for "visitors"
- **Color Scheme**: Blues (#87CEEB, #4682B4), aqua highlights (#00CED1)

### 2.5 Desert Area (沙漠区) - Specialized Habitat
- **Position**: Bottom-left (0, 800) to (480, 1200)
- **Background**: Sandy dunes with occasional oases
- **Target Animals**: Heat-adapted animals and special variants
- **Elements**:
  - Sand dunes with wind patterns
  - Cactus plants and desert rocks
  - Small oasis with palm trees
  - Sun-bleached bones (decorative)
- **Color Scheme**: Sandy yellows (#F4A460), warm oranges (#DEB887)

### 2.6 Mountain Area (山地区) - Divine Beast Habitat
- **Position**: Bottom-right (1440, 800) to (1920, 1200)
- **Background**: Rocky peaks with mystical atmosphere
- **Target Animals**: 神兽系列 (Dragons, Phoenixes, Qilins, Unicorns)
- **Elements**:
  - Layered mountain peaks
  - Mystical clouds and light effects
  - Ancient stone structures
  - Glowing crystals or gems
  - Floating platforms (for flying creatures)
- **Color Scheme**: Cool grays (#708090), mystical purples (#9370DB), gold highlights (#FFD700)

### 2.7 Rest Areas (休息区)
- **Position**: Bottom-right facilities (1440, 960) to (1920, 1200)
- **Elements**:
  - Picnic tables and benches
  - Vending machines (decorative)
  - Shade structures
  - Information kiosks
- **Color Scheme**: Neutral browns and greens

## 3. Pathways and Movement System

### 3.1 Main Pathways
- **Width**: 60px (3/4 tile width)
- **Material**: Cobblestone texture with grass edges
- **Network**: Connects all major zones in circular pattern
- **Color**: Warm gray (#A9A9A9) with darker borders (#696969)

### 3.2 Animal Movement Zones
- **Zone Boundaries**: Invisible collision detection areas
- **Preferred Paths**: Animals gravitate toward their habitat zones but can explore others
- **Movement Speed Modifiers**:
  - Native habitat: 100% speed
  - Adjacent zones: 80% speed
  - Opposite zones: 60% speed

### 3.3 Interactive Hotspots
- **Feeding Areas**: Animals gather here periodically
- **Water Sources**: All animals visit for drinking
- **Social Spots**: Areas where animals can interact
- **Rest Areas**: Animals occasionally stop to "rest"

## 4. Visual Style Guidelines

### 4.1 Art Style
- **Overall Aesthetic**: Cheerful, cartoonish, hand-drawn feel
- **Inspiration**: Studio Ghibli environments meets mobile game aesthetics
- **Rendering**: 2D sprites with subtle shadowing and highlights
- **Animation Style**: Smooth, bouncy movements with personality

### 4.2 Color Palette
```css
/* Primary Colors */
--grass-green: #90EE90;
--water-blue: #87CEEB;
--earth-brown: #8B4513;
--stone-gray: #A9A9A9;

/* Accent Colors */
--divine-gold: #FFD700;
--pet-pink: #FFB6C1;
--working-tan: #D2B48C;

/* Environmental Colors */
--forest-dark: #006400;
--desert-sand: #F4A460;
--mountain-purple: #9370DB;
--sky-light: #E6F3FF;
```

### 4.3 Lighting and Atmosphere
- **Time of Day**: Bright daylight with soft shadows
- **Special Effects**: 
  - Twinkling particles around divine beasts
  - Gentle wind animation in grass and trees
  - Water ripples and reflections
  - Floating dust motes in sunbeams

## 5. Asset Requirements

### 5.1 Background Layers
- **Base Terrain**: Large tileable textures for each zone
- **Decoration Layer**: Trees, rocks, structures (PNG with transparency)
- **Overlay Effects**: Lighting, particles, weather effects
- **Path Network**: Seamless path tiles and intersections

### 5.2 Environmental Objects
- **Static Decorations**: 50+ unique objects per zone type
- **Interactive Elements**: Feeding stations, water sources, play structures
- **Boundary Markers**: Subtle visual cues for zone transitions
- **Seasonal Variants**: Optional seasonal decoration swaps

### 5.3 Animal Sprites
- **Sprite Sheets**: 8-direction movement, idle, interaction animations
- **Size Standards**: 32x32px for small animals, 64x64px for large animals
- **Special Effects**: Trails for divine beasts, hearts for happy pets
- **Customization**: Color variants and accessories for user personalization

## 6. UI Integration Points

### 6.1 HUD Elements
- **Mini-Map**: Top-right corner, 200x150px
- **Animal Info Panel**: Bottom-left, expandable
- **Action Buttons**: Bottom-center toolbar
- **Notification Area**: Top-center for system messages

### 6.2 Interactive Features
- **Click/Tap Targets**: All animals are clickable for information
- **Zoom Controls**: Pinch-to-zoom support, 0.5x to 2x range
- **Camera Pan**: Smooth follow camera with manual override
- **Context Menus**: Right-click/long-press for animal interactions

### 6.3 Performance Optimization
- **LOD System**: Reduced detail sprites for distant animals
- **Culling**: Only render visible screen area
- **Sprite Pooling**: Reuse animal sprites efficiently
- **Progressive Loading**: Load zone assets as needed

## 7. Technical Implementation Notes

### 7.1 Coordinate System
```javascript
// Zone boundaries for collision detection
const ZONES = {
  ENTRANCE: { x: 0, y: 0, width: 320, height: 240 },
  GRASSLAND: { x: 320, y: 0, width: 640, height: 480 },
  FOREST: { x: 960, y: 0, width: 640, height: 480 },
  DESERT: { x: 0, y: 800, width: 480, height: 400 },
  POND: { x: 800, y: 480, width: 320, height: 320 },
  MOUNTAIN: { x: 1440, y: 800, width: 480, height: 400 }
};
```

### 7.2 Animal Spawning Rules
- **Rarity Distribution**: Weighted random spawning based on animal rarity percentages
- **Zone Preferences**: Animals spawn in preferred habitats but can move freely
- **Population Limits**: Maximum animals per zone to prevent overcrowding
- **Respawn Mechanics**: New animals appear at zone entrances

### 7.3 Animation Framework
- **State Machine**: Idle, Walking, Interacting, Sleeping states
- **Smooth Transitions**: Easing functions for natural movement
- **Performance**: Target 60fps with 50+ simultaneous animals
- **Responsive Design**: Scales appropriately for different screen sizes

## 8. User Experience Considerations

### 8.1 Accessibility
- **Color Blind Support**: High contrast mode available
- **Text Size**: Scalable UI text (14px minimum)
- **Navigation**: Keyboard navigation support
- **Screen Readers**: Alt text for all interactive elements

### 8.2 Engagement Features
- **Discovery**: Hidden areas and rare animal spawns
- **Collection**: Animal gallery with stats and information
- **Interaction**: Feeding, petting, and playing with animals
- **Progression**: Unlock new areas or decorations over time

### 8.3 Mobile Optimization
- **Touch Targets**: Minimum 44px tap targets
- **Gesture Support**: Pinch zoom, two-finger pan
- **Battery Optimization**: Efficient rendering and animation
- **Offline Mode**: Core functionality works without internet

This design creates an engaging, scalable environment that accommodates the three-tier animal system while providing an entertaining experience for office workers looking for a cheerful break from their daily routine.