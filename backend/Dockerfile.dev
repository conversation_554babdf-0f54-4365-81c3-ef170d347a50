# 开发版本的Dockerfile - 简单直接
FROM node:18-alpine

# 安装Canvas依赖
RUN apk add --no-cache \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    python3 \
    make \
    g++ \
    curl

WORKDIR /app

# 复制所有文件
COPY package*.json ./
RUN npm install

COPY . .

# 创建uploads目录
RUN mkdir -p uploads/generated-avatars

EXPOSE 3000

# 直接运行，不切换用户
CMD ["node", "simple-server.js"]