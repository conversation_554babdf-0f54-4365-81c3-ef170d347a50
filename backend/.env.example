# 牛马动物园后端服务环境变量配置示例

# 基础配置
NODE_ENV=development
PORT=3000
APP_VERSION=1.0.0

# 数据库配置
# PostgreSQL 数据库
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=niuma_user
DB_PASSWORD=niuma_password
DB_DATABASE=niuma_zoo
DATABASE_URL=postgresql://niuma_user:niuma_password@localhost:5432/niuma_zoo

# MongoDB 配置（如果需要）
MONGODB_URI=mongodb://localhost:27017/niuma_zoo

# Redis 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# AI服务配置
# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here
# 获取地址: https://makersuite.google.com/app/apikey

# OpenAI API (DALL-E)
OPENAI_API_KEY=your_openai_api_key_here
# 获取地址: https://platform.openai.com/api-keys

# Replicate API (Stable Diffusion)
REPLICATE_API_TOKEN=your_replicate_api_token_here
# 获取地址: https://replicate.com/account/api-tokens

# 代理配置（如果需要）
# https_proxy=http://localhost:7890
# http_proxy=http://localhost:7890

# 限流配置
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# 日志配置
LOG_LEVEL=info

# 文件上传配置
UPLOAD_MAX_SIZE=********  # 10MB
UPLOAD_ALLOWED_TYPES=image/png,image/jpeg,image/jpg,image/gif,image/webp

# 邮件配置（如果需要）
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password

# 其他API配置
WEATHER_API_KEY=your_weather_api_key_here
NOTIFICATION_SERVICE_URL=https://your-notification-service.com

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here

# 前端配置
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=http://localhost:5173,http://localhost:3000