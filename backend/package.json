{"name": "niuma-zoo-backend", "version": "1.0.0", "description": "牛马动物园后端API服务", "author": "<PERSON>", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:migrate": "npm run typeorm -- migration:run", "db:migrate:revert": "npm run typeorm -- migration:revert", "db:generate": "npm run typeorm -- migration:generate", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js --config src/config/ormconfig.ts", "seed": "ts-node -r tsconfig-paths/register src/database/seeds/seed.ts", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d"}, "dependencies": {"@faker-js/faker": "^10.0.0", "@google/generative-ai": "^0.24.1", "@nestjs/bull": "^10.0.0", "@nestjs/cache-manager": "^2.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/throttler": "^4.0.0", "@nestjs/typeorm": "^10.0.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "bull": "^4.10.0", "cache-manager-redis-store": "^3.0.1", "canvas": "^3.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^6.8.1", "global-agent": "^3.0.0", "helmet": "^7.0.0", "https-proxy-agent": "^7.0.6", "joi": "^17.9.0", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.0.0", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.0", "node-fetch": "^2.7.0", "openai": "^5.15.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.32.6", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "undici": "^7.14.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.195", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}