// 测试Canvas输出，看看实际生成的图像什么样子
const axios = require('axios');
const fs = require('fs');
const { createCanvas } = require('canvas');

// 创建测试画像
function createTestImage() {
    const canvas = createCanvas(300, 300);
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 300, 300);
    
    // 画一个简单的脸
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 4;
    
    // 脸部轮廓
    ctx.beginPath();
    ctx.arc(150, 150, 80, 0, Math.PI * 2);
    ctx.stroke();
    
    // 眼睛
    ctx.beginPath();
    ctx.arc(130, 130, 10, 0, Math.PI * 2);
    ctx.arc(170, 130, 10, 0, Math.PI * 2);
    ctx.stroke();
    ctx.fill();
    
    // 鼻子
    ctx.beginPath();
    ctx.moveTo(150, 140);
    ctx.lineTo(145, 155);
    ctx.lineTo(155, 155);
    ctx.closePath();
    ctx.stroke();
    
    // 嘴巴
    ctx.beginPath();
    ctx.arc(150, 170, 20, 0.2 * Math.PI, 0.8 * Math.PI);
    ctx.stroke();
    
    return canvas.toDataURL();
}

async function testCanvasOutput() {
    console.log('🎨 测试Canvas生成的实际图像效果...\n');
    
    const testImage = createTestImage();
    
    try {
        const response = await axios.post('http://localhost:3005/api/v1/avatar/real-generate', {
            imageData: testImage,
            animalType: 'OXHORSE'
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        
        console.log('✅ API调用成功');
        console.log('📊 生成方法:', response.data.avatar.generationMethod);
        console.log('🔍 像素分析:', response.data.avatar.geminiAnalysis.includes('像素分析'));
        
        // 保存生成的图像到文件，以便查看
        const base64Data = response.data.avatar.imageUrl.replace(/^data:image\/png;base64,/, '');
        
        // 保存原始用户画像
        const originalBase64 = testImage.replace(/^data:image\/png;base64,/, '');
        fs.writeFileSync('/Users/<USER>/WorkSpace/niuma/user_drawing.png', Buffer.from(originalBase64, 'base64'));
        
        // 保存生成的融合图像
        fs.writeFileSync('/Users/<USER>/WorkSpace/niuma/generated_avatar.png', Buffer.from(base64Data, 'base64'));
        
        console.log('📁 文件已保存:');
        console.log('   用户原画: /Users/<USER>/WorkSpace/niuma/user_drawing.png');
        console.log('   生成头像: /Users/<USER>/WorkSpace/niuma/generated_avatar.png');
        
        console.log('\n🔍 分析结果预览:');
        console.log(response.data.avatar.geminiAnalysis.slice(0, 200) + '...');
        
        console.log('\n📝 描述结果预览:');
        console.log(response.data.avatar.characterDescription.slice(0, 150) + '...');
        
        // 分析图像大小
        const imageSize = Buffer.from(base64Data, 'base64').length;
        console.log(`\n📊 生成图像信息:`);
        console.log(`   文件大小: ${(imageSize / 1024).toFixed(1)}KB`);
        console.log(`   是否包含动物特征: ${response.data.avatar.characterDescription.includes('bovine') ? '✅ 是' : '❌ 否'}`);
        console.log(`   是否是素描风格: ${response.data.avatar.characterDescription.includes('pencil sketch') ? '✅ 是' : '❌ 否'}`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

testCanvasOutput();