// Gemini 2.0 Flash Image Generation 真实实现
const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const sharp = require('sharp');
const app = express();

app.use(cors());
app.use(express.json({ limit: '50mb' }));

// 初始化 Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// 健康检查
app.get('/api/v1/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'Gemini 2.0 Flash Image Generation',
        apiKeyConfigured: !!process.env.GEMINI_API_KEY
    });
});

// Gemini 2.0 Flash 图像生成接口
app.post('/api/v1/avatar/gemini-generate', async (req, res) => {
    const { imageData, animalType, analysisData } = req.body;
    
    if (!process.env.GEMINI_API_KEY) {
        return res.status(500).json({
            success: false,
            error: 'GEMINI_API_KEY not configured',
            message: '请设置 GEMINI_API_KEY 环境变量'
        });
    }
    
    try {
        // 使用 Gemini 2.0 Flash 模型
        const model = genAI.getGenerativeModel({ 
            model: "gemini-2.0-flash-exp",
            generationConfig: {
                temperature: 1.0,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: "image/png" // 请求生成图像
            }
        });
        
        // 准备用户自画像数据
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        
        // 选择动物类型
        const animalMapping = {
            'DIVINE': '龙或凤凰',
            'PET': '猫或狗',
            'OXHORSE': '牛或马'
        };
        
        const selectedAnimal = animalMapping[animalType] || '牛';
        
        // 构建融合提示词
        const prompt = `
请将自画像和一个动物的头像结合，形成一个人身动物头的打工人形象，做深度的融合，保持素描。
其中，动物类型为${selectedAnimal}，体现${animalType === 'DIVINE' ? '神圣优雅' : animalType === 'PET' ? '可爱友善' : '勤劳踏实'}的特质。

具体要求：
1. 基于提供的自画像，保留人物的面部特征和表情
2. 将头部替换为${selectedAnimal}的头部，但要自然融合
3. 身体保持人类形态，穿着职业装
4. 整体风格为素描风格，黑白线条
5. 输出完整的站立全身像

请生成这个融合后的人身动物头打工人形象。
        `;
        
        // 构建多模态输入
        const parts = [
            { text: prompt },
            {
                inlineData: {
                    mimeType: "image/png",
                    data: userDrawingBase64
                }
            }
        ];
        
        console.log('调用 Gemini 2.0 Flash 生成图像...');
        
        // 生成内容
        const result = await model.generateContent(parts);
        const response = await result.response;
        
        // 提取生成的图像
        let generatedImage = null;
        
        // Gemini 2.0 Flash 返回的可能是图像数据或图像描述
        if (response.candidates && response.candidates[0]) {
            const candidate = response.candidates[0];
            
            // 检查是否有图像数据
            if (candidate.content && candidate.content.parts) {
                for (const part of candidate.content.parts) {
                    if (part.inlineData && part.inlineData.mimeType.includes('image')) {
                        generatedImage = part.inlineData.data;
                        break;
                    }
                }
            }
        }
        
        // 如果没有直接生成图像，尝试使用图像生成 API
        if (!generatedImage) {
            // 使用 Imagen 3 或其他 Google 图像生成服务
            generatedImage = await generateWithImagen(prompt, userDrawingBase64);
        }
        
        if (!generatedImage) {
            // 如果还是没有图像，生成一个占位符
            generatedImage = await generatePlaceholderImage(selectedAnimal, animalType);
        }
        
        // 后处理图像为素描风格
        const sketchImage = await applySketchEffect(generatedImage);
        
        // 返回结果
        res.json({
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: animalType,
                animalHead: selectedAnimal,
                imageUrl: `data:image/png;base64,${sketchImage}`,
                prompt: prompt,
                features: {
                    style: 'sketch',
                    fusion: 'deep',
                    quality: 'high'
                },
                stats: generateWorkerStats(),
                geminiResponse: response.text() || '图像生成成功'
            },
            message: 'Gemini 2.0 Flash 生成成功！'
        });
        
    } catch (error) {
        console.error('Gemini API 错误:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            suggestion: '请确认 Gemini 2.0 Flash 是否支持图像生成，或尝试使用 Imagen API'
        });
    }
});

// 使用 Google Imagen API 生成图像（如果可用）
async function generateWithImagen(prompt, referenceImage) {
    try {
        // Imagen 3 API 调用
        // 注意：这需要单独的 API 访问权限
        const imagenModel = genAI.getGenerativeModel({ 
            model: "imagen-3.0-generate-001" // Imagen 3 模型
        });
        
        const result = await imagenModel.generateImages({
            prompt: prompt,
            numberOfImages: 1,
            aspectRatio: "4:5",
            personGeneration: "allow",
            safetyFilterLevel: "block_some",
            languageCode: "zh-CN"
        });
        
        if (result.images && result.images[0]) {
            return result.images[0].bytesBase64Encoded;
        }
    } catch (error) {
        console.log('Imagen API 不可用，使用备用方案');
    }
    
    return null;
}

// 应用素描效果
async function applySketchEffect(imageBase64) {
    try {
        const buffer = Buffer.from(imageBase64, 'base64');
        
        // 使用 sharp 应用素描效果
        const processedBuffer = await sharp(buffer)
            .resize(512, 640, { fit: 'contain' })
            .greyscale() // 转为灰度
            .normalize() // 标准化对比度
            .linear(1.5, -(128 * 1.5) + 128) // 增加对比度
            .blur(0.5) // 轻微模糊
            .sharpen() // 然后锐化，创造素描效果
            .toBuffer();
        
        return processedBuffer.toString('base64');
    } catch (error) {
        console.error('图像处理失败:', error);
        return imageBase64;
    }
}

// 生成占位符图像
async function generatePlaceholderImage(animal, type) {
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(512, 640);
    const ctx = canvas.getContext('2d');
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 512, 640);
    
    // 绘制素描风格的占位符
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    
    // 头部（动物）
    ctx.beginPath();
    ctx.arc(256, 150, 80, 0, Math.PI * 2);
    ctx.stroke();
    
    // 耳朵或角
    if (animal.includes('猫') || animal.includes('狗')) {
        // 耳朵
        ctx.beginPath();
        ctx.moveTo(200, 120);
        ctx.lineTo(180, 80);
        ctx.lineTo(220, 100);
        ctx.closePath();
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(312, 120);
        ctx.lineTo(332, 80);
        ctx.lineTo(292, 100);
        ctx.closePath();
        ctx.stroke();
    } else if (animal.includes('龙')) {
        // 角
        ctx.beginPath();
        ctx.moveTo(220, 100);
        ctx.lineTo(200, 60);
        ctx.moveTo(292, 100);
        ctx.lineTo(312, 60);
        ctx.stroke();
    }
    
    // 眼睛
    ctx.beginPath();
    ctx.arc(230, 150, 8, 0, Math.PI * 2);
    ctx.arc(282, 150, 8, 0, Math.PI * 2);
    ctx.fill();
    
    // 鼻子
    ctx.beginPath();
    if (animal.includes('牛') || animal.includes('马')) {
        ctx.ellipse(256, 180, 15, 10, 0, 0, Math.PI * 2);
    } else {
        ctx.arc(256, 175, 5, 0, Math.PI * 2);
    }
    ctx.stroke();
    
    // 身体（人类）
    ctx.beginPath();
    ctx.moveTo(256, 230);
    ctx.lineTo(180, 350);
    ctx.lineTo(180, 500);
    ctx.lineTo(256, 500);
    ctx.lineTo(332, 500);
    ctx.lineTo(332, 350);
    ctx.closePath();
    ctx.stroke();
    
    // 领带
    ctx.beginPath();
    ctx.moveTo(256, 250);
    ctx.lineTo(246, 350);
    ctx.lineTo(256, 360);
    ctx.lineTo(266, 350);
    ctx.closePath();
    ctx.stroke();
    
    // 手臂
    ctx.beginPath();
    ctx.moveTo(180, 350);
    ctx.lineTo(140, 450);
    ctx.moveTo(332, 350);
    ctx.lineTo(372, 450);
    ctx.stroke();
    
    // 腿
    ctx.beginPath();
    ctx.moveTo(220, 500);
    ctx.lineTo(220, 600);
    ctx.moveTo(292, 500);
    ctx.lineTo(292, 600);
    ctx.stroke();
    
    // 添加一些素描纹理
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 0.5;
    for (let i = 0; i < 20; i++) {
        ctx.beginPath();
        ctx.moveTo(Math.random() * 512, Math.random() * 640);
        ctx.lineTo(Math.random() * 512, Math.random() * 640);
        ctx.stroke();
    }
    
    return canvas.toDataURL().split(',')[1];
}

function generateWorkerStats() {
    return {
        workEfficiency: Math.floor(Math.random() * 40) + 60,
        happiness: Math.floor(Math.random() * 40) + 40,
        energy: Math.floor(Math.random() * 40) + 50,
        creativity: Math.floor(Math.random() * 40) + 45
    };
}

const PORT = process.env.PORT || 3004;
app.listen(PORT, () => {
    console.log(`✅ Gemini 2.0 Flash 图像生成服务运行在 http://localhost:${PORT}`);
    if (!process.env.GEMINI_API_KEY) {
        console.log(`⚠️  请设置 GEMINI_API_KEY 环境变量:`);
        console.log(`   export GEMINI_API_KEY=your_api_key_here`);
        console.log(`   获取密钥: https://makersuite.google.com/app/apikey`);
    }
    console.log(`\n📌 注意：Gemini 2.0 Flash 的图像生成功能可能需要特殊权限`);
    console.log(`   如果无法生成图像，将使用占位符或考虑使用 Imagen API`);
});

module.exports = app;