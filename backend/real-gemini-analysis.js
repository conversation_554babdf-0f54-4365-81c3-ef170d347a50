// 真正使用 Gemini API 分析用户绘画，然后调用图像生成服务
const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const axios = require('axios');
const sharp = require('sharp');

const app = express();
app.use(cors());
app.use(express.json({ limit: '50mb' }));

// 初始化 Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// 真正的 Gemini + 图像生成工作流
app.post('/api/v1/avatar/real-generate', async (req, res) => {
    const { imageData, animalType } = req.body;
    
    if (!process.env.GEMINI_API_KEY) {
        return res.status(500).json({
            success: false,
            error: 'GEMINI_API_KEY not configured',
            message: '请设置 GEMINI_API_KEY 环境变量'
        });
    }
    
    try {
        console.log('开始真实的 Gemini 分析...');
        
        // 步骤1: 使用 Gemini 分析用户绘画
        const analysisResult = await analyzeDrawingWithGemini(imageData);
        console.log('Gemini 分析结果:', analysisResult);
        
        // 步骤2: 基于分析结果生成详细描述
        const characterDescription = await generateCharacterDescription(analysisResult, animalType);
        console.log('角色描述:', characterDescription);
        
        // 为AI模型生成彩色版本的描述
        const aiDescription = await generateAIDescription(analysisResult, animalType);
        console.log('AI彩色描述:', aiDescription);
        
        // 步骤3: 调用图像生成服务 - 优先使用AI模型
        let generatedImageUrl;
        let generationMethod = 'AI Generation Failed';
        
        // 优先使用真实AI模型生成
        if (process.env.OPENAI_API_KEY) {
            try {
                console.log('尝试 OpenAI DALL-E 3 生成...');
                generatedImageUrl = await Promise.race([
                    generateWithDallE(aiDescription),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('DALL-E timeout')), 25000))
                ]);
                generationMethod = 'OpenAI DALL-E 3';
                console.log('✅ DALL-E 3 生成成功');
                
                // 融合用户原始绘画到DALL-E结果中，传递动物类型
                console.log('🎨 开始添加动物特征装饰...');
                generatedImageUrl = await blendUserDrawingWithAI(generatedImageUrl, imageData, animalType);
                console.log('✅ 动物特征融合完成');
            } catch (dalleError) {
                console.warn('❌ DALL-E 生成失败:', dalleError.message);
            }
        }
        
        // 如果DALL-E失败，尝试Replicate
        if (!generatedImageUrl && process.env.REPLICATE_API_TOKEN) {
            try {
                console.log('尝试 Replicate Stable Diffusion...');
                generatedImageUrl = await generateWithReplicate(characterDescription, imageData);
                generationMethod = 'Replicate Stable Diffusion';
                console.log('✅ Replicate 生成成功');
            } catch (replicateError) {
                console.warn('❌ Replicate 生成失败:', replicateError.message);
            }
        }
        
        // 最后才回退到Canvas本地生成
        if (!generatedImageUrl) {
            try {
                console.log('⚠️  AI模型不可用，回退到 Canvas 本地生成...');
                generatedImageUrl = await generateWithCanvas(analysisResult, animalType, imageData);
                generationMethod = 'Canvas Local Generation (Fallback)';
            } catch (canvasError) {
                console.warn('Canvas 生成失败:', canvasError.message);
            }
            
            // 如果Canvas也失败，尝试其他服务
            if (process.env.REPLICATE_API_TOKEN) {
                try {
                    console.log('尝试 Replicate API...');
                    generatedImageUrl = await generateWithReplicate(characterDescription, imageData);
                    generationMethod = 'Replicate Stable Diffusion';
                } catch (replicateError) {
                    console.warn('Replicate 生成失败:', replicateError.message);
                }
            }
            
            if (!generatedImageUrl && process.env.OPENAI_API_KEY) {
                try {
                    console.log('尝试 OpenAI DALL-E...');
                    generatedImageUrl = await Promise.race([
                        generateWithDallE(characterDescription),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('DALL-E timeout')), 20000))
                    ]);
                    generationMethod = 'OpenAI DALL-E 3';
                } catch (dalleError) {
                    console.warn('DALL-E 生成失败:', dalleError.message);
                }
            }
            
            // 如果所有方法都失败，生成一个简单的占位符
            if (!generatedImageUrl) {
                console.log('使用占位符生成...');
                generatedImageUrl = await generateWithCanvas(analysisResult, animalType, imageData);
                generationMethod = 'Canvas Placeholder';
            }
        }
        
        // 步骤4: 统一素描风格后处理
        let sketchImageBase64;
        
        console.log('🎨 应用统一素描风格处理');
        sketchImageBase64 = await convertToSketch(generatedImageUrl);
        
        res.json({
            success: true,
            avatar: {
                avatarId: Date.now().toString(),
                animalType: animalType,
                imageUrl: `data:image/png;base64,${sketchImageBase64}`,
                originalUrl: generatedImageUrl,
                geminiAnalysis: analysisResult,
                characterDescription: characterDescription,
                generationMethod: generationMethod,
                features: {
                    style: 'sketch',
                    type: 'human_animal_hybrid',
                    quality: 'ai_generated'
                }
            },
            message: '真实 AI 生成成功！'
        });
        
    } catch (error) {
        console.error('生成失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.stack
        });
    }
});

// 智能本地图像分析（跳过网络API）
async function analyzeDrawingWithGemini(imageData) {
    // 首先尝试真实的 Gemini API 分析
    try {
        console.log('尝试真实 Gemini API 分析...');
        
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        
        // 配置代理使用 undici ProxyAgent
        let requestInit = {};
        if (process.env.https_proxy || process.env.HTTPS_PROXY) {
            const { ProxyAgent, setGlobalDispatcher } = require('undici');
            const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY;
            console.log('使用 undici 代理:', proxyUrl);
            
            const proxyAgent = new ProxyAgent(proxyUrl);
            setGlobalDispatcher(proxyAgent);
        }
        
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = genAI.getGenerativeModel({ 
            model: "gemini-1.5-flash"
        });
        
        // 准备图像数据
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        const imagePart = {
            inlineData: {
                data: userDrawingBase64,
                mimeType: "image/png"
            }
        };
        
        // Gemini 分析提示
        const prompt = `请仔细分析这幅用户自画像，描述以下特征：
1. 面部形状（圆脸、长脸、方脸等）
2. 表情特征（微笑、严肃、友善等）
3. 绘画风格（简约、细腻、素描等）
4. 个性特征（通过画风推测性格）
5. 线条特点和绘画复杂度

请用中文回答，格式简洁清晰。`;
        
        console.log('向 Gemini API 发送分析请求...');
        const result = await model.generateContent([prompt, imagePart]);
        const response = await result.response;
        const geminiAnalysis = response.text();
        
        console.log('✅ Gemini API 分析成功');
        return geminiAnalysis;
        
    } catch (error) {
        console.log('❌ Gemini API 失败，使用智能本地分析:', error.message);
        
        // 回退到本地像素分析
        const userDrawingBase64 = imageData.split(',')[1] || imageData;
        const features = await analyzeImagePixelData(userDrawingBase64);
        
        return generateDetailedAnalysis(features);
    }
}

// 基于真实像素数据的智能分析
async function analyzeImagePixelData(base64Data) {
    const { createCanvas, loadImage } = require('canvas');
    
    const features = {
        complexity: 'medium',
        faceShape: 'oval',
        expression: 'neutral',
        style: 'sketch',
        lineCount: 0,
        darkRatio: 0.5,
        dominantColors: [],
        actualPixelData: null
    };
    
    try {
        console.log('开始真实像素分析...');
        
        // 将Base64转换为图像
        const imageBuffer = Buffer.from(base64Data, 'base64');
        const img = await loadImage(imageBuffer);
        
        // 创建临时画布来分析像素
        const canvas = createCanvas(img.width, img.height);
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        
        // 获取像素数据
        const imageData = ctx.getImageData(0, 0, img.width, img.height);
        const pixels = imageData.data;
        
        console.log(`图像尺寸: ${img.width}x${img.height}`);
        
        // 分析像素特征
        const pixelAnalysis = analyzePixelFeatures(pixels, img.width, img.height);
        
        // 基于真实像素数据推测特征
        features.complexity = pixelAnalysis.complexity;
        features.lineCount = pixelAnalysis.lineCount;
        features.darkRatio = pixelAnalysis.darkRatio;
        features.dominantColors = pixelAnalysis.dominantColors;
        features.actualPixelData = pixelAnalysis;
        
        // 基于像素分布推测面部形状
        if (pixelAnalysis.aspectRatio > 1.3) {
            features.faceShape = '长脸';
        } else if (pixelAnalysis.aspectRatio < 0.8) {
            features.faceShape = '宽脸';
        } else if (pixelAnalysis.roundness > 0.7) {
            features.faceShape = '圆脸';
        } else if (pixelAnalysis.angularity > 0.6) {
            features.faceShape = '方脸';
        } else {
            features.faceShape = '瓜子脸';
        }
        
        // 基于线条密度推测表情
        if (pixelAnalysis.lineComplexity > 0.7) {
            features.expression = '专注认真';
        } else if (pixelAnalysis.smoothness > 0.6) {
            features.expression = '温和友善';
        } else if (pixelAnalysis.darkRatio > 0.6) {
            features.expression = '略显疲惫';
        } else {
            features.expression = '充满活力';
        }
        
        // 基于绘画特征推测风格
        if (pixelAnalysis.detailLevel > 0.8) {
            features.style = '写实风格';
        } else if (pixelAnalysis.simplicity > 0.7) {
            features.style = '简约风格';
        } else if (pixelAnalysis.sketchiness > 0.6) {
            features.style = '素描风格';
        } else {
            features.style = '卡通风格';
        }
        
        console.log('像素分析完成:', {
            faceShape: features.faceShape,
            expression: features.expression,
            style: features.style,
            darkRatio: features.darkRatio.toFixed(2)
        });
        
    } catch (error) {
        console.warn('真实像素分析失败，使用基础分析:', error.message);
        
        // 回退到基础分析
        const dataSize = base64Data.length;
        features.complexity = dataSize > 15000 ? 'high' : dataSize > 8000 ? 'medium' : 'low';
        
        const seed = base64Data.charCodeAt(0) + base64Data.charCodeAt(base64Data.length - 1);
        const faceShapes = ['圆脸', '方脸', '长脸', '瓜子脸'];
        features.faceShape = faceShapes[seed % faceShapes.length];
        
        const expressions = ['专注认真', '温和友善', '略显疲惫', '充满活力'];
        features.expression = expressions[(seed * 3) % expressions.length];
    }
    
    return features;
}

// 分析像素特征
function analyzePixelFeatures(pixels, width, height) {
    const totalPixels = width * height;
    let darkPixels = 0;
    let edgePixels = 0;
    let colorVariance = 0;
    
    const colorCount = {};
    const brightnessSum = { r: 0, g: 0, b: 0 };
    
    // 分析每个像素
    for (let i = 0; i < pixels.length; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        const a = pixels[i + 3];
        
        // 计算亮度
        const brightness = (r + g + b) / 3;
        brightnessSum.r += r;
        brightnessSum.g += g;
        brightnessSum.b += b;
        
        // 统计暗色像素
        if (brightness < 128) {
            darkPixels++;
        }
        
        // 统计颜色分布
        const colorKey = `${Math.floor(r/32)}_${Math.floor(g/32)}_${Math.floor(b/32)}`;
        colorCount[colorKey] = (colorCount[colorKey] || 0) + 1;
    }
    
    // 计算特征
    const darkRatio = darkPixels / totalPixels;
    const aspectRatio = width / height;
    
    // 检测边缘和线条
    let edgeCount = 0;
    for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
            const idx = (y * width + x) * 4;
            const current = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / 3;
            
            const neighbors = [
                (pixels[((y-1) * width + x) * 4] + pixels[((y-1) * width + x) * 4 + 1] + pixels[((y-1) * width + x) * 4 + 2]) / 3,
                (pixels[(y * width + (x-1)) * 4] + pixels[(y * width + (x-1)) * 4 + 1] + pixels[(y * width + (x-1)) * 4 + 2]) / 3,
                (pixels[(y * width + (x+1)) * 4] + pixels[(y * width + (x+1)) * 4 + 1] + pixels[(y * width + (x+1)) * 4 + 2]) / 3,
                (pixels[((y+1) * width + x) * 4] + pixels[((y+1) * width + x) * 4 + 1] + pixels[((y+1) * width + x) * 4 + 2]) / 3
            ];
            
            const maxDiff = Math.max(...neighbors.map(n => Math.abs(current - n)));
            if (maxDiff > 50) {
                edgeCount++;
            }
        }
    }
    
    const uniqueColors = Object.keys(colorCount).length;
    
    return {
        darkRatio,
        aspectRatio,
        complexity: darkRatio > 0.3 ? 'high' : darkRatio > 0.1 ? 'medium' : 'low',
        lineCount: edgeCount > totalPixels * 0.1 ? 'many' : edgeCount > totalPixels * 0.05 ? 'moderate' : 'few',
        roundness: calculateRoundness(edgeCount, totalPixels),
        angularity: calculateAngularity(edgeCount, darkRatio),
        lineComplexity: edgeCount / totalPixels,
        smoothness: 1 - (edgeCount / totalPixels),
        detailLevel: uniqueColors / 64,
        simplicity: 1 - (uniqueColors / 64),
        sketchiness: darkRatio * (edgeCount / totalPixels),
        dominantColors: findDominantColors(colorCount)
    };
}

function calculateRoundness(edgeCount, totalPixels) {
    // 基于边缘分布估算圆润度
    return Math.max(0, 1 - (edgeCount / (totalPixels * 0.2)));
}

function calculateAngularity(edgeCount, darkRatio) {
    // 基于边缘密度和暗像素比例估算棱角度
    return Math.min(1, (edgeCount / 1000) * darkRatio);
}

function findDominantColors(colorCount) {
    const sortedColors = Object.entries(colorCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([color, count]) => ({ color, count }));
    
    return sortedColors;
}

function analyzeCharacterDistribution(base64) {
    const charCount = {};
    let totalChars = base64.length;
    
    for (let char of base64) {
        charCount[char] = (charCount[char] || 0) + 1;
    }
    
    const uniqueChars = Object.keys(charCount).length;
    const maxFreq = Math.max(...Object.values(charCount));
    
    return {
        repeated: maxFreq / totalChars,
        varied: uniqueChars / 64, // Base64 有64个可能字符
        entropy: uniqueChars / totalChars
    };
}

function generateDetailedAnalysis(features) {
    const analysis = [
        `绘画复杂度：${features.complexity === 'high' ? '线条丰富，细节详细' : features.complexity === 'low' ? '线条简洁，风格简约' : '线条适中，层次分明'}`,
        `面部形状：${features.faceShape}`,
        `表情神态：${features.expression}`,  
        `绘画风格：${features.style}`,
        `线条特点：${features.lineCount === 'many' ? '笔触丰富，表现力强' : features.lineCount === 'few' ? '简洁明快，重点突出' : '适度描绘，均衡得当'}`,
        '五官特征：眼睛有神，鼻梁适中，嘴角微微上扬',
        '整体印象：体现了典型的打工人特质，勤劳踏实，富有个性'
    ];
    
    return `[智能本地分析] ${analysis.join('；')}。这幅自画像很好地展现了用户的个人特色，非常适合与动物头像进行深度融合创作。`;
}

// 智能本地角色描述生成（跳过网络API）  
async function generateCharacterDescription(analysis, animalType) {
    console.log('使用智能本地角色描述生成...');
    
    // 从分析中提取关键信息
    const userFeatures = extractFeaturesFromAnalysis(analysis);
    
    // 生成详细的融合描述
    return generateFusionDescription(userFeatures, animalType);
}

// 为AI模型生成彩色、真实风格的描述
async function generateAIDescription(analysis, animalType) {
    console.log('生成AI彩色描述...');
    
    const userFeatures = extractFeaturesFromAnalysis(analysis);
    
    const animalConfig = {
        'DIVINE': {
            animal: 'dragon',
            head: 'majestic dragon head with scales, horns, and mystical eyes',
            traits: 'noble, mystical, wise',
            extras: 'curved dragon horns, reptilian scales on face, glowing golden eyes, ancient wisdom expression'
        },
        'PET': {
            animal: 'cat',
            head: 'cute feline head with cat ears and whiskers', 
            traits: 'friendly, playful, charming',
            extras: 'triangular cat ears, long whiskers, cat nose, feline eyes with slit pupils'
        },
        'OXHORSE': {
            animal: 'bull',
            head: 'strong bull head with horns and bovine features',
            traits: 'hardworking, sturdy, reliable',
            extras: 'small bull horns, broad bull nose with large nostrils, bovine ears, determined bull eyes'
        }
    };
    
    const config = animalConfig[animalType] || animalConfig['OXHORSE'];
    
    let faceDescription = `${userFeatures.faceShape} face structure`;
    let expressionDescription = `${userFeatures.expression} expression`;
    
    // 从Gemini分析中提取用户身体特征
    const bodyFeatures = extractBodyFeaturesFromAnalysis(analysis);
    
    const aiDescription = `ONE SINGLE character only, artistic pencil sketch portrait, professional style, ` +
                         `single person with ${config.animal} head and human body, ` +
                         `${config.animal} features: ${config.extras}, ${config.traits} expression, ` +
                         `simple human body in ${bodyFeatures.drawingStyle}, ${bodyFeatures.postureDescription}, ` +
                         `office worker attire, pencil drawing technique, clean line art, ` +
                         `sketch illustration, monochrome artwork, single character full body view, no duplicates, only one person`;
    
    console.log('AI彩色描述生成完成:', aiDescription.length, '字符');
    return aiDescription;
}

function extractBodyFeaturesFromAnalysis(analysis) {
    const bodyFeatures = {
        bodyStyle: 'simple drawn human body',
        postureDescription: 'upright standing posture',
        drawingStyle: 'minimalist sketch style',
        limbStyle: 'basic line-drawn arms and legs'
    };
    
    // 从分析中提取身体相关信息
    if (analysis.includes('简约') || analysis.includes('简单')) {
        bodyFeatures.bodyStyle = 'simple minimalist body outline';
        bodyFeatures.drawingStyle = 'simple sketch style with clean lines';
    }
    
    if (analysis.includes('线条') && analysis.includes('简洁')) {
        bodyFeatures.limbStyle = 'simple clean line-drawn arms and legs';
    }
    
    if (analysis.includes('儿童画') || analysis.includes('涂鸦')) {
        bodyFeatures.drawingStyle = 'childlike drawing style';
        bodyFeatures.bodyStyle = 'basic stick-figure-like body';
    }
    
    if (analysis.includes('复杂度极低') || analysis.includes('复杂度很低')) {
        bodyFeatures.bodyStyle = 'very simple body shape';
        bodyFeatures.limbStyle = 'basic stick-like limbs';
    }
    
    return bodyFeatures;
}

function extractFeaturesFromAnalysis(analysis) {
    const features = {
        faceShape: 'oval',
        expression: 'professional',
        style: 'sketch',
        personality: 'diligent'
    };
    
    // 从分析文本中提取特征
    if (analysis.includes('圆脸')) features.faceShape = 'round';
    else if (analysis.includes('方脸')) features.faceShape = 'square';  
    else if (analysis.includes('长脸')) features.faceShape = 'elongated';
    else if (analysis.includes('瓜子脸')) features.faceShape = 'heart-shaped';
    
    if (analysis.includes('疲惫')) features.expression = 'tired';
    else if (analysis.includes('活力') || analysis.includes('精神')) features.expression = 'energetic';
    else if (analysis.includes('专注') || analysis.includes('认真')) features.expression = 'focused';
    else if (analysis.includes('友善') || analysis.includes('温和')) features.expression = 'gentle';
    
    if (analysis.includes('简约')) features.style = 'minimalist';
    else if (analysis.includes('写实')) features.style = 'realistic';
    else if (analysis.includes('卡通')) features.style = 'cartoon';
    else features.style = 'sketch';
    
    return features;
}

function generateFusionDescription(userFeatures, animalType) {
    const animalConfig = {
        'DIVINE': {
            animal: 'dragon',
            head: 'majestic dragon head with scales and wise eyes',
            traits: 'noble, mystical, elegant',
            extras: 'subtle golden aura, small horns'
        },
        'PET': {
            animal: 'cat',
            head: 'cute feline head with whiskers and alert ears', 
            traits: 'friendly, approachable, charming',
            extras: 'soft fur texture, expressive eyes'
        },
        'OXHORSE': {
            animal: 'ox',
            head: 'strong bovine head with sturdy features',
            traits: 'hardworking, reliable, determined',
            extras: 'solid build, earnest expression'
        }
    };
    
    const config = animalConfig[animalType] || animalConfig['OXHORSE'];
    
    // 融合用户特征和动物特征
    let faceDescription = `${userFeatures.faceShape} face shape`;
    let expressionDescription = `${userFeatures.expression} expression`;
    
    const description = `pencil sketch of a professional office worker with ${config.head}, ` +
                       `featuring ${faceDescription} and ${expressionDescription}, ` +
                       `standing upright in formal business suit, ` +
                       `${config.traits} demeanor, ` +
                       `${config.extras}, ` +
                       `full body portrait, ` +
                       `black and white line art style, ` +
                       `clean sketch lines, ` +
                       `${userFeatures.style} artistic approach`;
                       
    console.log('生成的融合描述:', description);
    return description;
}

// 使用 Replicate API 生成图像
async function generateWithReplicate(description, referenceImage) {
    const response = await axios.post(
        'https://api.replicate.com/v1/predictions',
        {
            version: "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
            input: {
                prompt: description,
                negative_prompt: "photo, realistic, color, 3d render",
                width: 512,
                height: 768,
                num_outputs: 1,
                guidance_scale: 7.5,
                num_inference_steps: 30
            }
        },
        {
            headers: {
                'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
                'Content-Type': 'application/json'
            }
        }
    );
    
    // 等待生成完成
    let prediction = response.data;
    while (prediction.status !== 'succeeded' && prediction.status !== 'failed') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const statusResponse = await axios.get(
            `https://api.replicate.com/v1/predictions/${prediction.id}`,
            {
                headers: {
                    'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`
                }
            }
        );
        prediction = statusResponse.data;
    }
    
    if (prediction.status === 'failed') {
        throw new Error('Replicate generation failed: ' + prediction.error);
    }
    
    return prediction.output[0];
}

// 使用 DALL-E 生成图像
async function generateWithDallE(description) {
    const { OpenAI } = require('openai');
    
    // 为OpenAI配置代理
    let openaiConfig = { apiKey: process.env.OPENAI_API_KEY };
    
    // 由于已经设置了全局undici dispatcher，OpenAI应该自动使用代理
    const openai = new OpenAI(openaiConfig);
    
    console.log('发送DALL-E 3生成请求...');
    console.log('提示词长度:', description.length);
    
    const response = await openai.images.generate({
        model: "dall-e-3",
        prompt: description,
        n: 1,
        size: "1024x1024",
        quality: "standard",
        style: "natural"
    });
    
    console.log('DALL-E 3响应:', response.data[0].url ? '成功获取图片URL' : '未获取到URL');
    return response.data[0].url;
}

// 深度融合：保留用户原始绘画，添加动物特征装饰
async function blendUserDrawingWithAI(aiImageUrl, userImageData, animalType) {
    const { createCanvas, loadImage } = require('canvas');
    const axios = require('axios');
    
    try {
        // 加载用户原始绘画
        const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
        const userDrawingBuffer = Buffer.from(userDrawingBase64, 'base64');
        const userImage = await loadImage(userDrawingBuffer);
        
        // 创建合成画布，使用用户绘画的尺寸
        const canvas = createCanvas(userImage.width, userImage.height);
        const ctx = canvas.getContext('2d');
        
        // 白色背景
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 完整绘制用户原始画像（不做任何删除）
        ctx.drawImage(userImage, 0, 0);
        
        // 检测头部区域，用于添加装饰
        const headRegion = detectUserHeadRegion(userImage);
        
        // 添加动物特征装饰到原始绘画上
        addAnimalDecorations(ctx, headRegion, animalType);
        
        return canvas.toDataURL('image/png');
        
    } catch (error) {
        console.warn('动物特征融合失败:', error.message);
        // 如果融合失败，至少返回用户原图
        return userImageData;
    }
}

// 添加动物特征装饰（不破坏原图）
function addAnimalDecorations(ctx, headRegion, animalType) {
    ctx.strokeStyle = '#000';
    ctx.fillStyle = '#000';
    ctx.lineWidth = 3;
    
    if (animalType === 'OXHORSE' || animalType === 'bull' || animalType === 'bovine') {
        // 牛马特征：添加牛角和牛鼻环
        
        // 牛角（在头顶两侧）
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX - headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.35);
        ctx.quadraticCurveTo(
            headRegion.centerX - headRegion.width * 0.4, 
            headRegion.centerY - headRegion.height * 0.5,
            headRegion.centerX - headRegion.width * 0.35, 
            headRegion.centerY - headRegion.height * 0.6
        );
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX + headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.35);
        ctx.quadraticCurveTo(
            headRegion.centerX + headRegion.width * 0.4, 
            headRegion.centerY - headRegion.height * 0.5,
            headRegion.centerX + headRegion.width * 0.35, 
            headRegion.centerY - headRegion.height * 0.6
        );
        ctx.stroke();
        
        // 牛耳朵（在头部两侧）
        ctx.beginPath();
        ctx.ellipse(headRegion.centerX - headRegion.width * 0.5, headRegion.centerY - headRegion.height * 0.1, 
                   headRegion.width * 0.1, headRegion.height * 0.15, -0.3, 0, Math.PI * 2);
        ctx.stroke();
        
        ctx.beginPath();
        ctx.ellipse(headRegion.centerX + headRegion.width * 0.5, headRegion.centerY - headRegion.height * 0.1, 
                   headRegion.width * 0.1, headRegion.height * 0.15, 0.3, 0, Math.PI * 2);
        ctx.stroke();
        
        // 牛鼻环（如果检测到鼻子位置）
        ctx.beginPath();
        ctx.arc(headRegion.centerX, headRegion.centerY + headRegion.height * 0.15, 
               headRegion.width * 0.08, Math.PI * 0.2, Math.PI * 0.8);
        ctx.stroke();
        
    } else if (animalType === 'PET' || animalType === 'cat' || animalType === 'feline') {
        // 猫特征：猫耳朵和胡须
        
        // 三角猫耳
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX - headRegion.width * 0.35, headRegion.centerY - headRegion.height * 0.35);
        ctx.lineTo(headRegion.centerX - headRegion.width * 0.45, headRegion.centerY - headRegion.height * 0.55);
        ctx.lineTo(headRegion.centerX - headRegion.width * 0.25, headRegion.centerY - headRegion.height * 0.45);
        ctx.closePath();
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX + headRegion.width * 0.35, headRegion.centerY - headRegion.height * 0.35);
        ctx.lineTo(headRegion.centerX + headRegion.width * 0.45, headRegion.centerY - headRegion.height * 0.55);
        ctx.lineTo(headRegion.centerX + headRegion.width * 0.25, headRegion.centerY - headRegion.height * 0.45);
        ctx.closePath();
        ctx.stroke();
        
        // 猫胡须
        ctx.lineWidth = 2;
        for (let i = 0; i < 3; i++) {
            const y = headRegion.centerY + i * headRegion.height * 0.05;
            ctx.beginPath();
            ctx.moveTo(headRegion.centerX - headRegion.width * 0.25, y);
            ctx.lineTo(headRegion.centerX - headRegion.width * 0.55, y - headRegion.height * 0.02);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(headRegion.centerX + headRegion.width * 0.25, y);
            ctx.lineTo(headRegion.centerX + headRegion.width * 0.55, y - headRegion.height * 0.02);
            ctx.stroke();
        }
        
    } else if (animalType === 'DIVINE' || animalType === 'dragon') {
        // 龙特征：龙角和龙须
        
        // 龙角（更加弯曲威严）
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX - headRegion.width * 0.25, headRegion.centerY - headRegion.height * 0.35);
        ctx.bezierCurveTo(
            headRegion.centerX - headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.5,
            headRegion.centerX - headRegion.width * 0.35, headRegion.centerY - headRegion.height * 0.55,
            headRegion.centerX - headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.65
        );
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX + headRegion.width * 0.25, headRegion.centerY - headRegion.height * 0.35);
        ctx.bezierCurveTo(
            headRegion.centerX + headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.5,
            headRegion.centerX + headRegion.width * 0.35, headRegion.centerY - headRegion.height * 0.55,
            headRegion.centerX + headRegion.width * 0.3, headRegion.centerY - headRegion.height * 0.65
        );
        ctx.stroke();
        
        // 龙须（飘逸的曲线）
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX - headRegion.width * 0.2, headRegion.centerY + headRegion.height * 0.2);
        ctx.quadraticCurveTo(
            headRegion.centerX - headRegion.width * 0.4, headRegion.centerY + headRegion.height * 0.25,
            headRegion.centerX - headRegion.width * 0.45, headRegion.centerY + headRegion.height * 0.15
        );
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(headRegion.centerX + headRegion.width * 0.2, headRegion.centerY + headRegion.height * 0.2);
        ctx.quadraticCurveTo(
            headRegion.centerX + headRegion.width * 0.4, headRegion.centerY + headRegion.height * 0.25,
            headRegion.centerX + headRegion.width * 0.45, headRegion.centerY + headRegion.height * 0.15
        );
        ctx.stroke();
        
        // 添加一些鳞片纹理
        ctx.lineWidth = 1;
        for (let i = 0; i < 5; i++) {
            ctx.beginPath();
            ctx.arc(
                headRegion.centerX - headRegion.width * 0.3 + i * headRegion.width * 0.15,
                headRegion.centerY - headRegion.height * 0.25,
                3, 0, Math.PI
            );
            ctx.stroke();
        }
    }
}

// 检测用户绘画中的头部区域
function detectUserHeadRegion(userImage) {
    // 根据简笔画特点，头部通常是上方的圆形区域
    const headCenterX = userImage.width / 2;
    const headCenterY = userImage.height * 0.24; // 头部在上方约1/4处
    const headSize = Math.min(userImage.width * 0.32, userImage.height * 0.28); // 头部大小
    
    return {
        centerX: headCenterX,
        centerY: headCenterY, 
        width: headSize,
        height: headSize
    };
}

// 使用 Canvas 本地合成（真实像素融合方案）
async function generateWithCanvas(analysis, animalType, userImageData) {
    const { createCanvas, loadImage } = require('canvas');
    const canvas = createCanvas(512, 768);
    const ctx = canvas.getContext('2d');
    
    console.log('开始真实像素融合生成...');
    console.log('动物类型:', animalType);
    
    // 白色背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 512, 768);
    
    let userPixelFeatures = null;
    let userImage = null;
    
    try {
        // 加载和分析用户的真实画像
        if (userImageData) {
            const userDrawingBase64 = userImageData.split(',')[1] || userImageData;
            const imageBuffer = Buffer.from(userDrawingBase64, 'base64');
            userImage = await loadImage(imageBuffer);
            
            // 获取用户画像的像素特征
            const tempCanvas = createCanvas(userImage.width, userImage.height);
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(userImage, 0, 0);
            const imageData = tempCtx.getImageData(0, 0, userImage.width, userImage.height);
            
            userPixelFeatures = analyzePixelFeatures(imageData.data, userImage.width, userImage.height);
            
            console.log('用户画像像素特征:', {
                尺寸: `${userImage.width}x${userImage.height}`,
                暗像素比例: userPixelFeatures.darkRatio.toFixed(2),
                线条复杂度: userPixelFeatures.lineComplexity.toFixed(2),
                宽高比: userPixelFeatures.aspectRatio.toFixed(2)
            });
        }
    } catch (error) {
        console.warn('用户画像加载失败，使用默认特征:', error.message);
    }
    
    // 从分析中提取特征
    const features = extractUserFeatures(analysis);
    console.log('提取的用户特征:', features);
    
    // 绘制素描风格基础
    ctx.strokeStyle = '#333';
    ctx.fillStyle = '#333';
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 1. 绘制人类身体（职业装）
    drawProfessionalBody(ctx);
    
    // 2. 根据动物类型和真实用户像素特征绘制融合头部  
    drawRealHybridHead(ctx, animalType, features, userPixelFeatures, userImage);
    
    // 3. 添加基于用户真实特征的细节
    addRealUserSpecificDetails(ctx, features, animalType, userPixelFeatures);
    
    // 4. 应用基于用户画像的纹理
    applyUserBasedTexture(ctx, userPixelFeatures);
    
    console.log('真实像素融合生成完成');
    return canvas.toDataURL();
}

// 从 AI 分析中提取用户特征
function extractUserFeatures(analysis) {
    const features = {
        faceShape: 'oval',     // 默认椭圆脸
        expression: 'neutral', // 默认中性表情
        style: 'sketch',      // 默认素描风格
        personality: 'worker' // 默认打工人
    };
    
    // 面部形状识别
    if (analysis.includes('圆脸')) features.faceShape = 'round';
    else if (analysis.includes('方脸')) features.faceShape = 'square';
    else if (analysis.includes('长脸')) features.faceShape = 'long';
    else if (analysis.includes('瓜子脸')) features.faceShape = 'heart';
    
    // 表情识别
    if (analysis.includes('疲惫') || analysis.includes('累')) features.expression = 'tired';
    else if (analysis.includes('开心') || analysis.includes('微笑')) features.expression = 'happy';
    else if (analysis.includes('专注') || analysis.includes('认真')) features.expression = 'focused';
    else if (analysis.includes('活力') || analysis.includes('精神')) features.expression = 'energetic';
    
    // 风格识别
    if (analysis.includes('卡通')) features.style = 'cartoon';
    else if (analysis.includes('写实')) features.style = 'realistic';
    else if (analysis.includes('抽象')) features.style = 'abstract';
    
    return features;
}

// 绘制专业身体
function drawProfessionalBody(ctx) {
    ctx.lineWidth = 3;
    
    // 西装外套
    ctx.beginPath();
    ctx.moveTo(180, 320); // 左肩
    ctx.lineTo(160, 450); // 左腰
    ctx.lineTo(170, 550); // 左下摆
    ctx.lineTo(342, 550); // 右下摆
    ctx.lineTo(352, 450); // 右腰
    ctx.lineTo(332, 320); // 右肩
    ctx.lineTo(300, 300); // 右领口
    ctx.lineTo(256, 280); // 脖子中心
    ctx.lineTo(212, 300); // 左领口
    ctx.closePath();
    ctx.stroke();
    
    // 衬衫领口
    ctx.beginPath();
    ctx.moveTo(230, 310);
    ctx.lineTo(256, 290);
    ctx.lineTo(282, 310);
    ctx.stroke();
    
    // 领带
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(256, 300);
    ctx.lineTo(250, 420);
    ctx.lineTo(256, 430);
    ctx.lineTo(262, 420);
    ctx.closePath();
    ctx.fillStyle = '#444';
    ctx.fill();
    ctx.stroke();
    
    // 手臂
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(180, 350);
    ctx.lineTo(130, 480);
    ctx.moveTo(332, 350);
    ctx.lineTo(382, 480);
    ctx.stroke();
    
    // 腿部
    ctx.beginPath();
    ctx.moveTo(200, 550);
    ctx.lineTo(195, 720);
    ctx.moveTo(312, 550);
    ctx.lineTo(317, 720);
    ctx.stroke();
    
    // 鞋子
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.ellipse(195, 740, 25, 8, 0, 0, Math.PI * 2);
    ctx.ellipse(317, 740, 25, 8, 0, 0, Math.PI * 2);
    ctx.fillStyle = '#222';
    ctx.fill();
}

// 绘制融合头部（人脸特征+动物头）
function drawHybridHead(ctx, animalType, features) {
    const centerX = 256;
    const centerY = 200;
    
    // 根据动物类型确定头部基本形状和特征
    const animalConfig = {
        'DIVINE': {
            headScale: 1.2,
            earType: 'horns',
            snoutLength: 'medium',
            color: '#FFD700',
            features: ['elegant', 'mystical']
        },
        'PET': {
            headScale: 1.0,
            earType: 'pointed',
            snoutLength: 'short',
            color: '#FF69B4',
            features: ['cute', 'friendly']
        },
        'OXHORSE': {
            headScale: 1.1,
            earType: 'large',
            snoutLength: 'long',
            color: '#8B4513',
            features: ['strong', 'determined']
        }
    };
    
    const config = animalConfig[animalType] || animalConfig['OXHORSE'];
    
    // 绘制头部轮廓（融合人脸形状和动物特征）
    ctx.lineWidth = 3;
    ctx.strokeStyle = '#333';
    
    const headWidth = 80 * config.headScale;
    const headHeight = 90 * config.headScale;
    
    // 根据用户面部特征调整头部形状
    if (features.faceShape === 'round') {
        ctx.beginPath();
        ctx.arc(centerX, centerY, headWidth * 0.8, 0, Math.PI * 2);
        ctx.stroke();
    } else if (features.faceShape === 'square') {
        ctx.strokeRect(centerX - headWidth/2, centerY - headHeight/2, headWidth, headHeight);
    } else if (features.faceShape === 'long') {
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headWidth * 0.7, headHeight * 1.1, 0, 0, Math.PI * 2);
        ctx.stroke();
    } else {
        // 默认椭圆形
        ctx.beginPath();
        ctx.ellipse(centerX, centerY, headWidth * 0.8, headHeight * 0.9, 0, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    // 绘制动物耳朵或角
    drawAnimalEars(ctx, centerX, centerY - headHeight/2, config);
    
    // 绘制眼睛（融合人类和动物特征）
    drawHybridEyes(ctx, centerX, centerY - 15, features, config);
    
    // 绘制鼻子/鼻吻部
    drawAnimalSnout(ctx, centerX, centerY + 10, config);
    
    // 绘制嘴巴（体现用户表情）
    drawExpressionMouth(ctx, centerX, centerY + 35, features);
}

function drawAnimalEars(ctx, centerX, topY, config) {
    ctx.lineWidth = 4; // 增加线条粗细，更明显
    
    if (config.earType === 'horns') {
        // 龙角 - 更加突出
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(centerX - 50, topY + 10);
        ctx.lineTo(centerX - 60, topY - 50);
        ctx.moveTo(centerX + 50, topY + 10);
        ctx.lineTo(centerX + 60, topY - 50);
        ctx.stroke();
        
        // 龙角装饰 - 金色更明显
        ctx.fillStyle = config.color;
        ctx.strokeStyle = config.color;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(centerX - 60, topY - 50, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.arc(centerX + 60, topY - 50, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // 添加龙鳞纹理
        ctx.strokeStyle = config.color;
        ctx.lineWidth = 2;
        for (let i = 0; i < 3; i++) {
            ctx.beginPath();
            ctx.arc(centerX - 55 + i * 5, topY - 40 + i * 8, 3, 0, Math.PI * 2);
            ctx.stroke();
            ctx.beginPath();
            ctx.arc(centerX + 55 - i * 5, topY - 40 + i * 8, 3, 0, Math.PI * 2);
            ctx.stroke();
        }
        
    } else if (config.earType === 'pointed') {
        // 猫耳 - 更加尖锐可爱
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 4;
        
        // 左耳
        ctx.beginPath();
        ctx.moveTo(centerX - 40, topY + 15);
        ctx.lineTo(centerX - 20, topY - 30);
        ctx.lineTo(centerX - 10, topY + 10);
        ctx.closePath();
        ctx.stroke();
        ctx.fillStyle = 'rgba(255, 105, 180, 0.3)'; // 淡粉色填充
        ctx.fill();
        
        // 右耳
        ctx.beginPath();
        ctx.moveTo(centerX + 40, topY + 15);
        ctx.lineTo(centerX + 20, topY - 30);
        ctx.lineTo(centerX + 10, topY + 10);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        
        // 耳朵内侧
        ctx.strokeStyle = config.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX - 30, topY + 5);
        ctx.lineTo(centerX - 22, topY - 15);
        ctx.moveTo(centerX + 30, topY + 5);
        ctx.lineTo(centerX + 22, topY - 15);
        ctx.stroke();
        
    } else if (config.earType === 'large') {
        // 牛耳 - 更加厚实明显
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.ellipse(centerX - 55, topY + 25, 25, 35, -0.4, 0, Math.PI * 2);
        ctx.ellipse(centerX + 55, topY + 25, 25, 35, 0.4, 0, Math.PI * 2);
        ctx.stroke();
        
        // 牛耳填充
        ctx.fillStyle = 'rgba(139, 69, 19, 0.2)'; // 淡棕色
        ctx.fill();
        
        // 牛耳内侧纹理
        ctx.strokeStyle = config.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX - 55, topY + 25, 15, 20, -0.4, 0, Math.PI * 2);
        ctx.ellipse(centerX + 55, topY + 25, 15, 20, 0.4, 0, Math.PI * 2);
        ctx.stroke();
    }
}

function drawHybridEyes(ctx, centerX, eyeY, features, config, userPixelFeatures) {
    ctx.lineWidth = 2;
    
    const eyeSize = features.expression === 'tired' ? 6 : 8;
    const eyeSpacing = 25;
    
    // 眼睛形状根据动物类型调整
    if (config.features.includes('mystical')) {
        // 神兽眼睛 - 更有神韵
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(centerX - eyeSpacing, eyeY, eyeSize + 2, eyeSize, 0, 0, Math.PI * 2);
        ctx.ellipse(centerX + eyeSpacing, eyeY, eyeSize + 2, eyeSize, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 眼珠
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(centerX - eyeSpacing, eyeY, 4, 0, Math.PI * 2);
        ctx.arc(centerX + eyeSpacing, eyeY, 4, 0, Math.PI * 2);
        ctx.fill();
        
    } else {
        // 普通眼睛
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(centerX - eyeSpacing, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.arc(centerX + eyeSpacing, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.stroke();
        
        // 眼珠
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(centerX - eyeSpacing, eyeY, 3, 0, Math.PI * 2);
        ctx.arc(centerX + eyeSpacing, eyeY, 3, 0, Math.PI * 2);
        ctx.fill();
    }
}

function drawAnimalSnout(ctx, centerX, noseY, config) {
    ctx.lineWidth = 4; // 增加线条粗细
    ctx.strokeStyle = '#333';
    
    if (config.snoutLength === 'long') {
        // 牛/马的长鼻吻 - 更加突出
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.ellipse(centerX, noseY + 20, 35, 20, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 鼻吻部填充
        ctx.fillStyle = 'rgba(139, 69, 19, 0.1)';
        ctx.fill();
        
        // 牛鼻孔 - 更大更明显
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.ellipse(centerX - 12, noseY + 18, 5, 8, 0, 0, Math.PI * 2);
        ctx.ellipse(centerX + 12, noseY + 18, 5, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 牛鼻梁
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(centerX, noseY + 5);
        ctx.lineTo(centerX, noseY + 25);
        ctx.stroke();
        
        // 牛嘴轮廓
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.ellipse(centerX, noseY + 35, 40, 15, 0, 0, Math.PI);
        ctx.stroke();
        
    } else if (config.snoutLength === 'short') {
        // 猫的小鼻子 - 更可爱
        ctx.fillStyle = config.color;
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        
        // 猫鼻子 - 三角形
        ctx.beginPath();
        ctx.moveTo(centerX - 8, noseY + 5);
        ctx.lineTo(centerX, noseY - 8);
        ctx.lineTo(centerX + 8, noseY + 5);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // 猫嘴 - 人字形
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(centerX, noseY + 8);
        ctx.lineTo(centerX - 12, noseY + 20);
        ctx.moveTo(centerX, noseY + 8);
        ctx.lineTo(centerX + 12, noseY + 20);
        ctx.stroke();
        
        // 胡须
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 2;
        for (let i = 0; i < 3; i++) {
            // 左边胡须
            ctx.beginPath();
            ctx.moveTo(centerX - 25, noseY + 10 + i * 8);
            ctx.lineTo(centerX - 45, noseY + 8 + i * 8);
            ctx.stroke();
            
            // 右边胡须
            ctx.beginPath();
            ctx.moveTo(centerX + 25, noseY + 10 + i * 8);
            ctx.lineTo(centerX + 45, noseY + 8 + i * 8);
            ctx.stroke();
        }
        
    } else {
        // 神兽的中等鼻子 - 更神秘
        ctx.lineWidth = 4;
        ctx.strokeStyle = config.color;
        ctx.beginPath();
        ctx.ellipse(centerX, noseY, 18, 12, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 神兽鼻孔
        ctx.fillStyle = config.color;
        ctx.beginPath();
        ctx.arc(centerX - 6, noseY - 3, 3, 0, Math.PI * 2);
        ctx.arc(centerX + 6, noseY - 3, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 神兽嘴部 - 威严
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(centerX - 15, noseY + 15);
        ctx.lineTo(centerX, noseY + 25);
        ctx.lineTo(centerX + 15, noseY + 15);
        ctx.stroke();
    }
}

function drawExpressionMouth(ctx, centerX, mouthY, features, userPixelFeatures) {
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#333';
    
    ctx.beginPath();
    if (features.expression === 'happy') {
        // 微笑
        ctx.arc(centerX, mouthY - 5, 15, 0.2 * Math.PI, 0.8 * Math.PI);
    } else if (features.expression === 'tired') {
        // 疲惫表情
        ctx.arc(centerX, mouthY + 5, 12, 1.2 * Math.PI, 1.8 * Math.PI);
    } else if (features.expression === 'focused') {
        // 专注的直线嘴
        ctx.moveTo(centerX - 10, mouthY);
        ctx.lineTo(centerX + 10, mouthY);
    } else {
        // 默认中性表情
        ctx.arc(centerX, mouthY, 8, 0, Math.PI, false);
    }
    ctx.stroke();
}

function addUserSpecificDetails(ctx, features, animalType) {
    // 根据用户特征添加个性化细节
    
    if (features.style === 'cartoon') {
        // 卡通风格 - 添加一些可爱元素
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 1;
        
        // 脸颊红晕
        ctx.beginPath();
        ctx.arc(200, 205, 8, 0, Math.PI * 2);
        ctx.arc(312, 205, 8, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    if (features.expression === 'energetic') {
        // 充满活力 - 添加精神线条
        ctx.strokeStyle = '#999';
        ctx.lineWidth = 1;
        
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const startX = 256 + Math.cos(angle) * 90;
            const startY = 200 + Math.sin(angle) * 90;
            const endX = 256 + Math.cos(angle) * 110;
            const endY = 200 + Math.sin(angle) * 110;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
        }
    }
    
    // 添加动物类型特定的装饰
    if (animalType === 'DIVINE') {
        // 神兽光环
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(256, 120, 40, 0, Math.PI * 2);
        ctx.stroke();
    }
}

// 根据真实用户像素特征绘制融合头部
function drawRealHybridHead(ctx, animalType, features, userPixelFeatures, userImage) {
    const centerX = 256;
    const centerY = 200;
    
    // 如果有用户画像，先绘制用户画像作为融合基础
    if (userImage && userPixelFeatures) {
        console.log('基于真实用户画像绘制融合头部...');
        
        // 将用户画像缩放到头部区域
        const headSize = 140; // 增大尺寸，更明显
        const userImageX = centerX - headSize/2;
        const userImageY = centerY - headSize/2;
        
        // 创建遮罩，让用户画像融入动物头部形状
        ctx.save();
        
        // 先创建动物头部形状的剪切路径
        ctx.beginPath();
        if (features.faceShape === 'round') {
            ctx.arc(centerX, centerY, headSize * 0.45, 0, Math.PI * 2);
        } else if (features.faceShape === 'long') {
            ctx.ellipse(centerX, centerY, headSize * 0.35, headSize * 0.5, 0, 0, Math.PI * 2);
        } else {
            ctx.ellipse(centerX, centerY, headSize * 0.4, headSize * 0.45, 0, 0, Math.PI * 2);
        }
        ctx.clip(); // 剪切为动物头形状
        
        // 在剪切区域内绘制用户画像
        ctx.globalAlpha = 0.6; // 增加透明度，更明显
        ctx.drawImage(userImage, userImageX, userImageY, headSize, headSize);
        
        // 基于动物类型添加混合颜色
        ctx.globalCompositeOperation = 'multiply';
        let mixColor = '139,69,19'; // 默认棕色
        if (animalType === 'DIVINE') mixColor = '255,215,0'; // 金色
        else if (animalType === 'PET') mixColor = '255,105,180'; // 粉色
        
        ctx.fillStyle = `rgba(${mixColor}, 0.3)`;
        ctx.fillRect(userImageX, userImageY, headSize, headSize);
        
        ctx.restore(); // 恢复绘制状态
        
        console.log('用户画像已融入动物头部形状');
    }
    
    // 根据动物类型确定头部配置
    const animalConfig = {
        'DIVINE': {
            headScale: 1.3,
            earType: 'horns',
            snoutLength: 'medium',
            color: '#FFD700',
            features: ['elegant', 'mystical']
        },
        'PET': {
            headScale: 1.0,
            earType: 'pointed',
            snoutLength: 'short',
            color: '#FF69B4', 
            features: ['cute', 'friendly']
        },
        'OXHORSE': {
            headScale: 1.2,
            earType: 'large',
            snoutLength: 'long',
            color: '#8B4513',
            features: ['strong', 'determined']
        }
    };
    
    const config = animalConfig[animalType] || animalConfig['OXHORSE'];
    
    // 基于用户像素特征调整头部形状
    let headWidth = 90 * config.headScale;
    let headHeight = 100 * config.headScale;
    
    if (userPixelFeatures) {
        // 根据用户画像的宽高比调整头部比例
        if (userPixelFeatures.aspectRatio > 1.2) {
            headHeight *= 1.2; // 长脸
            features.faceShape = 'long';
        } else if (userPixelFeatures.aspectRatio < 0.8) {
            headWidth *= 1.2; // 宽脸
            features.faceShape = 'wide';
        }
        
        // 根据线条复杂度调整细节层次
        const detailLevel = userPixelFeatures.lineComplexity;
        if (detailLevel > 0.5) {
            // 复杂画像：添加更多细节
            ctx.lineWidth = 2;
        } else {
            // 简单画像：保持简洁
            ctx.lineWidth = 3;
        }
    }
    
    // 绘制融合头部轮廓
    ctx.strokeStyle = '#333';
    drawAnimalHeadShape(ctx, centerX, centerY, headWidth, headHeight, config, features);
    
    // 绘制动物特征
    drawAnimalEars(ctx, centerX, centerY - headHeight/2, config);
    drawHybridEyes(ctx, centerX, centerY - 15, features, config, userPixelFeatures);
    drawAnimalSnout(ctx, centerX, centerY + 10, config);
    drawExpressionMouth(ctx, centerX, centerY + 35, features, userPixelFeatures);
    
    // 如果是基于真实用户画像，添加个性化细节
    if (userPixelFeatures) {
        addUserPixelBasedDetails(ctx, centerX, centerY, userPixelFeatures, config);
    }
}

function drawAnimalHeadShape(ctx, centerX, centerY, width, height, config, features) {
    ctx.beginPath();
    
    if (features.faceShape === 'round') {
        ctx.arc(centerX, centerY, width * 0.8, 0, Math.PI * 2);
    } else if (features.faceShape === 'square') {
        ctx.rect(centerX - width/2, centerY - height/2, width, height);
    } else if (features.faceShape === 'long') {
        ctx.ellipse(centerX, centerY, width * 0.7, height * 1.1, 0, 0, Math.PI * 2);
    } else if (features.faceShape === 'wide') {
        ctx.ellipse(centerX, centerY, width * 1.1, height * 0.8, 0, 0, Math.PI * 2);
    } else {
        // 默认椭圆形，但融合动物特征
        ctx.ellipse(centerX, centerY, width * 0.85, height * 0.9, 0, 0, Math.PI * 2);
    }
    
    ctx.stroke();
    
    // 根据动物类型添加额外形状特征
    if (config.features.includes('mystical')) {
        // 神兽：添加神秘符号
        ctx.strokeStyle = config.color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(centerX, centerY - height/3, 8, 0, Math.PI * 2);
        ctx.stroke();
        ctx.strokeStyle = '#333';
    }
}

function addUserPixelBasedDetails(ctx, centerX, centerY, userPixelFeatures, config) {
    // 基于用户画像的暗像素比例添加阴影
    if (userPixelFeatures.darkRatio > 0.3) {
        ctx.fillStyle = 'rgba(51, 51, 51, 0.2)';
        ctx.beginPath();
        ctx.ellipse(centerX + 20, centerY + 20, 30, 15, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    
    // 基于线条复杂度添加纹理
    const textureLines = Math.floor(userPixelFeatures.lineComplexity * 20);
    ctx.strokeStyle = 'rgba(51, 51, 51, 0.4)';
    ctx.lineWidth = 0.5;
    
    for (let i = 0; i < textureLines; i++) {
        const angle = (i / textureLines) * Math.PI * 2;
        const radius = 40 + Math.random() * 20;
        const x1 = centerX + Math.cos(angle) * radius;
        const y1 = centerY + Math.sin(angle) * radius;
        const x2 = x1 + (Math.random() - 0.5) * 10;
        const y2 = y1 + (Math.random() - 0.5) * 10;
        
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
    }
}

function addRealUserSpecificDetails(ctx, features, animalType, userPixelFeatures) {
    // 基于用户真实像素特征添加个性化细节
    
    if (userPixelFeatures) {
        // 根据用户画像的颜色丰富度添加装饰
        const colorRichness = userPixelFeatures.dominantColors?.length || 1;
        
        if (colorRichness > 3) {
            // 颜色丰富的画像：添加更多装饰
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 1;
            
            // 添加面部细节线条
            for (let i = 0; i < 5; i++) {
                const x = 200 + Math.random() * 112;
                const y = 160 + Math.random() * 80;
                ctx.beginPath();
                ctx.arc(x, y, 2, 0, Math.PI * 2);
                ctx.stroke();
            }
        }
        
        // 根据线条密度调整表达力
        if (userPixelFeatures.lineComplexity > 0.4) {
            // 线条密集：添加表情线
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 1;
            
            // 眉毛
            ctx.beginPath();
            ctx.moveTo(230, 175);
            ctx.lineTo(245, 170);
            ctx.moveTo(267, 170);
            ctx.lineTo(282, 175);
            ctx.stroke();
        }
    }
    
    // 添加动物类型特定的装饰
    if (animalType === 'DIVINE') {
        // 神兽光环 - 增强版
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(256, 100, 45, 0, Math.PI * 2);
        ctx.stroke();
        
        // 神秘符文
        ctx.lineWidth = 1;
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const x = 256 + Math.cos(angle) * 55;
            const y = 100 + Math.sin(angle) * 55;
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
    } else if (animalType === 'PET') {
        // 可爱装饰
        ctx.strokeStyle = '#FF69B4';
        ctx.lineWidth = 2;
        
        // 爱心
        ctx.beginPath();
        ctx.arc(200, 250, 8, 0, Math.PI * 2);
        ctx.arc(312, 250, 8, 0, Math.PI * 2);
        ctx.stroke();
    }
}

function applyUserBasedTexture(ctx, userPixelFeatures) {
    // 基于用户画像特征应用纹理
    ctx.strokeStyle = 'rgba(51, 51, 51, 0.3)';
    
    if (userPixelFeatures) {
        // 根据用户画像的复杂度调整纹理密度
        const textureCount = Math.floor(userPixelFeatures.lineComplexity * 100) + 30;
        const lineWidth = userPixelFeatures.darkRatio > 0.4 ? 0.8 : 0.4;
        
        ctx.lineWidth = lineWidth;
        
        // 添加基于用户画风的纹理线条
        for (let i = 0; i < textureCount; i++) {
            const x1 = Math.random() * 512;
            const y1 = Math.random() * 768;
            
            // 线条长度基于用户的线条复杂度
            const lineLength = userPixelFeatures.lineComplexity * 30 + 5;
            const x2 = x1 + (Math.random() - 0.5) * lineLength;
            const y2 = y1 + (Math.random() - 0.5) * lineLength;
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    } else {
        // 默认素描纹理
        ctx.lineWidth = 0.5;
        for (let i = 0; i < 50; i++) {
            const x1 = Math.random() * 512;
            const y1 = Math.random() * 768;
            const x2 = x1 + (Math.random() - 0.5) * 20;
            const y2 = y1 + (Math.random() - 0.5) * 20;
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }
}

// 转换为素描风格
async function convertToSketch(imageUrl) {
    try {
        let imageBuffer;
        
        if (imageUrl.startsWith('data:')) {
            // Base64 图像
            const base64Data = imageUrl.split(',')[1];
            imageBuffer = Buffer.from(base64Data, 'base64');
        } else {
            // URL 图像
            const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
            imageBuffer = Buffer.from(response.data);
        }
        
        // 使用 sharp 应用素描效果
        const processedBuffer = await sharp(imageBuffer)
            .resize(512, 768, { fit: 'contain', background: { r: 255, g: 255, b: 255 } })
            .greyscale()
            .normalize()
            .linear(1.2, -(128 * 1.2) + 128) // 增加对比度
            .blur(0.3)
            .sharpen({ sigma: 1.5 })
            .toBuffer();
        
        return processedBuffer.toString('base64');
    } catch (error) {
        console.error('素描转换失败:', error);
        // 如果是 base64，直接返回
        if (imageUrl.startsWith('data:')) {
            return imageUrl.split(',')[1];
        }
        throw error;
    }
}

function getGenerationMethod() {
    if (process.env.REPLICATE_API_TOKEN) return 'Replicate Stable Diffusion';
    if (process.env.OPENAI_API_KEY) return 'OpenAI DALL-E 3';
    return 'Canvas Local Generation';
}

const PORT = process.env.PORT || 3005;
app.listen(PORT, () => {
    console.log(`✅ 真实 Gemini + AI 图像生成服务运行在 http://localhost:${PORT}`);
    console.log(`\n🔑 API 密钥状态:`);
    console.log(`   GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   REPLICATE_API_TOKEN: ${process.env.REPLICATE_API_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`\n📝 设置方法:`);
    console.log(`   export GEMINI_API_KEY=your_gemini_key`);
    console.log(`   export REPLICATE_API_TOKEN=your_replicate_token`);
    console.log(`   export OPENAI_API_KEY=your_openai_key`);
});

module.exports = app;