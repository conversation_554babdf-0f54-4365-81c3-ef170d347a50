# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# NestJS build (we're using simple server)
dist
build

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Development
.vscode
.idea

# OS
.DS_Store
Thumbs.db

# Test
coverage
.nyc_output
test

# Cache
.cache

# Logs
*.log
logs
backend.log
server.log
frontend.log

# Git
.git
.gitignore

# Docker
Dockerfile*
.dockerignore
docker-compose*

# Documentation
README.md
*.md

# Temporary files
.tmp
.temp

# PID files
*.pid

# Uploads (will be mounted as volume)
uploads/generated-avatars/*.png