#!/usr/bin/env node

/**
 * 测试AI服务功能的简单脚本
 * 用于验证Gemini 2.0 Flash、DALL-E 3和动物园AI系统
 */

const BASE_URL = 'http://localhost:3000';

// 模拟用户画像数据
const mockDrawingData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';

async function testAPI(endpoint, options = {}) {
  try {
    console.log(`🔬 测试 ${endpoint}...`);
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer your-test-token`, // 需要替换为实际的token
        ...options.headers
      },
      ...options
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ ${endpoint} 测试成功`);
      return data;
    } else {
      console.log(`❌ ${endpoint} 测试失败: ${response.status} ${response.statusText}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ ${endpoint} 测试出错: ${error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('🚀 开始测试牛马动物园AI系统...\n');

  // 1. 测试绘画分析API
  console.log('📝 测试绘画分析功能...');
  const analysisResult = await testAPI('/api/v1/drawing/analyze', {
    method: 'POST',
    body: JSON.stringify({
      imageData: mockDrawingData,
      metadata: { testMode: true }
    })
  });

  if (analysisResult) {
    console.log('   - 分析结果示例:', {
      description: analysisResult.data?.description?.substring(0, 50) + '...',
      confidence: analysisResult.data?.confidence,
      animalType: analysisResult.data?.detectedAnimalType
    });
  }

  // 2. 测试头像生成API
  console.log('\n🎨 测试头像生成功能...');
  const avatarResult = await testAPI('/api/v1/drawing/generate-avatar', {
    method: 'POST',
    body: JSON.stringify({
      imageData: mockDrawingData,
      animalType: 'WORKING_ANIMAL',
      animalSpecies: 'OX',
      fusionOptions: {
        fusionStrength: 0.7,
        keepUserFeatures: true,
        workEnvironment: 'office',
        clothingStyle: 'formal'
      }
    })
  });

  if (avatarResult) {
    console.log('   - 生成结果示例:', {
      generationMethod: avatarResult.data?.generationMethod,
      processingTime: avatarResult.data?.processingTime,
      hasImage: !!avatarResult.data?.imageBase64
    });
  }

  // 3. 测试动物园场景API
  console.log('\n🎪 测试动物园场景功能...');
  const sceneResult = await testAPI('/api/v1/zoo/scene');
  
  if (sceneResult) {
    console.log('   - 场景信息示例:', {
      sceneName: sceneResult.data?.sceneName,
      animalCount: sceneResult.data?.animals?.length || 0,
      facilities: sceneResult.data?.facilities?.length || 0
    });
  }

  // 4. 测试环境状态API
  console.log('\n🌤️ 测试环境状态功能...');
  const envResult = await testAPI('/api/v1/zoo/environment');
  
  if (envResult) {
    console.log('   - 环境状态示例:', {
      timeOfDay: envResult.timeOfDay,
      weather: envResult.weather?.condition,
      crowdLevel: envResult.crowdLevel,
      activeEvents: envResult.events?.length || 0
    });
  }

  // 5. 测试统计信息API
  console.log('\n📊 测试统计信息功能...');
  const statsResult = await testAPI('/api/v1/zoo/statistics');
  
  if (statsResult) {
    console.log('   - 统计信息示例:', {
      totalAnimals: statsResult.totalAnimals,
      activeAnimals: statsResult.activeAnimals,
      averageHappiness: statsResult.averageHappiness,
      behaviorQueueSize: statsResult.behaviorQueueSize || 0
    });
  }

  // 6. 测试事件触发API
  console.log('\n🎉 测试事件触发功能...');
  const eventResult = await testAPI('/api/v1/zoo/events/trigger', {
    method: 'POST',
    body: JSON.stringify({
      type: '测试事件',
      duration: 30000
    })
  });

  if (eventResult) {
    console.log('   - 事件触发结果:', eventResult.success ? '成功' : '失败');
  }

  // 7. 测试AI调度器控制API
  console.log('\n🤖 测试AI调度器控制功能...');
  const schedulerResult = await testAPI('/api/v1/zoo/ai/scheduler', {
    method: 'PUT',
    body: JSON.stringify({
      enabled: true
    })
  });

  if (schedulerResult) {
    console.log('   - 调度器控制结果:', schedulerResult.message);
  }

  // 8. 测试行为模式调试API
  console.log('\n🔍 测试行为模式调试功能...');
  const debugResult = await testAPI('/api/v1/zoo/debug/behavior-patterns');
  
  if (debugResult) {
    console.log('   - 行为模式信息:', debugResult.message);
  }

  console.log('\n🏁 测试完成！');
  console.log('\n📝 测试总结:');
  console.log('   - 如果看到绿色的✅，表示对应的API功能正常');
  console.log('   - 如果看到红色的❌，可能需要检查服务配置或API key');
  console.log('   - 某些功能可能需要有效的认证token才能正常工作');
  console.log('\n💡 提示:');
  console.log('   - 确保后端服务正在运行 (npm run start:dev)');
  console.log('   - 检查环境变量中的API keys配置');
  console.log('   - 查看服务器日志获取更多调试信息');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testAPI, runTests };