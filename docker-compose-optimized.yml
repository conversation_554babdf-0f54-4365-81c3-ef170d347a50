# 🐂🐴 牛马动物园 - 优化版Docker部署
version: '3.8'

services:
  # 后端服务
  backend:
    image: node:18-alpine
    container_name: niuma-backend
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - AI_GENERATION_ENABLED=true
      - USE_REAL_AI=true
      - PREFERRED_AI_MODEL=gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - PORT=3000
    volumes:
      - ./backend:/app:cached
      - /app/node_modules
    command: >
      sh -c "
        echo '🚀 安装后端依赖...' &&
        npm install --only=production &&
        echo '✅ 后端依赖安装完成' &&
        echo '🌟 启动后端服务...' &&
        node simple-server.js
      "
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - niuma-network
    
  # 前端服务
  frontend:
    image: node:18-alpine
    container_name: niuma-frontend
    working_dir: /app
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000
    volumes:
      - ./frontend:/app:cached
      - /app/node_modules
    command: >
      sh -c "
        echo '🚀 安装前端依赖...' &&
        npm install &&
        echo '✅ 前端依赖安装完成' &&
        echo '🌟 启动前端开发服务器...' &&
        npm run dev -- --host 0.0.0.0 --port 3001
      "
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - niuma-network

networks:
  niuma-network:
    driver: bridge

volumes:
  backend_node_modules:
  frontend_node_modules: