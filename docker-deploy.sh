#!/bin/bash

# 🐂🐴 牛马动物园 Docker 部署脚本 (优化版)

set -e  # 遇到错误立即退出

echo "🚀 开始部署牛马动物园到Docker..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装，请先安装Docker Desktop${NC}"
        exit 1
    fi
    
    if ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose不可用，请确保Docker Desktop正在运行${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker环境检查通过${NC}"
}

# 创建.env文件
create_env() {
    if [ ! -f .env ]; then
        echo -e "${YELLOW}📝 创建环境配置文件...${NC}"
        cat > .env << EOF
# AI服务配置
GEMINI_API_KEY=${GEMINI_API_KEY:-your_gemini_api_key_here}
AI_GENERATION_ENABLED=true
USE_REAL_AI=true
PREFERRED_AI_MODEL=gemini

# 应用配置
NODE_ENV=production
FRONTEND_PORT=3001
BACKEND_PORT=3000
NGINX_PORT=80
EOF
        echo -e "${YELLOW}⚠️  请编辑 .env 文件，设置正确的 GEMINI_API_KEY${NC}"
        read -p "是否现在编辑 .env 文件？(y/N): " edit_env
        if [[ $edit_env == "y" || $edit_env == "Y" ]]; then
            ${EDITOR:-nano} .env
        fi
    else
        echo -e "${GREEN}✅ .env 文件已存在${NC}"
    fi
}

# 检查端口占用
check_ports() {
    echo -e "${YELLOW}🔍 检查端口占用情况...${NC}"
    
    for port in 3000 3001; do
        if lsof -i :$port &> /dev/null; then
            echo -e "${YELLOW}⚠️ 端口 $port 被占用，尝试释放...${NC}"
            local pid=$(lsof -t -i :$port 2>/dev/null || true)
            if [ ! -z "$pid" ]; then
                kill -9 $pid 2>/dev/null || true
                sleep 2
            fi
        fi
    done
}

# 停止现有容器
stop_containers() {
    echo -e "${YELLOW}🛑 停止现有容器...${NC}"
    docker compose -f docker-compose-optimized.yml down --remove-orphans 2>/dev/null || true
}

# 启动服务
start_services() {
    echo -e "${GREEN}🚀 启动优化版Docker服务...${NC}"
    docker compose -f docker-compose-optimized.yml up -d
    
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    sleep 15
    
    # 健康检查
    echo -e "${BLUE}🔍 检查服务状态...${NC}"
    docker compose -f docker-compose-optimized.yml ps
}

# 验证部署
verify_deployment() {
    echo -e "${BLUE}🔍 验证部署状态...${NC}"
    
    # 等待后端启动
    echo -e "${YELLOW}⏳ 检查后端服务...${NC}"
    for i in {1..20}; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            echo -e "${GREEN}✅ 后端服务正常运行${NC}"
            break
        elif [ $i -eq 20 ]; then
            echo -e "${RED}❌ 后端服务启动失败${NC}"
            docker compose -f docker-compose-optimized.yml logs backend
            return 1
        else
            echo -e "${YELLOW}⏳ 等待后端服务... ($i/20)${NC}"
            sleep 3
        fi
    done
    
    # 等待前端启动
    echo -e "${YELLOW}⏳ 检查前端服务...${NC}"
    for i in {1..20}; do
        if curl -f http://localhost:3001 &> /dev/null; then
            echo -e "${GREEN}✅ 前端服务正常运行${NC}"
            break
        elif [ $i -eq 20 ]; then
            echo -e "${RED}❌ 前端服务启动失败${NC}"
            docker compose -f docker-compose-optimized.yml logs frontend
            return 1
        else
            echo -e "${YELLOW}⏳ 等待前端服务... ($i/20)${NC}"
            sleep 3
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📱 访问地址：${NC}"
    echo -e "   前端应用: ${GREEN}http://localhost:3001${NC}"
    echo -e "   后端API:  ${GREEN}http://localhost:3000${NC}"
    echo ""
    echo -e "${BLUE}📋 常用命令：${NC}"
    echo -e "   查看日志: ${YELLOW}docker compose -f docker-compose-optimized.yml logs -f${NC}"
    echo -e "   停止服务: ${YELLOW}docker compose -f docker-compose-optimized.yml down${NC}"
    echo -e "   重启服务: ${YELLOW}docker compose -f docker-compose-optimized.yml restart${NC}"
    echo ""
    echo -e "${GREEN}✨ 牛马动物园已成功部署到Docker！✨${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}=====================================\n"
    echo -e "🐂🐴 牛马动物园 Docker 优化部署工具\n"
    echo -e "=====================================${NC}\n"
    
    check_docker
    create_env
    check_ports
    stop_containers
    start_services
    verify_deployment
    show_access_info
    
    echo -e "\n${GREEN}🎊 优化版部署完成！祝您使用愉快！${NC}"
}

# 捕获错误
trap 'echo -e "${RED}❌ 部署过程中发生错误${NC}"; exit 1' ERR

# 执行主函数
main "$@"