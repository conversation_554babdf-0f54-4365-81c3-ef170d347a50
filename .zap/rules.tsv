# ZAP安全扫描规则配置文件
# 格式: WARN|FAIL|IGNORE|INFO URL_REGEX

# 忽略静态资源
IGNORE	(.*\.js$|.*\.css$|.*\.png$|.*\.jpg$|.*\.jpeg$|.*\.gif$|.*\.svg$|.*\.ico$)

# 忽略测试端点
IGNORE	.*\/test\/.*
IGNORE	.*\/health.*

# API安全检查
WARN	.*\/api\/v1\/auth\/.*
WARN	.*\/api\/v1\/user\/.*
FAIL	.*\/api\/v1\/admin\/.*

# 重要端点必须通过所有安全检查
FAIL	.*\/api\/v1\/auth\/login
FAIL	.*\/api\/v1\/auth\/register
FAIL	.*\/api\/v1\/payment\/.*

# SQL注入检查
FAIL	.*\?.*'.*
FAIL	.*\?.*union.*
FAIL	.*\?.*select.*

# XSS检查
FAIL	.*\?.*<script.*
FAIL	.*\?.*javascript:.*

# 目录遍历检查
FAIL	.*\.\.\/.*
FAIL	.*\.\.\\.*

# 信息泄露检查
WARN	.*\.env.*
WARN	.*config.*
WARN	.*backup.*

# CSRF检查
WARN	.*\/api\/v1\/.*\?(.*&)?_method=.*