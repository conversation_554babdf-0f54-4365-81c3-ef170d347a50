# 🐂🐴 牛马动物园 Docker 部署指南

## 📋 部署概述

本项目支持完整的Docker容器化部署，包含前端、后端和反向代理服务。

## 🚀 快速开始

### 方式1：自动部署脚本（推荐）

```bash
# 1. 克隆项目
git clone <your-repo-url>
cd niuma

# 2. 设置环境变量（重要！）
export GEMINI_API_KEY="your_actual_gemini_api_key"

# 3. 运行自动部署脚本
./docker-deploy.sh
```

### 方式2：手动部署

```bash
# 1. 创建环境配置
cp .env.example .env
# 编辑 .env 文件，设置 GEMINI_API_KEY

# 2. 启动服务
docker-compose up -d

# 3. 查看状态
docker-compose ps
```

## 🏗️ 架构说明

### 服务组件

- **Frontend**: React + Vite，端口 3001
- **Backend**: Express + AI服务，端口 3000  
- **Nginx**: 反向代理（可选），端口 80

### 网络架构

```
Internet
    ↓
[ Nginx:80 ] (可选)
    ↓
[ Frontend:3001 ] ← → [ Backend:3000 ]
```

## 📁 Docker 文件说明

- `docker-compose.yml` - 生产环境配置
- `docker-compose.dev.yml` - 开发环境配置
- `frontend/Dockerfile` - 前端镜像构建
- `backend/Dockerfile.simple` - 后端镜像构建
- `nginx/nginx.conf` - Nginx 配置

## 🔧 环境变量配置

创建 `.env` 文件：

```env
# AI服务配置
GEMINI_API_KEY=your_gemini_api_key_here
AI_GENERATION_ENABLED=true
USE_REAL_AI=true
PREFERRED_AI_MODEL=gemini

# 应用配置  
NODE_ENV=production
FRONTEND_PORT=3001
BACKEND_PORT=3000
NGINX_PORT=80
```

## 🎯 访问地址

部署完成后：

- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:3000
- **API测试**: http://localhost:3000/api/v1/zoo/animals
- **完整服务**(启用Nginx): http://localhost

## 🛠️ 开发环境

```bash
# 启动开发环境（热重载）
docker-compose -f docker-compose.dev.yml up -d

# 查看开发环境日志
docker-compose -f docker-compose.dev.yml logs -f
```

## 📊 常用操作

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 所有服务日志
docker-compose logs -f

# 单个服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启单个服务
docker-compose restart backend
```

### 停止服务
```bash
docker-compose down
```

### 完全清理
```bash
# 停止并删除所有相关资源
docker-compose down --volumes --remove-orphans
docker image prune -f
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :3001
   ```

2. **GEMINI_API_KEY 未设置**
   - 确保在 `.env` 文件中正确设置了API密钥
   - 检查环境变量：`echo $GEMINI_API_KEY`

3. **Canvas 依赖问题**
   - 后端Dockerfile已包含必要的系统依赖
   - 如果构建失败，检查Alpine包管理器状态

4. **内存不足**
   ```bash
   # 检查Docker资源限制
   docker system df
   docker system prune -f
   ```

### 健康检查

```bash
# 检查容器健康状态
docker-compose ps

# 测试API连通性
curl http://localhost:3000/api/v1/zoo/animals

# 测试前端访问
curl http://localhost:3001
```

## 🚀 生产环境部署

### 启用完整反向代理
```bash
# 使用生产配置文件
docker-compose --profile production up -d
```

### SSL 配置
1. 将SSL证书放在 `nginx/ssl/` 目录
2. 修改 `nginx/nginx.conf` 添加SSL配置
3. 重启Nginx服务

### 性能优化
- 启用 gzip 压缩（已配置）
- 配置静态资源缓存（已配置）
- 添加CDN支持（可选）

## 📝 注意事项

1. **安全**：
   - 生产环境请使用强密码
   - 定期更新Docker镜像
   - 配置防火墙规则

2. **备份**：
   - 定期备份上传文件：`docker-compose exec backend tar czf backup.tar.gz uploads/`
   - 导出数据库（如果使用）

3. **监控**：
   - 监控容器健康状态
   - 监控磁盘空间使用
   - 监控API响应时间

## 🆘 获取帮助

如果遇到问题，请：

1. 查看详细日志：`docker-compose logs -f`
2. 检查容器状态：`docker-compose ps`
3. 验证环境变量：`docker-compose config`
4. 重新构建镜像：`docker-compose build --no-cache`

---

🎉 **祝您部署愉快！** 如有问题，欢迎提交Issue。