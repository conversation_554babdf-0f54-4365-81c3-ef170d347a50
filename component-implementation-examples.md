# 现代化组件实现示例

## 🚀 主页 Hero 区域现代化实现

### 更新后的 HomePage.tsx 核心部分

```tsx
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowRight, Sparkles, Users, TrendingUp, Award } from 'lucide-react'
import { useAppSelector } from '@/store'
import toast from 'react-hot-toast'

export default function HomePage() {
  const navigate = useNavigate()
  const { currentUser } = useAppSelector(state => state.user)
  const [hasGeneratedAvatar, setHasGeneratedAvatar] = useState(false)
  const [stats, setStats] = useState({
    totalUsers: 2847,
    activeAnimals: 156,
    totalInteractions: 12459
  })

  // Hero Section with Modern Design
  const HeroSection = () => (
    <section className="relative min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-primary-200/30 to-secondary-200/30 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-secondary-200/20 to-primary-200/20 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        
        {/* Floating Elements */}
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-32 left-1/4 text-4xl opacity-60"
        >
          💼
        </motion.div>
        <motion.div
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
          className="absolute top-1/3 right-1/3 text-5xl opacity-40"
        >
          🎨
        </motion.div>
      </div>
      
      <div className="relative z-10 container mx-auto px-6 py-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center min-h-screen">
          {/* Left Content */}
          <div className="text-center lg:text-left space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4 mr-2" />
                AI驱动的职场社交平台
              </div>
              
              <h1 className="text-5xl lg:text-7xl font-display font-bold text-neutral-900 mb-6 leading-tight">
                欢迎来到
                <span className="block text-gradient mt-2">牛马动物园</span>
              </h1>
              
              <p className="text-xl text-neutral-600 leading-relaxed max-w-2xl lg:mx-0 mx-auto">
                用AI科技重新定义职场体验。绘制你的自画像，生成专属的打工人形象，
                在虚拟动物园中与同伴们一起工作、成长、快乐生活。
              </p>
            </motion.div>
            
            {/* Stats Bar */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="flex justify-center lg:justify-start space-x-8 text-center"
            >
              <div>
                <div className="text-2xl font-bold text-primary-600">{stats.totalUsers.toLocaleString()}+</div>
                <div className="text-sm text-neutral-500">注册用户</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-secondary-600">{stats.activeAnimals}+</div>
                <div className="text-sm text-neutral-500">活跃动物</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary-600">{stats.totalInteractions.toLocaleString()}+</div>
                <div className="text-sm text-neutral-500">总互动数</div>
              </div>
            </motion.div>
            
            {/* User Status Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-soft border border-white/50"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-neutral-900 mb-1 flex items-center">
                    <span className="mr-2">👋</span>
                    {currentUser?.username || '打工人'}
                  </h3>
                  <p className="text-sm text-neutral-600">
                    {hasGeneratedAvatar ? (
                      <span className="flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                        已拥有专属形象
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <span className="w-2 h-2 bg-amber-400 rounded-full mr-2 animate-pulse"></span>
                        准备开启职场之旅
                      </span>
                    )}
                  </p>
                </div>
                <div className="text-4xl animate-bounce-gentle">
                  {hasGeneratedAvatar ? '🦄' : '🎯'}
                </div>
              </div>
            </motion.div>
            
            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              <button
                onClick={hasGeneratedAvatar ? () => navigate('/zoo') : () => navigate('/drawing')}
                className="btn-gradient btn-lg px-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 group"
              >
                <span className="mr-2 text-xl">
                  {hasGeneratedAvatar ? '🎪' : '🎨'}
                </span>
                {hasGeneratedAvatar ? '进入动物园' : '开始创造'}
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
              
              <button
                onClick={() => navigate('/test')}
                className="btn-outline btn-lg px-8 group"
              >
                <span className="mr-2">🔮</span>
                人格测试
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </motion.div>
          </div>
          
          {/* Right Visual */}
          <div className="relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-primary-400 via-secondary-400 to-primary-500 rounded-3xl shadow-2xl flex items-center justify-center overflow-hidden relative">
                {/* 3D Zoo Preview */}
                <div className="text-9xl animate-bounce-gentle filter drop-shadow-lg">
                  🎪
                </div>
                
                {/* Overlay Elements */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/90 backdrop-blur-md rounded-xl p-4 text-center">
                    <p className="text-sm font-medium text-neutral-800">实时在线</p>
                    <p className="text-2xl font-bold text-primary-600">{stats.activeAnimals}</p>
                  </div>
                </div>
              </div>
              
              {/* Floating Action Elements */}
              <motion.div
                animate={{ y: [0, -8, 0], rotate: [0, 5, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-6 -right-6 bg-yellow-400 rounded-2xl p-4 shadow-lg"
              >
                <span className="text-2xl">✨</span>
              </motion.div>
              
              <motion.div
                animate={{ y: [0, -6, 0], rotate: [0, -5, 0] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                className="absolute -bottom-6 -left-6 bg-pink-400 rounded-2xl p-4 shadow-lg"
              >
                <span className="text-2xl">🎨</span>
              </motion.div>
              
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                className="absolute top-1/2 -left-8 bg-secondary-400 rounded-2xl p-3 shadow-lg"
              >
                <span className="text-xl">💼</span>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )

  // Modern Features Section
  const FeaturesSection = () => {
    const features = [
      {
        id: 'ai-analysis',
        icon: '🧠',
        title: 'AI个性分析',
        description: '通过先进的AI技术分析你的绘画风格，深入了解你的性格特征和职场潜能。',
        gradient: 'from-blue-400 to-indigo-500',
        path: '/test',
        stats: '准确率98%'
      },
      {
        id: 'zoo-experience',
        icon: '🎪',
        title: '3D虚拟动物园',
        description: '在immersive的3D环境中与其他打工人互动，体验真实的职场社交体验。',
        gradient: 'from-green-400 to-teal-500',
        path: '/zoo',
        stats: '156+在线'
      },
      {
        id: 'creative-drawing',
        icon: '🎨',
        title: '创意绘画生成',
        description: '用画笔表达自己，AI将基于你的绘画风格生成独特的拟人化角色形象。',
        gradient: 'from-purple-400 to-pink-500',
        path: '/drawing',
        stats: '无限创意'
      },
      {
        id: 'social-ranking',
        icon: '🏆',
        title: '社交排行榜',
        description: '与其他打工人比较各项能力指标，在健康的竞争中不断提升自己。',
        gradient: 'from-yellow-400 to-orange-500',
        path: '/ranking',
        stats: '实时更新'
      },
      {
        id: 'community',
        icon: '💬',
        title: '打工人社区',
        description: '分享经验、交流心得，建立属于打工人的温暖社交网络和互助平台。',
        gradient: 'from-red-400 to-pink-500',
        path: '/posts',
        stats: '温暖社区'
      },
      {
        id: 'real-time',
        icon: '⚡',
        title: '实时互动体验',
        description: '支持实时移动、聊天互动，体验身临其境的虚拟办公室社交乐趣。',
        gradient: 'from-cyan-400 to-blue-500',
        path: '/zoo',
        stats: '毫秒级响应'
      }
    ]

    return (
      <section className="py-24 bg-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FF6B35' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>
        
        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Award className="w-4 h-4 mr-2" />
                核心功能特色
              </div>
              
              <h2 className="text-4xl lg:text-5xl font-display font-bold text-neutral-900 mb-6">
                成为独一无二的职场达人
              </h2>
              
              <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
                通过先进的AI技术，从绘画分析到角色生成，在虚拟环境中体验真实的职场社交，
                <br className="hidden md:block" />
                发现更好的自己，建立更广的人脉。
              </p>
            </motion.div>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div 
                  className="card-interactive h-full p-8 cursor-pointer"
                  onClick={() => navigate(feature.path)}
                >
                  {/* Icon and Badge */}
                  <div className="flex items-start justify-between mb-6">
                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <span className="text-3xl text-white filter drop-shadow-sm">
                        {feature.icon}
                      </span>
                    </div>
                    <div className="bg-gradient-to-r from-primary-50 to-secondary-50 text-primary-700 px-3 py-1 rounded-full text-xs font-medium">
                      {feature.stats}
                    </div>
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-xl font-semibold text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
                    {feature.title}
                  </h3>
                  
                  <p className="text-neutral-600 leading-relaxed mb-6">
                    {feature.description}
                  </p>
                  
                  {/* Action */}
                  <div className="flex items-center text-primary-500 group-hover:text-primary-600 transition-colors">
                    <span className="text-sm font-medium mr-2">立即体验</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      {/* ... other sections */}
    </div>
  )
}
```

## 🎨 绘画页面工具栏现代化

### 增强版 DrawingCanvas 工具栏

```tsx
import { useState, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Palette, Brush, Eraser, RotateCcw, Save, 
  Download, Zap, Sparkles, Eye, EyeOff 
} from 'lucide-react'

interface DrawingToolbarProps {
  onToolChange: (tool: string) => void
  onColorChange: (color: string) => void
  onBrushSizeChange: (size: number) => void
  onClear: () => void
  onSave: () => void
  currentTool: string
  currentColor: string
  brushSize: number
}

export const ModernDrawingToolbar: React.FC<DrawingToolbarProps> = ({
  onToolChange,
  onColorChange,
  onBrushSizeChange,
  onClear,
  onSave,
  currentTool,
  currentColor,
  brushSize
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)

  const tools = [
    { id: 'brush', icon: Brush, name: '画笔', shortcut: 'B' },
    { id: 'eraser', icon: Eraser, name: '橡皮擦', shortcut: 'E' },
    { id: 'fill', icon: Palette, name: '填充', shortcut: 'F' }
  ]

  const colors = [
    '#000000', '#FFFFFF', '#FF6B35', '#4ECDC4', 
    '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
    '#FF7675', '#74B9FF', '#A29BFE', '#6C5CE7',
    '#FDCB6E', '#E17055', '#00B894', '#00CEC9'
  ]

  const brushSizes = [2, 4, 8, 12, 16, 24, 32, 48]

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`fixed left-6 top-1/2 -translate-y-1/2 z-40 transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-80'
      }`}
    >
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/50 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-500 to-secondary-500 p-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Sparkles className="w-6 h-6 mr-2" />
              {!isCollapsed && (
                <div>
                  <h3 className="font-semibold">创作工具</h3>
                  <p className="text-xs opacity-80">释放你的创造力</p>
                </div>
              )}
            </div>
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 hover:bg-white/20 rounded-lg transition-colors"
            >
              <motion.div
                animate={{ rotate: isCollapsed ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                {isCollapsed ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </motion.div>
            </button>
          </div>
        </div>

        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="p-6 space-y-6"
            >
              {/* Tools Section */}
              <div>
                <label className="text-sm font-medium text-neutral-700 mb-3 block flex items-center">
                  <Brush className="w-4 h-4 mr-2" />
                  绘画工具
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {tools.map(tool => {
                    const Icon = tool.icon
                    return (
                      <button
                        key={tool.id}
                        onClick={() => onToolChange(tool.id)}
                        className={`
                          p-3 rounded-xl border-2 transition-all relative group
                          ${currentTool === tool.id
                            ? 'border-primary-500 bg-primary-50 shadow-md' 
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }
                        `}
                        title={`${tool.name} (${tool.shortcut})`}
                      >
                        <Icon className={`w-6 h-6 mx-auto ${
                          currentTool === tool.id ? 'text-primary-600' : 'text-neutral-600'
                        }`} />
                        <span className="text-xs mt-1 block font-medium">
                          {tool.name}
                        </span>
                        
                        {/* Keyboard shortcut badge */}
                        <div className="absolute -top-1 -right-1 bg-neutral-600 text-white text-xs rounded px-1 py-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                          {tool.shortcut}
                        </div>
                      </button>
                    )
                  })}
                </div>
              </div>

              {/* Colors Section */}
              <div>
                <label className="text-sm font-medium text-neutral-700 mb-3 block flex items-center">
                  <Palette className="w-4 h-4 mr-2" />
                  颜色选择
                </label>
                
                {/* Current Color Display */}
                <div className="flex items-center mb-3">
                  <div 
                    className="w-12 h-12 rounded-xl border-4 border-white shadow-lg mr-3"
                    style={{ backgroundColor: currentColor }}
                  />
                  <div>
                    <p className="text-sm font-medium text-neutral-800">当前颜色</p>
                    <p className="text-xs text-neutral-500 font-mono">{currentColor}</p>
                  </div>
                </div>
                
                {/* Color Palette */}
                <div className="grid grid-cols-8 gap-2">
                  {colors.map(color => (
                    <button
                      key={color}
                      onClick={() => onColorChange(color)}
                      className={`
                        w-8 h-8 rounded-lg border-2 transition-all hover:scale-110
                        ${currentColor === color 
                          ? 'border-neutral-800 shadow-lg' 
                          : 'border-white shadow-md hover:shadow-lg'
                        }
                      `}
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
                
                {/* Custom Color Picker */}
                <button
                  onClick={() => setShowColorPicker(!showColorPicker)}
                  className="mt-3 w-full p-2 border-2 border-dashed border-gray-300 rounded-lg text-sm text-neutral-600 hover:border-primary-300 hover:text-primary-600 transition-colors"
                >
                  <Zap className="w-4 h-4 inline mr-2" />
                  自定义颜色
                </button>
              </div>

              {/* Brush Size Section */}
              <div>
                <label className="text-sm font-medium text-neutral-700 mb-3 block">
                  画笔大小: {brushSize}px
                </label>
                
                {/* Size Preview */}
                <div className="flex items-center justify-center mb-4 h-16 bg-neutral-50 rounded-lg">
                  <div 
                    className="rounded-full transition-all duration-200"
                    style={{ 
                      width: `${Math.min(brushSize, 48)}px`, 
                      height: `${Math.min(brushSize, 48)}px`,
                      backgroundColor: currentColor 
                    }}
                  />
                </div>
                
                {/* Size Slider */}
                <input
                  type="range"
                  min="2"
                  max="48"
                  value={brushSize}
                  onChange={(e) => onBrushSizeChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider mb-3"
                  style={{
                    background: `linear-gradient(to right, #FF6B35 0%, #FF6B35 ${(brushSize - 2) / 46 * 100}%, #E5E5E5 ${(brushSize - 2) / 46 * 100}%, #E5E5E5 100%)`
                  }}
                />
                
                {/* Preset Sizes */}
                <div className="flex space-x-2">
                  {[4, 8, 16, 32].map(size => (
                    <button
                      key={size}
                      onClick={() => onBrushSizeChange(size)}
                      className={`
                        flex-1 py-2 px-3 rounded-lg text-xs font-medium transition-colors
                        ${brushSize === size
                          ? 'bg-primary-500 text-white'
                          : 'bg-gray-100 text-neutral-600 hover:bg-gray-200'
                        }
                      `}
                    >
                      {size}px
                    </button>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="grid grid-cols-2 gap-3 pt-4 border-t border-gray-200">
                <button
                  onClick={onClear}
                  className="btn-outline btn-sm flex items-center justify-center"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  清空
                </button>
                <button
                  onClick={onSave}
                  className="btn-primary btn-sm flex items-center justify-center"
                >
                  <Save className="w-4 h-4 mr-2" />
                  保存
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}
```

## 🏆 全新排行榜组件实现

### RankingPage 完整实现

```tsx
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Trophy, Medal, Star, TrendingUp, Users, 
  Calendar, Filter, Search, Award, Crown,
  Zap, Heart, Lightbulb, Briefcase
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface RankingUser {
  id: string
  name: string
  avatar: string
  animalType: 'OXHORSE' | 'PET' | 'DIVINE'
  score: number
  rank: number
  level: number
  title: string
  badges: string[]
  stats: {
    workEfficiency: number
    happiness: number
    creativity: number
    socialSkill: number
  }
  trendChange: number // +1, 0, -1
}

export default function RankingPage() {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overall')
  const [timeRange, setTimeRange] = useState('week')
  const [searchTerm, setSearchTerm] = useState('')
  const [rankings, setRankings] = useState<RankingUser[]>([])
  const [userRank, setUserRank] = useState(42)
  const [isLoading, setIsLoading] = useState(true)

  const rankingCategories = [
    { 
      id: 'overall', 
      name: '综合排行', 
      icon: Trophy, 
      color: 'yellow',
      description: '综合各项能力的总体排名'
    },
    { 
      id: 'work', 
      name: '工作效率', 
      icon: Briefcase, 
      color: 'blue',
      description: '工作表现和效率排名'
    },
    { 
      id: 'happiness', 
      name: '快乐指数', 
      icon: Heart, 
      color: 'pink',
      description: '职场快乐度和满意度排名'
    },
    { 
      id: 'social', 
      name: '社交达人', 
      icon: Users, 
      color: 'green',
      description: '社交互动和人际关系排名'
    },
    { 
      id: 'creative', 
      name: '创意之星', 
      icon: Lightbulb, 
      color: 'purple',
      description: '创新能力和创意表现排名'
    }
  ]

  const timeRanges = [
    { id: 'day', name: '今日', icon: Calendar },
    { id: 'week', name: '本周', icon: Calendar },
    { id: 'month', name: '本月', icon: Calendar },
    { id: 'all', name: '总榜', icon: Star }
  ]

  // Mock data - 在实际应用中应该从API获取
  useEffect(() => {
    const loadRankings = async () => {
      setIsLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockData: RankingUser[] = [
        {
          id: '1',
          name: '超级打工牛马',
          avatar: '/avatar1.jpg',
          animalType: 'OXHORSE',
          score: 9847,
          rank: 1,
          level: 25,
          title: '工作狂魔',
          badges: ['🏆', '⚡', '🔥'],
          stats: { workEfficiency: 98, happiness: 75, creativity: 85, socialSkill: 90 },
          trendChange: 1
        },
        {
          id: '2',
          name: '快乐小宠物',
          avatar: '/avatar2.jpg',
          animalType: 'PET',
          score: 9234,
          rank: 2,
          level: 22,
          title: '开心果',
          badges: ['😊', '🎉', '💖'],
          stats: { workEfficiency: 70, happiness: 99, creativity: 88, socialSkill: 95 },
          trendChange: 0
        },
        {
          id: '3',
          name: '职场神兽王',
          avatar: '/avatar3.jpg',
          animalType: 'DIVINE',
          score: 8956,
          rank: 3,
          level: 30,
          title: '传奇领袖',
          badges: ['👑', '⭐', '🚀'],
          stats: { workEfficiency: 95, happiness: 85, creativity: 100, socialSkill: 92 },
          trendChange: -1
        }
        // ... more mock data
      ]
      
      setRankings(mockData)
      setIsLoading(false)
    }
    
    loadRankings()
  }, [activeTab, timeRange])

  const getAnimalEmoji = (type: string) => {
    switch (type) {
      case 'OXHORSE': return '🐂'
      case 'PET': return '🐱'
      case 'DIVINE': return '🦌'
      default: return '❓'
    }
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="w-6 h-6 text-yellow-500" />
    if (rank === 2) return <Medal className="w-6 h-6 text-gray-400" />
    if (rank === 3) return <Award className="w-6 h-6 text-amber-600" />
    return <span className="text-lg font-bold text-neutral-600">#{rank}</span>
  }

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-4 h-4 text-green-500" />
    if (change < 0) return <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />
    return <div className="w-4 h-4 bg-gray-300 rounded-full" />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50">
      <div className="container mx-auto px-6 py-12">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full mb-6 shadow-xl">
            <Trophy className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-neutral-900 mb-4">
            打工人排行榜
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto leading-relaxed">
            看看谁是最优秀的职场达人！在各个维度上展现你的实力，
            <br className="hidden md:block" />
            与全球打工人一起竞技成长
          </p>
        </motion.div>

        {/* Navigation */}
        <div className="mb-8 space-y-6">
          {/* Category Tabs */}
          <div className="flex flex-wrap justify-center gap-3">
            {rankingCategories.map(category => {
              const Icon = category.icon
              return (
                <motion.button
                  key={category.id}
                  onClick={() => setActiveTab(category.id)}
                  className={`
                    px-6 py-3 rounded-2xl font-medium transition-all group relative overflow-hidden
                    ${activeTab === category.id
                      ? `bg-${category.color}-500 text-white shadow-lg scale-105`
                      : 'bg-white text-neutral-700 hover:bg-neutral-50 shadow-md hover:shadow-lg'
                    }
                  `}
                  whileHover={{ scale: activeTab === category.id ? 1.05 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center relative z-10">
                    <Icon className="w-5 h-5 mr-2" />
                    <span>{category.name}</span>
                  </div>
                  
                  {/* Animated background */}
                  {activeTab === category.id && (
                    <motion.div
                      layoutId="activeTab"
                      className={`absolute inset-0 bg-${category.color}-500 rounded-2xl`}
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </motion.button>
              )
            })}
          </div>

          {/* Time Range & Search */}
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            {/* Time Range */}
            <div className="flex bg-white rounded-2xl p-1 shadow-md">
              {timeRanges.map(range => (
                <button
                  key={range.id}
                  onClick={() => setTimeRange(range.id)}
                  className={`
                    px-4 py-2 rounded-xl text-sm font-medium transition-all flex items-center
                    ${timeRange === range.id
                      ? 'bg-primary-500 text-white shadow-md'
                      : 'text-neutral-600 hover:text-neutral-800 hover:bg-neutral-50'
                    }
                  `}
                >
                  <range.icon className="w-4 h-4 mr-1" />
                  {range.name}
                </button>
              ))}
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-neutral-400" />
              <input
                type="text"
                placeholder="搜索用户名..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10 pr-4 py-2 w-80 bg-white"
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Rankings List */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
              {/* Top 3 Podium */}
              <div className="bg-gradient-to-r from-yellow-400 via-amber-400 to-orange-400 p-8">
                <div className="grid md:grid-cols-3 gap-6">
                  {rankings.slice(0, 3).map((user, index) => (
                    <motion.div
                      key={user.id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`text-center ${index === 0 ? 'md:order-2' : index === 1 ? 'md:order-1' : 'md:order-3'}`}
                    >
                      <div className="relative mb-4">
                        {/* Podium Height Effect */}
                        <div className={`
                          mx-auto bg-white/20 rounded-t-lg
                          ${index === 0 ? 'w-20 h-6' : index === 1 ? 'w-16 h-4' : 'w-16 h-2'}
                        `} />
                        
                        <div className="relative">
                          <img
                            src={user.avatar}
                            alt={user.name}
                            className="w-20 h-20 rounded-full mx-auto border-4 border-white shadow-xl"
                          />
                          <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                            {getRankIcon(user.rank)}
                          </div>
                          <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 text-2xl">
                            {getAnimalEmoji(user.animalType)}
                          </div>
                        </div>
                      </div>
                      
                      <h3 className="font-bold text-white mb-1 text-lg">{user.name}</h3>
                      <p className="text-yellow-100 text-sm mb-2">{user.title}</p>
                      <div className="bg-white/20 rounded-full px-3 py-1 inline-block">
                        <p className="text-white font-semibold">{user.score.toLocaleString()}分</p>
                      </div>
                      
                      {/* Badges */}
                      <div className="flex justify-center space-x-1 mt-2">
                        {user.badges.map((badge, idx) => (
                          <span key={idx} className="text-lg">{badge}</span>
                        ))}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Detailed Rankings */}
              <div className="p-6">
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="animate-pulse flex items-center space-x-4 p-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                        </div>
                        <div className="h-6 bg-gray-200 rounded w-16"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {rankings.slice(3).map((user, index) => (
                      <motion.div
                        key={user.id}
                        initial={{ opacity: 0, x: -30 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="flex items-center justify-between p-4 rounded-2xl hover:bg-gray-50 transition-colors group cursor-pointer"
                        onClick={() => navigate(`/profile/${user.id}`)}
                      >
                        <div className="flex items-center space-x-4">
                          {/* Rank */}
                          <div className="w-12 text-center flex items-center justify-center">
                            {getRankIcon(user.rank)}
                          </div>
                          
                          {/* Avatar */}
                          <div className="relative">
                            <img
                              src={user.avatar}
                              alt={user.name}
                              className="w-14 h-14 rounded-full border-2 border-gray-200 group-hover:border-primary-300 transition-colors"
                            />
                            <div className="absolute -bottom-1 -right-1 text-lg">
                              {getAnimalEmoji(user.animalType)}
                            </div>
                          </div>
                          
                          {/* Info */}
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold text-neutral-900 group-hover:text-primary-600 transition-colors">
                                {user.name}
                              </h4>
                              <span className="text-sm text-neutral-500">Lv.{user.level}</span>
                              {getTrendIcon(user.trendChange)}
                            </div>
                            <p className="text-sm text-neutral-500">{user.title}</p>
                            <div className="flex space-x-1 mt-1">
                              {user.badges.slice(0, 3).map((badge, idx) => (
                                <span key={idx} className="text-sm">{badge}</span>
                              ))}
                            </div>
                          </div>
                        </div>
                        
                        {/* Score */}
                        <div className="text-right">
                          <div className="font-bold text-xl text-neutral-900">
                            {user.score.toLocaleString()}
                          </div>
                          <div className="text-sm text-neutral-500">分</div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Personal Rank Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-2xl p-6 shadow-lg"
            >
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Star className="w-5 h-5 mr-2 text-yellow-500" />
                我的排名
              </h3>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <span className="text-2xl text-white font-bold">#{userRank}</span>
                </div>
                <p className="font-semibold text-neutral-900 text-lg">8,234分</p>
                <p className="text-sm text-neutral-500">本周排名</p>
                <div className="mt-3 flex items-center justify-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-600">上升3名</span>
                </div>
              </div>
            </motion.div>

            {/* Achievement Badges */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2 text-purple-500" />
                成就徽章
              </h3>
              <div className="grid grid-cols-4 gap-3">
                {['🏆', '⚡', '🔥', '💪', '🎯', '⭐', '👑', '🚀'].map((badge, idx) => (
                  <motion.div
                    key={idx}
                    whileHover={{ scale: 1.1 }}
                    className={`
                      aspect-square rounded-xl border-2 flex items-center justify-center text-2xl cursor-pointer transition-all
                      ${idx < 4 
                        ? 'border-yellow-400 bg-yellow-50 hover:bg-yellow-100' 
                        : 'border-gray-200 bg-gray-50 grayscale hover:grayscale-0'
                      }
                    `}
                    title={idx < 4 ? '已解锁' : '未解锁'}
                  >
                    {badge}
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Weekly Tasks */}
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border border-purple-100">
              <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-purple-600" />
                本周挑战
              </h3>
              <div className="space-y-3">
                {[
                  { name: '完成绘画创作', reward: 100, completed: true, emoji: '🎨' },
                  { name: '互动5次以上', reward: 50, completed: true, emoji: '🤝' },
                  { name: '连续登录7天', reward: 200, completed: false, emoji: '📅' },
                  { name: '获得10个赞', reward: 75, completed: false, emoji: '👍' }
                ].map((task, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-lg mr-3">{task.emoji}</span>
                      <div>
                        <p className="font-medium text-purple-900 text-sm">{task.name}</p>
                        <p className="text-xs text-purple-600">+{task.reward}分</p>
                      </div>
                    </div>
                    <div className={`
                      w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors
                      ${task.completed 
                        ? 'bg-green-500 border-green-500' 
                        : 'border-purple-300'
                      }
                    `}>
                      {task.completed && <span className="text-white text-sm">✓</span>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
```

这些组件实现展示了如何将现代设计理念转化为实际的React代码。每个组件都包含了：

1. **现代化视觉设计**: 使用渐变、阴影、圆角等现代元素
2. **流畅动画效果**: Framer Motion提供的进场、过渡动画
3. **响应式布局**: 适配不同屏幕尺寸的网格系统
4. **交互性增强**: 悬停效果、点击反馈、状态变化
5. **可访问性支持**: 语义化标签、键盘导航、ARIA属性
6. **性能优化**: 懒加载、条件渲染、状态管理

通过这些实现，"打工人动物园"将从功能完整但视觉粗糙的应用，升级为具有专业级用户体验的现代化产品。